<!--小程序客服页面示例-->
<view class="container">
  <!-- 顶部导航 -->
  <view class="header">
    <view class="header-title">
      <text class="title">在线客服</text>
      <text class="subtitle">专业充电桩维修服务</text>
    </view>
    <view class="status-indicator {{isConnected ? 'connected' : 'disconnected'}}">
      <text>{{isConnected ? '客服在线' : '等待接入'}}</text>
    </view>
  </view>

  <!-- 消息列表 -->
  <scroll-view class="messages-container" scroll-y="true" scroll-top="{{scrollTop}}" scroll-into-view="{{toView}}">
    <view class="messages-list">
      <block wx:for="{{messages}}" wx:key="id">
        <view class="message-item {{item.senderType === 'user' ? 'user' : 'admin'}}" id="msg{{index}}">
          <!-- 管理员消息 -->
          <view wx:if="{{item.senderType === 'admin'}}" class="message admin">
            <view class="avatar">
              <image src="{{item.senderAvatar || '/images/admin-avatar.png'}}" mode="aspectFill"></image>
            </view>
            <view class="message-content">
              <view class="sender-name">{{item.senderName || '客服'}}</view>
              <view class="content-bubble">
                <text>{{item.content}}</text>
              </view>
              <view class="message-time">{{item.timeStr}}</view>
            </view>
          </view>

          <!-- 用户消息 -->
          <view wx:else class="message user">
            <view class="message-content">
              <view class="content-bubble">
                <text>{{item.content}}</text>
              </view>
              <view class="message-time">{{item.timeStr}}</view>
            </view>
            <view class="avatar">
              <image src="{{userInfo.avatarUrl || '/images/user-avatar.png'}}" mode="aspectFill"></image>
            </view>
          </view>
        </view>
      </block>

      <!-- 空状态 -->
      <view wx:if="{{messages.length === 0}}" class="empty-state">
        <image src="/images/chat-empty.png" class="empty-icon"></image>
        <text class="empty-text">开始与客服聊天吧</text>
        <text class="empty-subtitle">我们将为您提供专业的充电桩维修服务</text>
      </view>
    </view>
  </scroll-view>

  <!-- 快捷回复（可选） -->
  <view wx:if="{{quickReplies.length > 0 && !hasStartedChat}}" class="quick-replies">
    <view class="quick-replies-title">常见问题</view>
    <view class="quick-replies-list">
      <view wx:for="{{quickReplies}}" wx:key="id" class="quick-reply-item" bindtap="sendQuickReply" data-content="{{item.content}}">
        <text>{{item.title}}</text>
      </view>
    </view>
  </view>

  <!-- 输入区域 -->
  <view class="input-area">
    <view class="input-container">
      <textarea 
        class="message-input" 
        placeholder="请输入您的问题..." 
        value="{{inputText}}"
        bindinput="onInputChange"
        auto-height
        maxlength="500"
        show-confirm-bar="{{false}}"
      ></textarea>
      <button class="send-button {{inputText.trim() ? 'active' : ''}}" bindtap="sendMessage" disabled="{{!inputText.trim()}}">
        <text>发送</text>
      </button>
    </view>
  </view>
</view>

<!-- 加载提示 -->
<view wx:if="{{loading}}" class="loading-overlay">
  <view class="loading-content">
    <view class="loading-spinner"></view>
    <text>连接客服中...</text>
  </view>
</view>
