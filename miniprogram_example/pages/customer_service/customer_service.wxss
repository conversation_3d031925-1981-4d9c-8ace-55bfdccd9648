/* 小程序客服页面样式示例 */
.container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

/* 顶部导航 */
.header {
  background: linear-gradient(135deg, #1e88e5 0%, #0d47a1 100%);
  padding: 20rpx 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
}

.header-title .title {
  font-size: 36rpx;
  font-weight: 600;
  display: block;
}

.header-title .subtitle {
  font-size: 24rpx;
  opacity: 0.8;
  display: block;
  margin-top: 8rpx;
}

.status-indicator {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  border: 1px solid rgba(255,255,255,0.3);
}

.status-indicator.connected {
  background-color: rgba(76, 175, 80, 0.2);
  border-color: #4caf50;
}

.status-indicator.disconnected {
  background-color: rgba(255, 152, 0, 0.2);
  border-color: #ff9800;
}

/* 消息容器 */
.messages-container {
  flex: 1;
  padding: 20rpx;
}

.messages-list {
  min-height: 100%;
}

/* 消息项 */
.message-item {
  margin-bottom: 30rpx;
}

.message {
  display: flex;
  align-items: flex-start;
  gap: 20rpx;
}

.message.user {
  flex-direction: row-reverse;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.avatar image {
  width: 100%;
  height: 100%;
}

.message-content {
  max-width: 70%;
  display: flex;
  flex-direction: column;
}

.message.user .message-content {
  align-items: flex-end;
}

.sender-name {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.content-bubble {
  padding: 24rpx 30rpx;
  border-radius: 36rpx;
  word-wrap: break-word;
  position: relative;
}

.message.admin .content-bubble {
  background-color: white;
  color: #333;
  border-bottom-left-radius: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.message.user .content-bubble {
  background-color: #1e88e5;
  color: white;
  border-bottom-right-radius: 8rpx;
}

.message-time {
  font-size: 20rpx;
  color: #999;
  margin-top: 8rpx;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  text-align: center;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  opacity: 0.3;
  margin-bottom: 40rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.empty-subtitle {
  font-size: 28rpx;
  color: #999;
}

/* 快捷回复 */
.quick-replies {
  background-color: white;
  padding: 30rpx;
  border-top: 1px solid #eee;
}

.quick-replies-title {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.quick-replies-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.quick-reply-item {
  padding: 16rpx 24rpx;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 40rpx;
  font-size: 26rpx;
  color: #495057;
  transition: all 0.3s ease;
}

.quick-reply-item:active {
  background-color: #1e88e5;
  color: white;
  border-color: #1e88e5;
}

/* 输入区域 */
.input-area {
  background-color: white;
  padding: 20rpx 30rpx;
  border-top: 1px solid #eee;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
}

.input-container {
  display: flex;
  align-items: flex-end;
  gap: 20rpx;
}

.message-input {
  flex: 1;
  min-height: 80rpx;
  max-height: 200rpx;
  padding: 20rpx 24rpx;
  border: 1px solid #ddd;
  border-radius: 40rpx;
  font-size: 28rpx;
  background-color: #f8f9fa;
}

.send-button {
  width: 120rpx;
  height: 80rpx;
  border-radius: 40rpx;
  background-color: #ccc;
  color: white;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  transition: all 0.3s ease;
}

.send-button.active {
  background-color: #1e88e5;
}

.send-button[disabled] {
  opacity: 0.6;
}

/* 加载提示 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  background-color: white;
  padding: 60rpx 80rpx;
  border-radius: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 30rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #1e88e5;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
