// 小程序客服页面逻辑示例
const app = getApp()

Page({
  data: {
    sessionId: '',
    messages: [],
    inputText: '',
    scrollTop: 0,
    toView: '',
    loading: false,
    isConnected: false,
    hasStartedChat: false,
    userInfo: {},
    quickReplies: [
      { id: 1, title: '充电桩不工作了', content: '您好，我的充电桩突然不工作了，请问可以上门维修吗？' },
      { id: 2, title: '预约维修服务', content: '我想预约充电桩维修服务，请问如何操作？' },
      { id: 3, title: '维修费用咨询', content: '请问充电桩维修大概需要多少费用？' },
      { id: 4, title: '服务范围查询', content: '请问你们的服务范围覆盖哪些地区？' }
    ]
  },

  onLoad(options) {
    this.setData({
      userInfo: app.globalData.userInfo || {}
    })
    this.initCustomerService()
  },

  onShow() {
    // 页面显示时刷新消息
    if (this.data.sessionId) {
      this.loadMessages()
    }
  },

  onHide() {
    // 页面隐藏时清除定时器
    if (this.messageTimer) {
      clearInterval(this.messageTimer)
    }
  },

  onUnload() {
    // 页面卸载时清除定时器
    if (this.messageTimer) {
      clearInterval(this.messageTimer)
    }
  },

  // 初始化客服服务
  initCustomerService() {
    this.setData({ loading: true })
    
    const openId = wx.getStorageSync('openId')
    if (!openId) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
      return
    }

    // 获取或创建会话
    wx.request({
      url: `${app.globalData.baseUrl}/api/customer-service/session`,
      method: 'POST',
      data: {
        openId: openId
      },
      success: (res) => {
        console.log('获取会话结果:', res.data)
        if (res.data.success) {
          this.setData({
            sessionId: res.data.data.sessionId,
            isConnected: res.data.data.status === 'active',
            loading: false
          })
          this.loadMessages()
          this.startMessagePolling()
        } else {
          wx.showToast({
            title: res.data.message || '连接失败',
            icon: 'none'
          })
          this.setData({ loading: false })
        }
      },
      fail: (err) => {
        console.error('连接客服失败:', err)
        wx.showToast({
          title: '连接失败，请稍后重试',
          icon: 'none'
        })
        this.setData({ loading: false })
      }
    })
  },

  // 加载消息历史
  loadMessages() {
    if (!this.data.sessionId) return

    wx.request({
      url: `${app.globalData.baseUrl}/api/customer-service/messages/${this.data.sessionId}`,
      method: 'GET',
      success: (res) => {
        if (res.data.success) {
          const messages = res.data.data.map(msg => ({
            ...msg,
            timeStr: this.formatTime(msg.createdAt)
          }))
          
          this.setData({
            messages: messages,
            hasStartedChat: messages.length > 0
          })
          
          // 滚动到底部
          this.scrollToBottom()
          
          // 标记消息为已读
          this.markAsRead()
        }
      },
      fail: (err) => {
        console.error('加载消息失败:', err)
      }
    })
  },

  // 发送消息
  sendMessage() {
    const content = this.data.inputText.trim()
    if (!content || !this.data.sessionId) return

    const openId = wx.getStorageSync('openId')
    
    wx.request({
      url: `${app.globalData.baseUrl}/api/customer-service/message`,
      method: 'POST',
      data: {
        sessionId: this.data.sessionId,
        openId: openId,
        content: content
      },
      success: (res) => {
        if (res.data.success) {
          this.setData({
            inputText: '',
            hasStartedChat: true
          })
          this.loadMessages()
        } else {
          wx.showToast({
            title: res.data.message || '发送失败',
            icon: 'none'
          })
        }
      },
      fail: (err) => {
        console.error('发送消息失败:', err)
        wx.showToast({
          title: '发送失败，请稍后重试',
          icon: 'none'
        })
      }
    })
  },

  // 发送快捷回复
  sendQuickReply(e) {
    const content = e.currentTarget.dataset.content
    this.setData({
      inputText: content
    })
    this.sendMessage()
  },

  // 输入框内容变化
  onInputChange(e) {
    this.setData({
      inputText: e.detail.value
    })
  },

  // 标记消息为已读
  markAsRead() {
    if (!this.data.sessionId) return

    wx.request({
      url: `${app.globalData.baseUrl}/api/customer-service/read`,
      method: 'POST',
      data: {
        sessionId: this.data.sessionId
      },
      success: (res) => {
        // 标记成功，无需特殊处理
      },
      fail: (err) => {
        console.error('标记已读失败:', err)
      }
    })
  },

  // 开始消息轮询
  startMessagePolling() {
    // 每5秒检查一次新消息
    this.messageTimer = setInterval(() => {
      this.loadMessages()
    }, 5000)
  },

  // 滚动到底部
  scrollToBottom() {
    const query = wx.createSelectorQuery()
    query.select('.messages-list').boundingClientRect()
    query.exec((res) => {
      if (res[0]) {
        this.setData({
          scrollTop: res[0].height,
          toView: `msg${this.data.messages.length - 1}`
        })
      }
    })
  },

  // 格式化时间
  formatTime(timeStr) {
    const date = new Date(timeStr)
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    const messageDate = new Date(date.getFullYear(), date.getMonth(), date.getDate())
    
    const hours = date.getHours().toString().padStart(2, '0')
    const minutes = date.getMinutes().toString().padStart(2, '0')
    
    if (messageDate.getTime() === today.getTime()) {
      return `${hours}:${minutes}`
    } else {
      const month = (date.getMonth() + 1).toString().padStart(2, '0')
      const day = date.getDate().toString().padStart(2, '0')
      return `${month}-${day} ${hours}:${minutes}`
    }
  }
})
