export const DebugHelper = {
  // 打印请求信息
  logRequest(method: string, url: string, data?: any) {
    console.group(`🚀 请求: ${method} ${url}`);
    console.log('参数:', data);
    console.groupEnd();
  },

  // 打印响应信息
  logResponse(method: string, url: string, response: any) {
    console.group(`✅ 响应: ${method} ${url}`);
    console.log('数据:', response);
    console.groupEnd();
  },

  // 打印错误信息
  logError(method: string, url: string, error: any) {
    console.group(`❌ 错误: ${method} ${url}`);
    console.error('错误信息:', error);
    console.groupEnd();
  },

  // 打印登录流程信息
  logLoginFlow(step: string, data?: any) {
    console.group(`👤 登录步骤: ${step}`);
    if (data) console.log('数据:', data);
    console.groupEnd();
  }
};