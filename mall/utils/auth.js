/**
 * 用户认证和状态管理工具
 * 负责登录状态的保持、检查和清除
 */

const STORAGE_KEYS = {
  OPEN_ID: 'openId',
  USER_INFO: 'userInfo',
  LOGIN_TIME: 'loginTime',
  SESSION_TOKEN: 'sessionToken'
};

// 登录状态过期时间（7天）
const LOGIN_EXPIRE_TIME = 7 * 24 * 60 * 60 * 1000;

/**
 * 保存登录状态
 */
const saveLoginState = (openId, userInfo, sessionToken = null) => {
  try {
    const loginTime = Date.now();
    
    // 保存到本地存储
    tt.setStorageSync(STORAGE_KEYS.OPEN_ID, openId);
    tt.setStorageSync(STORAGE_KEYS.USER_INFO, userInfo);
    tt.setStorageSync(STORAGE_KEYS.LOGIN_TIME, loginTime);
    
    if (sessionToken) {
      tt.setStorageSync(STORAGE_KEYS.SESSION_TOKEN, sessionToken);
    }
    
    // 更新全局状态
    const app = getApp();
    if (app.globalData) {
      app.globalData.openId = openId;
      app.globalData.userInfo = userInfo;
      app.globalData.isLoggedIn = true;
      app.globalData.sessionToken = sessionToken;
    }
    
    console.log('✅ 登录状态已保存', { openId, userInfo: userInfo.nickName });
    return true;
  } catch (error) {
    console.error('❌ 保存登录状态失败:', error);
    return false;
  }
};

/**
 * 获取登录状态
 */
const getLoginState = () => {
  try {
    const openId = tt.getStorageSync(STORAGE_KEYS.OPEN_ID);
    const userInfo = tt.getStorageSync(STORAGE_KEYS.USER_INFO);
    const loginTime = tt.getStorageSync(STORAGE_KEYS.LOGIN_TIME);
    const sessionToken = tt.getStorageSync(STORAGE_KEYS.SESSION_TOKEN);
    
    // 检查是否有基本的登录信息
    if (!openId || !userInfo) {
      return {
        isLoggedIn: false,
        openId: null,
        userInfo: null,
        sessionToken: null
      };
    }
    
    // 检查登录是否过期
    if (loginTime && (Date.now() - loginTime > LOGIN_EXPIRE_TIME)) {
      console.log('⚠️ 登录状态已过期，清除本地数据');
      clearLoginState();
      return {
        isLoggedIn: false,
        openId: null,
        userInfo: null,
        sessionToken: null
      };
    }
    
    return {
      isLoggedIn: true,
      openId,
      userInfo,
      sessionToken,
      loginTime
    };
  } catch (error) {
    console.error('❌ 获取登录状态失败:', error);
    return {
      isLoggedIn: false,
      openId: null,
      userInfo: null,
      sessionToken: null
    };
  }
};

/**
 * 检查是否已登录
 */
const isLoggedIn = () => {
  const loginState = getLoginState();
  return loginState.isLoggedIn;
};

/**
 * 获取当前用户的openId
 */
const getCurrentOpenId = () => {
  const loginState = getLoginState();
  return loginState.openId;
};

/**
 * 获取当前用户信息
 */
const getCurrentUserInfo = () => {
  const loginState = getLoginState();
  return loginState.userInfo;
};

/**
 * 清除登录状态
 */
const clearLoginState = () => {
  try {
    // 清除本地存储
    tt.removeStorageSync(STORAGE_KEYS.OPEN_ID);
    tt.removeStorageSync(STORAGE_KEYS.USER_INFO);
    tt.removeStorageSync(STORAGE_KEYS.LOGIN_TIME);
    tt.removeStorageSync(STORAGE_KEYS.SESSION_TOKEN);
    
    // 清除全局状态
    const app = getApp();
    if (app.globalData) {
      app.globalData.openId = null;
      app.globalData.userInfo = null;
      app.globalData.isLoggedIn = false;
      app.globalData.sessionToken = null;
    }
    
    console.log('✅ 登录状态已清除');
    return true;
  } catch (error) {
    console.error('❌ 清除登录状态失败:', error);
    return false;
  }
};

/**
 * 更新用户信息
 */
const updateUserInfo = (userInfo) => {
  try {
    const currentState = getLoginState();
    if (!currentState.isLoggedIn) {
      console.warn('⚠️ 用户未登录，无法更新用户信息');
      return false;
    }
    
    // 合并用户信息
    const updatedUserInfo = { ...currentState.userInfo, ...userInfo };
    
    // 保存更新后的信息
    tt.setStorageSync(STORAGE_KEYS.USER_INFO, updatedUserInfo);
    
    // 更新全局状态
    const app = getApp();
    if (app.globalData) {
      app.globalData.userInfo = updatedUserInfo;
    }
    
    console.log('✅ 用户信息已更新', updatedUserInfo);
    return true;
  } catch (error) {
    console.error('❌ 更新用户信息失败:', error);
    return false;
  }
};

/**
 * 刷新登录状态（延长过期时间）
 */
const refreshLoginState = () => {
  try {
    const currentState = getLoginState();
    if (!currentState.isLoggedIn) {
      return false;
    }
    
    // 更新登录时间
    const newLoginTime = Date.now();
    tt.setStorageSync(STORAGE_KEYS.LOGIN_TIME, newLoginTime);
    
    console.log('✅ 登录状态已刷新');
    return true;
  } catch (error) {
    console.error('❌ 刷新登录状态失败:', error);
    return false;
  }
};

/**
 * 检查登录状态并自动跳转
 */
const checkLoginAndRedirect = (redirectUrl = '/pages/user_center/user_center') => {
  if (!isLoggedIn()) {
    tt.showModal({
      title: '请先登录',
      content: '您需要登录后才能使用此功能',
      confirmText: '去登录',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          tt.navigateTo({
            url: '/pages/login/login?redirect=' + encodeURIComponent(redirectUrl)
          });
        }
      }
    });
    return false;
  }
  return true;
};

/**
 * 初始化登录状态（应用启动时调用）
 */
const initLoginState = () => {
  try {
    const loginState = getLoginState();
    
    // 更新全局状态
    const app = getApp();
    if (app.globalData) {
      app.globalData.openId = loginState.openId;
      app.globalData.userInfo = loginState.userInfo;
      app.globalData.isLoggedIn = loginState.isLoggedIn;
      app.globalData.sessionToken = loginState.sessionToken;
    }
    
    console.log('🚀 登录状态初始化完成', {
      isLoggedIn: loginState.isLoggedIn,
      openId: loginState.openId,
      userNickName: loginState.userInfo?.nickName
    });
    
    return loginState;
  } catch (error) {
    console.error('❌ 初始化登录状态失败:', error);
    return {
      isLoggedIn: false,
      openId: null,
      userInfo: null,
      sessionToken: null
    };
  }
};

/**
 * 获取登录状态摘要（用于调试）
 */
const getLoginStateSummary = () => {
  const state = getLoginState();
  return {
    isLoggedIn: state.isLoggedIn,
    openId: state.openId ? state.openId.substring(0, 8) + '...' : null,
    userNickName: state.userInfo?.nickName,
    loginTime: state.loginTime ? new Date(state.loginTime).toLocaleString() : null,
    hasSessionToken: !!state.sessionToken
  };
};

module.exports = {
  // 核心方法
  saveLoginState,
  getLoginState,
  isLoggedIn,
  clearLoginState,
  
  // 便捷方法
  getCurrentOpenId,
  getCurrentUserInfo,
  updateUserInfo,
  refreshLoginState,
  
  // 工具方法
  checkLoginAndRedirect,
  initLoginState,
  getLoginStateSummary,
  
  // 常量
  STORAGE_KEYS,
  LOGIN_EXPIRE_TIME
};
