/**
 * 平台检测和适配工具
 * 支持抖音小程序和微信小程序
 */

/**
 * 获取当前运行平台
 * @returns {string} 'douyin' | 'wechat' | 'unknown'
 */
const getPlatform = () => {
  // 检测抖音小程序环境
  if (typeof tt !== 'undefined' && tt.getSystemInfoSync) {
    try {
      const systemInfo = tt.getSystemInfoSync();
      if (systemInfo.platform || systemInfo.brand) {
        return 'douyin';
      }
    } catch (e) {
      console.log('检测抖音环境失败:', e);
    }
  }
  
  // 检测微信小程序环境
  if (typeof wx !== 'undefined' && wx.getSystemInfoSync) {
    try {
      const systemInfo = wx.getSystemInfoSync();
      if (systemInfo.platform || systemInfo.brand) {
        return 'wechat';
      }
    } catch (e) {
      console.log('检测微信环境失败:', e);
    }
  }
  
  return 'unknown';
};

/**
 * 获取当前平台的API对象
 * @returns {object} tt | wx
 */
const getPlatformAPI = () => {
  const platform = getPlatform();
  
  if (platform === 'douyin') {
    return tt;
  } else if (platform === 'wechat') {
    return wx;
  } else {
    throw new Error('不支持的平台: ' + platform);
  }
};

/**
 * 获取平台配置信息
 * @returns {object} 平台相关配置
 */
const getPlatformConfig = () => {
  const platform = getPlatform();
  
  const configs = {
    douyin: {
      name: '抖音小程序',
      loginAPI: 'tt.login',
      getUserProfileAPI: 'tt.getUserProfile',
      storagePrefix: 'tt_',
      checkSessionAPI: 'tt.checkSession'
    },
    wechat: {
      name: '微信小程序',
      loginAPI: 'wx.login',
      getUserProfileAPI: 'wx.getUserProfile',
      storagePrefix: 'wx_',
      checkSessionAPI: 'wx.checkSession'
    }
  };
  
  return configs[platform] || {
    name: '未知平台',
    loginAPI: 'unknown',
    getUserProfileAPI: 'unknown',
    storagePrefix: 'unknown_',
    checkSessionAPI: 'unknown'
  };
};

/**
 * 平台适配的存储key
 * @param {string} key 原始key
 * @returns {string} 带平台前缀的key
 */
const getPlatformStorageKey = (key) => {
  const config = getPlatformConfig();
  return config.storagePrefix + key;
};

/**
 * 检查当前平台是否支持
 * @returns {boolean}
 */
const isPlatformSupported = () => {
  const platform = getPlatform();
  return platform === 'douyin' || platform === 'wechat';
};

/**
 * 获取平台特定的用户授权描述
 * @returns {string}
 */
const getPlatformAuthDesc = () => {
  const platform = getPlatform();
  
  const descs = {
    douyin: '用于完善会员资料',
    wechat: '用于完善会员资料'
  };
  
  return descs[platform] || '用于完善会员资料';
};

/**
 * 平台适配的API调用
 */
const platformAPI = {
  /**
   * 统一的登录接口
   */
  login: (options) => {
    const api = getPlatformAPI();
    return api.login(options);
  },
  
  /**
   * 统一的获取用户信息接口
   */
  getUserProfile: (options) => {
    const api = getPlatformAPI();
    const desc = options.desc || getPlatformAuthDesc();
    
    return api.getUserProfile({
      ...options,
      desc: desc
    });
  },
  
  /**
   * 统一的检查登录状态接口
   */
  checkSession: (options) => {
    const api = getPlatformAPI();
    return api.checkSession(options);
  },
  
  /**
   * 统一的存储接口
   */
  setStorage: (options) => {
    const api = getPlatformAPI();
    return api.setStorage(options);
  },
  
  setStorageSync: (key, data) => {
    const api = getPlatformAPI();
    const platformKey = getPlatformStorageKey(key);
    return api.setStorageSync(platformKey, data);
  },
  
  getStorage: (options) => {
    const api = getPlatformAPI();
    const platformKey = getPlatformStorageKey(options.key);
    return api.getStorage({
      ...options,
      key: platformKey
    });
  },
  
  getStorageSync: (key) => {
    const api = getPlatformAPI();
    const platformKey = getPlatformStorageKey(key);
    return api.getStorageSync(platformKey);
  },
  
  removeStorage: (options) => {
    const api = getPlatformAPI();
    const platformKey = getPlatformStorageKey(options.key);
    return api.removeStorage({
      ...options,
      key: platformKey
    });
  },
  
  removeStorageSync: (key) => {
    const api = getPlatformAPI();
    const platformKey = getPlatformStorageKey(key);
    return api.removeStorageSync(platformKey);
  },
  
  /**
   * 统一的导航接口
   */
  navigateTo: (options) => {
    const api = getPlatformAPI();
    return api.navigateTo(options);
  },
  
  redirectTo: (options) => {
    const api = getPlatformAPI();
    return api.redirectTo(options);
  },
  
  switchTab: (options) => {
    const api = getPlatformAPI();
    return api.switchTab(options);
  },
  
  navigateBack: (options) => {
    const api = getPlatformAPI();
    return api.navigateBack(options);
  },
  
  /**
   * 统一的提示接口
   */
  showToast: (options) => {
    const api = getPlatformAPI();
    return api.showToast(options);
  },
  
  showModal: (options) => {
    const api = getPlatformAPI();
    return api.showModal(options);
  },
  
  showLoading: (options) => {
    const api = getPlatformAPI();
    return api.showLoading(options);
  },
  
  hideLoading: () => {
    const api = getPlatformAPI();
    return api.hideLoading();
  },
  
  /**
   * 统一的网络请求接口
   */
  request: (options) => {
    const api = getPlatformAPI();
    return api.request(options);
  },
  
  /**
   * 统一的系统信息接口
   */
  getSystemInfoSync: () => {
    const api = getPlatformAPI();
    return api.getSystemInfoSync();
  }
};

/**
 * 调试信息
 */
const getDebugInfo = () => {
  const platform = getPlatform();
  const config = getPlatformConfig();
  const supported = isPlatformSupported();
  
  return {
    platform,
    platformName: config.name,
    supported,
    hasAPI: platform !== 'unknown',
    config
  };
};

module.exports = {
  // 核心方法
  getPlatform,
  getPlatformAPI,
  getPlatformConfig,
  isPlatformSupported,
  
  // 工具方法
  getPlatformStorageKey,
  getPlatformAuthDesc,
  
  // 统一API
  platformAPI,
  
  // 调试方法
  getDebugInfo
};
