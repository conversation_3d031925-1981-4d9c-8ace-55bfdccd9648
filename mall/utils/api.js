/**
 * API迁移工具
 * 用于将旧的API调用迁移到新的HTTPS配置
 */

// 配置信息（直接定义，避免ES6模块导入问题）
const CONFIG = {
  API_BASE_URL: 'https://www.zhuanglz.cn:8443/api',  // 使用HTTPS协议
  SERVER_BASE_URL: 'https://www.zhuanglz.cn:8443',   // 服务器基础地址（用于图片等资源）
  DEBUG: true
};

const API_CONFIG = {
  BASE_URL: CONFIG.API_BASE_URL
};

/**
 * 兼容性API包装器
 * 保持与旧API文件相同的接口，但使用新的HTTPS配置
 */

// 请求方法 - 使用新的HTTPS地址
const request = (url, method, data) => {
  return new Promise((resolve, reject) => {
    // 构建完整URL
    const fullUrl = url.startsWith('http') ? url : `${API_CONFIG.BASE_URL}${url}`;

    // 调试日志
    if (CONFIG.DEBUG) {
    }
    
    tt.request({
      url: fullUrl,
      method: method,
      data: data,
      sslVerify: false,
      header: {
        'content-type': 'application/json'
      },
      success: (res) => {
        if (res.statusCode === 200) {
          resolve(res.data);
        } else {
          reject(res.data || { message: '请求失败' });
        }
      },
      fail: (err) => {
        reject(err);
      }
    });
  });
};

// ========== 用户相关API ==========

// 用户登录（原抖音接口，保持兼容）
const login = (code, userInfo) => {
  return request('/user/login', 'POST', { code, userInfo });
};

// 简化登录，只需要code就可以获取openId（原抖音接口，保持兼容）
const code2Session = (code) => {
  return request('/user/code2Session', 'POST', { code });
};

// 多平台统一登录接口
const platformLogin = (code, platform, userInfo) => {
  return request(`/user/login/${platform}`, 'POST', { code, userInfo });
};

// 多平台简化登录接口
const platformCode2Session = (code, platform) => {
  return request(`/user/code2Session/${platform}`, 'POST', { code });
};

// 获取用户信息
const getUserInfo = (userId) => {
  return request(`/user/${userId}`, 'GET');
};

// 更新用户手机号
const updateUserPhone = (userId, phone) => {
  return request('/user/updatePhone', 'POST', { userId, phone });
};

// ========== 地址相关API ==========

// 获取用户地址列表
const getAddressList = (openId) => {
  return request(`/address/list?openId=${openId}`, 'GET');
};

// 获取用户默认地址
const getDefaultAddress = (openId) => {
  return request(`/address/default?openId=${openId}`, 'GET');
};

// 保存地址（新增或修改）
const saveAddress = (addressData) => {
  return request('/address/save', 'POST', addressData);
};

// 删除地址
const deleteAddress = (id, openId) => {
  return request(`/address/${id}?openId=${openId}`, 'DELETE');
};

// 设置默认地址
const setDefaultAddress = (id, openId) => {
  return request('/address/setDefault', 'POST', { id, openId });
};

// ========== 工具函数 ==========

/**
 * 处理头像URL，统一处理相对路径和绝对路径
 * @param {string} avatarUrl - 原始头像URL
 * @returns {string} - 处理后的完整URL
 */
const processAvatarUrl = (avatarUrl) => {
  if (!avatarUrl || avatarUrl.trim() === '') {
    return '';
  }

  // 如果是完整的HTTP URL，直接返回
  if (avatarUrl.startsWith('http')) {
    return avatarUrl;
  }

  // 如果是相对路径，添加服务器地址
  if (avatarUrl.startsWith('/uploads/')) {
    return `${CONFIG.SERVER_BASE_URL}${avatarUrl}`;
  }

  // 如果只是文件名，添加完整路径
  return `${CONFIG.SERVER_BASE_URL}/uploads/${avatarUrl}`;
};

/**
 * 处理产品图片URL
 * @param {string} imageUrl - 原始图片URL
 * @returns {string} - 处理后的完整URL
 */
const processImageUrl = (imageUrl) => {
  if (!imageUrl || imageUrl.trim() === '') {
    return '';
  }

  // 如果已经是完整的HTTP/HTTPS URL，直接返回
  if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
    return imageUrl;
  }

  // 如果是相对路径，添加服务器地址
  if (imageUrl.startsWith('/uploads/')) {
    return `${CONFIG.SERVER_BASE_URL}${imageUrl}`;
  }

  // 如果只是文件名，添加完整路径
  if (!imageUrl.startsWith('/')) {
    return `${CONFIG.SERVER_BASE_URL}/uploads/${imageUrl}`;
  }

  // 其他情况，添加服务器地址
  return `${CONFIG.SERVER_BASE_URL}${imageUrl}`;
};

// ========== 维修订单相关API ==========

// 创建维修订单
const createRepairOrder = (orderData) => {
  return request('/repair/create', 'POST', orderData);
};

// 获取用户维修订单列表
const getRepairOrderList = (openId, status = null) => {
  const url = status ? `/repair/list?openId=${openId}&status=${status}` : `/repair/list?openId=${openId}`;
  return request(url, 'GET');
};

// 获取维修订单详情
const getRepairOrderDetail = (id, openId) => {
  return request(`/repair/detail/${id}?openId=${openId}`, 'GET');
};

// 取消维修订单
const cancelRepairOrder = (id, openId) => {
  return request('/repair/cancel', 'POST', { id, openId });
};

// 获取用户维修订单统计
const getRepairOrderStats = (openId) => {
  return request(`/repair/stats?openId=${openId}`, 'GET');
};

// ========== 商品相关API ==========

// 获取商品分类列表
const getProductCategories = () => {
  return request('/products/categories', 'GET');
};

// 获取商品列表
const getProductList = (categoryId = 0, sortType = 'default', priceOrder = 'desc') => {
  return request(`/products/list?categoryId=${categoryId}&sortType=${sortType}&priceOrder=${priceOrder}`, 'GET');
};

// 获取热门产品（按sort_order排序，取前几个）
const getHotProducts = (limit = 2) => {
  return request(`/products/list?sortType=sortOrder&limit=${limit}`, 'GET');
};

// 获取商品详情
const getProductDetail = (id) => {
  return request(`/products/detail/${id}`, 'GET');
};

// 获取商品评价列表
const getProductReviews = (id) => {
  return request(`/products/${id}/reviews`, 'GET');
};

// ========== 收藏相关API ==========

// 添加商品收藏
const addFavorite = (openId, productId) => {
  return request('/favorites/add', 'POST', { openId, productId });
};

// 取消商品收藏
const removeFavorite = (openId, productId) => {
  return request('/favorites/remove', 'POST', { openId, productId });
};

// 检查商品是否已收藏
const checkFavorite = (openId, productId) => {
  return request(`/favorites/check?openId=${openId}&productId=${productId}`, 'GET');
};

// 获取用户收藏的商品列表
const getFavoriteProducts = (openId) => {
  return request(`/favorites/list?openId=${openId}`, 'GET');
};

// 获取用户收藏数量
const getFavoriteCount = (openId) => {
  return request(`/favorites/count?openId=${openId}`, 'GET');
};

// ========== 工程师相关API ==========

// 获取所有已审核通过的工程师列表
const getApprovedEngineers = () => {
  return request('/engineers/approved', 'GET');
};

// 获取在线且可接单的工程师列表
const getAvailableEngineers = () => {
  return request('/engineers/available', 'GET');
};

// 根据专业领域查询工程师
const getEngineersBySpecialty = (specialty) => {
  return request(`/engineers/specialty/${specialty}`, 'GET');
};

// 获取工程师统计信息
const getEngineerStats = () => {
  return request('/engineers/stats', 'GET');
};

// 获取工程师详情
const getEngineerDetail = (id) => {
  return request(`/engineers/detail/${id}`, 'GET');
};

// 提交工程师申请
const submitEngineerApplication = (applicationData) => {
  return request('/engineers/apply', 'POST', applicationData);
};

// ========== 服务网点相关API ==========

// 获取所有已审核通过的服务网点列表
const getApprovedServiceCenters = () => {
  return request('/service-centers/approved', 'GET');
};

// 获取推荐的服务网点列表
const getFeaturedServiceCenters = () => {
  return request('/service-centers/featured', 'GET');
};

// 根据城市查询服务网点
const getServiceCentersByCity = (city) => {
  return request(`/service-centers/city/${city}`, 'GET');
};

// 根据服务类型查询服务网点
const getServiceCentersByServiceType = (serviceType) => {
  return request(`/service-centers/service-type/${serviceType}`, 'GET');
};

// 搜索服务网点
const searchServiceCenters = (keyword) => {
  return request(`/service-centers/search?keyword=${keyword}`, 'GET');
};

// 根据位置查询附近的服务网点
const getServiceCentersByLocation = (latitude, longitude, radius = 10) => {
  return request(`/service-centers/nearby?lat=${latitude}&lng=${longitude}&radius=${radius}`, 'GET');
};

// 获取服务网点统计信息
const getServiceCenterStats = () => {
  return request('/service-centers/stats', 'GET');
};

// 获取服务网点详情
const getServiceCenterDetail = (id) => {
  return request(`/service-centers/detail/${id}`, 'GET');
};

// 提交服务网点申请
const submitServiceCenterApplication = (applicationData) => {
  return request('/applications/station', 'POST', applicationData);
};

// ========== 文件上传相关API ==========

// 上传单个图片
const uploadImage = (filePath) => {
  return new Promise((resolve, reject) => {
    tt.uploadFile({
      url: `${API_CONFIG.BASE_URL}/upload/image`,
      filePath: filePath,
      name: 'file',
      header: {
        'Content-Type': 'multipart/form-data'
      },
      success: (res) => {
        try {
          const data = JSON.parse(res.data);
          resolve(data);
        } catch (e) {
          reject(new Error('响应数据解析失败'));
        }
      },
      fail: (err) => {
        reject(err);
      }
    });
  });
};

// 上传多个图片
const uploadImages = (filePaths) => {
  return Promise.all(filePaths.map(filePath => uploadImage(filePath)));
};

// 批量上传图片（使用后端的批量接口）
const uploadImagesBatch = (filePaths) => {
  return new Promise((resolve, reject) => {
    if (!filePaths || filePaths.length === 0) {
      resolve([]);
      return;
    }

    // 抖音小程序不支持批量上传，所以还是使用单个上传
    const uploadPromises = filePaths.map(filePath => uploadImage(filePath));

    Promise.all(uploadPromises)
      .then(results => {
        resolve(results);
      })
      .catch(error => {
        reject(error);
      });
  });
};

// 导出所有API方法（保持与旧API文件兼容）
module.exports = {
  // 用户相关
  login,
  code2Session,
  platformLogin,
  platformCode2Session,
  getUserInfo,
  updateUserPhone,
  
  // 地址相关
  getAddressList,
  getDefaultAddress,
  saveAddress,
  deleteAddress,
  setDefaultAddress,
  
  // 维修订单相关
  createRepairOrder,
  getRepairOrderList,
  getRepairOrderDetail,
  cancelRepairOrder,
  getRepairOrderStats,
  
  // 商品相关
  getProductCategories,
  getProductList,
  getHotProducts,
  getProductDetail,
  getProductReviews,

  // 收藏相关
  addFavorite,
  removeFavorite,
  checkFavorite,
  getFavoriteProducts,
  getFavoriteCount,
  
  // 工程师相关
  getApprovedEngineers,
  getAvailableEngineers,
  getEngineersBySpecialty,
  getEngineerStats,
  getEngineerDetail,
  submitEngineerApplication,
  
  // 服务网点相关
  getApprovedServiceCenters,
  getFeaturedServiceCenters,
  getServiceCentersByCity,
  getServiceCentersByServiceType,
  searchServiceCenters,
  getServiceCentersByLocation,
  getServiceCenterStats,
  getServiceCenterDetail,
  submitServiceCenterApplication,
  
  // 文件上传相关
  uploadImage,
  uploadImages,
  uploadImagesBatch,

  // 工具方法
  request,
  processAvatarUrl,
  processImageUrl
};
