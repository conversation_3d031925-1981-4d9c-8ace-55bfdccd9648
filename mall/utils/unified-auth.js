/**
 * 统一认证工具
 * 支持抖音小程序和微信小程序的统一登录
 */

const platform = require('./platform');
const api = require('./api');

// 存储键名
const STORAGE_KEYS = {
  OPEN_ID: 'openId',
  USER_INFO: 'userInfo',
  LOGIN_TIME: 'loginTime',
  SESSION_TOKEN: 'sessionToken',
  PLATFORM: 'platform'
};

// 登录状态过期时间（7天）
const LOGIN_EXPIRE_TIME = 7 * 24 * 60 * 60 * 1000;

/**
 * 统一登录方法
 * @param {object} userInfo 用户信息（可选）
 * @returns {Promise} 登录结果
 */
const login = (userInfo = null) => {
  return new Promise((resolve, reject) => {
    const currentPlatform = platform.getPlatform();
    
    if (!platform.isPlatformSupported()) {
      reject(new Error('不支持的平台: ' + currentPlatform));
      return;
    }
    
    console.log('🔐 开始统一登录, 平台:', currentPlatform);
    
    platform.platformAPI.login({
      success: (res) => {
        if (res.code) {
          console.log('✅ 获取登录code成功:', res.code);
          
          // 调用后端统一登录接口
          api.platformLogin(res.code, currentPlatform, userInfo)
            .then(loginRes => {
              if (loginRes.success) {
                // 保存登录状态
                const success = saveLoginState(
                  loginRes.openId,
                  loginRes.userInfo || userInfo,
                  loginRes.sessionToken,
                  currentPlatform
                );
                
                if (success) {
                  console.log('✅ 统一登录成功，用户:', loginRes.userInfo?.nickName);
                  resolve(loginRes);
                } else {
                  reject(new Error('保存登录状态失败'));
                }
              } else {
                console.error('❌ 后端登录失败:', loginRes.message);
                reject(new Error(loginRes.message || '登录失败'));
              }
            })
            .catch(err => {
              console.error('❌ 登录请求失败:', err);
              reject(err);
            });
        } else {
          console.error('❌ 获取登录code失败:', res.errMsg);
          reject(new Error('获取登录code失败'));
        }
      },
      fail: (err) => {
        console.error('❌ 平台登录失败:', err);
        reject(err);
      }
    });
  });
};

/**
 * 静默登录（不需要用户授权）
 * @returns {Promise} 登录结果
 */
const silentLogin = () => {
  return new Promise((resolve, reject) => {
    const currentPlatform = platform.getPlatform();
    
    console.log('🔄 开始静默登录, 平台:', currentPlatform);
    
    platform.platformAPI.login({
      success: (res) => {
        if (res.code) {
          console.log('🔄 静默登录获取code:', res.code);
          
          // 只发送code到后端，不获取用户信息
          api.platformCode2Session(res.code, currentPlatform)
            .then(loginRes => {
              if (loginRes.success && loginRes.openId) {
                // 保存基本登录状态
                const basicUserInfo = {
                  nickName: '用户',
                  avatarUrl: ''
                };
                
                const success = saveLoginState(
                  loginRes.openId,
                  basicUserInfo,
                  loginRes.sessionToken,
                  currentPlatform
                );
                
                if (success) {
                  console.log('✅ 静默登录成功，openId:', loginRes.openId);
                  resolve(loginRes);
                } else {
                  reject(new Error('保存登录状态失败'));
                }
              } else {
                console.log('⚠️ 静默登录失败，需要用户主动登录');
                reject(new Error('静默登录失败'));
              }
            })
            .catch(err => {
              console.error('❌ 静默登录请求失败:', err);
              reject(err);
            });
        } else {
          reject(new Error('获取登录code失败'));
        }
      },
      fail: (err) => {
        console.error('❌ 静默登录失败:', err);
        reject(err);
      }
    });
  });
};

/**
 * 获取用户授权信息
 * @param {string} desc 授权描述
 * @returns {Promise} 用户信息
 */
const getUserProfile = (desc = null) => {
  return new Promise((resolve, reject) => {
    const authDesc = desc || platform.getPlatformAuthDesc();
    
    platform.platformAPI.getUserProfile({
      desc: authDesc,
      success: (userRes) => {
        console.log('✅ 获取用户信息成功:', userRes.userInfo?.nickName);
        resolve(userRes);
      },
      fail: (err) => {
        console.error('❌ 获取用户信息失败:', err);
        reject(err);
      }
    });
  });
};

/**
 * 完整登录（包含用户授权）
 * @param {string} desc 授权描述
 * @returns {Promise} 登录结果
 */
const fullLogin = (desc = null) => {
  return new Promise((resolve, reject) => {
    // 先获取用户授权
    getUserProfile(desc)
      .then(userRes => {
        // 再进行登录
        return login(userRes.userInfo);
      })
      .then(resolve)
      .catch(reject);
  });
};

/**
 * 保存登录状态
 * @param {string} openId 用户openId
 * @param {object} userInfo 用户信息
 * @param {string} sessionToken 会话token
 * @param {string} platformName 平台名称
 * @returns {boolean} 是否保存成功
 */
const saveLoginState = (openId, userInfo, sessionToken = null, platformName = null) => {
  try {
    const loginTime = Date.now();
    const currentPlatform = platformName || platform.getPlatform();
    
    // 保存到本地存储（使用平台适配的存储）
    platform.platformAPI.setStorageSync(STORAGE_KEYS.OPEN_ID, openId);
    platform.platformAPI.setStorageSync(STORAGE_KEYS.USER_INFO, userInfo);
    platform.platformAPI.setStorageSync(STORAGE_KEYS.LOGIN_TIME, loginTime);
    platform.platformAPI.setStorageSync(STORAGE_KEYS.PLATFORM, currentPlatform);
    
    if (sessionToken) {
      platform.platformAPI.setStorageSync(STORAGE_KEYS.SESSION_TOKEN, sessionToken);
    }
    
    // 更新全局状态
    const app = getApp();
    if (app.globalData) {
      app.globalData.openId = openId;
      app.globalData.userInfo = userInfo;
      app.globalData.isLoggedIn = true;
      app.globalData.sessionToken = sessionToken;
      app.globalData.platform = currentPlatform;
    }
    
    console.log('✅ 登录状态保存成功, 平台:', currentPlatform);
    return true;
  } catch (error) {
    console.error('❌ 保存登录状态失败:', error);
    return false;
  }
};

/**
 * 获取登录状态
 * @returns {object} 登录状态信息
 */
const getLoginState = () => {
  try {
    const openId = platform.platformAPI.getStorageSync(STORAGE_KEYS.OPEN_ID);
    const userInfo = platform.platformAPI.getStorageSync(STORAGE_KEYS.USER_INFO);
    const loginTime = platform.platformAPI.getStorageSync(STORAGE_KEYS.LOGIN_TIME);
    const sessionToken = platform.platformAPI.getStorageSync(STORAGE_KEYS.SESSION_TOKEN);
    const platformName = platform.platformAPI.getStorageSync(STORAGE_KEYS.PLATFORM);
    
    // 检查是否有基本的登录信息
    if (!openId || !userInfo) {
      return {
        isLoggedIn: false,
        openId: null,
        userInfo: null,
        sessionToken: null,
        platform: null
      };
    }
    
    // 检查登录是否过期
    if (loginTime && (Date.now() - loginTime > LOGIN_EXPIRE_TIME)) {
      clearLoginState();
      return {
        isLoggedIn: false,
        openId: null,
        userInfo: null,
        sessionToken: null,
        platform: null
      };
    }
    
    return {
      isLoggedIn: true,
      openId,
      userInfo,
      sessionToken,
      platform: platformName,
      loginTime
    };
  } catch (error) {
    console.error('❌ 获取登录状态失败:', error);
    return {
      isLoggedIn: false,
      openId: null,
      userInfo: null,
      sessionToken: null,
      platform: null
    };
  }
};

/**
 * 检查是否已登录
 * @returns {boolean}
 */
const isLoggedIn = () => {
  const state = getLoginState();
  return state.isLoggedIn;
};

/**
 * 清除登录状态
 */
const clearLoginState = () => {
  try {
    platform.platformAPI.removeStorageSync(STORAGE_KEYS.OPEN_ID);
    platform.platformAPI.removeStorageSync(STORAGE_KEYS.USER_INFO);
    platform.platformAPI.removeStorageSync(STORAGE_KEYS.LOGIN_TIME);
    platform.platformAPI.removeStorageSync(STORAGE_KEYS.SESSION_TOKEN);
    platform.platformAPI.removeStorageSync(STORAGE_KEYS.PLATFORM);
    
    // 清除全局状态
    const app = getApp();
    if (app.globalData) {
      app.globalData.openId = null;
      app.globalData.userInfo = null;
      app.globalData.isLoggedIn = false;
      app.globalData.sessionToken = null;
      app.globalData.platform = null;
    }
    
    console.log('✅ 登录状态已清除');
  } catch (error) {
    console.error('❌ 清除登录状态失败:', error);
  }
};

/**
 * 检查登录状态并自动跳转
 * @param {string} redirectUrl 重定向URL
 * @returns {boolean} 是否已登录
 */
const checkLoginAndRedirect = (redirectUrl = '/pages/user_center/user_center') => {
  if (!isLoggedIn()) {
    platform.platformAPI.showModal({
      title: '请先登录',
      content: '您需要登录后才能使用此功能',
      confirmText: '去登录',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          platform.platformAPI.navigateTo({
            url: '/pages/login/login?redirect=' + encodeURIComponent(redirectUrl)
          });
        }
      }
    });
    return false;
  }
  return true;
};

/**
 * 初始化登录状态（应用启动时调用）
 * @returns {object} 登录状态
 */
const initLoginState = () => {
  try {
    const loginState = getLoginState();
    
    // 更新全局状态
    const app = getApp();
    if (app.globalData) {
      app.globalData.openId = loginState.openId;
      app.globalData.userInfo = loginState.userInfo;
      app.globalData.isLoggedIn = loginState.isLoggedIn;
      app.globalData.sessionToken = loginState.sessionToken;
      app.globalData.platform = loginState.platform;
    }
    
    console.log('🔄 登录状态初始化完成, 平台:', loginState.platform, '登录状态:', loginState.isLoggedIn);
    return loginState;
  } catch (error) {
    console.error('❌ 初始化登录状态失败:', error);
    return {
      isLoggedIn: false,
      openId: null,
      userInfo: null,
      sessionToken: null,
      platform: null
    };
  }
};

module.exports = {
  // 核心登录方法
  login,
  silentLogin,
  fullLogin,
  getUserProfile,
  
  // 状态管理
  saveLoginState,
  getLoginState,
  isLoggedIn,
  clearLoginState,
  
  // 工具方法
  checkLoginAndRedirect,
  initLoginState,
  
  // 常量
  STORAGE_KEYS,
  LOGIN_EXPIRE_TIME
};
