/**
 * 应用配置文件
 * 统一管理API地址、常量等配置信息
 */

// 环境配置
const ENV = {
  // 开发环境
  development: {
    API_BASE_URL: 'https://www.zhuanglz.cn:8443',
    WS_BASE_URL: 'wss://www.zhuanglz.cn:8443',
    DEBUG: true
  },
  // 生产环境
  production: {
    API_BASE_URL: 'https://www.zhuanglz.cn',
    WS_BASE_URL: 'wss://www.zhuanglz.cn',
    DEBUG: false
  }
};

// 当前环境（开发时使用development，上线时改为production）
const CURRENT_ENV = 'development';

// 导出当前环境配置
export const CONFIG = ENV[CURRENT_ENV];

// API接口地址
export const API = {
  // 基础地址
  BASE_URL: CONFIG.API_BASE_URL,
  
  // 用户相关
  USER: {
    LOGIN: `${CONFIG.API_BASE_URL}/api/user/login`,
    PROFILE: `${CONFIG.API_BASE_URL}/api/user/profile`,
    UPDATE: `${CONFIG.API_BASE_URL}/api/user/update`
  },
  
  // 客服相关
  CUSTOMER_SERVICE: {
    SESSION: `${CONFIG.API_BASE_URL}/api/customer-service/session`,
    MESSAGES: `${CONFIG.API_BASE_URL}/api/customer-service/messages`,
    SEND_MESSAGE: `${CONFIG.API_BASE_URL}/api/customer-service/message`,
    MARK_READ: `${CONFIG.API_BASE_URL}/api/customer-service/read`
  },
  
  // 订单相关
  ORDER: {
    LIST: `${CONFIG.API_BASE_URL}/api/orders`,
    DETAIL: `${CONFIG.API_BASE_URL}/api/orders`,
    CREATE: `${CONFIG.API_BASE_URL}/api/orders`,
    CANCEL: `${CONFIG.API_BASE_URL}/api/orders/cancel`,
    PAY: `${CONFIG.API_BASE_URL}/api/orders/pay`
  },
  
  // 工程师相关
  ENGINEER: {
    LIST: `${CONFIG.API_BASE_URL}/api/engineers`,
    DETAIL: `${CONFIG.API_BASE_URL}/api/engineers`,
    SEARCH: `${CONFIG.API_BASE_URL}/api/engineers/search`,
    FAVORITE: `${CONFIG.API_BASE_URL}/api/engineers/favorite`
  },
  
  // 服务网点相关
  SERVICE: {
    LIST: `${CONFIG.API_BASE_URL}/api/services`,
    DETAIL: `${CONFIG.API_BASE_URL}/api/services`,
    SEARCH: `${CONFIG.API_BASE_URL}/api/services/search`,
    FAVORITE: `${CONFIG.API_BASE_URL}/api/services/favorite`
  },
  
  // 收藏相关
  FAVORITE: {
    LIST: `${CONFIG.API_BASE_URL}/api/favorites`,
    ADD: `${CONFIG.API_BASE_URL}/api/favorites`,
    REMOVE: `${CONFIG.API_BASE_URL}/api/favorites`
  },
  
  // 文件上传
  UPLOAD: {
    IMAGE: `${CONFIG.API_BASE_URL}/api/upload/image`,
    FILE: `${CONFIG.API_BASE_URL}/api/upload/file`
  }
};

// 常量配置
export const CONSTANTS = {
  // 存储键名
  STORAGE_KEYS: {
    OPEN_ID: 'openId',
    USER_INFO: 'userInfo',
    TOKEN: 'token',
    LAST_LOGIN: 'lastLogin'
  },
  
  // 页面路径
  PAGES: {
    LOGIN: '/pages/login/login',
    HOME: '/pages/index/index',
    USER_CENTER: '/pages/user_center/user_center',
    CUSTOMER_SERVICE: '/pages/customer_service/customer_service',
    FAVORITES: '/pages/favorites/favorites',
    ORDER_LIST: '/pages/order_list/order_list',
    ORDER_DETAIL: '/pages/order_detail/order_detail'
  },
  
  // 消息类型
  MESSAGE_TYPES: {
    TEXT: 'text',
    IMAGE: 'image',
    SYSTEM: 'system'
  },
  
  // 订单状态
  ORDER_STATUS: {
    PENDING: 'pending',
    CONFIRMED: 'confirmed',
    IN_PROGRESS: 'in_progress',
    COMPLETED: 'completed',
    CANCELLED: 'cancelled'
  },
  
  // 请求超时时间（毫秒）
  REQUEST_TIMEOUT: 10000,
  
  // 分页配置
  PAGE_SIZE: 20
};

// 工具函数
export const UTILS = {
  /**
   * 构建完整的API URL
   */
  buildApiUrl: (path: string): string => {
    return `${CONFIG.API_BASE_URL}${path}`;
  },
  
  /**
   * 获取图片完整URL
   */
  getImageUrl: (imagePath: string): string => {
    if (!imagePath) return '';
    if (imagePath.startsWith('http')) return imagePath;
    return `${CONFIG.API_BASE_URL}${imagePath}`;
  },
  
  /**
   * 检查是否为开发环境
   */
  isDevelopment: (): boolean => {
    return CURRENT_ENV === 'development';
  },
  
  /**
   * 调试日志（仅在开发环境输出）
   */
  debugLog: (message: string, data?: any): void => {
    if (CONFIG.DEBUG) {
      console.log(`[DEBUG] ${message}`, data || '');
    }
  }
};

// SSL证书相关配置
export const SSL_CONFIG = {
  // 自签名证书信息
  SELF_SIGNED: {
    COMMON_NAME: 'localhost',
    ORGANIZATION: '成都桩郎中新能源技术有限公司',
    VALIDITY_DAYS: 365,
    KEY_SIZE: 2048
  },
  
  // 证书验证配置
  VERIFICATION: {
    // 开发环境跳过证书验证（仅用于自签名证书）
    SKIP_VERIFICATION: CURRENT_ENV === 'development',
    // 允许的主机名
    ALLOWED_HOSTS: ['localhost', '127.0.0.1']
  }
};
