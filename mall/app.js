const auth = require('./utils/unified-auth');
const platform = require('./utils/platform');

App({
  globalData: {
    userInfo: null,
    userId: null,
    openId: null,
    sessionKey: null,
    sessionToken: null,
    isLoggedIn: false,
    cartItems: [],
    repairInfo: null,
    primaryColor: '#1e88e5',
    platform: null
  },
  onLaunch: function () {
    // 初始化应用时执行
    const currentPlatform = platform.getPlatform();
    console.log('🚀 App launched on platform:', currentPlatform);

    // 检查平台支持
    if (!platform.isPlatformSupported()) {
      console.error('❌ 不支持的平台:', currentPlatform);
      return;
    }

    // 初始化登录状态
    const loginState = auth.initLoginState();

    // 检查当前平台的session状态
    platform.platformAPI.checkSession({
      success: () => {
        // 登录态有效
        console.log('✅ 平台Session有效, 平台:', currentPlatform);

        // 如果本地有登录状态，直接使用
        if (loginState.isLoggedIn) {
          console.log('✅ 使用本地登录状态:', loginState.userInfo?.nickName);
        } else {
          // 本地没有登录状态，但平台session有效，尝试静默登录
          console.log('🔄 尝试静默登录...');
          this.silentLogin();
        }
      },
      fail: () => {
        // 登录态过期，清除本地状态
        console.log('⚠️ 平台Session过期，清除本地登录状态, 平台:', currentPlatform);
        auth.clearLoginState();
      }
    });
  },

  // 静默登录（不需要用户授权）
  silentLogin: function() {
    auth.silentLogin()
      .then(loginRes => {
        console.log('✅ 静默登录成功，openId:', loginRes.openId);
      })
      .catch(err => {
        console.log('⚠️ 静默登录失败，需要用户主动登录:', err.message);
      });
  },

  // 完整登录（需要用户授权）
  login: function() {
    auth.fullLogin('用于完善会员资料')
      .then(loginRes => {
        console.log('✅ 完整登录成功，用户:', loginRes.userInfo?.nickName);
        
        // 显示成功提示
        platform.platformAPI.showToast({
          title: '登录成功',
          icon: 'success'
        });
      })
      .catch(err => {
        console.error('❌ 完整登录失败:', err);
        
        if (err.message && err.message.includes('授权')) {
          platform.platformAPI.showModal({
            title: '提示',
            content: '需要您的授权才能正常使用小程序功能',
            showCancel: false
          });
        } else {
          platform.platformAPI.showToast({
            title: '登录失败，请重试',
            icon: 'none'
          });
        }
      });
  },

  // 获取用户信息
  getUserInfo: function() {
    const loginState = auth.getLoginState();
    return loginState.userInfo;
  },

  // 获取OpenId
  getOpenId: function() {
    const loginState = auth.getLoginState();
    return loginState.openId;
  },

  // 检查登录状态
  checkLogin: function() {
    return auth.isLoggedIn();
  },

  // 退出登录
  logout: function() {
    auth.clearLoginState();
    console.log('✅ 用户已退出登录');
    
    platform.platformAPI.showToast({
      title: '已退出登录',
      icon: 'success'
    });
  },

  // 获取平台信息
  getPlatformInfo: function() {
    return platform.getDebugInfo();
  }
});
