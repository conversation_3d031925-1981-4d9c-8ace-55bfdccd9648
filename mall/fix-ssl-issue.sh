#!/bin/bash

echo "🔧 抖音小程序HTTPS证书问题一键修复"
echo "=================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo ""
echo -e "${BLUE}请选择解决方案:${NC}"
echo "1. 配置开发者工具跳过证书验证 (推荐用于开发)"
echo "2. 使用 ngrok 内网穿透 (推荐用于真机测试)"
echo "3. 使用 localtunnel 内网穿透 (免费替代方案)"
echo "4. 切换到HTTP协议 (临时方案)"
echo "5. 显示详细配置指南"
echo ""

read -p "请输入选项 (1-5): " choice

case $choice in
    1)
        echo ""
        echo -e "${GREEN}方案1: 配置开发者工具${NC}"
        echo "=============================="
        echo ""
        echo "📋 请按以下步骤操作:"
        echo ""
        echo "1️⃣  在抖音开发者工具中:"
        echo "   - 点击右上角 '详情' 按钮"
        echo "   - 选择 '项目配置' 标签"
        echo ""
        echo "2️⃣  勾选以下选项:"
        echo "   ✅ 不校验合法域名、HTTPS证书"
        echo "   ✅ 不校验TLS版本"
        echo ""
        echo "3️⃣  在开发域名中添加:"
        echo "   - ***********:8443"
        echo "   - localhost:8443"
        echo ""
        echo "4️⃣  保存设置并重新编译项目"
        echo ""
        echo -e "${YELLOW}⚠️  注意: 此方案仅适用于开发环境${NC}"
        ;;
        
    2)
        echo ""
        echo -e "${GREEN}方案2: 使用 ngrok 内网穿透${NC}"
        echo "================================"
        echo ""
        
        # 检查ngrok是否安装
        if command -v ngrok &> /dev/null; then
            echo -e "${GREEN}✅ ngrok 已安装${NC}"
            echo ""
            echo "🚀 启动 ngrok 隧道..."
            echo "请在新终端窗口中运行以下命令:"
            echo ""
            echo -e "${BLUE}cd ../mall_server && ngrok http 8443${NC}"
            echo ""
            echo "获得域名后，运行以下命令更新API配置:"
            echo -e "${BLUE}node update-api-domain.js https://YOUR_NGROK_DOMAIN${NC}"
        else
            echo -e "${RED}❌ ngrok 未安装${NC}"
            echo ""
            echo "📥 安装步骤:"
            echo "1. 访问 https://ngrok.com/download"
            echo "2. 下载并安装 ngrok"
            echo "3. 注册账号获取 authtoken"
            echo "4. 运行: ngrok config add-authtoken YOUR_TOKEN"
            echo ""
            echo "💡 macOS 快速安装:"
            echo "brew install ngrok/ngrok/ngrok"
        fi
        ;;
        
    3)
        echo ""
        echo -e "${GREEN}方案3: 使用 localtunnel${NC}"
        echo "=========================="
        echo ""
        
        # 检查npm是否安装
        if command -v npm &> /dev/null; then
            echo "📦 安装 localtunnel..."
            npm install -g localtunnel
            
            echo ""
            echo "🚀 启动 localtunnel 隧道..."
            echo "请在新终端窗口中运行:"
            echo ""
            echo -e "${BLUE}lt --port 8443 --subdomain zlz-mall${NC}"
            echo ""
            echo "获得域名后，运行:"
            echo -e "${BLUE}node update-api-domain.js https://zlz-mall.loca.lt${NC}"
        else
            echo -e "${RED}❌ npm 未安装${NC}"
            echo "请先安装 Node.js 和 npm"
        fi
        ;;
        
    4)
        echo ""
        echo -e "${YELLOW}方案4: 切换到HTTP协议 (临时方案)${NC}"
        echo "======================================="
        echo ""
        echo "⚠️  警告: HTTP协议不安全，仅用于开发测试"
        echo ""
        read -p "确定要切换到HTTP吗? (y/N): " confirm
        
        if [[ $confirm =~ ^[Yy]$ ]]; then
            echo "🔄 更新API配置为HTTP..."
            
            # 更新api.js
            if [ -f "utils/api.js" ]; then
                sed -i.bak 's|https://***********:8443|http://***********:8080|g' utils/api.js
                echo "✅ 已更新 utils/api.js"
            fi
            
            # 更新config.ts
            if [ -f "utils/config.ts" ]; then
                sed -i.bak 's|https://***********:8443|http://***********:8080|g' utils/config.ts
                echo "✅ 已更新 utils/config.ts"
            fi
            
            echo ""
            echo -e "${GREEN}✅ 已切换到HTTP协议${NC}"
            echo "📱 请重新编译小程序"
            echo ""
            echo -e "${YELLOW}⚠️  记得在生产环境中切换回HTTPS${NC}"
        else
            echo "❌ 操作已取消"
        fi
        ;;
        
    5)
        echo ""
        echo -e "${GREEN}详细配置指南${NC}"
        echo "================"
        echo ""
        echo "📖 请查看以下文档:"
        echo "- douyin-dev-config.md (开发者工具配置)"
        echo "- ../mall_server/SSL_SETUP_GUIDE.md (SSL配置指南)"
        echo ""
        echo "🔧 可用工具:"
        echo "- update-api-domain.js (API域名更新工具)"
        echo "- ../mall_server/setup-ngrok.sh (ngrok配置脚本)"
        echo ""
        echo "🌐 测试命令:"
        echo "curl -k https://***********:8443/api/user/login"
        ;;
        
    *)
        echo -e "${RED}❌ 无效选项${NC}"
        echo "请重新运行脚本并选择 1-5"
        ;;
esac

echo ""
echo "🔗 相关资源:"
echo "- ngrok: https://ngrok.com/"
echo "- localtunnel: https://localtunnel.github.io/www/"
echo "- 抖音小程序文档: https://developer.open-douyin.com/"
echo ""
echo -e "${BLUE}💡 提示: 如果问题仍然存在，请检查防火墙设置和网络连接${NC}"
