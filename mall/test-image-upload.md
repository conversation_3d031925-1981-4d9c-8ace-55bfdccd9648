# 🧪 故障图片上传功能测试指南

## 📋 测试流程

### 1. 小程序端测试

#### 📱 故障图片上传测试
1. **打开维修预约页面**
   - 进入小程序
   - 点击"维修预约"
   - 填写故障信息

2. **上传故障图片**
   - 在故障照片区域点击"+"按钮
   - 选择相册或拍照
   - 最多可选择3张图片
   - 验证图片预览功能

3. **提交订单**
   - 填写完整信息
   - 选择预约时间
   - 点击提交
   - 观察上传进度提示

4. **查看订单详情**
   - 在订单列表中找到刚创建的订单
   - 点击查看详情
   - 验证故障图片是否正确显示
   - 测试图片预览功能

### 2. 管理端测试

#### 🖥️ 管理端图片显示测试
1. **登录管理后台**
   - 访问 `https://localhost:8443/admin/login`
   - 使用管理员账号登录

2. **查看订单列表**
   - 进入订单管理页面
   - 找到包含故障图片的订单
   - 验证图片缩略图显示

3. **查看订单详情**
   - 点击"查看详情"按钮
   - 验证故障图片完整显示
   - 测试图片点击放大功能

## 🔍 验证要点

### ✅ 小程序端验证
- [ ] 图片选择功能正常
- [ ] 图片预览功能正常
- [ ] 图片删除功能正常
- [ ] 上传进度提示正常
- [ ] 订单提交成功
- [ ] 订单详情图片显示正常

### ✅ 管理端验证
- [ ] 订单列表显示图片缩略图
- [ ] 订单详情显示完整图片
- [ ] 图片点击放大功能正常
- [ ] 图片加载速度合理

### ✅ 后端验证
- [ ] 图片文件成功保存到服务器
- [ ] 数据库中正确存储图片URL
- [ ] 图片访问路径正确

## 🐛 常见问题排查

### 问题1: 图片上传失败
**可能原因:**
- 网络连接问题
- 服务器存储空间不足
- 图片格式不支持
- 图片大小超限

**排查步骤:**
1. 检查网络连接
2. 查看服务器日志
3. 验证图片格式和大小
4. 检查上传接口响应

### 问题2: 管理端图片不显示
**可能原因:**
- 图片路径错误
- 静态资源访问配置问题
- 图片文件丢失

**排查步骤:**
1. 检查图片URL是否正确
2. 验证静态资源配置
3. 确认图片文件存在
4. 检查浏览器控制台错误

### 问题3: 图片预览功能异常
**可能原因:**
- JavaScript错误
- 图片URL格式问题
- 浏览器兼容性问题

**排查步骤:**
1. 检查浏览器控制台
2. 验证图片URL格式
3. 测试不同浏览器

## 📊 测试数据

### 测试图片要求
- **格式**: JPG, PNG, GIF, BMP, WEBP
- **大小**: 不超过5MB
- **数量**: 最多3张
- **分辨率**: 建议不超过4096x4096

### 测试场景
1. **正常场景**: 上传1-3张符合要求的图片
2. **边界场景**: 上传最大尺寸图片
3. **异常场景**: 上传不支持格式或超大文件
4. **网络场景**: 弱网环境下上传

## 🔧 调试工具

### 小程序调试
```javascript
// 在repair_time.ts中添加调试日志
console.log('📸 开始上传图片:', imagePaths);
console.log('✅ 图片上传完成:', uploadedImageUrls);
```

### 管理端调试
```javascript
// 在浏览器控制台中检查
console.log('🖼️ 图片数据:', document.querySelectorAll('.image-gallery'));
```

### 后端调试
```bash
# 查看上传文件
ls -la /uploads/

# 查看服务器日志
tail -f logs/spring.log
```

## 📈 性能测试

### 上传性能
- 单张图片上传时间 < 5秒
- 3张图片批量上传时间 < 15秒
- 上传成功率 > 95%

### 显示性能
- 图片加载时间 < 3秒
- 缩略图生成时间 < 1秒
- 预览打开时间 < 1秒

## 🎯 测试结果记录

### 测试环境
- **小程序版本**: 
- **管理端浏览器**: 
- **服务器环境**: 
- **测试时间**: 

### 测试结果
| 功能 | 状态 | 备注 |
|------|------|------|
| 图片选择 | ✅/❌ |  |
| 图片上传 | ✅/❌ |  |
| 图片显示 | ✅/❌ |  |
| 图片预览 | ✅/❌ |  |
| 管理端显示 | ✅/❌ |  |

### 发现的问题
1. 
2. 
3. 

### 改进建议
1. 
2. 
3. 

## 🚀 部署检查清单

### 生产环境部署前
- [ ] 配置正确的上传路径
- [ ] 设置合适的文件大小限制
- [ ] 配置图片压缩
- [ ] 设置CDN加速
- [ ] 配置HTTPS证书
- [ ] 测试所有功能

### 监控指标
- 图片上传成功率
- 图片加载速度
- 存储空间使用情况
- 用户反馈
