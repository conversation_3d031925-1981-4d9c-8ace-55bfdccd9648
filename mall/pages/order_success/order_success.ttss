.container {
  padding: 30rpx;
}

.success-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 0;
  background-color: white;
  border-radius: 24rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.success-icon {
  width: 160rpx;
  height: 160rpx;
  background-color: #e8f5e9;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 32rpx;
  color: #4caf50;
  font-size: 80rpx;
}

.success-title {
  font-size: 40rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
}

.success-message {
  font-size: 28rpx;
  color: #757575;
  margin-bottom: 48rpx;
}

.order-info {
  width: 100%;
  padding: 0 32rpx;
  box-sizing: border-box;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24rpx;
  font-size: 28rpx;
}

.info-label {
  color: #757575;
}

.info-value {
  font-weight: 500;
}

.action-buttons {
  display: flex;
  gap: 32rpx;
  margin-top: 48rpx;
}

.action-btn {
  width: 300rpx;
  padding: 24rpx 0;
  text-align: center;
  border-radius: 48rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.primary-btn {
  background-color: #1e88e5;
  color: white;
}

.secondary-btn {
  background-color: #f5f5f5;
  color: #333333;
}

.recommended-title {
  font-size: 32rpx;
  font-weight: 600;
  margin: 48rpx 0 32rpx;
}
