const app = getApp()

Page({
  data: {
    orderId: '',
    orderAmount: '0.00',
    deliveryDate: '',
    recommendProducts: [
      {
        id: 3,
        name: '充电桩控制模块',
        price: 499.00,
        image: '/images/products/充电控制模块.png'
      },
      {
        id: 4,
        name: '充电桩防水保护盒',
        price: 159.00,
        image: '/images/products/充电桩防水保护盒.png'
      }
    ]
  },

  onLoad: function (options) {
    // 获取订单ID
    const orderId = options.id || '';

    // 获取订单信息
    this.getOrderInfo(orderId);

    // 计算预计送达日期（示例：3天后）
    const deliveryDate = this.calculateDeliveryDate(3);

    this.setData({
      orderId: orderId,
      deliveryDate: deliveryDate
    });
  },

  // 获取订单信息
  getOrderInfo: function(orderId) {
    // 从全局数据中获取最近的订单
    const lastOrder = app.globalData.lastOrder;

    if (lastOrder && lastOrder.id === orderId) {
      this.setData({
        orderAmount: lastOrder.actualAmount
      });
    } else {
      // 如果没有找到订单，使用默认值
      this.setData({
        orderAmount: '269.00'
      });
    }
  },

  // 计算预计送达日期
  calculateDeliveryDate: function(days) {
    const date = new Date();
    date.setDate(date.getDate() + days);

    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();

    return `${year}-${month < 10 ? '0' + month : month}-${day < 10 ? '0' + day : day}`;
  },

  // 返回首页
  goToHome: function() {
    // 使用reLaunch方法跳转到首页，并关闭所有其他页面
    tt.reLaunch({
      url: '/pages/index/index',
      success: () => {
      },
      fail: (err) => {
      }
    });
  },

  // 查看订单详情
  goToOrderDetail: function() {
    tt.showToast({
      title: '订单详情功能开发中',
      icon: 'none'
    });

    // 实际开发中应该跳转到订单详情页面
    // tt.navigateTo({
    //   url: `/pages/order_detail/order_detail?id=${this.data.orderId}`
    // });
  },

  // 跳转到商品详情页
  goToProductDetail: function(e) {
    const id = e.currentTarget.dataset.id;
    tt.navigateTo({
      url: `/pages/product_detail/product_detail?id=${id}`
    });
  }
})
