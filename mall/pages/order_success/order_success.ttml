<view class="container">
  <!-- 成功信息 -->
  <view class="success-container">
    <view class="success-icon">
      <image src="/images/icons/成功.png" mode="aspectFit" style="width: 100rpx; height: 100rpx;"></image>
    </view>
    <view class="success-title">订单提交成功</view>
    <view class="success-message">您的订单已提交，我们将尽快为您发货</view>
    
    <view class="order-info">
      <view class="info-item">
        <view class="info-label">订单编号</view>
        <view class="info-value">{{orderId}}</view>
      </view>
      <view class="info-item">
        <view class="info-label">订单金额</view>
        <view class="info-value">¥{{orderAmount}}</view>
      </view>
      <view class="info-item">
        <view class="info-label">支付方式</view>
        <view class="info-value">微信支付</view>
      </view>
      <view class="info-item">
        <view class="info-label">预计送达</view>
        <view class="info-value">{{deliveryDate}}</view>
      </view>
    </view>
    
    <view class="action-buttons">
      <view class="action-btn secondary-btn" bindtap="goToHome">返回首页</view>
      <view class="action-btn primary-btn" bindtap="goToOrderDetail">查看订单</view>
    </view>
  </view>
  
  <!-- 推荐商品 -->
  <view class="recommended-title">为您推荐</view>
  
  <view class="grid">
    <view class="product-card" tt:for="{{recommendProducts}}" tt:key="id" bindtap="goToProductDetail" data-id="{{item.id}}">
      <image src="{{item.image}}" mode="aspectFill" />
      <view class="info">
        <view class="title">{{item.name}}</view>
        <view class="price">¥{{item.price}}</view>
      </view>
    </view>
  </view>
</view>
