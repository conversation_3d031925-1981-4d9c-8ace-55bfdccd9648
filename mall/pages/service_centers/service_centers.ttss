.container {
  padding: 30rpx 30rpx 0;
}

.map-container {
  width: 100%;
  height: 600rpx;
  margin-bottom: 32rpx;
  border-radius: 24rpx;
  overflow: hidden;
}

map {
  width: 100%;
  height: 100%;
}

.service-center {
  background-color: white;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.service-center-name {
  font-weight: 600;
  margin-bottom: 16rpx;
  font-size: 32rpx;
}

.service-center-info {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  font-size: 28rpx;
  color: #757575;
}

.service-center-info .iconfont {
  margin-right: 16rpx;
  width: 32rpx;
  text-align: center;
}

.service-center-actions {
  display: flex;
  margin-top: 24rpx;
  gap: 24rpx;
}

.action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx 24rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  flex: 1;
}

.action-button .iconfont {
  margin-right: 8rpx;
}

.call-button {
  background-color: #e3f2fd;
  color: #1e88e5;
}

.navigate-button {
  background-color: #1e88e5;
  color: white;
}

.loading, .empty {
  text-align: center;
  padding: 100rpx 40rpx;
  color: #999;
  font-size: 28rpx;
}

.loading-more, .no-more {
  text-align: center;
  padding: 30rpx 0;
  color: #757575;
  font-size: 28rpx;
}
