const app = getApp()
const api = require('../../utils/api.js')

Page({
  data: {
    longitude: 121.4737,
    latitude: 31.2304,
    scale: 12,
    markers: [],
    serviceCenters: [],
    allServiceCenters: [],
    sortType: 'all',
    hasMore: false,
    searchKeyword: '',
    loading: false
  },

  onLoad: function () {

    // 获取用户位置
    this.getUserLocation();

    // 加载服务网点数据
    this.loadServiceCenters();
  },

  // 获取用户位置
  getUserLocation: function() {
    tt.getLocation({
      type: 'gcj02',
      success: (res) => {
        this.setData({
          latitude: res.latitude,
          longitude: res.longitude
        });

        // 更新服务网点距离
        this.updateServiceCenterDistance(res.latitude, res.longitude);

        // 更新地图标记
        this.updateMapMarkers();
      },
      fail: () => {
        tt.showToast({
          title: '获取位置失败',
          icon: 'none'
        });
      }
    });
  },

  // 加载服务网点数据
  loadServiceCenters: function() {
    this.setData({ loading: true });

    api.getApprovedServiceCenters().then(res => {
      if (res.success) {
        // 处理服务网点数据
        const serviceCenters = res.data.map(center => {
          // 解析JSON字段
          let serviceTypes = [];
          let facilities = [];

          try {
            serviceTypes = JSON.parse(center.serviceTypes || '[]');
            facilities = JSON.parse(center.facilities || '[]');
          } catch (e) {
          }

          return {
            id: center.id,
            name: center.name,
            address: center.address,
            phone: center.phone,
            businessHours: center.businessHours,
            rating: center.rating || 5.0,
            reviewCount: center.reviewCount || 0,
            distance: 0, // 初始距离，后续计算
            latitude: parseFloat(center.latitude),
            longitude: parseFloat(center.longitude),
            serviceTypes: serviceTypes,
            facilities: facilities,
            serviceFee: center.serviceFee || 0,
            inspectionFee: center.inspectionFee || 0,
            parkingInfo: center.parkingInfo || '',
            serviceDescription: center.serviceDescription || ''
          };
        });

        // 创建地图标记
        const markers = serviceCenters.map(item => {
          return {
            id: item.id,
            latitude: item.latitude,
            longitude: item.longitude,
            title: item.name,
            iconPath: '/images/marker.png', // 需要提供标记图标
            width: 32,
            height: 32
          };
        });

        this.setData({
          serviceCenters: serviceCenters,
          allServiceCenters: serviceCenters,
          markers: markers,
          hasMore: false,
          loading: false
        });

        // 如果已经获取到用户位置，更新距离
        if (this.data.latitude !== 31.2304) {
          this.updateServiceCenterDistance(this.data.latitude, this.data.longitude);
        }
      } else {
        this.setData({ loading: false });
      }
    }).catch(err => {
      this.setData({ loading: false });
    });
  },

  // 更新服务网点距离
  updateServiceCenterDistance: function(latitude, longitude) {
    const serviceCenters = this.data.serviceCenters.map(item => {
      // 计算距离（简化版，实际应该使用更精确的算法）
      const distance = this.calculateDistance(
        latitude, longitude,
        item.latitude, item.longitude
      );

      return {
        ...item,
        distance: distance.toFixed(1)
      };
    });

    this.setData({
      serviceCenters: serviceCenters
    });
  },

  // 计算两点之间的距离（简化版）
  calculateDistance: function(lat1, lon1, lat2, lon2) {
    const R = 6371; // 地球半径，单位km
    const dLat = this.deg2rad(lat2 - lat1);
    const dLon = this.deg2rad(lon2 - lon1);
    const a =
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(this.deg2rad(lat1)) * Math.cos(this.deg2rad(lat2)) *
      Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    const d = R * c; // 距离，单位km
    return d;
  },

  // 角度转弧度
  deg2rad: function(deg) {
    return deg * (Math.PI/180);
  },

  // 更新地图标记
  updateMapMarkers: function() {
    const markers = this.data.serviceCenters.map(item => {
      return {
        id: item.id,
        latitude: item.latitude,
        longitude: item.longitude,
        title: item.name,
        iconPath: '/images/marker.png', // 需要提供标记图标
        width: 32,
        height: 32
      };
    });

    this.setData({
      markers: markers
    });
  },

  // 搜索输入
  onSearchInput: function(e) {
    const keyword = e.detail.value;

    this.setData({
      searchKeyword: keyword
    });

    // 搜索服务网点
    this.searchServiceCenters(keyword);
  },

  // 搜索服务网点
  searchServiceCenters: function(keyword) {
    if (!keyword) {
      // 如果关键词为空，显示所有服务网点
      this.setData({
        serviceCenters: this.data.allServiceCenters
      });
      this.updateMapMarkers();
      return;
    }

    // 使用本地筛选（也可以调用API搜索）
    const filteredCenters = this.data.allServiceCenters.filter(item => {
      return item.name.includes(keyword) ||
             item.address.includes(keyword) ||
             item.serviceDescription.includes(keyword);
    });

    this.setData({
      serviceCenters: filteredCenters
    });

    // 更新地图标记
    this.updateMapMarkers();
  },

  // 排序方式点击
  onSortTap: function(e) {
    const type = e.currentTarget.dataset.type;

    if (type === this.data.sortType) {
      return;
    }

    this.setData({
      sortType: type
    });

    // 根据排序方式排序
    this.sortServiceCenters(type);
  },

  // 排序服务网点
  sortServiceCenters: function(type) {
    let sortedCenters = [...this.data.serviceCenters];

    switch(type) {
      case 'distance':
        // 按距离排序
        sortedCenters.sort((a, b) => parseFloat(a.distance) - parseFloat(b.distance));
        break;
      case 'rating':
        // 按评分排序
        sortedCenters.sort((a, b) => b.rating - a.rating);
        break;
      default:
        // 默认排序（按ID）
        sortedCenters.sort((a, b) => a.id - b.id);
        break;
    }

    this.setData({
      serviceCenters: sortedCenters
    });
  },

  // 显示筛选
  showFilter: function() {
    tt.showToast({
      title: '筛选功能开发中',
      icon: 'none'
    });
  },

  // 地图标记点击
  onMarkerTap: function(e) {
    const markerId = e.markerId;
    const serviceCenter = this.data.serviceCenters.find(item => item.id === markerId);

    if (serviceCenter) {
      // 显示服务网点详情
      this.showServiceCenterDetail(serviceCenter);
    }
  },

  // 服务网点点击
  onServiceCenterTap: function(e) {
    const id = e.currentTarget.dataset.id;
    const serviceCenter = this.data.serviceCenters.find(item => item.id === id);

    if (serviceCenter) {
      // 显示服务网点详情
      this.showServiceCenterDetail(serviceCenter);
    }
  },

  // 显示服务网点详情
  showServiceCenterDetail: function(serviceCenter) {
    // 实际开发中应该跳转到服务网点详情页面
    tt.showModal({
      title: serviceCenter.name,
      content: `地址：${serviceCenter.address}\n电话：${serviceCenter.phone}\n营业时间：${serviceCenter.businessHours}\n`,
      showCancel: false
    });
  },

  // 拨打服务网点电话
  callServiceCenter: function(e) {
    const phone = e.currentTarget.dataset.phone;

    tt.makePhoneCall({
      phoneNumber: phone
    });
  },

  // 导航到服务网点
  navigateToServiceCenter: function(e) {
    const latitude = e.currentTarget.dataset.latitude;
    const longitude = e.currentTarget.dataset.longitude;
    const name = e.currentTarget.dataset.name;

    tt.openLocation({
      latitude: latitude,
      longitude: longitude,
      name: name,
      scale: 18
    });
  }
})
