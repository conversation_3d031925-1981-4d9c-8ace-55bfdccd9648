const app = getApp()
const api = require('../../utils/api.js')

Page({
  data: {
    formData: {
      name: '',
      contactPerson: '',
      phone: '',
      email: '',
      address: '',
      province: '',
      city: '',
      district: '',
      businessHours: '09:00-18:00',
      serviceTypes: [],
      equipmentTypes: [],
      serviceDescription: '',
      facilities: [],
      serviceFee: '',
      inspectionFee: '',
      parkingInfo: '',
      businessLicense: '',
      qualificationCertificates: [],
      images: []
    },

    // 选项数据
    serviceTypeOptions: [
      { id: 1, name: '充电桩维修', checked: false },
      { id: 2, name: '充电桩安装', checked: false },
      { id: 3, name: '设备检测', checked: false },
      { id: 4, name: '系统升级', checked: false },
      { id: 5, name: '预防性维护', checked: false },
      { id: 6, name: '应急抢修', checked: false }
    ],

    equipmentTypeOptions: [
      { id: 1, name: '交流充电桩', checked: false },
      { id: 2, name: '直流充电桩', checked: false },
      { id: 3, name: '超级充电桩', checked: false },
      { id: 4, name: '便携式充电器', checked: false },
      { id: 5, name: '充电线缆', checked: false },
      { id: 6, name: '充电管理系统', checked: false }
    ],

    facilityOptions: [
      { id: 1, name: '专业维修车间', checked: false },
      { id: 2, name: '设备检测室', checked: false },
      { id: 3, name: '配件仓库', checked: false },
      { id: 4, name: '客户休息区', checked: false },
      { id: 5, name: '停车场', checked: false },
      { id: 6, name: '应急服务车', checked: false }
    ],

    submitting: false
  },

  onLoad: function () {
  },

  // 输入框变化
  onInputChange: function(e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;

    this.setData({
      [`formData.${field}`]: value
    });
  },

  // 服务类型选择
  onServiceTypeChange: function(e) {
    const index = e.currentTarget.dataset.index;
    const serviceTypes = this.data.serviceTypeOptions;
    serviceTypes[index].checked = !serviceTypes[index].checked;

    // 更新选中的服务类型
    const selectedTypes = serviceTypes.filter(item => item.checked).map(item => item.name);

    this.setData({
      serviceTypeOptions: serviceTypes,
      'formData.serviceTypes': selectedTypes
    });
  },

  // 设备类型选择
  onEquipmentTypeChange: function(e) {
    const index = e.currentTarget.dataset.index;
    const equipmentTypes = this.data.equipmentTypeOptions;
    equipmentTypes[index].checked = !equipmentTypes[index].checked;

    // 更新选中的设备类型
    const selectedTypes = equipmentTypes.filter(item => item.checked).map(item => item.name);

    this.setData({
      equipmentTypeOptions: equipmentTypes,
      'formData.equipmentTypes': selectedTypes
    });
  },

  // 设施选择
  onFacilityChange: function(e) {
    const index = e.currentTarget.dataset.index;
    const facilities = this.data.facilityOptions;
    facilities[index].checked = !facilities[index].checked;

    // 更新选中的设施
    const selectedFacilities = facilities.filter(item => item.checked).map(item => item.name);

    this.setData({
      facilityOptions: facilities,
      'formData.facilities': selectedFacilities
    });
  },

  // 选择地址
  chooseLocation: function() {
    tt.chooseLocation({
      success: (res) => {
        this.setData({
          'formData.address': res.address,
          'formData.latitude': res.latitude,
          'formData.longitude': res.longitude
        });
      },
      fail: (err) => {
        tt.showToast({
          title: '选择位置失败',
          icon: 'none'
        });
      }
    });
  },

  // 上传营业执照
  uploadBusinessLicense: function() {
    tt.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        // 显示上传中提示
        tt.showLoading({
          title: '上传中...'
        });

        // 上传到服务器
        api.uploadImage(res.tempFilePaths[0]).then(uploadRes => {
          tt.hideLoading();

          if (uploadRes.success) {
            this.setData({
              'formData.businessLicense': uploadRes.data.url
            });

            tt.showToast({
              title: '上传成功',
              icon: 'success'
            });
          } else {
            tt.showToast({
              title: uploadRes.message || '上传失败',
              icon: 'none'
            });
          }
        }).catch(err => {
          tt.hideLoading();
          tt.showToast({
            title: '上传失败',
            icon: 'none'
          });
        });
      },
      fail: (err) => {
        tt.showToast({
          title: '选择图片失败',
          icon: 'none'
        });
      }
    });
  },

  // 上传资质证书
  uploadCertificates: function() {
    const maxCount = 9 - this.data.formData.qualificationCertificates.length;
    if (maxCount <= 0) {
      tt.showToast({
        title: '最多只能上传9张证书',
        icon: 'none'
      });
      return;
    }

    tt.chooseImage({
      count: maxCount,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        // 显示上传中提示
        tt.showLoading({
          title: '上传中...'
        });

        // 批量上传图片
        api.uploadImages(res.tempFilePaths).then(uploadResults => {
          tt.hideLoading();

          const successUrls = [];
          const errors = [];

          uploadResults.forEach((result, index) => {
            if (result.success) {
              successUrls.push(result.data.url);
            } else {
              errors.push(`第${index + 1}张图片上传失败`);
            }
          });

          if (successUrls.length > 0) {
            const certificates = [...this.data.formData.qualificationCertificates, ...successUrls];
            this.setData({
              'formData.qualificationCertificates': certificates
            });
          }

          if (errors.length > 0) {
            tt.showToast({
              title: `${successUrls.length}张成功，${errors.length}张失败`,
              icon: 'none'
            });
          } else {
            tt.showToast({
              title: '上传成功',
              icon: 'success'
            });
          }
        }).catch(err => {
          tt.hideLoading();
          tt.showToast({
            title: '上传失败',
            icon: 'none'
          });
        });
      },
      fail: (err) => {
        tt.showToast({
          title: '选择图片失败',
          icon: 'none'
        });
      }
    });
  },

  // 删除证书
  deleteCertificate: function(e) {
    const index = e.currentTarget.dataset.index;
    const certificates = this.data.formData.qualificationCertificates;
    certificates.splice(index, 1);

    this.setData({
      'formData.qualificationCertificates': certificates
    });
  },

  // 上传网点图片
  uploadImages: function() {
    const maxCount = 9 - this.data.formData.images.length;
    if (maxCount <= 0) {
      tt.showToast({
        title: '最多只能上传9张图片',
        icon: 'none'
      });
      return;
    }

    tt.chooseImage({
      count: maxCount,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        // 显示上传中提示
        tt.showLoading({
          title: '上传中...'
        });

        // 批量上传图片到服务器
        const api = require('../../utils/api');
        api.uploadImages(res.tempFilePaths).then(uploadResults => {
          tt.hideLoading();

          const successUrls = [];
          const errors = [];

          uploadResults.forEach((result, index) => {
            if (result.success) {
              successUrls.push(result.data.url);
            } else {
              errors.push(`第${index + 1}张图片上传失败`);
            }
          });

          if (successUrls.length > 0) {
            const images = [...this.data.formData.images, ...successUrls];
            this.setData({
              'formData.images': images
            });

            if (errors.length > 0) {
              tt.showToast({
                title: `${successUrls.length}张成功，${errors.length}张失败`,
                icon: 'none'
              });
            } else {
              tt.showToast({
                title: '上传成功',
                icon: 'success'
              });
            }
          } else {
            tt.showToast({
              title: '上传失败',
              icon: 'none'
            });
          }
        }).catch(err => {
          tt.hideLoading();
          tt.showToast({
            title: '上传失败',
            icon: 'none'
          });
        });
      },
      fail: (err) => {
        tt.showToast({
          title: '选择图片失败',
          icon: 'none'
        });
      }
    });
  },

  // 删除图片
  deleteImage: function(e) {
    const index = e.currentTarget.dataset.index;
    const images = this.data.formData.images;
    images.splice(index, 1);

    this.setData({
      'formData.images': images
    });
  },

  // 预览营业执照
  previewBusinessLicense: function(e) {
    e.stopPropagation(); // 阻止事件冒泡，避免触发上传
    const src = e.currentTarget.dataset.src;
    if (src) {
      tt.previewImage({
        current: src,
        urls: [src],
        showmenu: true,
        success: () => {
        },
        fail: (err: any) => {
          tt.showToast({
            title: '预览失败',
            icon: 'none'
          });
        }
      });
    }
  },

  // 预览资质证书
  previewCertificate: function(e) {
    e.stopPropagation(); // 阻止事件冒泡
    const src = e.currentTarget.dataset.src;
    const index = e.currentTarget.dataset.index;

    if (src && this.data.formData.qualificationCertificates.length > 0) {
      tt.previewImage({
        current: src,
        urls: this.data.formData.qualificationCertificates,
        showmenu: true,
        success: () => {
        },
        fail: (err: any) => {
          tt.showToast({
            title: '预览失败',
            icon: 'none'
          });
        }
      });
    }
  },

  // 预览网点照片
  previewStationImage: function(e) {
    e.stopPropagation(); // 阻止事件冒泡
    const src = e.currentTarget.dataset.src;
    const index = e.currentTarget.dataset.index;

    if (src && this.data.formData.images.length > 0) {
      tt.previewImage({
        current: src,
        urls: this.data.formData.images,
        showmenu: true,
        success: () => {
        },
        fail: (err: any) => {
          tt.showToast({
            title: '预览失败',
            icon: 'none'
          });
        }
      });
    }
  },

  // 表单验证
  validateForm: function() {
    const { formData } = this.data;

    if (!formData.name) {
      tt.showToast({ title: '请填写网点名称', icon: 'none' });
      return false;
    }

    if (!formData.contactPerson) {
      tt.showToast({ title: '请填写联系人', icon: 'none' });
      return false;
    }

    if (!formData.phone) {
      tt.showToast({ title: '请填写联系电话', icon: 'none' });
      return false;
    }

    if (!/^1[3-9]\d{9}$/.test(formData.phone)) {
      tt.showToast({ title: '请填写正确的手机号', icon: 'none' });
      return false;
    }

    if (!formData.address) {
      tt.showToast({ title: '请选择网点地址', icon: 'none' });
      return false;
    }

    if (formData.serviceTypes.length === 0) {
      tt.showToast({ title: '请选择至少一种服务类型', icon: 'none' });
      return false;
    }

    if (!formData.businessLicense) {
      tt.showToast({ title: '请上传营业执照', icon: 'none' });
      return false;
    }

    return true;
  },

  // 提交申请
  submitApplication: function() {
    if (!this.validateForm()) {
      return;
    }

    this.setData({ submitting: true });

    // 构建提交数据
    const submitData = {
      ...this.data.formData,
      serviceTypes: JSON.stringify(this.data.formData.serviceTypes),
      equipmentTypes: JSON.stringify(this.data.formData.equipmentTypes),
      facilities: JSON.stringify(this.data.formData.facilities),
      qualificationCertificates: JSON.stringify(this.data.formData.qualificationCertificates),
      images: JSON.stringify(this.data.formData.images)
    };

    // 调用API提交申请
    api.submitServiceCenterApplication(submitData).then(res => {
      this.setData({ submitting: false });

      if (res.success) {
        tt.showModal({
          title: '申请提交成功',
          content: '您的充电站入驻申请已提交，我们将在3-5个工作日内完成审核，请耐心等待。',
          showCancel: false,
          confirmText: '确定',
          success: () => {
            tt.navigateBack();
          }
        });
      } else {
        tt.showModal({
          title: '申请提交失败',
          content: res.message || '提交失败，请稍后重试',
          showCancel: false,
          confirmText: '确定'
        });
      }
    }).catch(err => {
      this.setData({ submitting: false });

      tt.showModal({
        title: '申请提交失败',
        content: '网络错误，请检查网络连接后重试',
        showCancel: false,
        confirmText: '确定'
      });
    });
  }
})
