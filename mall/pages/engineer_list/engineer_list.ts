const app = getApp()
const api = require('../../utils/api.js')

Page({
  data: {
    engineers: [],
    filteredEngineers: [],
    specialties: [],
    currentSpecialty: '',
    searchKeyword: '',
    sortType: 'rating', // rating, experience, orders
    loading: false,

    // 筛选选项
    filterOptions: {
      showFilter: false,
      experienceRanges: [
        { label: '不限', value: '' },
        { label: '1-3年', value: '1-3' },
        { label: '4-6年', value: '4-6' },
        { label: '7-10年', value: '7-10' },
        { label: '10年以上', value: '10+' }
      ],
      selectedExperience: '',
      ratingRanges: [
        { label: '不限', value: '' },
        { label: '4.5分以上', value: '4.5' },
        { label: '4.0分以上', value: '4.0' },
        { label: '3.5分以上', value: '3.5' }
      ],
      selectedRating: ''
    }
  },

  onLoad: function () {
    this.loadEngineers();
  },

  // 加载工程师数据
  loadEngineers: function() {
    this.setData({ loading: true });

    api.getApprovedEngineers().then(res => {
      if (res.success) {
        const engineers = res.data.map(engineer => {
          // 解析JSON字段
          let specialties = [];
          let skills = [];
          let workAreas = [];
          let certifications = [];

          try {
            specialties = JSON.parse(engineer.specialties || '[]');
            skills = JSON.parse(engineer.skills || '[]');
            workAreas = JSON.parse(engineer.workAreas || '[]');
            certifications = JSON.parse(engineer.certifications || '[]');
          } catch (e) {
          }

          return {
            id: engineer.id,
            name: engineer.name,
            avatar: engineer.avatar,
            processedAvatar: this.processAvatarUrl(engineer.avatar),
            rating: engineer.rating || 5.0,
            ratingStars: Math.floor(engineer.rating || 5),
            totalOrders: engineer.totalOrders || 0,
            completedOrders: engineer.completedOrders || 0,
            successRate: engineer.successRate || 100,
            experienceYears: engineer.experienceYears || 0,
            education: engineer.education || '',
            bio: engineer.bio || '',
            introduction: engineer.introduction || '',
            workTime: engineer.workTime || '',
            hourlyRate: engineer.hourlyRate || 0,
            serviceFee: engineer.serviceFee || 0,
            specialties: specialties,
            skills: skills,
            workAreas: workAreas,
            certifications: certifications,
            phone: engineer.phone || ''
          };
        });

        this.setData({
          engineers: engineers,
          filteredEngineers: engineers,
          loading: false
        });

        // 数据加载完成后再加载专业领域
        this.loadSpecialties();
      } else {
        this.setData({ loading: false });
      }
    }).catch(err => {
      this.setData({ loading: false });
    });
  },

  // 加载专业领域
  loadSpecialties: function() {
    // 从工程师数据中提取专业领域
    const allSpecialties = new Set();
    this.data.engineers.forEach(engineer => {
      engineer.specialties.forEach(specialty => {
        allSpecialties.add(specialty);
      });
    });

    const specialties = [
      { label: '全部', value: '' },
      ...Array.from(allSpecialties).map(specialty => ({
        label: specialty,
        value: specialty
      }))
    ];

    this.setData({ specialties });
  },

  // 搜索功能
  onSearchInput: function(e) {
    const keyword = e.detail.value;
    this.setData({ searchKeyword: keyword });
    this.filterEngineers();
  },

  // 专业领域筛选
  onSpecialtyChange: function(e) {
    const specialty = e.currentTarget.dataset.specialty;
    this.setData({ currentSpecialty: specialty });
    this.filterEngineers();
  },

  // 排序切换
  onSortChange: function(e) {
    const sortType = e.currentTarget.dataset.sort;
    this.setData({ sortType });
    this.filterEngineers();
  },

  // 显示/隐藏筛选面板
  toggleFilter: function() {
    this.setData({
      'filterOptions.showFilter': !this.data.filterOptions.showFilter
    });
  },

  // 经验筛选
  onExperienceChange: function(e) {
    const experience = e.currentTarget.dataset.experience;
    this.setData({
      'filterOptions.selectedExperience': experience
    });
    this.filterEngineers();
  },

  // 评分筛选
  onRatingChange: function(e) {
    const rating = e.currentTarget.dataset.rating;
    this.setData({
      'filterOptions.selectedRating': rating
    });
    this.filterEngineers();
  },

  // 重置筛选
  resetFilter: function() {
    this.setData({
      currentSpecialty: '',
      searchKeyword: '',
      sortType: 'rating',
      'filterOptions.selectedExperience': '',
      'filterOptions.selectedRating': '',
      'filterOptions.showFilter': false
    });
    this.filterEngineers();
  },

  // 筛选和排序工程师
  filterEngineers: function() {
    let filtered = [...this.data.engineers];

    // 关键词搜索
    if (this.data.searchKeyword) {
      const keyword = this.data.searchKeyword.toLowerCase();
      filtered = filtered.filter(engineer =>
        engineer.name.toLowerCase().includes(keyword) ||
        engineer.bio.toLowerCase().includes(keyword) ||
        engineer.specialties.some(s => s.toLowerCase().includes(keyword))
      );
    }

    // 专业领域筛选
    if (this.data.currentSpecialty) {
      filtered = filtered.filter(engineer =>
        engineer.specialties.includes(this.data.currentSpecialty)
      );
    }

    // 经验筛选
    if (this.data.filterOptions.selectedExperience) {
      const experience = this.data.filterOptions.selectedExperience;
      filtered = filtered.filter(engineer => {
        const years = engineer.experienceYears;
        switch (experience) {
          case '1-3': return years >= 1 && years <= 3;
          case '4-6': return years >= 4 && years <= 6;
          case '7-10': return years >= 7 && years <= 10;
          case '10+': return years > 10;
          default: return true;
        }
      });
    }

    // 评分筛选
    if (this.data.filterOptions.selectedRating) {
      const minRating = parseFloat(this.data.filterOptions.selectedRating);
      filtered = filtered.filter(engineer => engineer.rating >= minRating);
    }

    // 排序
    filtered.sort((a, b) => {
      switch (this.data.sortType) {
        case 'rating':
          return b.rating - a.rating;
        case 'experience':
          return b.experienceYears - a.experienceYears;
        case 'orders':
          return b.totalOrders - a.totalOrders;
        default:
          return 0;
      }
    });

    this.setData({ filteredEngineers: filtered });
  },

  // 查看工程师详情
  viewEngineerDetail: function(e) {
    const engineerId = e.currentTarget.dataset.id;
    tt.navigateTo({
      url: `/pages/engineer_detail/engineer_detail?id=${engineerId}`
    });
  },

  // 联系工程师
  contactEngineer: function(e) {
    const phone = e.currentTarget.dataset.phone;
    const name = e.currentTarget.dataset.name;

    tt.showModal({
      title: `联系${name}`,
      content: `电话：${phone}`,
      confirmText: '拨打电话',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          tt.makePhoneCall({
            phoneNumber: phone,
            fail: () => {
              tt.showToast({
                title: '拨打失败',
                icon: 'none'
              });
            }
          });
        }
      }
    });
  },

  // 处理头像URL
  processAvatarUrl: function(avatarUrl) {
    if (!avatarUrl || avatarUrl.trim() === '') {
      return '';
    }

    // 如果是完整的HTTP URL，直接返回
    if (avatarUrl.startsWith('http')) {
      return avatarUrl;
    }

    // 如果已经包含域名，直接返回
    if (avatarUrl.startsWith('https://localhost:8443') || avatarUrl.startsWith('https://your-domain.com')) {
      return avatarUrl;
    }

    // 如果是相对路径，添加服务器地址
    if (avatarUrl.startsWith('/uploads/')) {
      return `https://localhost:8443${avatarUrl}`;
    }

    // 如果只是文件名，添加完整路径
    return `https://localhost:8443/uploads/${avatarUrl}`;
  },

  // 头像加载失败处理
  onAvatarError: function(e) {
    const index = e.currentTarget.dataset.index;

    // 将失败的头像URL设置为空，显示默认图标
    const engineers = this.data.filteredEngineers;
    if (engineers[index]) {
      engineers[index].processedAvatar = '';
      this.setData({
        filteredEngineers: engineers
      });
    }
  }
})
