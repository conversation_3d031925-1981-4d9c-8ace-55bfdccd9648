page {
  background-color: #f5f5f5;
}

.container {
  height: 100vh;
  padding-bottom: calc(200rpx + env(safe-area-inset-bottom));
  box-sizing: border-box;
}

/* 申请须知 */
.notice {
  background-color: #fff3cd;
  border: 1rpx solid #ffeaa7;
  border-radius: 12rpx;
  margin: 20rpx;
  padding: 20rpx;
}

.notice-title {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  font-weight: 600;
  color: #856404;
  margin-bottom: 15rpx;
}

.notice-title .iconfont {
  margin-right: 10rpx;
  font-size: 32rpx;
}

.notice-content {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.notice-content text {
  font-size: 24rpx;
  color: #856404;
  line-height: 1.4;
}

/* 表单区域 */
.form-section {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 12rpx;
  padding: 30rpx;
  box-sizing: border-box;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  padding-bottom: 15rpx;
  border-bottom: 2rpx solid #1e88e5;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
  font-weight: 500;
}

.required {
  color: #ff4757;
  margin-left: 4rpx;
}

.form-input {
  width: calc(100% - 40rpx);
  height: 80rpx;
  background-color: #f8f9fa;
  border: 1rpx solid #e9ecef;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #1e88e5;
  background-color: #fff;
}

.form-textarea {
  width: calc(100% - 40rpx);
  min-height: 120rpx;
  background-color: #f8f9fa;
  border: 1rpx solid #e9ecef;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}

.form-textarea:focus {
  border-color: #1e88e5;
  background-color: #fff;
}

/* 选择器 */
.form-picker {
  width: calc(100% - 40rpx);
  height: 80rpx;
  background-color: #f8f9fa;
  border: 1rpx solid #e9ecef;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  padding: 0 20rpx;
  box-sizing: border-box;
}

.picker-text {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

.picker-text.placeholder {
  color: #999;
}

/* 复选框组 */
.checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
  margin-top: 10rpx;
}

.checkbox-item {
  display: flex;
  align-items: center;
  padding: 12rpx 20rpx;
  background-color: #f8f9fa;
  border: 1rpx solid #e9ecef;
  border-radius: 20rpx;
  font-size: 26rpx;
  color: #666;
  transition: all 0.3s ease;
  min-width: fit-content;
  white-space: nowrap;
}

.checkbox-item.checked {
  background-color: #e3f2fd;
  border-color: #1e88e5;
  color: #1e88e5;
}

.checkbox-icon {
  width: 32rpx;
  height: 32rpx;
  border: 2rpx solid #ddd;
  border-radius: 4rpx;
  margin-right: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  color: #fff;
  background-color: #fff;
}

.checkbox-item.checked .checkbox-icon {
  background-color: #1e88e5;
  border-color: #1e88e5;
}

/* 上传网格 */
.upload-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15rpx;
  margin-top: 10rpx;
}

.upload-item {
  position: relative;
  width: 100%;
  height: 150rpx;
  border-radius: 8rpx;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.upload-item:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
}

.upload-item.upload-placeholder {
  border: 2rpx dashed #ddd;
  background-color: #fafafa;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
}

.upload-item.upload-placeholder .iconfont {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.upload-item.upload-placeholder text:last-child {
  font-size: 22rpx;
}

.uploaded-image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
  transition: all 0.3s ease;
}

.uploaded-image:active {
  transform: scale(0.95);
  opacity: 0.8;
}

/* 图片遮罩层 */
.image-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 8rpx;
}

.upload-item:active .image-mask {
  opacity: 1;
}

.image-mask .iconfont {
  color: #fff;
  font-size: 48rpx;
}

/* 优化删除按钮 */
.delete-btn {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: #ff4757;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 20rpx;
  z-index: 4;
  box-shadow: 0 2rpx 8rpx rgba(255, 71, 87, 0.3);
  transition: all 0.3s ease;
}

.delete-btn:active {
  transform: scale(0.9);
  background-color: #ff3742;
}

.upload-tip {
  font-size: 22rpx;
  color: #999;
  margin-top: 10rpx;
  line-height: 1.4;
}

/* 提交区域 */
.submit-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 30rpx;
  padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
  border-top: 1rpx solid #e9ecef;
  z-index: 100;
  box-sizing: border-box;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background-color: #1e88e5;
  color: #fff;
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
}

.submit-btn.submitting {
  background-color: #999;
}

.submit-btn::after {
  border: none;
}

.submit-tip {
  text-align: center;
  font-size: 22rpx;
  color: #999;
  margin-top: 15rpx;
  line-height: 1.4;
}
