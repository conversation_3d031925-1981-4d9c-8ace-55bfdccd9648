const app = getApp()
const api = require('../../utils/api.js')
const auth = require('../../utils/auth.js')

Page({
  data: {
    formData: {
      openId: '', // 添加openId字段
      name: '',
      phone: '',
      email: '',
      gender: '',
      age: '',
      idCard: '',
      specialties: [],
      experienceYears: '',
      education: '',
      certifications: [],
      skills: [],
      workAreas: [],
      workTime: '',
      hourlyRate: '',
      serviceFee: '',
      bio: '',
      introduction: '',
      workPhotos: []
    },

    // 选项数据
    genderOptions: [
      { label: '男', value: '男' },
      { label: '女', value: '女' }
    ],

    educationOptions: [
      { label: '高中及以下', value: '高中' },
      { label: '中专/技校', value: '中专' },
      { label: '大专', value: '大专' },
      { label: '本科', value: '本科' },
      { label: '硕士及以上', value: '硕士' }
    ],

    specialtyOptions: [
      { id: 1, name: '充电桩安装', checked: false },
      { id: 2, name: '充电桩维修', checked: false },
      { id: 3, name: '电路检测', checked: false },
      { id: 4, name: '设备调试', checked: false },
      { id: 5, name: '系统升级', checked: false },
      { id: 6, name: '故障排除', checked: false }
    ],

    skillOptions: [
      { id: 1, name: '故障诊断', checked: false },
      { id: 2, name: '电路维修', checked: false },
      { id: 3, name: '设备安装', checked: false },
      { id: 4, name: '系统调试', checked: false },
      { id: 5, name: '预防性维护', checked: false },
      { id: 6, name: '应急抢修', checked: false },
      { id: 7, name: '技术培训', checked: false },
      { id: 8, name: '质量检测', checked: false }
    ],

    workAreaOptions: [
      { id: 1, name: '北京市', checked: false },
      { id: 2, name: '上海市', checked: false },
      { id: 3, name: '广州市', checked: false },
      { id: 4, name: '深圳市', checked: false },
      { id: 5, name: '成都市', checked: false },
      { id: 6, name: '杭州市', checked: false },
      { id: 7, name: '南京市', checked: false },
      { id: 8, name: '武汉市', checked: false }
    ],

    submitting: false
  },

  onLoad: function () {
    console.log('Engineer apply page loaded');

    // 使用认证系统获取用户信息
    this.initUserInfo();
  },

  // 初始化用户信息
  initUserInfo: function() {
    // 检查登录状态
    if (!auth.isLoggedIn()) {
      console.warn('⚠️ 用户未登录，跳转到登录页面');
      tt.showModal({
        title: '需要登录',
        content: '请先登录后再申请成为工程师',
        showCancel: false,
        confirmText: '去登录',
        success: () => {
          tt.navigateTo({
            url: '/pages/login/login'
          });
        }
      });
      return;
    }

    // 获取当前用户的openId
    const openId = auth.getCurrentOpenId();
    const userInfo = auth.getCurrentUserInfo();

    if (openId) {
      this.setData({
        'formData.openId': openId
      });
      console.log('✅ 用户openId已获取:', openId);
      console.log('✅ 用户信息:', userInfo?.nickName);
    } else {
      console.error('❌ 无法获取用户openId');
      tt.showModal({
        title: '获取用户信息失败',
        content: '请重新登录后再试',
        showCancel: false,
        confirmText: '确定',
        success: () => {
          tt.navigateBack();
        }
      });
    }
  },

  // 输入框变化
  onInputChange: function(e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;

    this.setData({
      [`formData.${field}`]: value
    });
  },

  // 性别选择
  onGenderChange: function(e) {
    const value = e.detail.value;
    const gender = this.data.genderOptions[value].value;

    this.setData({
      'formData.gender': gender
    });
  },

  // 学历选择
  onEducationChange: function(e) {
    const value = e.detail.value;
    const education = this.data.educationOptions[value].value;

    this.setData({
      'formData.education': education
    });
  },

  // 专业领域选择
  onSpecialtyChange: function(e) {
    const index = e.currentTarget.dataset.index;
    const specialties = this.data.specialtyOptions;
    specialties[index].checked = !specialties[index].checked;

    const selectedSpecialties = specialties.filter(item => item.checked).map(item => item.name);

    this.setData({
      specialtyOptions: specialties,
      'formData.specialties': selectedSpecialties
    });
  },

  // 技能选择
  onSkillChange: function(e) {
    const index = e.currentTarget.dataset.index;
    const skills = this.data.skillOptions;
    skills[index].checked = !skills[index].checked;

    const selectedSkills = skills.filter(item => item.checked).map(item => item.name);

    this.setData({
      skillOptions: skills,
      'formData.skills': selectedSkills
    });
  },

  // 工作区域选择
  onWorkAreaChange: function(e) {
    const index = e.currentTarget.dataset.index;
    const workAreas = this.data.workAreaOptions;
    workAreas[index].checked = !workAreas[index].checked;

    const selectedAreas = workAreas.filter(item => item.checked).map(item => item.name);

    this.setData({
      workAreaOptions: workAreas,
      'formData.workAreas': selectedAreas
    });
  },

  // 上传资质证书
  uploadCertificates: function() {
    const maxCount = 9 - this.data.formData.certifications.length;
    if (maxCount <= 0) {
      tt.showToast({
        title: '最多只能上传9张证书',
        icon: 'none'
      });
      return;
    }

    tt.chooseImage({
      count: maxCount,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        // 显示上传中提示
        tt.showLoading({
          title: '上传中...'
        });

        // 批量上传图片
        api.uploadImages(res.tempFilePaths).then(uploadResults => {
          tt.hideLoading();

          const successUrls = [];
          const errors = [];

          uploadResults.forEach((result, index) => {
            if (result.success) {
              successUrls.push(result.data.url);
            } else {
              errors.push(`第${index + 1}张图片上传失败`);
            }
          });

          if (successUrls.length > 0) {
            const certificates = [...this.data.formData.certifications, ...successUrls];
            this.setData({
              'formData.certifications': certificates
            });
          }

          if (errors.length > 0) {
            tt.showToast({
              title: `${successUrls.length}张成功，${errors.length}张失败`,
              icon: 'none'
            });
          } else {
            tt.showToast({
              title: '上传成功',
              icon: 'success'
            });
          }
        }).catch(err => {
          tt.hideLoading();
          console.error('上传失败:', err);
          tt.showToast({
            title: '上传失败',
            icon: 'none'
          });
        });
      },
      fail: (err) => {
        console.error('选择图片失败:', err);
        tt.showToast({
          title: '选择图片失败',
          icon: 'none'
        });
      }
    });
  },

  // 删除证书
  deleteCertificate: function(e) {
    const index = e.currentTarget.dataset.index;
    const certificates = this.data.formData.certifications;
    certificates.splice(index, 1);

    this.setData({
      'formData.certifications': certificates
    });
  },

  // 上传工作照片
  uploadWorkPhotos: function() {
    const maxCount = 9 - this.data.formData.workPhotos.length;
    if (maxCount <= 0) {
      tt.showToast({
        title: '最多只能上传9张照片',
        icon: 'none'
      });
      return;
    }

    tt.chooseImage({
      count: maxCount,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        // 显示上传中提示
        tt.showLoading({
          title: '上传中...'
        });

        // 批量上传图片到服务器
        api.uploadImages(res.tempFilePaths).then(uploadResults => {
          tt.hideLoading();

          const successUrls = [];
          const errors = [];

          uploadResults.forEach((result, index) => {
            if (result.success) {
              successUrls.push(result.data.url);
            } else {
              errors.push(`第${index + 1}张图片上传失败`);
            }
          });

          if (successUrls.length > 0) {
            const photos = [...this.data.formData.workPhotos, ...successUrls];
            this.setData({
              'formData.workPhotos': photos
            });
          }

          if (errors.length > 0) {
            tt.showToast({
              title: `${successUrls.length}张成功，${errors.length}张失败`,
              icon: 'none'
            });
          } else {
            tt.showToast({
              title: '上传成功',
              icon: 'success'
            });
          }
        }).catch(err => {
          tt.hideLoading();
          console.error('上传失败:', err);
          tt.showToast({
            title: '上传失败',
            icon: 'none'
          });
        });
      },
      fail: (err) => {
        console.error('选择图片失败:', err);
        tt.showToast({
          title: '选择图片失败',
          icon: 'none'
        });
      }
    });
  },

  // 删除工作照片
  deleteWorkPhoto: function(e) {
    const index = e.currentTarget.dataset.index;
    const photos = this.data.formData.workPhotos;
    photos.splice(index, 1);

    this.setData({
      'formData.workPhotos': photos
    });
  },

  // 预览资质证书
  previewCertification: function(e) {
    e.stopPropagation(); // 阻止事件冒泡
    const src = e.currentTarget.dataset.src;
    const index = e.currentTarget.dataset.index;

    if (src && this.data.formData.certifications.length > 0) {
      tt.previewImage({
        current: src,
        urls: this.data.formData.certifications,
        showmenu: true,
        success: () => {
          console.log(`📸 资质证书${index}预览成功`);
        },
        fail: (err: any) => {
          console.error('❌ 资质证书预览失败:', err);
          tt.showToast({
            title: '预览失败',
            icon: 'none'
          });
        }
      });
    }
  },

  // 预览工作照片
  previewWorkPhoto: function(e) {
    e.stopPropagation(); // 阻止事件冒泡
    const src = e.currentTarget.dataset.src;
    const index = e.currentTarget.dataset.index;

    if (src && this.data.formData.workPhotos.length > 0) {
      tt.previewImage({
        current: src,
        urls: this.data.formData.workPhotos,
        showmenu: true,
        success: () => {
          console.log(`📸 工作照片${index}预览成功`);
        },
        fail: (err: any) => {
          console.error('❌ 工作照片预览失败:', err);
          tt.showToast({
            title: '预览失败',
            icon: 'none'
          });
        }
      });
    }
  },

  // 表单验证
  validateForm: function() {
    const { formData } = this.data;

    if (!formData.name) {
      tt.showToast({ title: '请填写姓名', icon: 'none' });
      return false;
    }

    if (!formData.phone) {
      tt.showToast({ title: '请填写联系电话', icon: 'none' });
      return false;
    }

    if (!/^1[3-9]\d{9}$/.test(formData.phone)) {
      tt.showToast({ title: '请填写正确的手机号', icon: 'none' });
      return false;
    }

    if (!formData.gender) {
      tt.showToast({ title: '请选择性别', icon: 'none' });
      return false;
    }

    if (!formData.age || formData.age < 18 || formData.age > 65) {
      tt.showToast({ title: '请填写正确的年龄(18-65岁)', icon: 'none' });
      return false;
    }

    if (!formData.experienceYears) {
      tt.showToast({ title: '请填写工作经验年数', icon: 'none' });
      return false;
    }

    if (formData.specialties.length === 0) {
      tt.showToast({ title: '请选择至少一个专业领域', icon: 'none' });
      return false;
    }

    if (formData.workAreas.length === 0) {
      tt.showToast({ title: '请选择至少一个工作区域', icon: 'none' });
      return false;
    }

    if (formData.certifications.length === 0) {
      tt.showToast({ title: '请上传至少一个资质证书', icon: 'none' });
      return false;
    }

    return true;
  },

  // 提交申请
  submitApplication: function() {
    if (!this.validateForm()) {
      return;
    }

    this.setData({ submitting: true });

    // 构建提交数据
    const submitData = {
      ...this.data.formData,
      specialties: JSON.stringify(this.data.formData.specialties),
      skills: JSON.stringify(this.data.formData.skills),
      workAreas: JSON.stringify(this.data.formData.workAreas),
      certifications: JSON.stringify(this.data.formData.certifications),
      workPhotos: JSON.stringify(this.data.formData.workPhotos)
    };

    // 调试日志
    console.log('🚀 提交工程师申请数据:', submitData);
    console.log('📝 openId:', submitData.openId);

    // 检查openId是否存在
    if (!submitData.openId) {
      this.setData({ submitting: false });

      // 尝试重新获取openId
      const currentOpenId = auth.getCurrentOpenId();
      if (currentOpenId) {
        submitData.openId = currentOpenId;
        this.setData({
          'formData.openId': currentOpenId
        });
        console.log('✅ 重新获取openId成功:', currentOpenId);
      } else {
        tt.showModal({
          title: '提交失败',
          content: '用户身份验证失败，请重新登录',
          showCancel: false,
          confirmText: '确定',
          success: () => {
            tt.navigateTo({
              url: '/pages/login/login'
            });
          }
        });
        return;
      }
    }

    // 调用API提交申请
    api.submitEngineerApplication(submitData).then(res => {
      this.setData({ submitting: false });

      if (res.success) {
        tt.showModal({
          title: '申请提交成功',
          content: '您的工程师入驻申请已提交，我们将在3-5个工作日内完成审核，请耐心等待。',
          showCancel: false,
          confirmText: '确定',
          success: () => {
            tt.navigateBack();
          }
        });
      } else {
        tt.showModal({
          title: '申请提交失败',
          content: res.message || '提交失败，请稍后重试',
          showCancel: false,
          confirmText: '确定'
        });
      }
    }).catch(err => {
      this.setData({ submitting: false });
      console.error('提交申请失败:', err);

      tt.showModal({
        title: '申请提交失败',
        content: '网络错误，请检查网络连接后重试',
        showCancel: false,
        confirmText: '确定'
      });
    });
  }
})
