.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;
  color: #666;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #1e88e5;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.service-header {
  background-color: #1e88e5;
  color: white;
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.service-title {
  font-size: 36rpx;
  font-weight: 600;
}

.service-status {
  display: flex;
  align-items: center;
  font-size: 28rpx;
}

.status-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  margin-right: 10rpx;
}

.status-dot.online {
  background-color: #4caf50;
}

.status-dot.waiting {
  background-color: #ff9800;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.chat-container {
  flex: 1;
  padding: 30rpx;
  overflow-y: auto;
}

.chat-timeline {
  text-align: center;
  color: #999;
  font-size: 24rpx;
  margin: 20rpx 0;
}

.system-message {
  text-align: center;
  background-color: rgba(0, 0, 0, 0.05);
  color: #666;
  font-size: 24rpx;
  padding: 10rpx 20rpx;
  border-radius: 8rpx;
  margin: 20rpx auto;
  width: fit-content;
  max-width: 80%;
}

.message {
  display: flex;
  margin-bottom: 30rpx;
}

.service-message {
  justify-content: flex-start;
}

.user-message {
  justify-content: flex-end;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: #e0e0e0;
}


.user-message .avatar {
  margin-left: 0;
  margin-right:  50rpx;
}

.message-content {
  max-width: 70%;
  margin: 0 20rpx;
}

.message-bubble {
  padding: 20rpx;
  border-radius: 20rpx;
  font-size: 28rpx;
  word-break: break-all;
}

.service-message .message-bubble {
  background-color: white;
  border: 1px solid #e0e0e0;
  border-top-left-radius: 0;
}

.user-message .message-bubble {
  background-color: #1e88e5;
  color: white;
  border-top-right-radius: 0;
}

/* 时间分隔线 */
.time-divider {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 30rpx 0;
  position: relative;
}

.time-divider::before {
  content: '';
  flex: 1;
  height: 1rpx;
  background: #e0e0e0;
  margin-right: 20rpx;
}

.time-divider::after {
  content: '';
  flex: 1;
  height: 1rpx;
  background: #e0e0e0;
  margin-left: 20rpx;
}

.time-text {
  font-size: 24rpx;
  color: #999;
  background: #f5f5f5;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  white-space: nowrap;
}

/* 消息时间 - 隐藏默认显示 */
.message-time {
  display: none;
}

/* 详细时间 - 按需显示 */
.message-time-detail {
  font-size: 22rpx;
  color: #999;
  text-align: center;
  margin-top: 8rpx;
  opacity: 0.8;
}

.quick-questions {
  height: 60rpx; 
  padding: 20rpx 30rpx;
  white-space: nowrap;
  border-top: 1px solid #e0e0e0;
  background-color: white;
}

.question-item {
  display: inline-block;
  padding: 10rpx 30rpx;
  background-color: #f0f0f0;
  border-radius: 30rpx;
  margin-right: 20rpx;
  font-size: 24rpx;
  color: #333;
}

.input-container {
  padding: 20rpx 30rpx;
  background-color: white;
  border-top: 1px solid #e0e0e0;
}

.input-box {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 40rpx;
  padding: 0 20rpx;
}

.input-box input {
  flex: 1;
  height: 80rpx;
  font-size: 28rpx;
}

.send-btn {
  width: 120rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  border-radius: 30rpx;
  background-color: #e0e0e0;
  color: #999;
  font-size: 28rpx;
}

.send-btn.active {
  background-color: #1e88e5;
  color: white;
}

.input-tools {
  display: flex;
  margin-top: 20rpx;
}

.tool-item {
  width: 60rpx;
  height: 60rpx;
  margin-right: 30rpx;
}

.tool-item image {
  width: 100%;
  height: 100%;
}
