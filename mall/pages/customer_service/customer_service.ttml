<view class="container">
  <!-- 加载状态 -->
  <view tt:if="{{isLoading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text>正在连接客服...</text>
  </view>

  <!-- 客服头部 -->
  <view class="service-header">
    <view class="service-title">充电桩维修客服</view>
    <view class="service-status">
      <view class="status-dot {{isConnected ? 'online' : 'waiting'}}"></view>
      <text>{{isConnected ? '已接入' : '等待接入'}}</text>
    </view>
  </view>
  
  <!-- 聊天区域 -->
  <scroll-view class="chat-container" scroll-y="true" scroll-into-view="{{scrollToMessage}}" scroll-with-animation="true">
    <view class="chat-timeline">今天 {{currentTime}}</view>

    <!-- 系统消息 -->
    <view class="system-message">
      <text>{{isConnected ? '客服已接入，请问有什么可以帮助您？' : '您的咨询已提交，客服将尽快为您服务'}}</text>
    </view>

    <!-- 欢迎消息（仅在没有聊天记录时显示） -->
    <view tt:if="{{!hasStartedChat}}" class="time-divider">
      <text class="time-text">{{currentTime}}</text>
    </view>
    <view tt:if="{{!hasStartedChat}}" class="message service-message">
      <image class="avatar" src="/images/icons/客服.png" mode="aspectFill"></image>
      <view class="message-content">
        <view class="message-bubble">
          您好，我是充电桩维修专员，很高兴为您服务。请问您需要咨询什么问题？
        </view>
      </view>
    </view>

    <!-- 历史消息 -->
    <view tt:for="{{messages}}" tt:key="id" id="msg-{{item.id}}">
      <!-- 时间分隔线 -->
      <view tt:if="{{item.isTimeLabel}}" class="time-divider">
        <text class="time-text">{{item.content}}</text>
      </view>

      <!-- 普通消息 -->
      <view tt:if="{{!item.isTimeLabel}}" class="message {{item.type === 'user' ? 'user-message' : 'service-message'}}"
            bindlongpress="onMessageLongPress" data-message-id="{{item.id}}">
        <image tt:if="{{item.type === 'service'}}" class="avatar" src="{{item.senderAvatar || '/images/icons/客服.png'}}" mode="aspectFill"></image>
        <view class="message-content">
          <view class="message-bubble">{{item.content}}</view>
          <!-- 长按显示详细时间 -->
          <view class="message-time-detail" tt:if="{{item.showDetailTime}}">{{item.time}}</view>
        </view>
        <image tt:if="{{item.type === 'user'}}" class="avatar" src="{{userInfo.avatarUrl || '/images/default-avatar.png'}}" mode="aspectFill"></image>
      </view>
    </view>

  </scroll-view>
  
  <!-- 快捷问题 -->
  <scroll-view class="quick-questions" scroll-x="true" enable-flex="true">
    <view class="question-item" tt:for="{{quickQuestions}}" tt:key="*this" bindtap="sendQuickQuestion" data-question="{{item}}">
      {{item}}
    </view>
  </scroll-view>
  
  <!-- 输入区域 -->
  <view class="input-container">
    <view class="input-box">
      <input type="text" value="{{inputMessage}}" bindinput="onInputChange" placeholder="请输入您的问题" confirm-type="send" bindconfirm="sendMessage" />
      <view class="send-btn {{inputMessage ? 'active' : ''}}" bindtap="sendMessage">发送</view>
    </view>
    <view class="input-tools">
      <view class="tool-item" bindtap="chooseImage">
        <image src="/images/icons/image.png" mode="aspectFit"></image>
      </view>
      <view class="tool-item" bindtap="makePhoneCall">
        <image src="/images/icons/phone.png" mode="aspectFit"></image>
      </view>
    </view>
  </view>
</view>
