const app = getApp()
import { API, UTILS } from '../../utils/config'

Page({
  data: {
    userInfo: null,
    currentTime: '',
    inputMessage: '',
    scrollToMessage: '',
    messages: [],
    sessionId: '',
    isConnected: false,
    isLoading: false,
    hasStartedChat: false,
    quickQuestions: [
      '充电桩无法充电怎么办？',
      '如何预约上门维修？',
      '维修费用是多少？',
      '有哪些常见故障？',
      '保修期是多久？'
    ]
  },

  // 消息轮询定时器
  messageTimer: null,
  
  onLoad: function () {
    // 获取当前时间
    this.updateCurrentTime();

    // 获取用户信息
    this.getUserInfo();

    // 初始化客服服务
    this.initCustomerService();
  },

  onShow: function () {
    // 页面显示时更新时间
    this.updateCurrentTime();

    // 页面显示时刷新消息
    if (this.data.sessionId) {
      this.loadMessages();
    }
  },

  onHide: function () {
    // 页面隐藏时清除定时器
    if (this.messageTimer) {
      clearInterval(this.messageTimer);
      this.messageTimer = null;
    }
  },

  onUnload: function () {
    // 页面卸载时清除定时器
    if (this.messageTimer) {
      clearInterval(this.messageTimer);
      this.messageTimer = null;
    }
  },
  
  // 更新当前时间
  updateCurrentTime: function() {
    const now = new Date();
    const hours = now.getHours().toString().padStart(2, '0');
    const minutes = now.getMinutes().toString().padStart(2, '0');
    
    this.setData({
      currentTime: `${hours}:${minutes}`
    });
  },
  
  // 获取用户信息
  getUserInfo: function() {
    const userInfo = app.globalData.userInfo;

    if (userInfo) {
      this.setData({
        userInfo: userInfo
      });
    } else {
      // 如果没有用户信息，尝试获取
      tt.getUserInfo({
        success: (res) => {
          app.globalData.userInfo = res.userInfo;
          this.setData({
            userInfo: res.userInfo
          });
        }
      });
    }
  },

  // 初始化客服服务
  initCustomerService: function() {
    this.setData({ isLoading: true });

    const openId = tt.getStorageSync('openId');
    if (!openId) {
      tt.showToast({
        title: '请先登录',
        icon: 'none'
      });
      setTimeout(() => {
        tt.navigateBack();
      }, 1500);
      return;
    }

    // 获取或创建会话
    tt.request({
      url: API.CUSTOMER_SERVICE.SESSION,
      method: 'POST',
      data: {
        openId: openId
      },
      success: (res) => {
        console.log('获取会话结果:', res.data);
        if (res.data.success) {
          this.setData({
            sessionId: res.data.data.sessionId,
            isConnected: res.data.data.status === 'active',
            isLoading: false
          });
          this.loadMessages();
          this.startMessagePolling();
        } else {
          tt.showToast({
            title: res.data.message || '连接失败',
            icon: 'none'
          });
          this.setData({ isLoading: false });
        }
      },
      fail: (err) => {
        console.error('连接客服失败:', err);
        tt.showToast({
          title: '连接失败，请稍后重试',
          icon: 'none'
        });
        this.setData({ isLoading: false });
      }
    });
  },

  // 加载消息历史
  loadMessages: function() {
    if (!this.data.sessionId) return;

    tt.request({
      url: `${API.CUSTOMER_SERVICE.MESSAGES}/${this.data.sessionId}`,
      method: 'GET',
      success: (res) => {
        if (res.data.success) {
          const rawMessages = res.data.data;
          const processedMessages = [];

          rawMessages.forEach((msg, index) => {
            const previousMsg = index > 0 ? rawMessages[index - 1] : null;

            // 判断是否需要显示时间分隔线
            if (this.shouldShowTimeLabel(msg, previousMsg)) {
              processedMessages.push({
                id: `time_${msg.id}`,
                type: 'time-label',
                content: this.getTimeLabelText(msg.createdAt),
                isTimeLabel: true
              });
            }

            // 添加消息
            processedMessages.push({
              id: msg.id,
              type: msg.senderType === 'user' ? 'user' : 'service',
              content: msg.content,
              time: this.formatTime(msg.createdAt),
              senderName: msg.senderName,
              senderAvatar: msg.senderAvatar,
              createdAt: msg.createdAt,
              isTimeLabel: false
            });
          });

          this.setData({
            messages: processedMessages,
            hasStartedChat: rawMessages.length > 0
          });

          // 滚动到底部
          this.scrollToBottom();

          // 标记消息为已读
          this.markAsRead();
        }
      },
      fail: (err) => {
        console.error('加载消息失败:', err);
      }
    });
  },
  
  // 输入框内容变化
  onInputChange: function(e) {
    this.setData({
      inputMessage: e.detail.value
    });
  },
  
  // 发送消息
  sendMessage: function() {
    const content = this.data.inputMessage.trim();
    if (!content || !this.data.sessionId) return;

    const openId = tt.getStorageSync('openId');

    tt.request({
      url: API.CUSTOMER_SERVICE.SEND_MESSAGE,
      method: 'POST',
      data: {
        sessionId: this.data.sessionId,
        openId: openId,
        content: content
      },
      success: (res) => {
        if (res.data.success) {
          this.setData({
            inputMessage: '',
            hasStartedChat: true
          });
          this.loadMessages();
        } else {
          tt.showToast({
            title: res.data.message || '发送失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error('发送消息失败:', err);
        tt.showToast({
          title: '发送失败，请稍后重试',
          icon: 'none'
        });
      }
    });
  },
  
  // 发送快捷问题
  sendQuickQuestion: function(e) {
    const content = e.currentTarget.dataset.question;
    this.setData({
      inputMessage: content
    });
    this.sendMessage();
  },

  // 长按消息显示详细时间
  onMessageLongPress: function(e) {
    const messageId = e.currentTarget.dataset.messageId;
    const messages = this.data.messages.map(msg => {
      if (msg.id === messageId && !msg.isTimeLabel) {
        return { ...msg, showDetailTime: !msg.showDetailTime };
      }
      return { ...msg, showDetailTime: false }; // 隐藏其他消息的详细时间
    });

    this.setData({ messages });
  },

  // 标记消息为已读
  markAsRead: function() {
    if (!this.data.sessionId) return;

    tt.request({
      url: API.CUSTOMER_SERVICE.MARK_READ,
      method: 'POST',
      data: {
        sessionId: this.data.sessionId
      },
      success: (res) => {
        // 标记成功，无需特殊处理
      },
      fail: (err) => {
        console.error('标记已读失败:', err);
      }
    });
  },

  // 开始消息轮询
  startMessagePolling: function() {
    // 每5秒检查一次新消息
    this.messageTimer = setInterval(() => {
      this.loadMessages();
    }, 5000);
  },

  // 滚动到底部
  scrollToBottom: function() {
    if (this.data.messages.length > 0) {
      const lastMessageId = this.data.messages[this.data.messages.length - 1].id;
      this.setData({
        scrollToMessage: `msg-${lastMessageId}`
      });
    }
  },

  // 格式化时间 - 智能显示
  formatTime: function(timeStr) {
    const date = new Date(timeStr);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    // 1分钟内显示"刚刚"
    if (diffMinutes < 1) {
      return '刚刚';
    }

    // 1小时内显示"X分钟前"
    if (diffMinutes < 60) {
      return `${diffMinutes}分钟前`;
    }

    // 今天显示时间
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const messageDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());

    if (messageDate.getTime() === today.getTime()) {
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      return `${hours}:${minutes}`;
    }

    // 昨天显示"昨天 HH:mm"
    const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
    if (messageDate.getTime() === yesterday.getTime()) {
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      return `昨天 ${hours}:${minutes}`;
    }

    // 一周内显示"周X HH:mm"
    if (diffDays < 7) {
      const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      return `${weekdays[date.getDay()]} ${hours}:${minutes}`;
    }

    // 超过一周显示完整日期
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    return `${month}-${day} ${hours}:${minutes}`;
  },

  // 判断是否需要显示时间分隔线
  shouldShowTimeLabel: function(currentMsg, previousMsg) {
    if (!previousMsg) return true;

    const currentTime = new Date(currentMsg.createdAt);
    const previousTime = new Date(previousMsg.createdAt);
    const diffMinutes = Math.floor((currentTime.getTime() - previousTime.getTime()) / (1000 * 60));

    // 超过5分钟显示时间分隔
    return diffMinutes >= 5;
  },

  // 获取时间分隔线显示文本
  getTimeLabelText: function(timeStr) {
    const date = new Date(timeStr);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));

    // 1分钟内
    if (diffMinutes < 1) {
      return '刚刚';
    }

    // 1小时内
    if (diffMinutes < 60) {
      return `${diffMinutes}分钟前`;
    }

    // 今天
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const messageDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());

    if (messageDate.getTime() === today.getTime()) {
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      return `今天 ${hours}:${minutes}`;
    }

    // 昨天
    const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
    if (messageDate.getTime() === yesterday.getTime()) {
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      return `昨天 ${hours}:${minutes}`;
    }

    // 其他日期
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    return `${month}月${day}日 ${hours}:${minutes}`;
  },
  
  // 选择图片
  chooseImage: function() {
    tt.chooseImage({
      count: 1,
      success: (res) => {
        tt.showToast({
          title: '图片发送功能开发中',
          icon: 'none'
        });
      }
    });
  },
  
  // 拨打电话
  makePhoneCall: function() {
    tt.makePhoneCall({
      phoneNumber: '************',
      success: () => {
        console.log('拨打电话成功');
      },
      fail: (err) => {
        console.error('拨打电话失败', err);
      }
    });
  }
})
