<view class="container">
  <view class="header">
    <view class="title">已完成</view>
    <view class="subtitle">维修已完成，感谢您的信任</view>
  </view>

  <view class="content">
    <block tt:if="{{orders.length > 0}}">
      <view class="order-list">
        <view class="order-item" tt:for="{{orders}}" tt:key="id" bindtap="goToOrderDetail" data-id="{{item.orderNo}}">
          <view class="order-header">
            <view class="order-no">订单号: {{item.orderNo}}</view>
            <view class="order-status">
              <text class="status-text status-completed">已完成</text>
            </view>
          </view>
          <view class="order-info">
            <view class="info-item">
              <text class="label">故障类型:</text>
              <text class="value">{{faultTypeMap[item.faultType] || '未知故障'}}</text>
            </view>
            <view class="info-item">
              <text class="label">充电桩型号:</text>
              <text class="value">{{item.model}}</text>
            </view>
            <view class="info-item">
              <text class="label">预约时间:</text>
              <text class="value">{{item.appointmentTime}}</text>
            </view>
            <view class="info-item">
              <text class="label">服务地址:</text>
              <text class="value address">{{item.fullAddress}}</text>
            </view>
          </view>
          <view class="engineer-info" tt:if="{{item.engineerName}}">
            <view class="engineer">
              <view class="engineer-avatar-container">
                <image
                  tt:if="{{item.processedEngineerAvatar}}"
                  class="engineer-avatar"
                  src="{{item.processedEngineerAvatar}}"
                  mode="aspectFill"
                  binderror="onEngineerAvatarError"
                  data-index="{{index}}"
                />
                <view tt:else class="avatar-placeholder">
                  <text class="avatar-text">{{item.engineerName.charAt(0)}}</text>
                </view>
              </view>
              <view class="engineer-detail">
                <view class="engineer-name">{{item.engineerName}}</view>
                <view class="engineer-title">充电桩维修工程师</view>
              </view>
            </view>
          </view>

          <!-- 维修详情 -->
          <view class="repair-detail" tt:if="{{item.repairDescription}}">
            <view class="detail-title">
              <text class="title-icon">🔧</text>
              <text class="title-text">维修详情</text>
            </view>
            <view class="detail-content">
              <view class="detail-item">
                <text class="detail-label">维修结果:</text>
                <text class="detail-value">{{getRepairResultText(item.repairResult)}}</text>
              </view>
              <view class="detail-item" tt:if="{{item.repairTime}}">
                <text class="detail-label">维修耗时:</text>
                <text class="detail-value">{{item.repairTime}}小时</text>
              </view>
              <view class="detail-item">
                <text class="detail-label">维修说明:</text>
                <text class="detail-value description">{{item.repairDescription}}</text>
              </view>
            </view>
          </view>

          <!-- 耗材明细 -->
          <view class="materials-detail" tt:if="{{item.materialsDetail && item.materialsDetail.length > 0}}">
            <view class="detail-title">
              <text class="title-icon">📦</text>
              <text class="title-text">耗材明细</text>
            </view>
            <view class="materials-list">
              <view class="material-item" tt:for="{{item.materialsDetail}}" tt:for-item="material" tt:key="index">
                <view class="material-info">
                  <view class="material-name">{{material.name}}</view>
                  <view class="material-spec" tt:if="{{material.specification}}">规格: {{material.specification}}</view>
                </view>
                <view class="material-price">
                  <view class="quantity">×{{material.quantity}}</view>
                  <view class="price">¥{{material.subtotal}}</view>
                </view>
              </view>
            </view>
          </view>

          <!-- 费用明细 -->
          <view class="cost-detail">
            <view class="detail-title">
              <text class="title-icon">💰</text>
              <text class="title-text">费用明细</text>
            </view>
            <view class="cost-list">
              <view class="cost-item" tt:if="{{item.laborFee && item.laborFee > 0}}">
                <text class="cost-label">人工费</text>
                <text class="cost-value">¥{{item.laborFee}}</text>
              </view>
              <view class="cost-item" tt:if="{{item.serviceFee && item.serviceFee > 0}}">
                <text class="cost-label">服务费</text>
                <text class="cost-value">¥{{item.serviceFee}}</text>
              </view>
              <view class="cost-item" tt:if="{{item.materialsFee && item.materialsFee > 0}}">
                <text class="cost-label">耗材费</text>
                <text class="cost-value">¥{{item.materialsFee}}</text>
              </view>
              <view class="cost-item total">
                <text class="cost-label">总费用</text>
                <text class="cost-value total-price">¥{{item.totalFee || '0.00'}}</text>
              </view>
            </view>
          </view>
          <view class="order-footer">
            <view class="time">完成时间: {{item.updatedAtFormatted || item.createdAtFormatted}}</view>
            <view class="actions">
              <button class="btn btn-primary" catchtap="goToRepair">再次预约</button>
            </view>
          </view>
        </view>
      </view>
    </block>
    <view class="empty-state" tt:else>
      <image class="empty-icon" src="/images/icons/empty.png" mode="aspectFit"></image>
      <text class="empty-text">暂无已完成订单</text>
    </view>
  </view>
</view>
