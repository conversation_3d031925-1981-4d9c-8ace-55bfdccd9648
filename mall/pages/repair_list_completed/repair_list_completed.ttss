.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  background-color: #8c8c8c;
  padding: 20px 15px;
  color: #fff;
}

.title {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 5px;
}

.subtitle {
  font-size: 14px;
  opacity: 0.8;
}

.content {
  flex: 1;
  padding: 15px;
}

.order-list {
  margin-bottom: 20px;
}

.order-item {
  background-color: #fff;
  border-radius: 8px;
  margin-bottom: 15px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.order-no {
  font-size: 14px;
  color: #666;
}

.order-status {
  font-size: 14px;
}

.status-text {
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.status-completed {
  background-color: #f9f9f9;
  color: #8c8c8c;
}

.order-info {
  padding: 10px 0;
}

.info-item {
  display: flex;
  margin-bottom: 8px;
}

.label {
  width: 80px;
  color: #999;
  font-size: 13px;
}

.value {
  flex: 1;
  color: #333;
  font-size: 13px;
}

.address {
  word-break: break-all;
}

.engineer-info {
  padding: 10px 0;
  border-top: 1px solid #eee;
}

.engineer {
  display: flex;
  align-items: center;
}

.engineer-avatar-container {
  width: 40px;
  height: 40px;
  margin-right: 10px;
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
}

.engineer-avatar {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-text {
  color: white;
  font-size: 14px;
  font-weight: 600;
}

.engineer-detail {
  flex: 1;
}

.engineer-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 3px;
}

.engineer-title {
  font-size: 12px;
  color: #999;
}

.order-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 10px;
  border-top: 1px solid #eee;
}

.time {
  font-size: 12px;
  color: #999;
}

.actions {
  display: flex;
}

.btn {
  margin-left: 10px;
  font-size: 12px;
  padding: 4px 12px;
  border-radius: 4px;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  border: none;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: translateY(-1px);
}

.btn-default {
  background-color: #fff;
  color: #666;
  border: 1px solid #d9d9d9;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px 0;
}

.empty-icon {
  width: 100px;
  height: 100px;
  margin-bottom: 15px;
}

.empty-text {
  color: #999;
  font-size: 14px;
  margin-bottom: 20px;
}

/* 维修详情样式 */
.repair-detail, .materials-detail, .cost-detail {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #eee;
}

.detail-title {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.title-icon {
  font-size: 16px;
  margin-right: 8px;
}

.title-text {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.detail-content {
  background-color: #f8f9fa;
  border-radius: 6px;
  padding: 12px;
}

.detail-item {
  display: flex;
  margin-bottom: 8px;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-label {
  width: 80px;
  color: #666;
  font-size: 13px;
  flex-shrink: 0;
}

.detail-value {
  flex: 1;
  color: #333;
  font-size: 13px;
}

.detail-value.description {
  line-height: 1.4;
  word-break: break-all;
}

/* 耗材明细样式 */
.materials-list {
  background-color: #f8f9fa;
  border-radius: 6px;
  padding: 8px;
}

.material-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #e9ecef;
}

.material-item:last-child {
  border-bottom: none;
}

.material-info {
  flex: 1;
}

.material-name {
  font-size: 13px;
  color: #333;
  font-weight: 500;
  margin-bottom: 2px;
}

.material-spec {
  font-size: 12px;
  color: #666;
}

.material-price {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.quantity {
  font-size: 12px;
  color: #666;
  margin-bottom: 2px;
}

.price {
  font-size: 13px;
  color: #ff4d4f;
  font-weight: 500;
}

/* 费用明细样式 */
.cost-list {
  background-color: #f8f9fa;
  border-radius: 6px;
  padding: 12px;
}

.cost-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.cost-item:last-child {
  margin-bottom: 0;
}

.cost-item.total {
  padding-top: 8px;
  border-top: 1px solid #e9ecef;
  margin-top: 8px;
}

.cost-label {
  font-size: 13px;
  color: #666;
}

.cost-value {
  font-size: 13px;
  color: #333;
  font-weight: 500;
}

.cost-item.total .cost-label {
  font-weight: 500;
  color: #333;
}

.total-price {
  font-size: 16px;
  color: #ff4d4f;
  font-weight: 600;
}
