const app = getApp()

Page({
  data: {
    orders: [],
    openId: '',
    faultTypeMap: {
      'no_charging': '无法充电',
      'slow_charging': '充电慢',
      'error_code': '报错代码',
      'port_damage': '接口损坏',
      'not_starting': '无法启动',
      'overheating': '过热',
      'display_issue': '显示故障',
      'other': '其他故障'
    }
  },

  onLoad: function () {
    console.log('Repair list completed page loaded');

    // 获取openId
    this.getOpenId();
  },

  onShow: function () {
    // 如果已经有openId，则获取订单列表
    if (this.data.openId) {
      this.getOrderList();
    }
  },

  // 获取openId
  getOpenId: function() {
    // 从全局数据中获取openId
    if (app.globalData.openId) {
      this.setData({
        openId: app.globalData.openId
      });
      this.getOrderList();
    } else {
      // 从本地存储中获取
      tt.getStorage({
        key: 'userInfo',
        success: (res) => {
          if (res.data && res.data.openId) {
            this.setData({
              openId: res.data.openId
            });
            this.getOrderList();
          } else {
            // 尝试从openId存储中获取
            tt.getStorage({
              key: 'openId',
              success: (res) => {
                if (res.data) {
                  this.setData({
                    openId: res.data
                  });
                  this.getOrderList();
                } else {
                  this.showLoginTip();
                }
              },
              fail: () => {
                this.showLoginTip();
              }
            });
          }
        },
        fail: () => {
          // 尝试从openId存储中获取
          tt.getStorage({
            key: 'openId',
            success: (res) => {
              if (res.data) {
                this.setData({
                  openId: res.data
                });
                this.getOrderList();
              } else {
                this.showLoginTip();
              }
            },
            fail: () => {
              this.showLoginTip();
            }
          });
        }
      });
    }
  },

  // 显示登录提示
  showLoginTip: function() {
    // 如果没有openId，提示用户登录
    tt.showToast({
      title: '请先登录',
      icon: 'none'
    });

    // 跳转到用户中心页面
    tt.switchTab({
      url: '/pages/user_center/user_center'
    });
  },

  // 获取订单列表
  getOrderList: function() {
    if (!this.data.openId) {
      console.error('获取订单列表失败：openId为空');
      return;
    }

    tt.showLoading({
      title: '加载中...'
    });

    const api = require('../../utils/api');
    api.getRepairOrderList(this.data.openId, 'completed').then(res => {
      tt.hideLoading();

      if (res.success) {
        // 格式化创建时间和更新时间，处理新增字段
        const orders = res.orders.map(order => {
          if (order.createdAt) {
            const date = new Date(order.createdAt);
            order.createdAtFormatted = `${date.getFullYear()}-${this.padZero(date.getMonth() + 1)}-${this.padZero(date.getDate())} ${this.padZero(date.getHours())}:${this.padZero(date.getMinutes())}`;
          } else {
            order.createdAtFormatted = '未知';
          }

          if (order.updatedAt) {
            const date = new Date(order.updatedAt);
            order.updatedAtFormatted = `${date.getFullYear()}-${this.padZero(date.getMonth() + 1)}-${this.padZero(date.getDate())} ${this.padZero(date.getHours())}:${this.padZero(date.getMinutes())}`;
          }

          // 处理耗材明细JSON字符串
          if (order.materialsDetail && typeof order.materialsDetail === 'string') {
            try {
              order.materialsDetail = JSON.parse(order.materialsDetail);
            } catch (e) {
              console.error('解析耗材明细失败:', e);
              order.materialsDetail = [];
            }
          } else if (!order.materialsDetail) {
            order.materialsDetail = [];
          }

          // 处理工程师头像URL
          if (order.engineerAvatar) {
            order.processedEngineerAvatar = this.processAvatarUrl(order.engineerAvatar);
          } else {
            order.processedEngineerAvatar = '';
          }

          return order;
        });

        this.setData({
          orders: orders
        });
      } else {
        console.error('获取订单列表失败:', res.message);
        tt.showToast({
          title: '获取订单列表失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      tt.hideLoading();
      console.error('获取订单列表请求失败:', err);
      tt.showToast({
        title: '获取订单列表失败',
        icon: 'none'
      });
    });
  },

  // 跳转到订单详情
  goToOrderDetail: function(e) {
    const id = e.currentTarget.dataset.id;

    tt.navigateTo({
      url: `/pages/repair_detail/repair_detail?id=${id}`
    });
  },

  // 去预约维修
  goToRepair: function() {
    tt.navigateTo({
      url: '/pages/repair_form/repair_form'
    });
  },

  // 数字补零
  padZero: function(num) {
    return num < 10 ? '0' + num : num;
  },

  // 获取维修结果文本
  getRepairResultText: function(result) {
    const resultMap = {
      'success': '维修成功',
      'partial': '部分修复',
      'failed': '维修失败',
      'replacement': '需要更换设备'
    };
    return resultMap[result] || '未知';
  },

    // 处理头像URL
  processAvatarUrl: function(avatarUrl) {
    // 使用统一的API工具函数
    const api = require('../../utils/api.js');
    return api.processAvatarUrl(avatarUrl);
  },

  // 工程师头像加载失败处理
  onEngineerAvatarError: function(e) {
    const index = e.currentTarget.dataset.index;
    console.log(`工程师头像加载失败，订单索引: ${index}`);

    // 将失败的头像URL设置为空，显示默认占位符
    const orders = this.data.orders;
    if (orders[index]) {
      orders[index].processedEngineerAvatar = '';
      this.setData({
        orders: orders
      });
    }
  }
})
