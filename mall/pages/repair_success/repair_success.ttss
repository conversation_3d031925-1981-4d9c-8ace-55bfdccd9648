.container {
  padding: 30rpx;
}

.success-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 0;
  background-color: white;
  border-radius: 24rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.success-icon {
  width: 160rpx;
  height: 160rpx;
  background-color: #e3f2fd;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 32rpx;
  color: #1e88e5;
  font-size: 80rpx;
}

.success-title {
  font-size: 40rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
}

.success-message {
  font-size: 28rpx;
  color: #757575;
  margin-bottom: 48rpx;
  text-align: center;
  padding: 0 40rpx;
}

.repair-info {
  width: 100%;
  padding: 0 32rpx;
  box-sizing: border-box;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24rpx;
  font-size: 28rpx;
}

.info-label {
  color: #757575;
}

.info-value {
  font-weight: 500;
}

.action-buttons {
  display: flex;
  gap: 32rpx;
  margin-top: 48rpx;
}

.action-btn {
  width: 300rpx;
  padding: 24rpx 0;
  text-align: center;
  border-radius: 48rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.primary-btn {
  background-color: #1e88e5;
  color: white;
}

.secondary-btn {
  background-color: #f5f5f5;
  color: #333333;
}

/* 工程师推荐区域 */
.engineer-section {
  background-color: white;
  border-radius: 24rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 32rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 12rpx;
  font-size: 30rpx;
  font-weight: 600;
}

.priority-badge {
  background: rgba(255, 255, 255, 0.2);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  backdrop-filter: blur(10rpx);
}

.engineer-card {
  display: flex;
  align-items: flex-start;
  padding: 32rpx;
  gap: 24rpx;
}

.engineer-avatar-container {
  position: relative;
}

.engineer-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  object-fit: cover;
  border: 4rpx solid #f0f0f0;
}

.engineer-avatar-placeholder {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 4rpx solid #f0f0f0;
}

.avatar-text {
  color: white;
  font-size: 48rpx;
  font-weight: 600;
}

.online-status {
  position: absolute;
  bottom: 8rpx;
  right: 8rpx;
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  border: 3rpx solid white;
}

.online-status.online {
  background-color: #4caf50;
}

.online-status.offline {
  background-color: #9e9e9e;
}

.engineer-info {
  flex: 1;
  min-width: 0;
}

.engineer-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 8rpx;
}

.engineer-title {
  font-size: 26rpx;
  color: #7f8c8d;
  margin-bottom: 6rpx;
}

.engineer-experience {
  font-size: 24rpx;
  color: #27ae60;
  margin-bottom: 12rpx;
  font-weight: 500;
}

.engineer-rating {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 16rpx;
}

.rating-stars {
  display: flex;
  gap: 4rpx;
}

.rating-stars .iconfont {
  color: #ffc107;
  font-size: 24rpx;
}

.rating-text {
  font-size: 24rpx;
  color: #666;
}

.engineer-specialties {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  margin-bottom: 16rpx;
}

.specialty-tag {
  background: #e3f2fd;
  color: #1976d2;
  padding: 6rpx 12rpx;
  border-radius: 16rpx;
  font-size: 20rpx;
  font-weight: 500;
}

.engineer-contact {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 100rpx;
}

.contact-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 16rpx 20rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
  font-weight: 500;
  text-align: center;
  transition: all 0.3s ease;
  height: 80rpx;
  width: 150rpx;
  box-sizing: border-box;
  gap: 6rpx;
}

.contact-btn .iconfont {
  font-size: 36rpx;
  line-height: 36rpx;
  height: 36rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0;
  padding: 0;
}

.contact-btn text {
  font-size: 22rpx;
  line-height: 22rpx;
  height: 22rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  margin: 0;
  padding: 0;
}

.call-btn {
  background: linear-gradient(135deg, #1e88e5 0%, #1565c0 100%);
  color: white;
  box-shadow: 0 4rpx 12rpx rgba(30, 136, 229, 0.3);
}

.call-btn .iconfont,
.call-btn text {
  color: white;
}

.contact-btn:active {
  transform: scale(0.95);
}

.call-btn:active {
  background: linear-gradient(135deg, #1565c0 0%, #0d47a1 100%);
  box-shadow: 0 2rpx 8rpx rgba(30, 136, 229, 0.4);
}

/* 加载和空状态 */
.engineer-loading,
.no-engineer {
  padding: 60rpx 32rpx;
  text-align: center;
}

.loading-text,
.no-engineer-text {
  font-size: 28rpx;
  color: #7f8c8d;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  margin: 48rpx 0 32rpx;
}

.mb-2 {
  margin-bottom: 16rpx;
}

.mt-3 {
  margin-top: 24rpx;
}
