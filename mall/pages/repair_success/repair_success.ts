const app = getApp()
const api = require('../../utils/api')

Page({
  data: {
    appointmentId: '',
    faultTypeName: '',
    appointmentTime: '',
    address: '',
    openId: '', // 用户openId
    orderStatus: 'pending', // 订单状态
    isLoadingEngineer: true, // 工程师加载状态
    engineer: {}, // 推荐的工程师信息
    recommendProducts: []
  },

  onLoad: function (options) {
    console.log('Repair success page loaded');

    // 获取预约ID
    const appointmentId = options.id || '';

    this.setData({
      appointmentId: appointmentId
    });

    // 获取openId
    this.getOpenId();

    // 加载推荐工程师
    this.loadRecommendedEngineer();

    // 加载推荐产品
    this.loadRecommendProducts();
  },

  // 获取openId
  getOpenId: function() {
    // 从全局数据中获取openId
    if (app.globalData.openId) {
      this.setData({
        openId: app.globalData.openId
      });
      this.getAppointmentInfo(this.data.appointmentId);
    } else {
      // 从本地存储中获取
      tt.getStorage({
        key: 'userInfo',
        success: (res) => {
          if (res.data && res.data.openId) {
            this.setData({
              openId: res.data.openId
            });
            this.getAppointmentInfo(this.data.appointmentId);
          } else {
            // 尝试从openId存储中获取
            tt.getStorage({
              key: 'openId',
              success: (res) => {
                if (res.data) {
                  this.setData({
                    openId: res.data
                  });
                  this.getAppointmentInfo(this.data.appointmentId);
                } else {
                  this.showLoginTip();
                }
              },
              fail: () => {
                this.showLoginTip();
              }
            });
          }
        },
        fail: () => {
          // 尝试从openId存储中获取
          tt.getStorage({
            key: 'openId',
            success: (res) => {
              if (res.data) {
                this.setData({
                  openId: res.data
                });
                this.getAppointmentInfo(this.data.appointmentId);
              } else {
                this.showLoginTip();
              }
            },
            fail: () => {
              this.showLoginTip();
            }
          });
        }
      });
    }
  },
  
  // 显示登录提示
  showLoginTip: function() {
    // 如果没有openId，提示用户登录
    tt.showToast({
      title: '请先登录',
      icon: 'none'
    });
    
    // 跳转到用户中心页面
    tt.switchTab({
      url: '/pages/user_center/user_center'
    });
  },

  // 加载推荐工程师
  loadRecommendedEngineer: function() {
    this.setData({ isLoadingEngineer: true });

    // 获取可用的工程师列表（按优先级排序）
    api.getAvailableEngineers()
      .then(res => {
        if (res.success && res.data && res.data.length > 0) {
          // 获取优先级最高的工程师（第一个）
          const topEngineer = res.data[0];

          // 处理工程师数据
          const engineer = this.processEngineerData(topEngineer);

          this.setData({
            engineer: engineer,
            isLoadingEngineer: false
          });
        } else {
          // 如果没有可用工程师，使用默认数据
          this.setDefaultEngineer();
        }
      })
      .catch(err => {
        console.error('获取工程师信息失败:', err);
        this.setDefaultEngineer();
      });
  },

  // 处理工程师数据
  processEngineerData: function(engineerData) {
    // 处理专业领域
    let specialtyList = [];
    if (engineerData.specialties) {
      try {
        specialtyList = typeof engineerData.specialties === 'string'
          ? JSON.parse(engineerData.specialties)
          : engineerData.specialties;
      } catch (e) {
        specialtyList = engineerData.specialties.split(',').map(s => s.trim());
      }
    }

    // 生成评分星星数组
    const rating = parseFloat(engineerData.rating || 5.0);
    const ratingStars = Array(Math.floor(rating)).fill(1);

    // 处理头像URL
    const processedAvatar = this.processAvatarUrl(engineerData.avatar);

    return {
      id: engineerData.id,
      name: engineerData.name,
      avatar: engineerData.avatar,
      processedAvatar: processedAvatar,
      title: engineerData.title || '充电桩维修专家',
      experience: `${engineerData.experienceYears || engineerData.experience || 5}年`,
      rating: rating.toFixed(1),
      totalOrders: engineerData.totalOrders || 0,
      completedOrders: engineerData.completedOrders || 0,
      phone: engineerData.phone,
      isOnline: engineerData.isOnline || false,
      specialties: engineerData.specialties,
      specialtyList: specialtyList.slice(0, 3), // 最多显示3个专业
      ratingStars: ratingStars
    };
  },

    // 处理头像URL
  processAvatarUrl: function(avatarUrl) {
    // 使用统一的API工具函数
    const api = require('../../utils/api.js');
    return api.processAvatarUrl(avatarUrl);
  },

  // 设置默认工程师
  setDefaultEngineer: function() {
    const defaultAvatar = '/images/default-engineer.png';
    this.setData({
      engineer: {
        id: 'default',
        name: '王工程师',
        avatar: defaultAvatar,
        processedAvatar: defaultAvatar,
        title: '充电桩高级技师',
        experience: '8年',
        rating: '5.0',
        totalOrders: 126,
        completedOrders: 120,
        phone: '13812345678',
        isOnline: true,
        specialtyList: ['充电桩维修', '故障诊断', '设备安装'],
        ratingStars: [1, 1, 1, 1, 1]
      },
      isLoadingEngineer: false
    });
  },

  // 加载推荐产品
  loadRecommendProducts: function() {
    console.log('🛍️ 开始加载推荐产品...');

    api.getHotProducts(2).then(res => {
      console.log('📦 推荐产品API响应:', res);

      if (res.success && res.data && res.data.length > 0) {
        // 处理产品数据，确保图片URL正确，并限制只显示前2个
        const recommendProducts = res.data.slice(0, 2).map(product => {
          return {
            id: product.id,
            name: product.name || product.shortName || '商品名称',
            price: product.price || 0,
            image: this.processProductImageUrl(product.mainImage)
          };
        });

        this.setData({
          recommendProducts: recommendProducts
        });

        console.log('✅ 推荐产品加载成功，数量:', recommendProducts.length);
        console.log('🖼️ 推荐产品数据:', recommendProducts);
        console.log('📊 原始数据数量:', res.data.length, '显示数量:', recommendProducts.length);
      } else {
        console.warn('⚠️ 没有获取到推荐产品数据，使用默认数据');
        // 如果没有数据，使用默认数据
        this.setDefaultRecommendProducts();
      }
    }).catch(err => {
      console.error('🚨 加载推荐产品失败:', err);
      // 出错时使用默认数据
      this.setDefaultRecommendProducts();
    });
  },

  // 设置默认推荐产品
  setDefaultRecommendProducts: function() {
    this.setData({
      recommendProducts: [
        {
          id: 1,
          name: '快充Type-C充电枪',
          price: 299.00,
          image: '/images/products/快充充电枪.png'
        },
        {
          id: 2,
          name: '5米加长型充电线缆',
          price: 199.00,
          image: '/images/products/充电线缆.png'
        }
      ]
    });
  },

  // 处理产品图片URL
  processProductImageUrl: function(imageUrl) {
    if (!imageUrl || imageUrl.trim() === '') {
      return '/images/products/充电线缆.png'; // 默认图片
    }

    // 如果已经是完整的HTTP/HTTPS URL，直接返回
    if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
      return imageUrl;
    }

    // 如果是相对路径，添加服务器地址
    if (imageUrl.startsWith('/uploads/')) {
      return `https://localhost:8443${imageUrl}`;
    }

    // 如果只是文件名，添加完整路径
    if (!imageUrl.startsWith('/')) {
      return `https://localhost:8443/uploads/${imageUrl}`;
    }

    // 其他情况，添加服务器地址
    return `https://localhost:8443${imageUrl}`;
  },

  // 获取预约信息
  getAppointmentInfo: function(appointmentId) {
    if (!appointmentId || !this.data.openId) {
      return;
    }

    tt.showLoading({
      title: '加载中...'
    });

    // 调用API获取维修订单详情
    const api = require('../../utils/api');
    api.getRepairOrderDetail(appointmentId, this.data.openId).then(res => {
      tt.hideLoading();
      
      if (res.success && res.order) {
        const order = res.order;
        
        // 获取故障类型名称
        const faultType = order.faultType;
        const faultTypes = [
          { type: 'no_charging', name: '无法充电' },
          { type: 'slow_charging', name: '充电慢' },
          { type: 'error_code', name: '报错代码' },
          { type: 'port_damage', name: '接口损坏' },
          { type: 'not_starting', name: '无法启动' },
          { type: 'overheating', name: '过热' },
          { type: 'display_issue', name: '显示故障' },
          { type: 'other', name: '其他故障' }
        ];

        const faultTypeObj = faultTypes.find(item => item.type === faultType);
        const faultTypeName = faultTypeObj ? faultTypeObj.name : '未知故障';

        // 拼接预约时间
        const appointmentTime = order.appointmentTime;

        this.setData({
          faultTypeName: faultTypeName,
          appointmentTime: appointmentTime,
          address: order.fullAddress, // 使用fullAddress字段
          orderStatus: order.status // 设置订单状态
        });
      } else {
        console.error('获取维修订单详情失败:', res.message);
        
        // 从全局数据中获取最近的预约
        const lastAppointment = app.globalData.lastAppointment;

        if (lastAppointment && lastAppointment.id === appointmentId) {
          // 获取故障类型名称
          const faultType = lastAppointment.faultType;
          const faultTypes = [
            { type: 'no_charging', name: '无法充电' },
            { type: 'slow_charging', name: '充电慢' },
            { type: 'error_code', name: '报错代码' },
            { type: 'port_damage', name: '接口损坏' },
            { type: 'not_starting', name: '无法启动' },
            { type: 'overheating', name: '过热' },
            { type: 'display_issue', name: '显示故障' },
            { type: 'other', name: '其他故障' }
          ];

          const faultTypeObj = faultTypes.find(item => item.type === faultType);
          const faultTypeName = faultTypeObj ? faultTypeObj.name : '未知故障';

          // 使用fullAddress字段
          const address = lastAppointment.fullAddress || '';

          // 拼接预约时间
          const appointmentTime = lastAppointment.appointmentDate + ' ' + lastAppointment.appointmentTime;

          this.setData({
            faultTypeName: faultTypeName,
            appointmentTime: appointmentTime,
            address: address,
            orderStatus: lastAppointment.status || 'pending'
          });
        } else {
          // 如果没有找到预约，使用默认值
          this.setData({
            faultTypeName: '充电桩无法启动',
            appointmentTime: '2023-05-18 14:00-15:00',
            address: '上海市浦东新区张江高科技园区博云路2号',
            orderStatus: 'pending'
          });
        }
      }
    }).catch(err => {
      tt.hideLoading();
      console.error('获取维修订单详情请求失败:', err);
      
      // 从全局数据中获取最近的预约
      const lastAppointment = app.globalData.lastAppointment;

      if (lastAppointment && lastAppointment.id === appointmentId) {
        // 获取故障类型名称
        const faultType = lastAppointment.faultType;
        const faultTypes = [
          { type: 'no_charging', name: '无法充电' },
          { type: 'slow_charging', name: '充电慢' },
          { type: 'error_code', name: '报错代码' },
          { type: 'port_damage', name: '接口损坏' },
          { type: 'not_starting', name: '无法启动' },
          { type: 'overheating', name: '过热' },
          { type: 'display_issue', name: '显示故障' },
          { type: 'other', name: '其他故障' }
        ];

        const faultTypeObj = faultTypes.find(item => item.type === faultType);
        const faultTypeName = faultTypeObj ? faultTypeObj.name : '未知故障';

        // 使用fullAddress字段
        const address = lastAppointment.fullAddress || '';

        // 拼接预约时间
        const appointmentTime = lastAppointment.appointmentDate + ' ' + lastAppointment.appointmentTime;

        this.setData({
          faultTypeName: faultTypeName,
          appointmentTime: appointmentTime,
          address: address,
          orderStatus: lastAppointment.status || 'pending'
        });
      } else {
        // 如果没有找到预约，使用默认值
        this.setData({
          faultTypeName: '充电桩无法启动',
          appointmentTime: '2023-05-18 14:00-15:00',
          address: '上海市浦东新区张江高科技园区博云路2号',
          orderStatus: 'pending'
        });
      }
    });
  },

  // 返回首页
  goToHome: function() {
    // 使用reLaunch方法跳转到首页，并关闭所有其他页面
    tt.reLaunch({
      url: '/pages/index/index',
      success: () => {
        console.log('跳转到首页成功');
      },
      fail: (err) => {
        console.error('跳转到首页失败:', err);
      }
    });
  },

  // 跳转到订单详情
  goToOrderDetail: function() {
    if (!this.data.appointmentId) {
      tt.showToast({
        title: '订单信息不完整',
        icon: 'none'
      });
      return;
    }

    tt.navigateTo({
      url: `/pages/repair_detail/repair_detail?id=${this.data.appointmentId}`,
      success: () => {
        console.log('跳转到订单详情成功');
      },
      fail: (err) => {
        console.error('跳转到订单详情失败:', err);
        tt.showToast({
          title: '跳转失败，请重试',
          icon: 'none'
        });
      }
    });
  },

  // 拨打工程师电话
  callEngineer: function(e) {
    const phone = e.currentTarget.dataset.phone || this.data.engineer.phone;

    if (!phone) {
      tt.showToast({
        title: '工程师电话不可用',
        icon: 'none'
      });
      return;
    }

    tt.makePhoneCall({
      phoneNumber: phone,
      success: () => {
        console.log('拨打电话成功');
      },
      fail: (err) => {
        console.error('拨打电话失败:', err);
        tt.showToast({
          title: '拨打失败，请重试',
          icon: 'none'
        });
      }
    });
  },

  // 发送消息给工程师（暂时注释，如需要可恢复）
  /*
  messageEngineer: function(e) {
    const engineer = e.currentTarget.dataset.engineer || this.data.engineer;

    if (!engineer.id) {
      tt.showToast({
        title: '工程师信息不可用',
        icon: 'none'
      });
      return;
    }

    // 跳转到客服页面，传递工程师信息
    tt.navigateTo({
      url: `/pages/customer_service/customer_service?engineerId=${engineer.id}&engineerName=${engineer.name}`,
      success: () => {
        console.log('跳转到消息页面成功');
      },
      fail: (err) => {
        console.error('跳转到消息页面失败:', err);
        tt.showToast({
          title: '消息功能暂不可用',
          icon: 'none'
        });
      }
    });
  },
  */

  // 跳转到商品详情页
  goToProductDetail: function(e) {
    const id = e.currentTarget.dataset.id;
    tt.navigateTo({
      url: `/pages/product_detail/product_detail?id=${id}`
    });
  },

  // 头像加载失败处理
  onAvatarError: function(e) {
    console.log('工程师头像加载失败');

    // 将头像设置为空，显示默认占位符
    const engineer = this.data.engineer;
    engineer.processedAvatar = '';

    this.setData({
      engineer: engineer
    });
  }
})
