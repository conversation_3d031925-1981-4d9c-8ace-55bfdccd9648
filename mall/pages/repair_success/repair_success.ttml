<view class="container">
  <!-- 成功信息 -->
  <view class="success-container">
    <view class="success-icon">
      <image src="/images/icons/成功.png" mode="aspectFit" style="width: 100rpx; height: 100rpx;"></image>
    </view>
    <view class="success-title">预约成功</view>
    <view class="success-message">您的维修预约已成功提交，工程师将按照预约时间上门服务</view>
    
    <view class="repair-info">
      <view class="info-item">
        <view class="info-label">预约编号</view>
        <view class="info-value">{{appointmentId}}</view>
      </view>
      <view class="info-item">
        <view class="info-label">故障类型</view>
        <view class="info-value">{{faultTypeName}}</view>
      </view>
      <view class="info-item">
        <view class="info-label">预约时间</view>
        <view class="info-value">{{appointmentTime}}</view>
      </view>
      <view class="info-item">
        <view class="info-label">上门地址</view>
        <view class="info-value">{{address}}</view>
      </view>
    </view>
    
    <view class="action-buttons">
      <view class="action-btn secondary-btn" bindtap="goToHome">返回首页</view>
      <view class="action-btn primary-btn" bindtap="goToOrderDetail">查看订单详情</view>
    </view>
  </view>
  
  <!-- 推荐工程师信息 -->
  <view class="engineer-section">
    <view class="section-header">
      <view class="section-title">
        <text class="iconfont icon-user"></text>
        <text>为您推荐优质工程师</text>
      </view>
      <view class="priority-badge">优先级最高</view>
    </view>

    <view class="engineer-card" tt:if="{{engineer.id}}">
      <view class="engineer-avatar-container">
        <image
          tt:if="{{engineer.processedAvatar}}"
          src="{{engineer.processedAvatar}}"
          class="engineer-avatar"
          mode="aspectFill"
          binderror="onAvatarError"
        />
        <view tt:else class="engineer-avatar-placeholder">
          <text class="avatar-text">{{engineer.name.charAt(0)}}</text>
        </view>
        <view class="online-status {{engineer.isOnline ? 'online' : 'offline'}}"></view>
      </view>
      <view class="engineer-info">
        <view class="engineer-name">{{engineer.name}}</view>
        <view class="engineer-title">{{engineer.title || '充电桩维修专家'}}</view>
        <view class="engineer-experience">{{engineer.experience || '5年+'}}经验</view>
        <view class="engineer-rating">
          <view class="rating-stars">
            <text class="iconfont icon-star-filled" tt:for="{{engineer.ratingStars}}" tt:key="*this"></text>
          </view>
          <text class="rating-text">{{engineer.rating || '5.0'}} ({{engineer.totalOrders || 0}}单)</text>
        </view>
        <view class="engineer-specialties" tt:if="{{engineer.specialties}}">
          <text class="specialty-tag" tt:for="{{engineer.specialtyList}}" tt:key="*this">{{item}}</text>
        </view>
      </view>
      <view class="engineer-contact">
        <view class="contact-btn call-btn" bindtap="callEngineer" data-phone="{{engineer.phone}}">
          <text>电话</text>
        </view>
      </view>
    </view>

    <!-- 工程师加载中状态 -->
    <view class="engineer-loading" tt:if="{{!engineer.id && isLoadingEngineer}}">
      <view class="loading-text">正在为您匹配最优工程师...</view>
    </view>

    <!-- 无工程师状态 -->
    <view class="no-engineer" tt:if="{{!engineer.id && !isLoadingEngineer}}">
      <view class="no-engineer-text">暂无可用工程师，客服将为您安排</view>
    </view>
  </view>
  
  <!-- 维修小贴士 -->
  <view class="card">
    <view class="text-sm font-bold mb-2">维修小贴士</view>
    <view class="text-sm">1. 请确保预约时间有人在家，以便工程师能够顺利上门服务</view>
    <view class="text-sm">2. 如需更改预约时间，请提前2小时联系客服或工程师</view>
    <view class="text-sm">3. 工程师上门前会提前联系您确认地址和时间</view>
    <view class="text-sm">4. 维修完成后，请对工程师的服务进行评价，您的反馈对我们很重要</view>
  </view>
  
  <!-- 相关配件推荐 -->
  <view class="section-title mt-3">
    <text>相关配件推荐</text>
  </view>
  
  <view class="grid">
    <view class="product-card" tt:for="{{recommendProducts}}" tt:key="id" bindtap="goToProductDetail" data-id="{{item.id}}">
      <image src="{{item.image}}" mode="aspectFill" />
      <view class="info">
        <view class="title">{{item.name}}</view>
        <view class="price">¥{{item.price}}</view>
      </view>
    </view>
  </view>
</view>
