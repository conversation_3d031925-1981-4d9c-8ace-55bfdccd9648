.container {
  padding: 30rpx 30rpx 120rpx;
}

.cart-item {
  display: flex;
  padding: 32rpx;
  background-color: white;
  border-radius: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  position: relative;
}

.cart-item-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 16rpx;
  object-fit: cover;
  margin: 0 24rpx;
}

.cart-item-info {
  flex: 1;
}

.cart-item-title {
  font-weight: 500;
  margin-bottom: 8rpx;
  font-size: 28rpx;
}

.cart-item-spec {
  font-size: 24rpx;
  color: #757575;
  margin-bottom: 16rpx;
}

.cart-item-price {
  color: #f44336;
  font-weight: 600;
}

.delete-btn {
  position: absolute;
  top: 32rpx;
  right: 32rpx;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #757575;
}

.cart-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  padding: 24rpx 30rpx;
  display: flex;
  align-items: center;
  border-top: 1px solid #e0e0e0;
  z-index: 100;
}

.select-all {
  display: flex;
  align-items: center;
  margin-right: 24rpx;
}

.total-price {
  flex: 1;
  text-align: right;
  margin-right: 24rpx;
}

.price-value {
  color: #f44336;
  font-weight: 600;
  font-size: 36rpx;
}

.checkout-btn {
  background-color: #1e88e5;
  color: white;
  padding: 16rpx 40rpx;
  border-radius: 40rpx;
  font-weight: 500;
}

.empty-cart {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

.empty-cart-icon {
  font-size: 120rpx;
  color: #e0e0e0;
  margin-bottom: 32rpx;
}

.empty-cart-text {
  color: #757575;
  margin-bottom: 48rpx;
}

.ml-2 {
  margin-left: 16rpx;
}
