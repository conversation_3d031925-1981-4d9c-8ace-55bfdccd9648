<view class="container">
  <!-- 购物车为空 -->
  <view class="empty-cart" tt:if="{{cartItems.length === 0}}">
    <view class="empty-cart-icon">
      <text class="iconfont icon-shopping-cart"></text>
    </view>
    <view class="empty-cart-text">购物车还是空的</view>
    <button class="btn" bindtap="goToMall">去逛逛</button>
  </view>
  
  <!-- 购物车列表 -->
  <view class="cart-list" tt:else>
    <!-- 购物车商品 -->
    <view class="cart-item" tt:for="{{cartItems}}" tt:key="id">
      <view class="checkbox {{item.selected ? 'checked' : ''}}" bindtap="toggleItemSelect" data-id="{{item.id}}">
        <text class="iconfont icon-check" tt:if="{{item.selected}}"></text>
      </view>
      <image src="{{item.image}}" class="cart-item-image" mode="aspectFill" />
      <view class="cart-item-info">
        <view class="cart-item-title">{{item.name}}</view>
        <view class="cart-item-spec">规格：{{item.spec}}</view>
        <view class="flex-between">
          <view class="cart-item-price">¥{{item.price}}</view>
          <view class="quantity-selector">
            <view class="quantity-btn" bindtap="decreaseQuantity" data-id="{{item.id}}">-</view>
            <input type="number" class="quantity-input" value="{{item.quantity}}" bindinput="onQuantityInput" data-id="{{item.id}}" />
            <view class="quantity-btn" bindtap="increaseQuantity" data-id="{{item.id}}">+</view>
          </view>
        </view>
      </view>
      <view class="delete-btn" bindtap="deleteItem" data-id="{{item.id}}">
        <text class="iconfont icon-delete"></text>
      </view>
    </view>
    
    <!-- 推荐商品 -->
    <view class="section-title mt-3">
      <text>猜你喜欢</text>
    </view>
    
    <view class="grid">
      <view class="product-card" tt:for="{{recommendProducts}}" tt:key="id" bindtap="goToProductDetail" data-id="{{item.id}}">
        <image src="{{item.image}}" mode="aspectFill" />
        <view class="info">
          <view class="title">{{item.name}}</view>
          <view class="price">¥{{item.price}}</view>
        </view>
      </view>
    </view>
  </view>
  
  <!-- 底部结算栏 -->
  <view class="cart-footer" tt:if="{{cartItems.length > 0}}">
    <view class="select-all" bindtap="toggleSelectAll">
      <view class="checkbox {{allSelected ? 'checked' : ''}}">
        <text class="iconfont icon-check" tt:if="{{allSelected}}"></text>
      </view>
      <text class="text-sm ml-2">全选</text>
    </view>
    <view class="total-price">
      合计: <text class="price-value">¥{{totalPrice}}</text>
    </view>
    <view class="checkout-btn" bindtap="checkout">结算({{selectedCount}})</view>
  </view>
</view>
