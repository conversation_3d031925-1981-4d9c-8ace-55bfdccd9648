const app = getApp()

Page({
  data: {
    cartItems: [],
    allSelected: false,
    totalPrice: 0,
    selectedCount: 0,
    recommendProducts: [
      {
        id: 3,
        name: '充电桩控制模块',
        price: 499.00,
        image: '/images/products/充电控制模块.png'
      },
      {
        id: 4,
        name: '充电桩防水保护盒',
        price: 159.00,
        image: '/images/products/充电桩防水保护盒.png'
      }
    ]
  },

  onLoad: function () {
    console.log('Cart page loaded');
  },

  onShow: function () {
    // 页面显示时获取最新的购物车数据
    this.getCartItems();
  },

  // 获取购物车商品
  getCartItems: function() {
    const cartItems = app.globalData.cartItems || [];

    // 为每个商品添加选中状态
    const items = cartItems.map(item => {
      return {
        ...item,
        selected: true
      };
    });

    this.setData({
      cartItems: items
    });

    // 更新全选状态和总价
    this.updateSelectStatus();
    this.calculateTotal();
  },

  // 切换商品选中状态
  toggleItemSelect: function(e) {
    const id = e.currentTarget.dataset.id;
    const cartItems = this.data.cartItems.map(item => {
      if (item.id === id) {
        return {
          ...item,
          selected: !item.selected
        };
      }
      return item;
    });

    this.setData({
      cartItems: cartItems
    });

    // 更新全选状态和总价
    this.updateSelectStatus();
    this.calculateTotal();
  },

  // 切换全选状态
  toggleSelectAll: function() {
    const allSelected = !this.data.allSelected;
    const cartItems = this.data.cartItems.map(item => {
      return {
        ...item,
        selected: allSelected
      };
    });

    this.setData({
      allSelected: allSelected,
      cartItems: cartItems
    });

    // 更新总价
    this.calculateTotal();
  },

  // 更新全选状态
  updateSelectStatus: function() {
    const allSelected = this.data.cartItems.length > 0 && this.data.cartItems.every(item => item.selected);

    this.setData({
      allSelected: allSelected
    });
  },

  // 计算总价和选中商品数量
  calculateTotal: function() {
    let total = 0;
    let count = 0;

    this.data.cartItems.forEach(item => {
      if (item.selected) {
        total += item.price * item.quantity;
        count += item.quantity;
      }
    });

    this.setData({
      totalPrice: total.toFixed(2),
      selectedCount: count
    });
  },

  // 减少商品数量
  decreaseQuantity: function(e) {
    const id = e.currentTarget.dataset.id;
    const cartItems = this.data.cartItems.map(item => {
      if (item.id === id && item.quantity > 1) {
        const newItem = {
          ...item,
          quantity: item.quantity - 1
        };

        // 更新全局购物车数据
        app.updateCartItemQuantity(id, newItem.quantity);

        return newItem;
      }
      return item;
    });

    this.setData({
      cartItems: cartItems
    });

    // 更新总价
    this.calculateTotal();
  },

  // 增加商品数量
  increaseQuantity: function(e) {
    const id = e.currentTarget.dataset.id;
    const cartItems = this.data.cartItems.map(item => {
      if (item.id === id) {
        const newItem = {
          ...item,
          quantity: item.quantity + 1
        };

        // 更新全局购物车数据
        app.updateCartItemQuantity(id, newItem.quantity);

        return newItem;
      }
      return item;
    });

    this.setData({
      cartItems: cartItems
    });

    // 更新总价
    this.calculateTotal();
  },

  // 输入商品数量
  onQuantityInput: function(e) {
    const id = e.currentTarget.dataset.id;
    let value = parseInt(e.detail.value);

    if (isNaN(value) || value < 1) {
      value = 1;
    }

    const cartItems = this.data.cartItems.map(item => {
      if (item.id === id) {
        const newItem = {
          ...item,
          quantity: value
        };

        // 更新全局购物车数据
        app.updateCartItemQuantity(id, newItem.quantity);

        return newItem;
      }
      return item;
    });

    this.setData({
      cartItems: cartItems
    });

    // 更新总价
    this.calculateTotal();
  },

  // 删除商品
  deleteItem: function(e) {
    const id = e.currentTarget.dataset.id;

    tt.showModal({
      title: '提示',
      content: '确定要删除该商品吗？',
      success: (res) => {
        if (res.confirm) {
          // 从全局购物车数据中删除
          app.removeFromCart(id);

          // 从页面数据中删除
          const cartItems = this.data.cartItems.filter(item => item.id !== id);

          this.setData({
            cartItems: cartItems
          });

          // 更新全选状态和总价
          this.updateSelectStatus();
          this.calculateTotal();
        }
      }
    });
  },

  // 结算
  checkout: function() {
    // 检查是否有选中的商品
    const hasSelected = this.data.cartItems.some(item => item.selected);

    if (!hasSelected) {
      tt.showToast({
        title: '请选择要结算的商品',
        icon: 'none'
      });
      return;
    }

    // 筛选出选中的商品
    const selectedItems = this.data.cartItems.filter(item => item.selected);

    // 将选中的商品保存到全局数据中，用于订单确认页面使用
    app.globalData.checkoutItems = selectedItems;

    // 跳转到订单确认页面
    tt.navigateTo({
      url: '/pages/order_confirm/order_confirm'
    });
  },

  // 跳转到商城页面
  goToMall: function() {
    // 使用reLaunch方法跳转到商城页面，并关闭所有其他页面
    tt.reLaunch({
      url: '/pages/mall/mall',
      success: () => {
        console.log('跳转到商城页面成功');
      },
      fail: (err) => {
        console.error('跳转到商城页面失败:', err);
      }
    });
  },

  // 跳转到商品详情页
  goToProductDetail: function(e) {
    const id = e.currentTarget.dataset.id;
    tt.navigateTo({
      url: `/pages/product_detail/product_detail?id=${id}`
    });
  }
})
