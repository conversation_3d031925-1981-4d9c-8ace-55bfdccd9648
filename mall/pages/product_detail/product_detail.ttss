.container {
  padding-bottom: 100rpx;
}

.product-image {
  width: 100%;
  height: 600rpx;
}

.product-image image {
  width: 100%;
  height: 100%;
}

.product-info {
  padding: 32rpx;
  background-color: white;
  margin-bottom: 24rpx;
}

.product-price {
  font-size: 48rpx;
  color: #f44336;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.original-price {
  font-size: 32rpx;
  color: #999;
  text-decoration: line-through;
  font-weight: normal;
}

.product-title {
  font-size: 36rpx;
  font-weight: 600;
  margin: 24rpx 0;
}

.product-subtitle {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.product-sales {
  color: #757575;
  font-size: 28rpx;
  display: flex;
  gap: 32rpx;
}

.product-brand {
  color: #757575;
  font-size: 28rpx;
  margin-top: 16rpx;
  display: flex;
  gap: 32rpx;
}

.product-actions {
  display: flex;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  border-top: 1px solid #e0e0e0;
  z-index: 100;
}

.action-btn {
  flex: 1;
  padding: 24rpx;
  text-align: center;
  font-size: 28rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}

.action-btn .iconfont {
  font-size: 40rpx;
  margin-bottom: 8rpx;
}

.cart-badge {
  position: absolute;
  top: 10rpx;
  right: 30rpx;
  background-color: #f44336;
  color: white;
  font-size: 20rpx;
  min-width: 32rpx;
  height: 32rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 6rpx;
}

.buy-now {
  background-color: #f44336;
  color: white;
}

.add-to-cart {
  background-color: #1e88e5;
  color: white;
}

.tab-content {
  padding: 32rpx;
  background-color: white;
}

.detail-section {
  margin-bottom: 32rpx;
}

.detail-title {
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
}

.detail-item {
  margin-bottom: 12rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.feature-icon {
  color: #4caf50;
  font-weight: bold;
  font-size: 32rpx;
}

.detail-text {
  font-size: 28rpx;
  line-height: 1.6;
}

.spec-table {
  border: 1px solid #e0e0e0;
  border-radius: 12rpx;
  overflow: hidden;
}

.spec-row {
  display: flex;
  border-bottom: 1px solid #e0e0e0;
}

.spec-row:last-child {
  border-bottom: none;
}

.spec-name {
  width: 200rpx;
  padding: 20rpx;
  background-color: #f5f5f5;
  font-weight: 500;
  font-size: 28rpx;
}

.spec-value {
  flex: 1;
  padding: 20rpx;
  font-size: 28rpx;
}

.review-item {
  padding: 24rpx 0;
  border-bottom: 1px solid #e0e0e0;
}

.review-item:last-child {
  border-bottom: none;
}

.review-images {
  display: flex;
  flex-wrap: wrap;
  margin-top: 16rpx;
}

.review-image {
  width: 160rpx;
  height: 160rpx;
  margin-right: 16rpx;
  margin-bottom: 16rpx;
  border-radius: 8rpx;
}

/* 规格选择器 */
.spec-selector {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  visibility: hidden;
  opacity: 0;
  transition: all 0.3s;
}

.spec-selector.show {
  visibility: visible;
  opacity: 1;
}

.spec-content {
  background-color: white;
  border-radius: 24rpx 24rpx 0 0;
  width: 100%;
  max-height: 80vh;
  overflow-y: auto;
  transform: translateY(100%);
  transition: transform 0.3s;
}

.spec-selector.show .spec-content {
  transform: translateY(0);
}

.spec-header {
  padding: 32rpx;
  display: flex;
  position: relative;
  border-bottom: 1px solid #e0e0e0;
}

.spec-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 12rpx;
  margin-right: 24rpx;
}

.spec-info {
  flex: 1;
}

.close-btn {
  position: absolute;
  top: 32rpx;
  right: 32rpx;
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: #f5f5f5;
}

.spec-body {
  padding: 32rpx;
}

.spec-group {
  margin-bottom: 32rpx;
}

.spec-group-title {
  font-weight: 500;
  margin-bottom: 16rpx;
}

.spec-options {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.spec-option {
  padding: 12rpx 24rpx;
  border: 1px solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
}

.spec-option.selected {
  border-color: #1e88e5;
  background-color: #e3f2fd;
  color: #1e88e5;
}

.spec-footer {
  padding: 32rpx;
  border-top: 1px solid #e0e0e0;
}
