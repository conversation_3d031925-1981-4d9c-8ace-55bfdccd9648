const app = getApp()
const api = require('../../utils/api.js')

Page({
  data: {
    id: null,
    product: null,
    reviews: [],
    reviewCount: 0,
    avgRating: 0,
    currentTab: 0,
    showSpec: false,
    selectedSpecId: null,
    selectedSpec: '',
    quantity: 1,
    cartCount: 0,
    isFavorite: false
  },

  onLoad: function (options) {
    const id = parseInt(options.id);
    this.setData({
      id: id
    });

    // 获取商品详情
    this.loadProductDetail(id);

    // 获取购物车数量
    this.getCartCount();

    // 检查收藏状态
    this.checkFavoriteStatus(id);
  },

  onShow: function () {
    // 页面显示时更新购物车数量
    this.getCartCount();
  },

  // 加载商品详情
  loadProductDetail: function(id) {
    console.log('🔄 开始加载商品详情，ID:', id);

    tt.showLoading({
      title: '加载中'
    });

    api.getProductDetail(id).then(res => {
      tt.hideLoading();
      console.log('📦 商品详情API响应:', res);

      if (res.success) {
        const productData = res.data;
        console.log('📋 原始商品数据:', productData);

        // 解析JSON字符串字段
        let product = this.processProductData(productData.product);
        console.log('✅ 处理后的商品数据:', product);

        // 处理评价数据
        let reviews = this.processReviewsData(productData.reviews || []);
        console.log('💬 处理后的评价数据:', reviews);

        this.setData({
          product: product,
          reviews: reviews,
          reviewCount: productData.reviewCount || 0,
          avgRating: productData.avgRating || 0
        });

        console.log('🎉 商品详情加载完成');
      } else {
        console.error('❌ 商品详情加载失败:', res.message);
        tt.showToast({
          title: res.message || '商品不存在',
          icon: 'none'
        });

        setTimeout(() => {
          tt.navigateBack();
        }, 1500);
      }
    }).catch(err => {
      tt.hideLoading();
      console.error('🚨 获取商品详情失败:', err);
      tt.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });

      setTimeout(() => {
        tt.navigateBack();
      }, 1500);
    });
  },

  // 处理商品数据
  processProductData: function(product) {
    if (!product) return {};

    // 处理图片数据
    product = this.processProductImages(product);

    // 处理JSON字段
    const jsonFields = ['features', 'specifications', 'specs', 'services'];
    jsonFields.forEach(field => {
      if (product[field] && typeof product[field] === 'string') {
        try {
          product[field] = JSON.parse(product[field]);
          console.log(`✅ 解析${field}成功:`, product[field]);
        } catch (e) {
          console.warn(`⚠️ 解析${field}失败:`, e);
          product[field] = [];
        }
      } else if (!product[field]) {
        product[field] = [];
      }
    });

    // 确保数值字段正确
    product.price = parseFloat(product.price) || 0;
    product.originalPrice = parseFloat(product.originalPrice) || 0;
    product.stock = parseInt(product.stock) || 0;
    product.sales = parseInt(product.sales) || 0;

    // 处理规格选项格式
    if (product.specs && Array.isArray(product.specs)) {
      product.specs = product.specs.map((spec, index) => {
        if (typeof spec === 'string') {
          return { id: index + 1, name: spec };
        }
        return spec;
      });
    }

    return product;
  },

  // 处理商品图片
  processProductImages: function(product) {
    // 处理轮播图片
    if (product.images && typeof product.images === 'string') {
      try {
        // 如果是JSON字符串，先解析
        if (product.images.startsWith('[') || product.images.startsWith('{')) {
          product.images = JSON.parse(product.images);
        } else {
          // 如果是逗号分隔的字符串，分割处理
          product.images = product.images.split(',').map(url => url.trim()).filter(url => url);
        }
      } catch (e) {
        console.warn('⚠️ 解析轮播图片失败:', e);
        product.images = [];
      }
    } else if (!Array.isArray(product.images)) {
      product.images = [];
    }

    // 如果没有轮播图片，使用主图作为轮播图片
    if (product.images.length === 0 && product.mainImage) {
      product.images = [product.mainImage];
    }

    // 确保所有图片URL都是完整的
    product.images = product.images.map(url => this.processImageUrl(url));

    if (product.mainImage) {
      product.mainImage = this.processImageUrl(product.mainImage);
    }

    if (product.detailImage) {
      product.detailImage = this.processImageUrl(product.detailImage);
    }

    console.log('🖼️ 图片处理完成:', {
      mainImage: product.mainImage,
      detailImage: product.detailImage,
      images: product.images
    });

    return product;
  },

  // 处理图片URL
  processImageUrl: function(imageUrl) {
    if (!imageUrl || imageUrl.trim() === '') {
      return '/images/products/充电线缆.png'; // 默认图片
    }

    // 如果已经是完整的HTTP/HTTPS URL，直接返回
    if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
      return imageUrl;
    }

    // 如果是相对路径，添加服务器地址
    if (imageUrl.startsWith('/uploads/')) {
      return `https://localhost:8443${imageUrl}`;
    }

    // 如果只是文件名，添加完整路径
    if (!imageUrl.startsWith('/')) {
      return `https://localhost:8443/uploads/${imageUrl}`;
    }

    // 其他情况，添加服务器地址
    return `https://localhost:8443${imageUrl}`;
  },

  // 处理评价数据
  processReviewsData: function(reviews) {
    if (!Array.isArray(reviews)) return [];

    return reviews.map(review => {
      // 处理评价图片
      if (review.images && typeof review.images === 'string') {
        try {
          review.images = JSON.parse(review.images);
        } catch (e) {
          review.images = [];
        }
      } else if (!Array.isArray(review.images)) {
        review.images = [];
      }

      // 格式化日期
      if (review.createdAt) {
        review.date = new Date(review.createdAt).toLocaleDateString();
      }

      // 使用用户昵称作为显示名称
      review.name = review.userNickname || '匿名用户';
      review.avatar = review.userAvatar || 'https://randomuser.me/api/portraits/lego/1.jpg';

      // 处理评分显示
      review.rating = Array(review.rating || 5).fill('★');

      return review;
    });
  },

  // 获取购物车数量
  getCartCount: function() {
    const cartItems = app.globalData.cartItems || [];
    const count = cartItems.reduce((total, item) => total + item.quantity, 0);

    this.setData({
      cartCount: count
    });
  },

  // 切换选项卡
  switchTab: function(e) {
    const tab = parseInt(e.currentTarget.dataset.tab);
    this.setData({
      currentTab: tab
    });
  },

  // 显示规格选择器
  showSpecSelector: function() {
    this.setData({
      showSpec: true
    });
  },

  // 隐藏规格选择器
  hideSpecSelector: function() {
    this.setData({
      showSpec: false
    });
  },

  // 防止冒泡
  preventBubble: function() {
    return;
  },

  // 选择规格
  selectSpec: function(e) {
    const id = e.currentTarget.dataset.id;
    const name = e.currentTarget.dataset.name;

    this.setData({
      selectedSpecId: id,
      selectedSpec: name
    });
  },

  // 减少数量
  decreaseQuantity: function() {
    if (this.data.quantity > 1) {
      this.setData({
        quantity: this.data.quantity - 1
      });
    }
  },

  // 增加数量
  increaseQuantity: function() {
    if (this.data.quantity < this.data.product.stock) {
      this.setData({
        quantity: this.data.quantity + 1
      });
    } else {
      tt.showToast({
        title: '已达到最大库存',
        icon: 'none'
      });
    }
  },

  // 输入数量
  onQuantityInput: function(e) {
    let value = parseInt(e.detail.value);

    if (isNaN(value) || value < 1) {
      value = 1;
    } else if (value > this.data.product.stock) {
      value = this.data.product.stock;
      tt.showToast({
        title: '已达到最大库存',
        icon: 'none'
      });
    }

    this.setData({
      quantity: value
    });
  },

  // 确认规格选择
  confirmSpec: function() {
    if (!this.data.selectedSpecId) {
      tt.showToast({
        title: '请选择规格',
        icon: 'none'
      });
      return;
    }

    this.hideSpecSelector();
  },

  // 添加到购物车
  addToCart: function() {
    tt.showModal({
      title: '商城暂未开放',
      content: '商城功能即将上线，请稍等。',
      showCancel: false,
      confirmText: '知道了',
      confirmColor: '#667eea'
    });
  },

  // 立即购买
  buyNow: function() {
    tt.showModal({
      title: '商城暂未开放',
      content: '商城功能即将上线，请稍等。',
      showCancel: false,
      confirmText: '知道了',
      confirmColor: '#667eea'
    });
  },

  // 跳转到首页
  goToHome: function() {
    // 使用reLaunch方法跳转到首页，并关闭所有其他页面
    tt.reLaunch({
      url: '/pages/index/index',
      success: () => {
        console.log('跳转到首页成功');
      },
      fail: (err) => {
        console.error('跳转到首页失败:', err);
      }
    });
  },

  // 跳转到购物车
  goToCart: function() {
    tt.showModal({
      title: '商城暂未开放',
      content: '商城功能暂未开放，等后面我有许可证后，再开放该功能。',
      showCancel: false,
      confirmText: '知道了',
      confirmColor: '#667eea'
    });
  },

  // 预览图片
  previewImage: function(e) {
    const url = e.currentTarget.dataset.url;
    const urls = e.currentTarget.dataset.urls;

    tt.previewImage({
      current: url,
      urls: urls
    });
  },

  // 检查收藏状态
  checkFavoriteStatus: function(productId) {
    const openId = app.globalData.openId;
    if (!openId) {
      console.warn('用户未登录，无法检查收藏状态');
      return;
    }

    api.checkFavorite(openId, productId)
      .then(res => {
        if (res.success) {
          this.setData({
            isFavorite: res.isFavorite
          });
        }
      })
      .catch(err => {
        console.error('检查收藏状态失败:', err);
      });
  },

  // 切换收藏状态
  toggleFavorite: function() {
    const productId = this.data.id;
    const isFavorite = this.data.isFavorite;
    const openId = app.globalData.openId;

    if (!openId) {
      tt.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    const apiCall = isFavorite ? api.removeFavorite : api.addFavorite;
    const successMessage = isFavorite ? '已取消收藏' : '已加入收藏';

    apiCall(openId, productId)
      .then(res => {
        if (res.success) {
          this.setData({
            isFavorite: !isFavorite
          });
          tt.showToast({
            title: successMessage,
            icon: 'success'
          });
        } else {
          tt.showToast({
            title: res.message || '操作失败',
            icon: 'none'
          });
        }
      })
      .catch(err => {
        console.error('收藏操作失败:', err);
        tt.showToast({
          title: '操作失败，请重试',
          icon: 'none'
        });
      });
  }
})
