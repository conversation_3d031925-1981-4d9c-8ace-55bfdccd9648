page {
  background-color: #f5f5f5;
}

.container {
  min-height: 100vh;
  box-sizing: border-box;
}

/* 头部介绍 */
.header {
  position: relative;
  height: 400rpx;
  overflow: hidden;
}

.header-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.bg-image {
  width: 100%;
  height: 100%;
}

.header-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(30, 136, 229, 0.8), rgba(30, 136, 229, 0.6));
}

.header-content {
  position: relative;
  z-index: 2;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  text-align: center;
  padding: 0 40rpx;
}

.header-title {
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.header-subtitle {
  font-size: 32rpx;
  margin-bottom: 15rpx;
  opacity: 0.9;
}

.header-desc {
  font-size: 26rpx;
  opacity: 0.8;
  line-height: 1.5;
}

/* 入驻选项 */
.join-options {
  padding: 40rpx 20rpx;
}

.option-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.option-card:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.option-icon {
  width: 120rpx;
  height: 120rpx;
  margin-right: 30rpx;
  flex-shrink: 0;
}

.option-icon image {
  width: 100%;
  height: 100%;
}

.option-content {
  flex: 1;
}

.option-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.option-subtitle {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.option-desc {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.option-desc text {
  font-size: 24rpx;
  color: #999;
  line-height: 1.4;
}

.option-arrow {
  margin-left: 20rpx;
  color: #1e88e5;
  font-size: 32rpx;
}

/* 合作优势 */
.advantages {
  padding: 40rpx 20rpx;
  background-color: #fff;
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 40rpx;
  color: #333;
}

.advantage-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30rpx;
}

.advantage-item {
  text-align: center;
  padding: 30rpx 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
}

.advantage-icon {
  width: 80rpx;
  height: 80rpx;
  margin: 0 auto 20rpx;
}

.advantage-icon image {
  width: 100%;
  height: 100%;
}

.advantage-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.advantage-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

/* 入驻流程 */
.process {
  padding: 40rpx 20rpx;
  background-color: #f8f9fa;
}

.process-steps {
  position: relative;
  padding: 20rpx 0;
}

.step-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 40rpx;
  position: relative;
}

.step-item:last-child {
  margin-bottom: 0;
}

.step-number {
  width: 60rpx;
  height: 60rpx;
  background-color: #1e88e5;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
  margin-right: 30rpx;
  flex-shrink: 0;
  z-index: 2;
  position: relative;
}

.step-content {
  flex: 1;
  padding-top: 8rpx;
}

.step-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.step-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

.step-line {
  position: absolute;
  left: 30rpx;
  top: 60rpx;
  width: 2rpx;
  height: 40rpx;
  background-color: #e0e0e0;
  z-index: 1;
}

.step-item:last-child + .step-line {
  display: none;
}

/* 联系我们 */
.contact {
  padding: 40rpx 20rpx;
  background-color: #fff;
}

.contact-info {
  background-color: #f8f9fa;
  border-radius: 12rpx;
  padding: 30rpx;
}

.contact-item {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.contact-item:last-child {
  margin-bottom: 0;
}

.contact-icon {
  width: 50rpx;
  height: 50rpx;
  background-color: #1e88e5;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  font-size: 24rpx;
}

.contact-text {
  flex: 1;
}

.contact-label {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 4rpx;
}

.contact-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}
