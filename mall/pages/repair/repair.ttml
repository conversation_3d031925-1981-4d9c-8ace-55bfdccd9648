<view class="container">
  <!-- Banner -->
  <swiper class="banner" indicator-dots="{{true}}" autoplay="{{true}}" interval="{{3000}}" duration="{{500}}">
    <swiper-item tt:for="{{banners}}" tt:key="index">
      <image src="{{item.image}}" mode="aspectFill" />
    </swiper-item>
  </swiper>

  <!-- 维修选项 -->
  <view class="section-title">
    <text>选择服务方式</text>
  </view>

  <view class="repair-option" bindtap="goToRepairForm" data-type="home">
    <view class="repair-icon">
      <!-- <text class="iconfont icon-home"></text> -->
      <image src="/images/icons/上门维修.png" mode="aspectFit" style="width: 60rpx; height: 60rpx;"></image>
    </view>
    <view class="repair-info">
      <view class="repair-title">上门维修</view>
      <view class="repair-desc">专业技师上门，快速解决充电桩故障</view>
    </view>
    <text class="iconfont icon-right text-light"></text>
  </view>

  <view class="repair-option" bindtap="goToServiceCenters">
    <view class="repair-icon">
      <!-- <text class="iconfont icon-map-marker"></text> -->
      <image src="/images/icons/网点维修.png" mode="aspectFit" style="width: 60rpx; height: 60rpx;"></image>
    </view>
    <view class="repair-info">
      <view class="repair-title">网点维修</view>
      <view class="repair-desc">就近选择服务网点，专业设备保障</view>
    </view>
    <text class="iconfont icon-right text-light"></text>
  </view>

  <view class="repair-option" bindtap="goToOnlineConsult">
    <view class="repair-icon">
      <!-- <text class="iconfont icon-headset"></text> -->
      <image src="/images/icons/远程指导.png" mode="aspectFit" style="width: 60rpx; height: 60rpx;"></image>
    </view>
    <view class="repair-info">
      <view class="repair-title">远程指导</view>
      <view class="repair-desc">在线专家指导，解决简单故障问题</view>
    </view>
    <text class="iconfont icon-right text-light"></text>
  </view>

  <!-- 常见故障 -->
  <view class="section-title mt-3">
    <text>常见故障类型</text>
  </view>

  <view class="card">
    <view class="fault-grid">
      <view class="fault-type" tt:for="{{faultTypes}}" tt:key="id" bindtap="goToRepairForm" data-type="{{item.type}}">
        <view class="fault-icon">
          <!-- <text class="iconfont {{item.icon}}"></text> -->
          <image src="{{item.image}}" mode="aspectFit" style="width: 60rpx; height: 60rpx;"></image>
        </view>
        <view class="fault-name">{{item.name}}</view>
      </view>
    </view>
  </view>

  <!-- 维修价格 -->
  <view class="section-title mt-3">
    <text>维修价格参考</text>
  </view>

  <view class="card">
    <view class="price-table">
      <view class="price-row" tt:for="{{priceList}}" tt:key="name">
        <view class="price-name">{{item.name}}</view>
        <view class="price-value">¥{{item.price}}</view>
      </view>
    </view>
    <view class="text-xs text-light mt-2">注：实际价格以技师现场检测为准，上门检测费用¥50（维修后免除）</view>
  </view>

  <!-- 技师团队 -->
  <view class="section-title mt-3">
    <text>专业技师团队</text>
    <view class="text-xs text-light">共{{engineerStats.approved}}名认证技师</view>
  </view>

  <view class="card">
    <view class="engineer" tt:for="{{engineers}}" tt:key="id" tt:if="{{index < 3}}">
      <view class="engineer-avatar-container">
        <image
          src="{{item.processedAvatar}}"
          class="engineer-avatar"
          mode="aspectFill"
          binderror="onAvatarError"
          data-index="{{index}}"
          tt:if="{{item.processedAvatar}}"
        />
        <view class="avatar-placeholder" tt:if="{{!item.processedAvatar}}">
          <text class="iconfont icon-user"></text>
        </view>
      </view>
      <view class="engineer-info">
        <view class="engineer-name">{{item.name}}</view>
        <view class="rating">
          <text class="iconfont icon-star" tt:for="{{item.rating}}" tt:key="*this"></text>
          <text class="text-xs text-light ml-1">{{item.ratingValue}} ({{item.reviewCount}}次服务)</text>
        </view>
        <view class="text-xs text-light">{{item.experience}} | {{item.title}}</view>
        <view class="text-xs text-light" tt:if="{{item.bio}}">{{item.bio}}</view>
        <view class="engineer-tags" tt:if="{{item.specialties && item.specialties.length > 0}}">
          <text class="tag" tt:for="{{item.specialties}}" tt:key="*this" tt:if="{{index < 3}}">{{item}}</text>
        </view>
      </view>
    </view>

    <view class="text-center mt-3" tt:if="{{engineers.length > 3}}">
      <text class="text-primary text-sm" bindtap="viewAllEngineers">查看全部{{engineers.length}}名技师 ></text>
    </view>
  </view>
</view>

<!-- 自定义导航栏 -->
<nav-bar active="repair"></nav-bar>
