.container {
  background-color: #f8f9fa;
  min-height: 100vh;
  width: 100%;
  max-width: 750rpx;
  margin: 0 auto;
  box-sizing: border-box;
}

/* 头部 */
.header {
  position: relative;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 40rpx 30rpx 30rpx;
  text-align: center;
  overflow: hidden;
}

.header-content {
  position: relative;
  z-index: 2;
}

.header-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="3" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1" fill="rgba(255,255,255,0.1)"/></svg>');
  opacity: 0.3;
}

.title {
  font-size: 40rpx;
  font-weight: 700;
  margin-bottom: 12rpx;
  letter-spacing: 1rpx;
}

.count {
  font-size: 28rpx;
  opacity: 0.9;
  font-weight: 500;
}

/* 内容区域 */
.content {
  height: calc(100vh - 180rpx);
  padding: 20rpx 30rpx;
  box-sizing: border-box;
}

/* 商品网格 */
.product-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
  padding-bottom: 40rpx;
  width: 100%;
  box-sizing: border-box;
}

.product-card {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 6rpx 24rpx rgba(0,0,0,0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  width: 100%;
  box-sizing: border-box;
}

.product-card:active {
  transform: translateY(-4rpx) scale(0.98);
  box-shadow: 0 12rpx 40rpx rgba(0,0,0,0.12);
}

/* 商品卡片图片 */
.card-image {
  position: relative;
  width: 100%;
  height: 160rpx;
  overflow: hidden;
}

.card-image image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.favorite-badge {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 40rpx;
  height: 40rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ff4757;
  font-size: 20rpx;
  backdrop-filter: blur(8rpx);
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
  transition: all 0.3s ease;
}

.favorite-badge:active {
  transform: scale(0.9);
  background: rgba(255, 255, 255, 1);
}

/* 商品卡片内容 */
.card-content {
  padding: 16rpx;
}

.product-name {
  font-size: 26rpx;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 6rpx;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  min-height: 68rpx;
}

.product-desc {
  font-size: 22rpx;
  color: #7f8c8d;
  margin-bottom: 12rpx;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
}

/* 价格行 */
.price-row {
  display: flex;
  align-items: baseline;
  gap: 8rpx;
  margin-bottom: 10rpx;
}

.current-price {
  font-size: 28rpx;
  font-weight: 700;
  color: #e74c3c;
  line-height: 1;
}

.original-price {
  font-size: 20rpx;
  color: #bdc3c7;
  text-decoration: line-through;
  line-height: 1;
}

/* 信息行 */
.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 20rpx;
  color: #95a5a6;
}

.sales {
  color: #27ae60;
  font-weight: 500;
}

.favorite-time {
  color: #95a5a6;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 120rpx 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.empty-illustration {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 40rpx;
  opacity: 0.6;
}

.empty-illustration image {
  width: 100%;
  height: 100%;
}

.empty-title {
  font-size: 36rpx;
  color: #2c3e50;
  margin-bottom: 16rpx;
  font-weight: 600;
}

.empty-desc {
  font-size: 28rpx;
  color: #7f8c8d;
  margin-bottom: 60rpx;
  line-height: 1.5;
}

.empty-actions {
  display: flex;
  justify-content: center;
}

.action-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 28rpx 48rpx;
  border-radius: 50rpx;
  font-size: 28rpx;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  gap: 12rpx;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
}

.action-btn:active {
  transform: translateY(-2rpx) scale(0.98);
  box-shadow: 0 12rpx 32rpx rgba(102, 126, 234, 0.4);
}

/* 响应式设计 */
@media screen and (max-width: 320px) {
  .content {
    padding: 16rpx 20rpx;
  }

  .product-grid {
    gap: 12rpx;
  }

  .card-content {
    padding: 12rpx;
  }

  .product-name {
    font-size: 24rpx;
    min-height: 60rpx;
  }

  .current-price {
    font-size: 26rpx;
  }
}

@media screen and (min-width: 768px) {
  .product-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 24rpx;
  }

  .content {
    padding: 30rpx 40rpx;
  }
}
