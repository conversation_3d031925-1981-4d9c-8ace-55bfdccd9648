const app = getApp()
const api = require('../../utils/api')

Page({
  data: {
    isRefreshing: false,
    favoriteProducts: [],
    favoriteCount: 0
  },

  onLoad: function() {
    this.loadFavoriteProducts();
  },

  onShow: function() {
    // 每次显示页面时刷新数据
    this.loadFavoriteProducts();
  },

  // 加载收藏的商品
  loadFavoriteProducts: function() {
    const openId = app.globalData.openId;
    if (!openId) {
      console.warn('用户未登录，无法加载收藏商品');
      this.setData({
        favoriteProducts: [],
        favoriteCount: 0
      });
      return;
    }

    console.log('🔄 开始加载收藏商品，openId:', openId);

    api.getFavoriteProducts(openId)
      .then(res => {
        console.log('📦 收藏商品API响应:', res);

        if (res.success) {
          const favoriteProducts = res.products.map(product => {
            // 处理图片URL，使用后端真实数据
            const processedImage = this.processProductImageUrl(product.mainImage || product.image);

            console.log('🖼️ 处理产品图片:', {
              productId: product.id,
              productName: product.name,
              originalImage: product.mainImage || product.image,
              processedImage: processedImage
            });

            return {
              ...product,
              mainImage: processedImage,
              favoriteTime: this.formatFavoriteTime(new Date(product.favorite_time || product.createdAt))
            };
          });

          this.setData({
            favoriteProducts: favoriteProducts,
            favoriteCount: favoriteProducts.length
          });

          console.log('✅ 收藏商品加载成功，数量:', favoriteProducts.length);
        } else {
          console.error('获取收藏商品失败:', res.message);
          this.setData({
            favoriteProducts: [],
            favoriteCount: 0
          });
        }
      })
      .catch(err => {
        console.error('加载收藏商品失败:', err);
        this.setData({
          favoriteProducts: [],
          favoriteCount: 0
        });
      });
  },

  // 处理产品图片URL
  processProductImageUrl: function(imageUrl) {
    console.log('🔍 处理图片URL:', imageUrl);

    // 如果没有图片URL，使用默认图片
    if (!imageUrl || imageUrl.trim() === '') {
      console.log('⚠️ 图片URL为空，使用默认图片');
      return '/images/products/充电线缆.png';
    }

    // 如果已经是完整的HTTP/HTTPS URL，直接返回
    if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
      console.log('✅ 图片URL已是完整URL:', imageUrl);
      return imageUrl;
    }

    // 如果是相对路径，添加服务器地址
    if (imageUrl.startsWith('/uploads/')) {
      const fullUrl = `https://localhost:8443${imageUrl}`;
      console.log('🔗 添加服务器地址:', fullUrl);
      return fullUrl;
    }

    // 如果只是文件名，添加完整路径
    if (!imageUrl.startsWith('/')) {
      const fullUrl = `https://localhost:8443/uploads/${imageUrl}`;
      console.log('📁 添加完整路径:', fullUrl);
      return fullUrl;
    }

    // 其他情况，添加服务器地址
    const fullUrl = `https://localhost:8443${imageUrl}`;
    console.log('🌐 其他情况添加服务器地址:', fullUrl);
    return fullUrl;
  },

  // 格式化收藏时间
  formatFavoriteTime: function(date) {
    const now = new Date();
    const diff = now - date;
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (days === 0) {
      return '今天';
    } else if (days === 1) {
      return '昨天';
    } else if (days < 7) {
      return `${days}天前`;
    } else {
      return `${Math.floor(days / 7)}周前`;
    }
  },

  // 下拉刷新
  onRefresh: function() {
    this.setData({ isRefreshing: true });

    setTimeout(() => {
      this.loadFavoriteProducts();
      this.setData({ isRefreshing: false });
      tt.showToast({
        title: '刷新成功',
        icon: 'success'
      });
    }, 1000);
  },

  // 取消收藏
  removeFavorite: function(e) {
    const id = e.currentTarget.dataset.id;
    const openId = app.globalData.openId;

    if (!openId) {
      tt.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    tt.showModal({
      title: '确认取消收藏',
      content: '确定要取消收藏这个商品吗？',
      success: (res) => {
        if (res.confirm) {
          api.removeFavorite(openId, id)
            .then(res => {
              if (res.success) {
                // 重新加载收藏数据
                this.loadFavoriteProducts();
                tt.showToast({
                  title: '已取消收藏',
                  icon: 'success'
                });
              } else {
                tt.showToast({
                  title: res.message || '取消收藏失败',
                  icon: 'none'
                });
              }
            })
            .catch(err => {
              console.error('取消商品收藏失败:', err);
              tt.showToast({
                title: '操作失败，请重试',
                icon: 'none'
              });
            });
        }
      }
    });
  },

  // 查看商品详情
  viewProduct: function(e) {
    const product = e.currentTarget.dataset.product;
    tt.navigateTo({
      url: `/pages/product_detail/product_detail?id=${product.id}`
    });
  },

  // 跳转到商城
  goToMall: function() {
    tt.switchTab({
      url: '/pages/mall/mall'
    });
  },

  // 图片加载失败处理
  onImageError: function(e) {
    const productId = e.currentTarget.dataset.productId;
    console.log('🚨 图片加载失败，产品ID:', productId);

    // 找到对应的产品并更新图片为默认图片
    const favoriteProducts = this.data.favoriteProducts.map(product => {
      if (product.id === productId) {
        console.log('🔄 更新产品图片为默认图片:', product.name);
        return {
          ...product,
          mainImage: '/images/products/充电线缆.png'
        };
      }
      return product;
    });

    this.setData({
      favoriteProducts: favoriteProducts
    });
  }
});
