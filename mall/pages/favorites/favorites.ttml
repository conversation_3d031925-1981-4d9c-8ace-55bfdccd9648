<view class="container">
  <!-- 头部 -->
  <view class="header">
    <view class="header-content">
      <view class="title">我的收藏</view>
      <view class="count">{{favoriteCount}}个商品</view>
    </view>
    <view class="header-bg"></view>
  </view>

  <!-- 收藏商品列表 -->
  <scroll-view class="content" scroll-y="true" refresher-enabled="true" bindrefresherrefresh="onRefresh" refresher-triggered="{{isRefreshing}}">

    <!-- 商品网格列表 -->
    <view class="product-grid" tt:if="{{favoriteProducts.length > 0}}">
      <view tt:for="{{favoriteProducts}}" tt:key="id" class="product-card" bindtap="viewProduct" data-product="{{item}}">
        <view class="card-image">
          <image
            src="{{item.mainImage}}"
            mode="aspectFill"
            lazy-load="{{true}}"
            binderror="onImageError"
            data-product-id="{{item.id}}"
          ></image>
          <view class="favorite-badge" bindtap="removeFavorite" data-id="{{item.id}}" catchtap="true">
            <text class="iconfont icon-heart-filled"></text>
          </view>
        </view>
        <view class="card-content">
          <view class="product-name">{{item.shortName || item.name}}</view>
          <view class="product-desc">{{item.description}}</view>
          <view class="price-row">
            <view class="current-price">¥{{item.price}}</view>
            <view class="original-price" tt:if="{{item.originalPrice}}">¥{{item.originalPrice}}</view>
          </view>
          <view class="info-row">
            <view class="sales">已售{{item.sales}}件</view>
            <view class="favorite-time">{{item.favoriteTime}}</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view tt:if="{{favoriteProducts.length === 0}}" class="empty-state">
      <view class="empty-illustration">
        <image src="/images/icons/我的收藏.png" mode="aspectFit"></image>
      </view>
      <view class="empty-title">暂无收藏商品</view>
      <view class="empty-desc">发现喜欢的商品就收藏起来吧</view>
      <view class="empty-actions">
        <view class="action-btn" bindtap="goToMall">
          <text class="iconfont icon-shop"></text>
          <text>去逛逛</text>
        </view>
      </view>
    </view>

  </scroll-view>
</view>
