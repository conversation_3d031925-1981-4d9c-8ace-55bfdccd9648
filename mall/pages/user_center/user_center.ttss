.container {
  padding: 30rpx 30rpx 0;
  min-height: 100vh;
  padding-bottom: 120rpx; /* 确保底部有足够空间 */
}

.user-header {
  background-color: #1e88e5;
  padding: 48rpx 32rpx;
  color: white;
  border-radius: 24rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.user-info {
  display: flex;
  align-items: center;
}

.user-avatar {
  width: 140rpx;
  height: 140rpx;
  border-radius: 50%;
  border: 4rpx solid white;
  margin-right: 32rpx;
}

.user-name {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}


.login-btn {
  font-size: 28rpx;
  background-color: rgba(255, 255, 255, 0.2);
  padding: 8rpx 24rpx;
  border-radius: 32rpx;
  display: inline-block;
  margin-top: 8rpx;
}

.user-stats {
  display: flex;
  justify-content: space-around;
  margin-top: 32rpx;
  text-align: center;
}

.stat-item {
  flex: 1;
}

.stat-value {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  opacity: 0.8;
}

.menu-group {
  background-color: white;
  border-radius: 24rpx;
  overflow: hidden;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1px solid #f0f0f0;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-icon {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 24rpx;
  color: #1e88e5;
  font-size: 40rpx;
}

.menu-title {
  flex: 1;
  font-size: 32rpx;
}

.menu-arrow {
  color: #757575;
  display: flex;
  align-items: center;
}

.menu-arrow .text-light {
  font-size: 28rpx;
  margin-right: 8rpx;
}

.badge-count {
  background-color: #f44336;
  color: white;
  border-radius: 20rpx;
  padding: 4rpx 12rpx;
  font-size: 24rpx;
  margin-right: 16rpx;
}

.order-icons {
  display: flex;
  padding: 32rpx 0;
}

.order-icon-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.order-icon {
  width: 80rpx;
  height: 80rpx;
  background-color: #e3f2fd;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 16rpx;
  color: #1e88e5;
  font-size: 40rpx;
  position: relative;
}

.order-icon-label {
  font-size: 24rpx;
  color: #333333;
}

.badge {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  background-color: #f44336;
  color: white;
  border-radius: 20rpx;
  min-width: 32rpx;
  height: 32rpx;
  padding: 0 8rpx;
  font-size: 20rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

/* 退出登录区域 */
.logout-section {
  margin-top: 40rpx;
  padding: 0 30rpx;
}

.logout-button {
  background: #f44336;
  border-radius: 12rpx;
  padding: 24rpx 0;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  margin-bottom: 20rpx;
}

.logout-button:active {
  background: #d32f2f;
  transform: scale(0.98);
}

.logout-text {
  color: white;
  font-size: 28rpx;
  font-weight: 500;
}

/* 底部间距 */
.bottom-spacing {
  height: 100rpx;
  background: transparent;
}
