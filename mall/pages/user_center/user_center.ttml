<view class="container">
  <!-- 用户头部 -->
  <view class="user-header">
    <view class="user-info">
      <image src="{{userInfo.avatarUrl || '/images/default-avatar.png'}}" class="user-avatar" mode="aspectFill" />
      <view>
        <view class="user-name">{{userInfo.nickname || userInfo.nickName || '未登录'}}</view>
        <view class="login-btn" tt:if="{{!userInfo.nickname && !userInfo.nickName}}" bindtap="login">点击登录</view>
      </view>
    </view>
    <view class="user-stats">
      <view class="stat-item" bindtap="goToOrderList" data-status="pending_payment">
        <view class="stat-value">{{orderStats.pendingPayment || 0}}</view>
        <view class="stat-label">待付款</view>
      </view>
      <view class="stat-item" bindtap="goToOrderList" data-status="pending_delivery">
        <view class="stat-value">{{orderStats.pendingDelivery || 0}}</view>
        <view class="stat-label">待发货</view>
      </view>
      <view class="stat-item" bindtap="goToOrderList" data-status="pending_receipt">
        <view class="stat-value">{{orderStats.pendingReceipt || 0}}</view>
        <view class="stat-label">待收货</view>
      </view>
      <view class="stat-item" bindtap="goToOrderList" data-status="completed">
        <view class="stat-value">{{orderStats.completed || 0}}</view>
        <view class="stat-label">已完成</view>
      </view>
    </view>
  </view>

  <!-- 订单 -->
  <!-- <view class="menu-group">
    <view class="menu-item" bindtap="goToOrderList" data-status="all">
      <view class="menu-title">我的订单</view>
      <view class="menu-arrow">
        <text class="text-light">查看全部订单</text>
        <text class="iconfont icon-right"></text>
      </view>
    </view> -->
    <!-- <view class="order-icons">
      <view class="order-icon-item" bindtap="goToOrderList" data-status="pending_payment">
        <view class="order-icon">
          <image src="/images/icons/待付款.png" mode="aspectFit" style="width: 50rpx; height: 50rpx;"></image>
        </view>
        <view class="order-icon-label">待付款</view>
      </view>
      <view class="order-icon-item" bindtap="goToOrderList" data-status="pending_delivery">
        <view class="order-icon">
          <image src="/images/icons/待发货.png" mode="aspectFit" style="width: 50rpx; height: 50rpx;"></image>
        </view>
        <view class="order-icon-label">待发货</view>
      </view>
      <view class="order-icon-item" bindtap="goToOrderList" data-status="pending_receipt">
        <view class="order-icon">
          <image src="/images/icons/待收货.png" mode="aspectFit" style="width: 50rpx; height: 50rpx;"></image>
        </view>
        <view class="order-icon-label">待收货</view>
      </view>
      <view class="order-icon-item" bindtap="goToOrderList" data-status="pending_review">
        <view class="order-icon">
          <image src="/images/icons/待评价.png" mode="aspectFit" style="width: 50rpx; height: 50rpx;"></image>
        </view>
        <view class="order-icon-label">待评价</view>
      </view>
      <view class="order-icon-item" bindtap="goToAfterSale">
        <view class="order-icon">
          <image src="/images/icons/退换售后.png" mode="aspectFit" style="width: 50rpx; height: 50rpx;"></image>
        </view>
        <view class="order-icon-label">退换/售后</view>
      </view>
    </view>
  </view> -->

  <!-- 维修服务 -->
  <view class="menu-group">
    <view class="menu-item" bindtap="goToRepairList">
      <!-- <view class="menu-icon">
        <text class="iconfont icon-tools"></text>
      </view> -->
      <view class="menu-title">维修服务</view>
      <view class="menu-arrow">
        <text class="text-light">查看全部维修</text>
        <text class="iconfont icon-right"></text>
      </view>
    </view>
    <view class="order-icons">
      <view class="order-icon-item" bindtap="goToRepairList" data-status="pending">
        <view class="order-icon">
          <image src="/images/icons/待接单.png" mode="aspectFit" style="width: 50rpx; height: 50rpx;"></image>
          <view class="badge" tt:if="{{repairStats.pending > 0}}">{{repairStats.pending}}</view>
        </view>
        <view class="order-icon-label">待接单</view>
      </view>
      <view class="order-icon-item" bindtap="goToRepairList" data-status="accepted">
        <view class="order-icon">
          <image src="/images/icons/待上门.png" mode="aspectFit" style="width: 50rpx; height: 50rpx;"></image>
          <view class="badge" tt:if="{{repairStats.accepted > 0}}">{{repairStats.accepted}}</view>
        </view>
        <view class="order-icon-label">待上门</view>
      </view>
      <view class="order-icon-item" bindtap="goToRepairList" data-status="processing">
        <view class="order-icon">
          <image src="/images/icons/维修中.png" mode="aspectFit" style="width: 50rpx; height: 50rpx;"></image>
          <view class="badge" tt:if="{{repairStats.processing > 0}}">{{repairStats.processing}}</view>
        </view>
        <view class="order-icon-label">维修中</view>
      </view>
      <view class="order-icon-item" bindtap="goToRepairList" data-status="completed">
        <view class="order-icon">
          <image src="/images/icons/已完成.png" mode="aspectFit" style="width: 50rpx; height: 50rpx;"></image>
          <view class="badge" tt:if="{{repairStats.completed > 0}}">{{repairStats.completed}}</view>
        </view>
        <view class="order-icon-label">已完成</view>
      </view>
    </view>
  </view>

  <!-- 我的服务 -->
  <view class="menu-group">
    <view class="menu-item" bindtap="goToAddressList">
      <view class="menu-icon">
        <image src="/images/icons/收货地址.png" mode="aspectFit" style="width: 40rpx; height: 40rpx;"></image>
      </view>
      <view class="menu-title">收货地址</view>
      <view class="menu-arrow">
        <text class="iconfont icon-right"></text>
      </view>
    </view>
    <view class="menu-item" bindtap="goToFavorites">
      <view class="menu-icon">
        <image src="/images/icons/我的收藏.png" mode="aspectFit" style="width: 40rpx; height: 40rpx;"></image>
      </view>
      <view class="menu-title">我的收藏</view>
      <view class="menu-arrow">
        <text class="iconfont icon-right"></text>
      </view>
    </view>
    <!-- <view class="menu-item" bindtap="goToReviews">
      <view class="menu-icon">
        <image src="/images/icons/我的评价.png" mode="aspectFit" style="width: 40rpx; height: 40rpx;"></image>
      </view>
      <view class="menu-title">我的评价</view>
      <view class="menu-arrow">
        <text class="iconfont icon-right"></text>
      </view>
    </view> -->
    <view class="menu-item" bindtap="goToCustomerService">
      <view class="menu-icon">
        <image src="/images/icons/客服中心.png" mode="aspectFit" style="width: 40rpx; height: 40rpx;"></image>
      </view>
      <view class="menu-title">客服中心</view>
      <view class="menu-arrow">
        <view class="badge-count" tt:if="{{unreadMessages > 0}}">{{unreadMessages}}</view>
        <text class="iconfont icon-right"></text>
      </view>
    </view>
  </view>

  <!-- 设置 -->
  <view class="menu-group">
    <!-- <view class="menu-item" bindtap="goToSettings">
      <view class="menu-icon">
        <image src="/images/icons/设置.png" mode="aspectFit" style="width: 40rpx; height: 40rpx;"></image>
      </view>
      <view class="menu-title">设置</view>
      <view class="menu-arrow">
        <text class="iconfont icon-right"></text>
      </view>
    </view> -->
    <!-- <view class="menu-item" bindtap="goToHelpCenter">
      <view class="menu-icon">
        <image src="/images/icons/帮助中心.png" mode="aspectFit" style="width: 40rpx; height: 40rpx;"></image>
      </view>
      <view class="menu-title">帮助中心</view>
      <view class="menu-arrow">
        <text class="iconfont icon-right"></text>
      </view>
    </view> -->
    <view class="menu-item" bindtap="goToAboutUs">
      <view class="menu-icon">
        <image src="/images/icons/关于我们.png" mode="aspectFit" style="width: 40rpx; height: 40rpx;"></image>
      </view>
      <view class="menu-title">加入我们</view>
      <view class="menu-arrow">
        <text class="iconfont icon-right"></text>
      </view>
    </view>
  </view>

  <!-- 退出登录按钮 - 放在最底部 -->
  <view tt:if="{{userInfo}}" class="logout-section">
    <view class="logout-button" bindtap="logout">
      <text class="logout-text">退出登录</text>
    </view>
  </view>

  <!-- 底部间距 -->
  <view class="bottom-spacing"></view>
</view>

<!-- 自定义导航栏 -->
<nav-bar active="user"></nav-bar>
