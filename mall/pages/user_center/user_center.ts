const app = getApp()
const auth = require('../../utils/auth.js')

Page({
  data: {
    userInfo: null,
    orderStats: {
      pendingPayment: 0,
      pendingDelivery: 0,
      pendingReceipt: 0,
      completed: 0
    },
    repairStats: {
      pending: 0,
      accepted: 0,
      processing: 0,
      completed: 0
    },
    unreadMessages: 0
  },

  onLoad: function () {
    console.log('User center page loaded');
  },

  onShow: function () {
    // 获取用户信息
    this.getUserInfo();

    // 获取订单统计
    this.getOrderStats();

    // 获取维修订单统计
    this.getRepairStats();

    // 更新自定义tabBar的选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 3
      });
      console.log('用户中心页更新tabBar成功');
    } else {
      console.log('用户中心页获取tabBar失败');
    }
  },

  // 获取用户信息
  getUserInfo: function() {
    // 使用新的认证系统获取用户信息
    const loginState = auth.getLoginState();

    if (loginState.isLoggedIn && loginState.userInfo) {
      console.log('✅ 从认证系统获取用户信息:', loginState.userInfo.nickName);
      this.setData({
        userInfo: loginState.userInfo
      });
    } else {
      console.log('⚠️ 用户未登录或登录状态已过期');
      this.setData({
        userInfo: null
      });
    }
  },

  // 获取订单统计
  getOrderStats: function() {
    // 实际开发中应该调用API获取订单统计数据
    // 这里使用模拟数据
    this.setData({
      orderStats: {
        pendingPayment: 0,
        pendingDelivery: 0,
        pendingReceipt: 0,
        completed: 0
      }
    });
  },

  // 获取维修订单统计
  getRepairStats: function() {
    const openId = app.globalData.openId;

    if (!openId) {
      // 尝试从本地存储中获取openId
      tt.getStorage({
        key: 'openId',
        success: (res) => {
          if (res.data) {
            this.fetchRepairStats(res.data);
          }
        }
      });
      return;
    }

    this.fetchRepairStats(openId);
  },

  // 获取维修订单统计数据
  fetchRepairStats: function(openId) {
    const api = require('../../utils/api');
    api.getRepairOrderStats(openId).then(res => {
      if (res.success && res.stats) {
        this.setData({
          repairStats: res.stats
        });
      } else {
        console.error('获取维修订单统计失败:', res.message);
      }
    }).catch(err => {
      console.error('获取维修订单统计请求失败:', err);
    });
  },

  // 登录
  login: function () {
    console.log('🔐 用户点击登录按钮');

    // 直接获取用户信息，必须由用户点击手势触发
    tt.getUserProfile({
      desc: '用于完善会员资料',
      success: (userRes) => {
        console.log('✅ 获取用户信息成功:', userRes.userInfo.nickName);
        const userInfo = userRes.userInfo;

        // 获取用户信息后，再调用login获取code
        tt.login({
          success: (res) => {
            if (res.code) {
              console.log('✅ 获取登录code成功:', res.code);
              const api = require('../../utils/api');

              // 使用完整登录API
              api.login(res.code, userInfo).then(loginRes => {
                if (loginRes.success) {
                  console.log('✅ 后端登录成功:', loginRes);

                  // 使用新的认证系统保存登录状态
                  const success = auth.saveLoginState(
                    loginRes.openId,
                    loginRes.userInfo || userInfo,
                    loginRes.sessionToken
                  );

                  if (success) {
                    // 更新页面数据
                    this.setData({
                      userInfo: loginRes.userInfo || userInfo
                    });

                    // 刷新统计数据
                    this.getRepairStats();

                    tt.showToast({
                      title: '登录成功',
                      icon: 'success'
                    });

                    console.log('✅ 登录状态保存成功');
                  } else {
                    console.error('❌ 登录状态保存失败');
                  }
                } else {
                  console.error('❌ 后端登录失败:', loginRes.message);
                  tt.showToast({
                    title: '登录失败',
                    icon: 'none'
                  });
                }
              }).catch(err => {
                console.error('❌ 登录请求失败:', err);
                tt.showToast({
                  title: '登录失败，请重试',
                  icon: 'none'
                });
              });

                        // 方式2：简化登录，只发送code到后端换取openId
                        // 使用简化登录
                        /*
                        api.code2Session(res.code).then(sessionRes => {
                          if (sessionRes.success) {
                            // 登录成功，保存openId和sessionKey
                            app.globalData.openId = sessionRes.openId;
                            app.globalData.sessionKey = sessionRes.sessionKey;
                            app.globalData.isLoggedIn = true;

                            if (!sessionRes.isNewUser && sessionRes.userId) {
                              app.globalData.userId = sessionRes.userId;
                            }

                            console.log('获取openId成功:', sessionRes.openId);

                            // 存储信息到本地
                            tt.setStorage({
                              key: 'sessionInfo',
                              data: {
                                openId: sessionRes.openId,
                                sessionKey: sessionRes.sessionKey,
                                userId: sessionRes.userId
                              }
                            });

                            // 单独存储openId，方便其他页面使用
                            tt.setStorage({
                              key: 'openId',
                              data: sessionRes.openId
                            });

                            // 更新页面数据
                            this.setData({
                              userInfo: {
                                ...userInfo,
                                userId: sessionRes.userId
                              }
                            });

                            tt.showToast({
                              title: '登录成功',
                              icon: 'success'
                            });
                          } else {
                            console.error('获取openId失败:', sessionRes.message);
                            tt.showToast({
                              title: '登录失败',
                              icon: 'none'
                            });
                          }
                        }).catch(err => {
                          console.error('获取openId请求失败:', err);
                          tt.showToast({
                            title: '登录失败，请重试',
                            icon: 'none'
                          });
                        });
                        */
                      } else {
                        console.log('Login failed:', res.errMsg);
                        tt.showToast({
                          title: '登录失败',
                          icon: 'none'
                        });
                      }
                    },
                    fail: (err) => {
                      console.error('Login error:', err);
                      tt.showToast({
                        title: '登录失败',
                        icon: 'none'
                      });
                    }
                  });
                },
                fail: (err) => {
                  console.error('Get user info failed:', err);
                  tt.showToast({
                    title: '获取用户信息失败',
                    icon: 'none'
                  });
                }
              });

  },

  // 跳转到订单列表
  goToOrderList: function(e) {
    const status = e.currentTarget.dataset.status;

    tt.showToast({
      title: '订单列表功能开发中',
      icon: 'none'
    });

    // 实际开发中应该跳转到订单列表页面
    // tt.navigateTo({
    //   url: `/pages/order_list/order_list?status=${status}`
    // });
  },

  // 跳转到售后服务
  goToAfterSale: function() {
    tt.showToast({
      title: '售后服务功能开发中',
      icon: 'none'
    });
  },

  // 跳转到维修列表
  goToRepairList: function(e) {
    try {
      // 获取状态参数
      const status = e.currentTarget.dataset.status;
      console.log('跳转到维修列表，状态:', status);

      // 根据状态跳转到不同的维修列表页面
      if (status === 'pending') {
        console.log('准备跳转到待接单页面');
        tt.navigateTo({
          url: '/pages/repair_list_pending/repair_list_pending',
          success: function() {
            console.log('跳转到待接单页面成功');
          },
          fail: function(err) {
            console.error('跳转到待接单页面失败:', err);
            tt.showToast({
              title: '页面跳转失败',
              icon: 'none'
            });
          }
        });
      } else if (status === 'accepted') {
        console.log('准备跳转到待上门页面');
        tt.navigateTo({
          url: '/pages/repair_list_accepted/repair_list_accepted',
          success: function() {
            console.log('跳转到待上门页面成功');
          },
          fail: function(err) {
            console.error('跳转到待上门页面失败:', err);
            tt.showToast({
              title: '页面跳转失败',
              icon: 'none'
            });
          }
        });
      } else if (status === 'processing') {
        console.log('准备跳转到维修中页面');
        tt.navigateTo({
          url: '/pages/repair_list_processing/repair_list_processing',
          success: function() {
            console.log('跳转到维修中页面成功');
          },
          fail: function(err) {
            console.error('跳转到维修中页面失败:', err);
            tt.showToast({
              title: '页面跳转失败',
              icon: 'none'
            });
          }
        });
      } else if (status === 'completed') {
        console.log('准备跳转到已完成页面');
        tt.navigateTo({
          url: '/pages/repair_list_completed/repair_list_completed',
          success: function() {
            console.log('跳转到已完成页面成功');
          },
          fail: function(err) {
            console.error('跳转到已完成页面失败:', err);
            tt.showToast({
              title: '页面跳转失败',
              icon: 'none'
            });
          }
        });
      } else {
        // 如果没有状态参数或状态参数不匹配，跳转到通用维修列表页面
        console.log('准备跳转到通用维修列表页面');
        tt.navigateTo({
          url: '/pages/repair_list/repair_list',
          success: function() {
            console.log('跳转到通用维修列表页面成功');
          },
          fail: function(err) {
            console.error('跳转到通用维修列表页面失败:', err);
            tt.showToast({
              title: '页面跳转失败',
              icon: 'none'
            });
          }
        });
      }
    } catch (error) {
      console.error('跳转异常:', error);
      tt.showToast({
        title: '页面跳转异常',
        icon: 'none'
      });
    }
  },

  // 跳转到地址列表
  goToAddressList: function() {
    tt.navigateTo({
      url: '/pages/address_list/address_list'
    });
  },

  // 跳转到收藏列表
  goToFavorites: function() {
    tt.navigateTo({
      url: '/pages/favorites/favorites'
    });
  },

  // 跳转到评价列表
  goToReviews: function() {
    tt.showToast({
      title: '评价列表功能开发中',
      icon: 'none'
    });
  },

  // 跳转到客服中心
  goToCustomerService: function() {
    // 检查用户是否已登录
    const openId = tt.getStorageSync('openId');
    if (!openId) {
      tt.showToast({
        title: '请先登录',
        icon: 'none'
      });
      // 跳转到登录页面
      setTimeout(() => {
        tt.navigateTo({
          url: '/pages/login/login'
        });
      }, 1500);
      return;
    }

    // 跳转到在线客服页面
    tt.navigateTo({
      url: '/pages/customer_service/customer_service'
    });
  },

  // 跳转到设置页面
  goToSettings: function() {
    tt.showToast({
      title: '设置功能开发中',
      icon: 'none'
    });
  },

  // 跳转到帮助中心
  goToHelpCenter: function() {
    tt.showToast({
      title: '帮助中心功能开发中',
      icon: 'none'
    });
  },

  // 跳转到加入我们
  goToAboutUs: function() {
    tt.navigateTo({
      url: '/pages/join_us/join_us'
    });
  },

  // 退出登录
  logout: function() {
    tt.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          // 清除登录状态
          auth.clearLoginState();

          // 更新页面数据
          this.setData({
            userInfo: null,
            repairStats: {
              pending: 0,
              accepted: 0,
              processing: 0,
              completed: 0
            }
          });

          tt.showToast({
            title: '已退出登录',
            icon: 'success'
          });

          console.log('✅ 用户已退出登录');
        }
      }
    });
  },

  // 检查登录状态
  checkLoginStatus: function() {
    const isLoggedIn = auth.isLoggedIn();
    console.log('🔍 当前登录状态:', isLoggedIn);
    return isLoggedIn;
  }
})
