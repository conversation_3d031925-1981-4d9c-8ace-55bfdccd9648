.container {
  padding: 30rpx;
  padding-bottom: 120rpx;
  background-color: #f5f5f5;
}

/* 步骤指示器 */
.steps-container {
  margin-bottom: 5rpx;
  overflow: hidden;
}

.steps {
  display: flex;
  white-space: nowrap;
  padding: 10rpx 0;
}

.step {
  display: inline-block;
  width: 180rpx;
  text-align: center;
  position: relative;
}

.step:not(:last-child):after {
  content: '';
  position: absolute;
  top: 24rpx;
  right: -50%;
  width: 100%;
  height: 4rpx;
  background-color: #e0e0e0;
  z-index: 1;
}

.step.active:not(:last-child):after {
  background-color: #1e88e5;
}

.step-number {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background-color: #e0e0e0;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto 16rpx;
  position: relative;
  z-index: 2;
  font-size: 24rpx;
}

.step.active .step-number {
  background-color: #1e88e5;
}

.step-label {
  font-size: 24rpx;
  color: #757575;
  white-space: nowrap;
}

.step.active .step-label {
  color: #1e88e5;
  font-weight: 500;
}

.form-group {
  margin-bottom: 32rpx;
}

.form-label {
  display: block;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.form-control {
  height: 88rpx;
  width: 100%;
  padding: 0 24rpx;
  border: 1px solid #e0e0e0;
  border-radius: 16rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  line-height: 88rpx; /* 与高度相同，确保文字垂直居中 */
}

textarea.form-control {
  height: 200rpx;
  line-height: 1.5; /* 恢复正常的行高 */
  padding: 24rpx; /* 恢复正常的内边距 */
}

.form-control:focus {
  border-color: #1e88e5;
  outline: none;
}

.picker {
  width: 100%;
  height: 88rpx;
  padding: 0 24rpx;
  border: 1px solid #e0e0e0;
  border-radius: 16rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  line-height: 88rpx; /* 与高度相同，确保文字垂直居中 */
  display: flex;
  align-items: center;
}

.picker.placeholder {
  color: #999;
}

/* 地址选择器 */
.address-selector {
  background-color: white;
  border-radius: 16rpx;
  border: 1px solid #e0e0e0;
  overflow: hidden;
}

.address-placeholder {
  padding: 0 24rpx;
  height: 88rpx;
  display: flex;
  align-items: center;
  color: #999;
  font-size: 28rpx;
}

.address-placeholder .iconfont {
  margin-right: 16rpx;
}

.address-placeholder .icon-right {
  margin-left: auto;
  margin-right: 0;
  color: #ccc;
}

.selected-address {
  padding: 0 24rpx;
  height: 88rpx;
  display: flex;
  align-items: center;
}

.address-info {
  flex: 1;
}

.address-header {
  margin-bottom: 12rpx;
}

.address-header .name {
  font-size: 32rpx;
  font-weight: 500;
  margin-right: 16rpx;
}

.address-header .phone {
  font-size: 28rpx;
  color: #666;
}

.address-detail {
  font-size: 28rpx;
  color: #333;
  line-height: 1.4;
}

.selected-address .icon-right {
  color: #ccc;
  margin-left: 16rpx;
}

.image-uploader {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.image-item {
  width: 160rpx;
  height: 160rpx;
  border-radius: 16rpx;
  overflow: hidden;
  position: relative;
}

.image-item image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-delete {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  font-size: 24rpx;
}

.image-add {
  width: 160rpx;
  height: 160rpx;
  border: 1px dashed #e0e0e0;
  border-radius: 16rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #757575;
  font-size: 48rpx;
}

/* 服务类型选择器 */
.service-type-selector {
  display: flex;
  gap: 24rpx;
}

.service-type-item {
  flex: 1;
  height: 120rpx;
  border: 1px solid #e0e0e0;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 20rpx;
  background-color: white;
}

.service-type-item.active {
  border-color: #1e88e5;
  background-color: #e3f2fd;
  color: #1e88e5;
}

.service-type-item .iconfont {
  font-size: 48rpx;
  margin-bottom: 12rpx;
}

/* 底部按钮 */
.bottom-btn-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx 30rpx;
  background-color: white;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
}

/* 价格卡片 */
.price-card {
  margin-bottom: 120rpx;
}

.price {
  color: #f44336;
  font-weight: 600;
  font-size: 36rpx;
}

/* 卡片样式 */
.card {
  margin-top: -100rpx;
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}
