<view class="container">
  <!-- 步骤指示器 -->
  <view class="steps-container">
    <scroll-view scroll-x="true" class="steps" show-scrollbar="false">
      <view class="step {{currentStep >= 1 ? 'active' : ''}}">
        <view class="step-number">1</view>
        <view class="step-label">填写信息</view>
      </view>
      <view class="step {{currentStep >= 2 ? 'active' : ''}}">
        <view class="step-number">2</view>
        <view class="step-label">选择时间</view>
      </view>
      <view class="step {{currentStep >= 3 ? 'active' : ''}}">
        <view class="step-number">3</view>
        <view class="step-label">提交订单</view>
      </view>
      <view class="step {{currentStep >= 4 ? 'active' : ''}}">
        <view class="step-number">4</view>
        <view class="step-label">等待上门</view>
      </view>
    </scroll-view>
  </view>

  <!-- 表单 -->
  <view class="card">
    <form bindsubmit="submitForm">
      <!-- 故障类型 -->
      <view class="form-group">
        <view class="form-label">故障类型</view>
        <picker bindchange="onFaultTypeChange" value="{{faultTypeIndex}}" range="{{faultTypes}}" range-key="name">
          <view class="picker {{faultTypeIndex === -1 ? 'placeholder' : ''}}">
            {{faultTypeIndex === -1 ? '请选择故障类型' : faultTypes[faultTypeIndex].name}}
          </view>
        </picker>
      </view>

      <!-- 充电桩型号 -->
      <view class="form-group">
        <view class="form-label">充电桩型号</view>
        <input class="form-control" name="model" placeholder="请输入充电桩型号" value="{{formData.model}}" bindinput="onModelInput" />
      </view>

      <!-- 故障描述 -->
      <view class="form-group">
        <view class="form-label">故障描述</view>
        <textarea class="form-control" name="description" placeholder="请详细描述故障情况，以便技师提前了解" value="{{formData.description}}" bindinput="onDescriptionInput" />
      </view>

      <!-- 联系信息 -->
      <view class="form-group">
        <view class="form-label">联系人</view>
        <input class="form-control" name="name" placeholder="请输入联系人姓名" value="{{formData.name}}" bindinput="onNameInput" />
      </view>

      <view class="form-group">
        <view class="form-label">联系电话</view>
        <input class="form-control" type="number" name="phone" placeholder="请输入联系电话" value="{{formData.phone}}" bindinput="onPhoneInput" />
      </view>

      <!-- 地址 -->
      <view class="form-group">
        <view class="form-label">维修地址</view>
        <view class="address-selector" bindtap="goToAddressList">
          <view tt:if="{{!formData.addressId}}" class="address-placeholder">
            <text class="iconfont icon-map-marker"></text>
            <text>请选择维修地址</text>
            <text class="iconfont icon-right"></text>
          </view>
          <view tt:else class="selected-address">
            <view class="address-info">
              <view class="address-header">
                <text class="name">{{formData.name}}</text>
                <text class="phone">{{formData.phone}}</text>
              </view>
              <view class="address-detail">{{formData.fullAddress}}</view>
            </view>
            <text class="iconfont icon-right"></text>
          </view>
        </view>
      </view>

      <!-- 服务方式 -->
      <view class="form-group">
        <view class="form-label">服务方式</view>
        <view class="service-type-selector">
          <view class="service-type-item {{formData.serviceType === 'home' ? 'active' : ''}}" bindtap="selectServiceType" data-type="home">
            <text class="iconfont icon-user-cog"></text>
            <text>上门维修</text>
          </view>
          <view class="service-type-item {{formData.serviceType === 'remote' ? 'active' : ''}}" bindtap="selectServiceType" data-type="remote">
            <text class="iconfont icon-headset"></text>
            <text>远程指导</text>
          </view>
        </view>
      </view>

      <!-- 故障照片 -->
      <view class="form-group">
        <view class="form-label">故障照片（选填）</view>
        <view class="image-uploader">
          <view class="image-item" tt:for="{{formData.images}}" tt:key="*this">
            <image src="{{item}}" mode="aspectFill" bindtap="previewImage" data-url="{{item}}" />
            <view class="image-delete" catchtap="deleteImage" data-index="{{index}}">
              <text class="iconfont icon-close"></text>
            </view>
          </view>
          <view class="image-add" bindtap="chooseImage" tt:if="{{formData.images.length < 3}}">
            <text class="iconfont icon-plus"></text>
          </view>
        </view>
        <view class="text-xs text-light mt-2">上传故障照片有助于技师提前了解情况</view>
      </view>
    </form>
  </view>

  <!-- 价格信息 -->
  <view class="card price-card">
    <view class="flex-between">
      <view>上门检测费</view>
      <view class="price">¥50.00</view>
    </view>
    <view class="text-xs text-light mt-2">注：上门检测费用将在维修服务完成后从维修费用中扣除</view>
  </view>

  <!-- 提交按钮 -->
  <view class="bottom-btn-container">
    <button class="btn btn-block" bindtap="nextStep">下一步</button>
  </view>
</view>
