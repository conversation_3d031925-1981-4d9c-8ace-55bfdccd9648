.container {
  padding: 30rpx;
}

.steps {
  display: flex;
  margin-bottom: 48rpx;
}

.step {
  flex: 1;
  text-align: center;
  position: relative;
}

.step:not(:last-child):after {
  content: '';
  position: absolute;
  top: 24rpx;
  right: -50%;
  width: 100%;
  height: 4rpx;
  background-color: #e0e0e0;
  z-index: 1;
}

.step.active:not(:last-child):after {
  background-color: #1e88e5;
}

.step-number {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background-color: #e0e0e0;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto 16rpx;
  position: relative;
  z-index: 2;
  font-size: 24rpx;
}

.step.active .step-number {
  background-color: #1e88e5;
}

.step-label {
  font-size: 24rpx;
  color: #757575;
}

.step.active .step-label {
  color: #1e88e5;
  font-weight: 500;
}

.calendar {
  background-color: white;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.calendar-title {
  font-weight: 600;
  font-size: 32rpx;
}

.calendar-nav {
  display: flex;
  gap: 32rpx;
}

.calendar-nav-btn {
  width: 56rpx;
  height: 56rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  background-color: #f5f5f5;
  color: #333333;
}

.calendar-weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  text-align: center;
  font-size: 24rpx;
  color: #757575;
  margin-bottom: 16rpx;
}

.calendar-days {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 16rpx;
}

.calendar-day {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 72rpx;
  border-radius: 50%;
  font-size: 28rpx;
}

.calendar-day.today {
  background-color: #e3f2fd;
  color: #1e88e5;
}

.calendar-day.selected {
  background-color: #1e88e5;
  color: white;
}

.calendar-day.disabled {
  color: #e0e0e0;
  pointer-events: none;
}

.time-slots {
  background-color: white;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.time-slot-title {
  font-weight: 600;
  margin-bottom: 32rpx;
}

.time-slot-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24rpx;
}

.time-slot {
  border: 1px solid #e0e0e0;
  border-radius: 16rpx;
  padding: 20rpx 0;
  text-align: center;
  font-size: 28rpx;
}

.time-slot.selected {
  border-color: #1e88e5;
  background-color: #e3f2fd;
  color: #1e88e5;
}

.time-slot.disabled {
  color: #e0e0e0;
  background-color: #f9f9f9;
  pointer-events: none;
}
