const app = getApp()

Page({
  data: {
    weekdays: ['日', '一', '二', '三', '四', '五', '六'],
    year: 0,
    month: 0,
    days: [],
    selectedDate: null,
    selectedDateStr: '',
    timeSlots: [
      { time: '08:00-09:00', disabled: true },
      { time: '09:00-10:00', disabled: true },
      { time: '10:00-11:00', disabled: false, selected: false },
      { time: '11:00-12:00', disabled: false, selected: false },
      { time: '13:00-14:00', disabled: false, selected: false },
      { time: '14:00-15:00', disabled: false, selected: true },
      { time: '15:00-16:00', disabled: false, selected: false },
      { time: '16:00-17:00', disabled: false, selected: false },
      { time: '17:00-18:00', disabled: false, selected: false }
    ],
    selectedTimeIndex: 5, // 默认选中14:00-15:00
    selectedTimeStr: '14:00-15:00',
    openId: '' // 用户openId
  },
  
  onLoad: function () {
    console.log('Repair time page loaded');
    
    // 获取openId
    this.getOpenId();
    
    // 初始化日历
    this.initCalendar();
  },
  
  // 获取openId
  getOpenId: function() {
    // 从全局数据中获取openId
    if (app.globalData.openId) {
      this.setData({
        openId: app.globalData.openId
      });
    } else {
      // 从本地存储中获取
      tt.getStorage({
        key: 'userInfo',
        success: (res) => {
          if (res.data && res.data.openId) {
            this.setData({
              openId: res.data.openId
            });
          } else {
            // 尝试从openId存储中获取
            tt.getStorage({
              key: 'openId',
              success: (res) => {
                if (res.data) {
                  this.setData({
                    openId: res.data
                  });
                } else {
                  this.showLoginTip();
                }
              },
              fail: () => {
                this.showLoginTip();
              }
            });
          }
        },
        fail: () => {
          // 尝试从openId存储中获取
          tt.getStorage({
            key: 'openId',
            success: (res) => {
              if (res.data) {
                this.setData({
                  openId: res.data
                });
              } else {
                this.showLoginTip();
              }
            },
            fail: () => {
              this.showLoginTip();
            }
          });
        }
      });
    }
  },
  
  // 显示登录提示
  showLoginTip: function() {
    // 如果没有openId，提示用户登录
    tt.showToast({
      title: '请先登录',
      icon: 'none'
    });
    
    // 跳转到用户中心页面
    tt.switchTab({
      url: '/pages/user_center/user_center'
    });
  },
  
  // 初始化日历
  initCalendar: function() {
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth() + 1;
    
    this.setData({
      year: year,
      month: month
    });
    
    this.generateDays(year, month);
    
    // 默认选中明天
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    this.selectDefaultDate(tomorrow);
  },
  
  // 生成日历天数
  generateDays: function(year, month) {
    const days = [];
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    
    // 获取当月第一天是星期几
    const firstDay = new Date(year, month - 1, 1).getDay();
    
    // 获取当月天数
    const daysInMonth = new Date(year, month, 0).getDate();
    
    // 获取上个月天数
    const daysInPrevMonth = new Date(year, month - 1, 0).getDate();
    
    // 填充上个月的日期
    for (let i = 0; i < firstDay; i++) {
      days.push({
        day: daysInPrevMonth - firstDay + i + 1,
        disabled: true
      });
    }
    
    // 填充当月的日期
    for (let i = 1; i <= daysInMonth; i++) {
      const date = new Date(year, month - 1, i);
      const isToday = date.getTime() === today.getTime();
      const isPast = date < today;
      
      days.push({
        day: i,
        date: date,
        disabled: isPast,
        today: isToday,
        selected: false
      });
    }
    
    // 填充下个月的日期
    const remainingDays = 7 - (days.length % 7);
    if (remainingDays < 7) {
      for (let i = 1; i <= remainingDays; i++) {
        days.push({
          day: i,
          disabled: true
        });
      }
    }
    
    this.setData({
      days: days
    });
  },
  
  // 选择默认日期
  selectDefaultDate: function(date) {
    const days = this.data.days;
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    
    // 如果默认日期不在当前月份，则切换到对应月份
    if (year !== this.data.year || month !== this.data.month) {
      this.setData({
        year: year,
        month: month
      });
      
      this.generateDays(year, month);
      
      // 重新获取days
      const newDays = this.data.days;
      
      // 查找对应日期
      for (let i = 0; i < newDays.length; i++) {
        if (!newDays[i].disabled && newDays[i].day === day) {
          newDays[i].selected = true;
          
          this.setData({
            days: newDays,
            selectedDate: new Date(year, month - 1, day),
            selectedDateStr: `${year}-${month < 10 ? '0' + month : month}-${day < 10 ? '0' + day : day}`
          });
          
          break;
        }
      }
    } else {
      // 查找对应日期
      for (let i = 0; i < days.length; i++) {
        if (!days[i].disabled && days[i].day === day) {
          days[i].selected = true;
          
          this.setData({
            days: days,
            selectedDate: new Date(year, month - 1, day),
            selectedDateStr: `${year}-${month < 10 ? '0' + month : month}-${day < 10 ? '0' + day : day}`
          });
          
          break;
        }
      }
    }
  },
  
  // 上个月
  prevMonth: function() {
    let year = this.data.year;
    let month = this.data.month;
    
    if (month === 1) {
      year--;
      month = 12;
    } else {
      month--;
    }
    
    this.setData({
      year: year,
      month: month
    });
    
    this.generateDays(year, month);
  },
  
  // 下个月
  nextMonth: function() {
    let year = this.data.year;
    let month = this.data.month;
    
    if (month === 12) {
      year++;
      month = 1;
    } else {
      month++;
    }
    
    this.setData({
      year: year,
      month: month
    });
    
    this.generateDays(year, month);
  },
  
  // 选择日期
  selectDate: function(e) {
    const index = e.currentTarget.dataset.index;
    const disabled = e.currentTarget.dataset.disabled;
    
    if (disabled) {
      return;
    }
    
    const days = this.data.days;
    
    // 取消之前选中的日期
    for (let i = 0; i < days.length; i++) {
      if (days[i].selected) {
        days[i].selected = false;
        break;
      }
    }
    
    // 选中新日期
    days[index].selected = true;
    
    const selectedDate = days[index].date;
    const year = selectedDate.getFullYear();
    const month = selectedDate.getMonth() + 1;
    const day = selectedDate.getDate();
    
    this.setData({
      days: days,
      selectedDate: selectedDate,
      selectedDateStr: `${year}-${month < 10 ? '0' + month : month}-${day < 10 ? '0' + day : day}`
    });
  },
  
  // 选择时间段
  selectTimeSlot: function(e) {
    const index = e.currentTarget.dataset.index;
    const disabled = e.currentTarget.dataset.disabled;
    
    if (disabled) {
      return;
    }
    
    const timeSlots = this.data.timeSlots;
    
    // 取消之前选中的时间段
    for (let i = 0; i < timeSlots.length; i++) {
      if (timeSlots[i].selected) {
        timeSlots[i].selected = false;
        break;
      }
    }
    
    // 选中新时间段
    timeSlots[index].selected = true;
    
    this.setData({
      timeSlots: timeSlots,
      selectedTimeIndex: index,
      selectedTimeStr: timeSlots[index].time
    });
  },

  // 上传图片到服务器
  uploadImages: function(imagePaths) {
    return new Promise((resolve, reject) => {
      if (!imagePaths || imagePaths.length === 0) {
        resolve([]);
        return;
      }

      const api = require('../../utils/api');
      const uploadPromises = imagePaths.map(imagePath => {
        return api.uploadImage(imagePath);
      });

      Promise.all(uploadPromises)
        .then(results => {
          const uploadedUrls = results.map(result => {
            if (result.success) {
              return result.data.url;
            } else {
              console.error('图片上传失败:', result.message);
              return null;
            }
          }).filter(url => url !== null);

          resolve(uploadedUrls);
        })
        .catch(error => {
          console.error('批量上传图片失败:', error);
          reject(error);
        });
    });
  },

  // 提交预约
  submitAppointment: function() {
    // 检查是否选择了日期和时间
    if (!this.data.selectedDate) {
      tt.showToast({
        title: '请选择预约日期',
        icon: 'none'
      });
      return;
    }
    
    if (this.data.selectedTimeIndex === -1) {
      tt.showToast({
        title: '请选择预约时间段',
        icon: 'none'
      });
      return;
    }
    
    // 检查是否有openId
    if (!this.data.openId) {
      this.showLoginTip();
      return;
    }
    
    // 获取维修信息
    const repairInfo = app.globalData.repairInfo || {};
    
    // 添加预约时间
    repairInfo.appointmentDate = this.data.selectedDateStr;
    repairInfo.appointmentTime = this.data.selectedTimeStr;
    
    // 添加openId
    repairInfo.openId = this.data.openId;
    
    // 更新全局数据
    app.globalData.repairInfo = repairInfo;
    
    // 显示加载中
    tt.showLoading({
      title: '上传图片中'
    });

    // 先上传图片，再创建订单
    this.uploadImages(repairInfo.images)
      .then(uploadedImageUrls => {
        console.log('图片上传完成:', uploadedImageUrls);

        // 更新加载提示
        tt.showLoading({
          title: '提交预约中'
        });

        // 调用API创建维修订单
        const api = require('../../utils/api');

        // 构建维修订单数据
        const repairOrderData = {
          openId: this.data.openId,
          faultType: repairInfo.faultType,
          model: repairInfo.model,
          description: repairInfo.description,
          name: repairInfo.name,
          phone: repairInfo.phone,
          addressId: repairInfo.addressId,
          fullAddress: repairInfo.fullAddress,
          serviceType: repairInfo.serviceType,
          images: uploadedImageUrls.length > 0 ? JSON.stringify(uploadedImageUrls) : null,
          appointmentDate: this.data.selectedDateStr,
          appointmentTime: this.data.selectedTimeStr
        };

        return api.createRepairOrder(repairOrderData);
      })
      .then(res => {
      tt.hideLoading();
      
      if (res.success) {
        console.log('创建维修订单成功:', res);
        
        // 保存预约信息到全局数据
        app.globalData.lastAppointment = {
          id: res.orderNo,
          ...repairInfo,
          status: 'pending',
          createTime: new Date().toISOString()
        };
        
        // 跳转到预约成功页面
        tt.redirectTo({
          url: '/pages/repair_success/repair_success?id=' + res.orderNo
        });
      } else {
        console.error('创建维修订单失败:', res.message);
        tt.showToast({
          title: res.message || '提交预约失败',
          icon: 'none'
        });
      }
      })
      .catch(err => {
        tt.hideLoading();
        console.error('提交预约失败:', err);
        tt.showToast({
          title: '提交预约失败，请重试',
          icon: 'none'
        });
      });
  }
})
