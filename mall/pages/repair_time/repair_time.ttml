<view class="container">
  <!-- 步骤指示器 -->
  <view class="steps">
    <view class="step active">
      <view class="step-number">1</view>
      <view class="step-label">填写信息</view>
    </view>
    <view class="step active">
      <view class="step-number">2</view>
      <view class="step-label">选择时间</view>
    </view>
    <view class="step">
      <view class="step-number">3</view>
      <view class="step-label">提交订单</view>
    </view>
    <view class="step">
      <view class="step-number">4</view>
      <view class="step-label">等待上门</view>
    </view>
  </view>
  
  <!-- 日历 -->
  <view class="calendar">
    <view class="calendar-header">
      <view class="calendar-title">{{year}}年{{month}}月</view>
      <view class="calendar-nav">
        <view class="calendar-nav-btn" bindtap="prevMonth">
          <text class="iconfont icon-left"></text>
        </view>
        <view class="calendar-nav-btn" bindtap="nextMonth">
          <text class="iconfont icon-right"></text>
        </view>
      </view>
    </view>
    <view class="calendar-weekdays">
      <view tt:for="{{weekdays}}" tt:key="*this">{{item}}</view>
    </view>
    <view class="calendar-days">
      <view class="calendar-day {{item.disabled ? 'disabled' : ''}} {{item.today ? 'today' : ''}} {{item.selected ? 'selected' : ''}}"
            tt:for="{{days}}" 
            tt:key="index"
            bindtap="selectDate"
            data-index="{{index}}"
            data-disabled="{{item.disabled}}">
        {{item.day}}
      </view>
    </view>
  </view>
  
  <!-- 时间段 -->
  <view class="time-slots">
    <view class="time-slot-title">选择时间段</view>
    <view class="time-slot-grid">
      <view class="time-slot {{item.disabled ? 'disabled' : ''}} {{item.selected ? 'selected' : ''}}"
            tt:for="{{timeSlots}}" 
            tt:key="index"
            bindtap="selectTimeSlot"
            data-index="{{index}}"
            data-disabled="{{item.disabled}}">
        {{item.time}}
      </view>
    </view>
  </view>
  
  <!-- 已选择时间 -->
  <view class="card">
    <view class="flex-between">
      <view>已选择</view>
      <view class="text-primary">{{selectedDateStr}} {{selectedTimeStr}}</view>
    </view>
  </view>
  
  <!-- 提交按钮 -->
  <view class="mt-3">
    <button class="btn btn-block" bindtap="submitAppointment">提交预约</button>
  </view>
</view>
