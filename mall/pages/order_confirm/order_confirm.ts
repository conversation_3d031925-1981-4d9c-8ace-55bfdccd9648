const app = getApp()

Page({
  data: {
    orderItems: [],
    address: null,
    totalAmount: '0.00',
    shippingFee: '0.00',
    discount: '0.00',
    actualAmount: '0.00',
    invoice: '',
    remark: ''
  },

  onLoad: function () {

    // 获取结算商品
    this.getCheckoutItems();

    // 获取默认地址
    this.getDefaultAddress();
  },

  onShow: function () {
    // 页面显示时刷新数据
    // 注意：如果是从地址列表页面返回，地址信息已经通过 setAddressFromList 方法设置
    // 这里主要是处理其他情况下的刷新

    // 如果没有地址信息，尝试获取默认地址
    if (!this.data.address) {
      this.getDefaultAddress();
    }
  },

  // 获取结算商品
  getCheckoutItems: function() {
    const checkoutItems = app.globalData.checkoutItems || [];

    if (checkoutItems.length === 0) {
      tt.showToast({
        title: '请先选择商品',
        icon: 'none'
      });

      setTimeout(() => {
        tt.navigateBack();
      }, 1500);

      return;
    }

    this.setData({
      orderItems: checkoutItems
    });

    // 计算订单金额
    this.calculateOrderAmount();
  },

  // 获取默认地址
  getDefaultAddress: function() {
    // 从本地存储中获取地址列表
    const addresses = tt.getStorageSync('addresses') || [];

    if (addresses.length > 0) {
      // 先查找默认地址
      let defaultAddress = addresses.find(item => item.isDefault);

      // 如果没有默认地址，使用第一个地址
      if (!defaultAddress && addresses.length > 0) {
        defaultAddress = addresses[0];
      }

      if (defaultAddress) {
        this.setData({
          address: defaultAddress
        });
        return;
      }
    }

    // 如果没有地址，使用示例数据
    this.setData({
      address: null
    });
  },

  // 计算订单金额
  calculateOrderAmount: function() {
    let total = 0;

    // 计算商品总金额
    this.data.orderItems.forEach(item => {
      total += item.price * item.quantity;
    });

    // 运费（示例固定值，实际应根据地址和商品计算）
    const shippingFee = 0;

    // 优惠金额（示例固定值，实际应根据优惠规则计算）
    const discount = total >= 200 ? 30 : 0;

    // 实付金额
    const actualAmount = total + shippingFee - discount;

    this.setData({
      totalAmount: total.toFixed(2),
      shippingFee: shippingFee.toFixed(2),
      discount: discount.toFixed(2),
      actualAmount: actualAmount.toFixed(2)
    });
  },

  // 选择收货地址
  chooseAddress: function() {
    // 跳转到地址选择页面
    tt.navigateTo({
      url: '/pages/address_list/address_list?from=order_confirm'
    });
  },

  // 从地址列表页面返回时设置地址
  setAddressFromList: function(address) {
    if (!address) return;

    this.setData({
      address: address
    });
  },

  // 显示发票选择器
  showInvoiceSelector: function() {
    tt.showActionSheet({
      itemList: ['不开发票', '电子发票', '纸质发票'],
      success: (res) => {
        if (res.tapIndex === 0) {
          this.setData({
            invoice: '不开发票'
          });
        } else if (res.tapIndex === 1) {
          this.setData({
            invoice: '电子发票'
          });
        } else if (res.tapIndex === 2) {
          this.setData({
            invoice: '纸质发票'
          });
        }
      }
    });
  },

  // 显示备注输入框
  showRemarkInput: function() {
    tt.showModal({
      title: '订单备注',
      content: '请输入订单备注信息',
      editable: true,
      placeholderText: '选填，建议先与商家沟通确认',
      success: (res) => {
        if (res.confirm && res.content) {
          this.setData({
            remark: res.content
          });
        }
      }
    });
  },

  // 提交订单
  submitOrder: function() {
    // 检查是否选择了收货地址
    if (!this.data.address) {
      tt.showToast({
        title: '请选择收货地址',
        icon: 'none'
      });
      return;
    }

    // 显示加载中
    tt.showLoading({
      title: '提交订单中'
    });

    // 模拟提交订单
    // 实际开发中应该调用API提交订单
    setTimeout(() => {
      tt.hideLoading();

      // 生成订单号
      const orderId = 'ORDER' + Date.now();

      // 保存订单信息到全局数据
      app.globalData.lastOrder = {
        id: orderId,
        items: this.data.orderItems,
        totalAmount: this.data.totalAmount,
        shippingFee: this.data.shippingFee,
        discount: this.data.discount,
        actualAmount: this.data.actualAmount,
        address: this.data.address,
        invoice: this.data.invoice,
        remark: this.data.remark,
        createTime: new Date().toISOString(),
        status: 'pending'
      };

      // 清空购物车中已结算的商品
      this.clearCheckoutItems();

      // 跳转到订单成功页面
      tt.redirectTo({
        url: '/pages/order_success/order_success?id=' + orderId
      });
    }, 1500);
  },

  // 清空已结算商品
  clearCheckoutItems: function() {
    // 获取全局购物车数据
    const cartItems = app.globalData.cartItems || [];
    const checkoutItemIds = this.data.orderItems.map(item => item.id);

    // 过滤掉已结算的商品
    const newCartItems = cartItems.filter(item => !checkoutItemIds.includes(item.id));

    // 更新全局购物车数据
    app.globalData.cartItems = newCartItems;

    // 清空结算商品
    app.globalData.checkoutItems = [];
  }
})
