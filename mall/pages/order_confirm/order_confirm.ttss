.container {
  padding: 30rpx 30rpx 120rpx;
}

.address-card {
  display: flex;
  align-items: flex-start;
  padding: 32rpx;
  background-color: white;
  border-radius: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.address-icon {
  margin-right: 24rpx;
  color: #1e88e5;
  font-size: 40rpx;
}

.address-info {
  flex: 1;
}

.address-contact {
  font-weight: 600;
  margin-bottom: 8rpx;
}

.address-detail {
  font-size: 28rpx;
  color: #333333;
  line-height: 1.4;
}

.address-placeholder {
  color: #757575;
}

.address-arrow {
  color: #757575;
}

.order-item {
  display: flex;
  padding: 32rpx;
  background-color: white;
  border-radius: 24rpx 24rpx 0 0;
  margin-bottom: 2rpx;
}

.order-item-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 16rpx;
  object-fit: cover;
  margin-right: 24rpx;
}

.order-item-info {
  flex: 1;
}

.order-item-title {
  font-weight: 500;
  margin-bottom: 8rpx;
  font-size: 28rpx;
}

.order-item-spec {
  font-size: 24rpx;
  color: #757575;
  margin-bottom: 16rpx;
}

.order-item-price {
  color: #f44336;
  font-weight: 600;
}

.order-section {
  background-color: white;
  padding: 32rpx;
  margin-bottom: 24rpx;
  border-radius: 0 0 24rpx 24rpx;
}

.order-section-title {
  font-weight: 600;
  margin-bottom: 24rpx;
}

.order-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  padding: 24rpx 30rpx;
  display: flex;
  align-items: center;
  border-top: 1px solid #e0e0e0;
  z-index: 100;
}

.total-price {
  flex: 1;
  text-align: right;
  margin-right: 24rpx;
}

.price-value {
  color: #f44336;
  font-weight: 600;
  font-size: 36rpx;
}

.submit-btn {
  background-color: #1e88e5;
  color: white;
  padding: 16rpx 40rpx;
  border-radius: 40rpx;
  font-weight: 500;
}
