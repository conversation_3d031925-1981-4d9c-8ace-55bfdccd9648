<view class="container">
  <!-- 收货地址 -->
  <view class="address-card" bindtap="chooseAddress">
    <view class="address-icon">
      <text class="iconfont icon-map-marker"></text>
    </view>
    <view class="address-info" tt:if="{{address}}">
      <view class="address-contact">{{address.name}} {{address.phone}}</view>
      <view class="address-detail">{{address.province}}{{address.city}}{{address.district}} {{address.detail}}</view>
    </view>
    <view class="address-info" tt:else>
      <view class="address-placeholder">请选择收货地址</view>
    </view>
    <view class="address-arrow">
      <text class="iconfont icon-right"></text>
    </view>
  </view>
  
  <!-- 订单商品 -->
  <view class="order-item" tt:for="{{orderItems}}" tt:key="id">
    <image src="{{item.image}}" class="order-item-image" mode="aspectFill" />
    <view class="order-item-info">
      <view class="order-item-title">{{item.name}}</view>
      <view class="order-item-spec">规格：{{item.spec}}</view>
      <view class="flex-between">
        <view class="order-item-price">¥{{item.price}}</view>
        <view class="text-light">x{{item.quantity}}</view>
      </view>
    </view>
  </view>
  
  <!-- 订单金额 -->
  <view class="order-section">
    <view class="flex-between mb-2">
      <view>商品金额</view>
      <view>¥{{totalAmount}}</view>
    </view>
    <view class="flex-between mb-2">
      <view>运费</view>
      <view>¥{{shippingFee}}</view>
    </view>
    <view class="flex-between">
      <view>优惠</view>
      <view class="text-error">-¥{{discount}}</view>
    </view>
  </view>
  
  <!-- 支付方式 -->
  <view class="card">
    <view class="order-section-title">支付方式</view>
    <view class="flex-between">
      <view class="flex gap-2 align-items-center">
        <text class="iconfont icon-wechat" style="color: #07C160; font-size: 40rpx;"></text>
        <text>微信支付</text>
      </view>
      <view class="checkbox checked">
        <text class="iconfont icon-check"></text>
      </view>
    </view>
  </view>
  
  <!-- 发票 -->
  <view class="card" bindtap="showInvoiceSelector">
    <view class="flex-between">
      <view>发票</view>
      <view class="flex gap-2 align-items-center text-light">
        <text>{{invoice || '不开发票'}}</text>
        <text class="iconfont icon-right"></text>
      </view>
    </view>
  </view>
  
  <!-- 订单备注 -->
  <view class="card" bindtap="showRemarkInput">
    <view class="flex-between">
      <view>订单备注</view>
      <view class="flex gap-2 align-items-center text-light">
        <text>{{remark || '选填，建议先与商家沟通确认'}}</text>
        <text class="iconfont icon-right"></text>
      </view>
    </view>
  </view>
  
  <!-- 底部结算栏 -->
  <view class="order-footer">
    <view class="total-price">
      实付款: <text class="price-value">¥{{actualAmount}}</text>
    </view>
    <view class="submit-btn" bindtap="submitOrder">提交订单</view>
  </view>
</view>
