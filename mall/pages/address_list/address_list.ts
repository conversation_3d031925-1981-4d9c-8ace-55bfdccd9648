const app = getApp()

Page({
  data: {
    addresses: [],
    showForm: false,
    editingAddress: null,
    formData: {
      id: '',
      name: '',
      phone: '',
      region: ['', '', ''],
      province: '',
      city: '',
      district: '',
      address: '',
      tag: '家',
      isDefault: false
    },
    fromPage: '', // 来源页面
    openId: '' // 用户openId
  },

  onLoad: function (options) {

    // 记录来源页面
    if (options.from) {
      this.setData({
        fromPage: options.from
      });
    }

    // 获取openId
    this.getOpenId();
  },

  onShow: function() {
    // 如果已经有openId，则获取地址列表
    if (this.data.openId) {
      this.getAddressList();
    }
  },

  // 获取openId
  getOpenId: function() {
    // 从全局数据中获取openId
    if (app.globalData.openId) {
      this.setData({
        openId: app.globalData.openId
      });
      this.getAddressList();
    } else {
      // 从本地存储中获取
      tt.getStorage({
        key: 'userInfo',
        success: (res) => {
          if (res.data && res.data.openId) {
            this.setData({
              openId: res.data.openId
            });
            this.getAddressList();
          } else {
            // 尝试从openId存储中获取
            tt.getStorage({
              key: 'openId',
              success: (res) => {
                if (res.data) {
                  this.setData({
                    openId: res.data
                  });
                  this.getAddressList();
                } else {
                  this.showLoginTip();
                }
              },
              fail: () => {
                this.showLoginTip();
              }
            });
          }
        },
        fail: () => {
          // 尝试从openId存储中获取
          tt.getStorage({
            key: 'openId',
            success: (res) => {
              if (res.data) {
                this.setData({
                  openId: res.data
                });
                this.getAddressList();
              } else {
                this.showLoginTip();
              }
            },
            fail: () => {
              this.showLoginTip();
            }
          });
        }
      });
    }
  },

  // 显示登录提示
  showLoginTip: function() {
    // 如果没有openId，提示用户登录
    tt.showToast({
      title: '请先登录',
      icon: 'none'
    });
    
    // 跳转到用户中心页面
    tt.switchTab({
      url: '/pages/user_center/user_center'
    });
  },

  // 获取地址列表
  getAddressList: function() {
    if (!this.data.openId) {
      return;
    }

    tt.showLoading({
      title: '加载中...'
    });

    const api = require('../../utils/api');
    api.getAddressList(this.data.openId).then(res => {
      tt.hideLoading();
      
      if (res.success) {
        this.setData({
          addresses: res.addresses || []
        });
      } else {
        tt.showToast({
          title: '获取地址列表失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      tt.hideLoading();
      tt.showToast({
        title: '获取地址列表失败',
        icon: 'none'
      });
    });
  },

  // 选择地址
  selectAddress: function(e) {
    const id = e.currentTarget.dataset.id;
    const address = this.data.addresses.find(item => item.id === id);

    // 如果是从其他页面跳转过来选择地址，则返回并传递选中的地址
    if (this.data.fromPage === 'repair_form' || this.data.fromPage === 'order_confirm') {
      // 将选中的地址信息传递回上一个页面
      const pages = getCurrentPages();
      const prevPage = pages[pages.length - 2]; // 上一个页面

      // 调用上一个页面的方法，传递地址信息
      prevPage.setAddressFromList(address);

      // 返回上一页
      tt.navigateBack();
    }
  },

  // 设置默认地址
  setDefault: function(e) {
    const id = e.currentTarget.dataset.id;
    
    if (!this.data.openId) {
      return;
    }

    tt.showLoading({
      title: '设置中...'
    });

    const api = require('../../utils/api');
    api.setDefaultAddress(id, this.data.openId).then(res => {
      tt.hideLoading();
      
      if (res.success) {
        // 更新地址列表
        this.getAddressList();
        
        tt.showToast({
          title: '设置成功',
          icon: 'success'
        });
      } else {
        tt.showToast({
          title: '设置失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      tt.hideLoading();
      tt.showToast({
        title: '设置失败',
        icon: 'none'
      });
    });
  },

  // 编辑地址
  editAddress: function(e) {
    const id = e.currentTarget.dataset.id;
    const address = this.data.addresses.find(item => item.id === id);

    if (address) {
      this.setData({
        editingAddress: address,
        formData: {
          id: address.id,
          name: address.name,
          phone: address.phone,
          region: [address.province, address.city, address.district],
          province: address.province,
          city: address.city,
          district: address.district,
          address: address.address,
          tag: address.tag || '家',
          isDefault: address.isDefault
        },
        showForm: true
      });
    }
  },

  // 删除地址
  deleteAddress: function(e) {
    const id = e.currentTarget.dataset.id;
    
    if (!this.data.openId) {
      return;
    }

    tt.showModal({
      title: '提示',
      content: '确定要删除这个地址吗？',
      success: (res) => {
        if (res.confirm) {
          tt.showLoading({
            title: '删除中...'
          });

          const api = require('../../utils/api');
          api.deleteAddress(id, this.data.openId).then(res => {
            tt.hideLoading();
            
            if (res.success) {
              // 更新地址列表
              this.getAddressList();
              
              tt.showToast({
                title: '删除成功',
                icon: 'success'
              });
            } else {
              tt.showToast({
                title: '删除失败',
                icon: 'none'
              });
            }
          }).catch(err => {
            tt.hideLoading();
            tt.showToast({
              title: '删除失败',
              icon: 'none'
            });
          });
        }
      }
    });
  },

  // 显示地址表单
  showAddressForm: function() {
    // 重置表单数据
    this.setData({
      editingAddress: null,
      formData: {
        id: '',
        name: '',
        phone: '',
        region: ['', '', ''],
        province: '',
        city: '',
        district: '',
        address: '',
        tag: '家',
        isDefault: this.data.addresses.length === 0 // 如果没有地址，则默认设为默认地址
      },
      showForm: true
    });
  },

  // 隐藏地址表单
  hideAddressForm: function() {
    this.setData({
      showForm: false
    });
  },

  // 表单输入事件
  onNameInput: function(e) {
    this.setData({
      'formData.name': e.detail.value
    });
  },

  onPhoneInput: function(e) {
    this.setData({
      'formData.phone': e.detail.value
    });
  },

  onRegionChange: function(e) {
    const region = e.detail.value;

    this.setData({
      'formData.region': region,
      'formData.province': region[0],
      'formData.city': region[1],
      'formData.district': region[2]
    });
  },

  onAddressInput: function(e) {
    this.setData({
      'formData.address': e.detail.value
    });
  },

  // 选择标签
  selectTag: function(e) {
    const tag = e.currentTarget.dataset.tag;

    this.setData({
      'formData.tag': tag
    });
  },

  // 设置默认地址开关
  onDefaultChange: function(e) {
    this.setData({
      'formData.isDefault': e.detail.value
    });
  },

  // 保存地址
  saveAddress: function() {
    const formData = this.data.formData;
    
    if (!this.data.openId) {
      tt.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    // 表单验证
    if (!formData.name) {
      tt.showToast({
        title: '请输入联系人姓名',
        icon: 'none'
      });
      return;
    }

    if (!formData.phone) {
      tt.showToast({
        title: '请输入手机号码',
        icon: 'none'
      });
      return;
    }

    // 简单的手机号验证
    if (!/^1\d{10}$/.test(formData.phone)) {
      tt.showToast({
        title: '请输入正确的手机号',
        icon: 'none'
      });
      return;
    }

    if (!formData.region[0]) {
      tt.showToast({
        title: '请选择所在地区',
        icon: 'none'
      });
      return;
    }

    if (!formData.address) {
      tt.showToast({
        title: '请输入详细地址',
        icon: 'none'
      });
      return;
    }

    // 构建地址数据
    const addressData = {
      id: formData.id || null,
      openId: this.data.openId,
      name: formData.name,
      phone: formData.phone,
      province: formData.province,
      city: formData.city,
      district: formData.district,
      address: formData.address,
      tag: formData.tag,
      isDefault: formData.isDefault
    };

    tt.showLoading({
      title: '保存中...'
    });

    const api = require('../../utils/api');
    api.saveAddress(addressData).then(res => {
      tt.hideLoading();
      
      if (res.success) {
        // 更新地址列表
        this.getAddressList();
        
        // 隐藏表单
        this.setData({
          showForm: false
        });
        
        tt.showToast({
          title: res.message || '保存成功',
          icon: 'success'
        });
      } else {
        tt.showToast({
          title: res.message || '保存失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      tt.hideLoading();
      tt.showToast({
        title: '保存失败',
        icon: 'none'
      });
    });
  }
})
