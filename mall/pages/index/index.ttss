.container {
  padding: 30rpx 30rpx 120rpx;
}

.section {
  margin-bottom: 40rpx;
}

.repair-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
}

/* 覆盖全局样式中的部分属性 */
.banner {
  height: 300rpx;
  border-radius: 24rpx;
  overflow: hidden;
  margin-bottom: 32rpx;
}

.banner image {
  width: 100%;
  height: 100%;
}

.category-grid {
  display: flex;
  justify-content: space-between;
  margin-bottom: 40rpx;
  padding: 0 10rpx;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  max-width: 25%;
}

.category-icon {
  width: 100rpx;
  height: 100rpx;
  background-color: #e3f2fd;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 16rpx;
  color: #1e88e5;
  font-size: 40rpx;
}

.category-name {
  font-size: 24rpx;
  color: #333;
  text-align: center;
  line-height: 1.2;
}