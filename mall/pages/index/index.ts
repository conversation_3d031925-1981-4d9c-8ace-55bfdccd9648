const app = getApp()
const api = require('../../utils/api');

Page({
  data: {
    banners: [
      {
        id: 1,
        image: '/images/轮播图/轮播图1.png',
        type: 'repair'
      },
      {
        id: 2,
        image: '/images/轮播图/轮播图2.png',
        type: 'product'
      },
      {
        id: 3,
        image: 'https://images.unsplash.com/photo-1581092580497-e0d23cbdf1dc?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80',
        type: 'service'
      }
    ],
    categories: [
      {
        id: 1,
        name: '故障报修',
        image:'/images/icons/故障报修.png',
        type: 'repair'
      },
      {
        id: 2,
        name: '配件产品',
        image:'/images/icons/商城.png',
        type: 'mall'
      },
      {
        id: 3,
        name: '服务网点',
        image:'/images/icons/服务网点.png',
        type: 'service'
      },
      {
        id: 4,
        name: '在线咨询',
        image:'/images/icons/在线咨询.png',
        type: 'consult'
      },
      {
        id: 5,
        name: '合作加盟',
        image:'/images/icons/关于我们.png',
        type: 'join'
      }
    ],
    hotProducts: [],
    commonFaults: [
      {
        id: 1,
        name: '无法充电',
        image: '/images/icons/无法充电.png',
        type: 'no_charging'
      },
      {
        id: 2,
        name: '充电慢',
        image: '/images/icons/充电慢.png',
        type: 'slow_charging'
      },
      {
        id: 3,
        name: '报错代码',
        image: '/images/icons/错误代码.png',
        type: 'error_code'
      }
    ]
  },

  onLoad: function () {
    // 页面加载时执行
    this.loadHotProducts();
  },

  onShow: function () {
    // 页面显示时执行

    // 更新自定义tabBar的选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 0
      });
    } else {
    }
  },

  // 加载热门产品
  loadHotProducts: function() {

    api.getHotProducts(2).then(res => {

      if (res.success && res.data && res.data.length > 0) {
        // 处理产品数据，确保图片URL正确，并限制只显示前2个
        const hotProducts = res.data.slice(0, 2).map(product => {
          return {
            id: product.id,
            name: product.name || product.shortName || '商品名称',
            price: product.price || 0,
            image: this.processImageUrl(product.mainImage)
          };
        });

        this.setData({
          hotProducts: hotProducts
        });

      } else {
        // 如果没有数据，使用默认数据
        this.setData({
          hotProducts: [
            {
              id: 1,
              name: '快充充电枪',
              price: 299.00,
              image: '/images/products/快充充电枪.png'
            },
            {
              id: 2,
              name: '5米加长型充电线缆',
              price: 199.00,
              image: '/images/products/充电线缆.png'
            }
          ]
        });
      }
    }).catch(err => {
      // 出错时使用默认数据
      this.setData({
        hotProducts: [
          {
            id: 1,
            name: '快充充电枪',
            price: 299.00,
            image: '/images/products/快充充电枪.png'
          },
          {
            id: 2,
            name: '5米加长型充电线缆',
            price: 199.00,
            image: '/images/products/充电线缆.png'
          }
        ]
      });
    });
  },

  // 处理图片URL
  processImageUrl: function(imageUrl) {
    if (!imageUrl || imageUrl.trim() === '') {
      return '/images/products/充电线缆.png'; // 默认图片
    }

    // 如果已经是完整的HTTP/HTTPS URL，直接返回
    if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
      return imageUrl;
    }

    // 如果是相对路径，添加服务器地址
    if (imageUrl.startsWith('/uploads/')) {
      return `https://localhost:8443${imageUrl}`;
    }

    // 如果只是文件名，添加完整路径
    if (!imageUrl.startsWith('/')) {
      return `https://localhost:8443/uploads/${imageUrl}`;
    }

    // 其他情况，添加服务器地址
    return `https://localhost:8443${imageUrl}`;
  },

  // 搜索框点击
  goToSearch: function() {
    tt.showToast({
      title: '搜索功能开发中',
      icon: 'none'
    });
  },

  // Banner点击
  onBannerTap: function(e) {
    const id = e.currentTarget.dataset.id;
    const banner = this.data.banners.find(item => item.id === id);

    if (banner.type === 'repair') {
      this.goToRepair();
    } else if (banner.type === 'product') {
      this.goToMall();
    } else if (banner.type === 'service') {
      this.goToServiceCenters();
    }
  },

  // 分类点击
  onCategoryTap: function(e) {
    const type = e.currentTarget.dataset.type;

    switch(type) {
      case 'repair':
        this.goToRepair();
        break;
      case 'mall':
        this.goToMall();
        break;
      case 'service':
        this.goToServiceCenters();
        break;
      case 'consult':
        this.goToConsult();
        break;
      case 'join':
        this.goToJoinUs();
        break;
    }
  },

  // 跳转到产品页面
  goToMall: function() {
    // 使用reLaunch方法跳转到产品页面，并关闭所有其他页面
    tt.reLaunch({
      url: '/pages/mall/mall',
      success: () => {
      },
      fail: (err) => {
      }
    });
  },

  // 跳转到产品详情页
  goToProductDetail: function(e) {
    const id = e.currentTarget.dataset.id;
    tt.navigateTo({
      url: `/pages/product_detail/product_detail?id=${id}`
    });
  },

  // 跳转到维修页面
  goToRepair: function() {
    // 使用reLaunch方法跳转到维修页面，并关闭所有其他页面
    tt.reLaunch({
      url: '/pages/repair/repair',
      success: () => {
      },
      fail: (err) => {
      }
    });
  },

  // 跳转到维修表单页面
  goToRepairForm: function(e) {
    const type = e.currentTarget.dataset.type;
    tt.navigateTo({
      url: `/pages/repair_form/repair_form?type=${type}`
    });
  },

  // 跳转到服务网点页面
  goToServiceCenters: function() {
    tt.navigateTo({
      url: '/pages/service_centers/service_centers'
    });
  },

  // 跳转到在线咨询
  goToConsult: function() {
    tt.navigateTo({
      url: '/pages/customer_service/customer_service'
    });
  },

  // 跳转到合作加盟页面
  goToJoinUs: function() {
    tt.navigateTo({
      url: '/pages/join_us/join_us'
    });
  },

})
