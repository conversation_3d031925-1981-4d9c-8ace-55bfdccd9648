<view class="container">
  <!-- 搜索栏 -->
  <view class="search-bar">
    <icon name="search" size="32" color="#757575"></icon>
    <input type="text" placeholder="搜索充电桩产品或维修服务" bindtap="goToSearch" />
  </view>

  <!-- 轮播图 -->
  <swiper class="banner" indicator-dots="{{true}}" autoplay="{{true}}" interval="{{3000}}" duration="{{500}}">
    <swiper-item tt:for="{{banners}}" tt:key="index">
      <image src="{{item.image}}" mode="aspectFill" bindtap="onBannerTap" data-id="{{item.id}}" />
    </swiper-item>
  </swiper>

  <!-- 快捷入口 -->
  <view class="category-grid">
    <view class="category-item" tt:for="{{categories}}" tt:key="id" bindtap="onCategoryTap" data-id="{{item.id}}" data-type="{{item.type}}">
      <view class="category-icon">
        <!-- 使用 <image> 来渲染图标，设置图片大小为40rpx -->
        <image src="{{item.image}}" mode="aspectFit" style="width: 60rpx; height: 60rpx;"></image>
      </view>
      <view class="category-name">{{item.name}}</view>
    </view>
  </view>


  <!-- 热门产品 -->
  <view class="section">
    <view class="section-title">
      <text>热门产品</text>
      <view class="more" bindtap="goToMall">查看更多 <text class="iconfont icon-right"></text></view>
    </view>
    <view class="grid">
      <view class="product-card" tt:for="{{hotProducts}}" tt:key="id" bindtap="goToProductDetail" data-id="{{item.id}}">
        <image src="{{item.image}}" mode="aspectFill" />
        <view class="info">
          <view class="title">{{item.name}}</view>
          <view class="price">¥{{item.price}}</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 维修服务 -->
  <view class="section">
    <view class="section-title">
      <text>维修服务</text>
      <view class="more" bindtap="goToRepair">查看更多 <text class="iconfont icon-right"></text></view>
    </view>
    <view class="card">
      <view class="flex-between mb-2">
        <view class="font-bold">常见故障快速修复</view>
        <view class="badge">专业维修</view>
      </view>
      <view class="grid-3">
        <view class="repair-item" tt:for="{{commonFaults}}" tt:key="id" bindtap="goToRepairForm" data-type="{{item.type}}">
          <view class="category-icon">
            <image src="{{item.image}}" mode="aspectFit" style="width: 60rpx; height: 60rpx;"></image>
          </view>
          <view class="text-sm">{{item.name}}</view>
        </view>
      </view>
      <view class="mt-3">
        <button class="btn btn-block" bindtap="goToRepair">立即报修</button>
      </view>
    </view>
  </view>


</view>

<!-- 自定义导航栏 -->
<nav-bar active="index"></nav-bar>