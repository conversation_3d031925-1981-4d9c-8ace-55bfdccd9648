.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 80px;
}

.status-bar {
  background-color: #1890ff;
  padding: 30px 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #fff;
}

.status-pending {
  background-color: #1890ff;
}

.status-accepted {
  background-color: #fa8c16;
}

.status-processing {
  background-color: #52c41a;
}

.status-completed {
  background-color: #8c8c8c;
}

.status-icon {
  margin-bottom: 10px;
}

.icon-wrapper {
  width: 60px;
  height: 60px;
  border-radius: 30px;
  background-color: rgba(255, 255, 255, 0.2);
  display: flex;
  justify-content: center;
  align-items: center;
}

.icon {
  width: 36px;
  height: 36px;
}

.status-text {
  font-size: 18px;
  font-weight: 500;
}

.card {
  background-color: #fff;
  border-radius: 8px;
  margin: 15px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.card-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.info-item {
  display: flex;
  margin-bottom: 12px;
}

.label {
  width: 80px;
  color: #999;
  font-size: 14px;
}

.value {
  flex: 1;
  color: #333;
  font-size: 14px;
}

.fee-total {
  color: #f5222d;
  font-weight: 500;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
}

.fault-image {
  width: 80px;
  height: 80px;
  margin-right: 10px;
  margin-bottom: 10px;
  border-radius: 4px;
}

/* 工程师信息卡片 */
.engineer-info .card-title {
  display: flex;
  align-items: center;
  gap: 12rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 32rpx;
}

.engineer-info .card-title .iconfont {
  color: #667eea;
  font-size: 28rpx;
}

.engineer {
  display: flex;
  align-items: flex-start;
  gap: 24rpx;
}

.engineer-avatar-container {
  position: relative;
}

.engineer-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  object-fit: cover;
  border: 4rpx solid #f0f0f0;
}

.engineer-avatar-placeholder {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 4rpx solid #f0f0f0;
}

.avatar-text {
  color: white;
  font-size: 48rpx;
  font-weight: 600;
}

.online-status {
  position: absolute;
  bottom: 8rpx;
  right: 8rpx;
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  border: 3rpx solid white;
}

.online-status.online {
  background-color: #4caf50;
}

.engineer-detail {
  flex: 1;
  min-width: 0;
}

.engineer-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 8rpx;
}

.engineer-title {
  font-size: 26rpx;
  color: #7f8c8d;
  margin-bottom: 12rpx;
}

.engineer-phone {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 24rpx;
  color: #34495e;
}

.engineer-phone .iconfont {
  color: #3498db;
  font-size: 20rpx;
}

.engineer-contact {
  display: flex;
  align-items: center;
}

.contact-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 16rpx 20rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
  font-weight: 500;
  text-align: center;
  transition: all 0.3s ease;
  height: 80rpx;
  width: 150rpx;
  box-sizing: border-box;
  gap: 6rpx;
  border: none;
}

.contact-btn .iconfont {
  font-size: 36rpx;
  line-height: 36rpx;
  height: 36rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0;
  padding: 0;
}

.contact-btn text {
  font-size: 22rpx;
  line-height: 22rpx;
  height: 22rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  margin: 0;
  padding: 0;
}

.call-btn {
  background: linear-gradient(135deg, #1e88e5 0%, #1565c0 100%);
  color: white;
  box-shadow: 0 4rpx 12rpx rgba(30, 136, 229, 0.3);
}

.call-btn .iconfont,
.call-btn text {
  color: white;
}

.contact-btn:active {
  transform: scale(0.95);
}

.call-btn:active {
  background: linear-gradient(135deg, #1565c0 0%, #0d47a1 100%);
  box-shadow: 0 2rpx 8rpx rgba(30, 136, 229, 0.4);
}

.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 10px 15px;
  display: flex;
  justify-content: center;
  gap: 20rpx; /* 按钮间距 */
  box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.05);
}

.footer .btn {
  flex: 0 0 auto;
  min-width: 160rpx;      /* 原来是 200rpx，改小 */
  height: 80rpx;          /* 设置高度更小 */
  font-size: 30rpx;       /* 字体缩小一点 */
  padding: 0 20rpx;       /* 左右内边距 */
  line-height: 80rpx;     /* 垂直居中对齐 */
  border-radius: 12rpx;   /* 圆角小一些，更简洁 */
}

/* .btn {
  margin-left: 10px;
  font-size: 14px;
  padding: 8px 20px;
  border-radius: 4px;
} */

.btn-primary {
  background-color: #1890ff;
  color: #fff;
  border: none;
}

.btn-default {
  background-color: #fff;
  color: #666;
  border: 1px solid #d9d9d9;
}

/* 维修进度时间轴样式 */
.progress-timeline {
  padding: 20px 0;
}

.timeline-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20px;
  position: relative;
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.timeline-item:not(:last-child)::after {
  content: '';
  position: absolute;
  left: 8px;
  top: 20px;
  width: 2px;
  height: calc(100% + 20px);
  background-color: #e9ecef;
}

.timeline-item.active:not(:last-child)::after {
  background-color: #007bff;
}

.timeline-dot {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: #e9ecef;
  margin-right: 15px;
  margin-top: 2px;
  flex-shrink: 0;
  position: relative;
  z-index: 1;
}

.timeline-item.active .timeline-dot {
  background-color: #007bff;
}

.timeline-content {
  flex: 1;
}

.timeline-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 5px;
}

.timeline-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

/* 耗材明细样式 */
.materials-list {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
}

.material-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #e9ecef;
}

.material-item:last-child {
  border-bottom: none;
}

.material-info {
  flex: 1;
}

.material-name {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  margin-bottom: 3px;
}

.material-spec {
  font-size: 12px;
  color: #666;
}

.material-price {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.quantity {
  font-size: 12px;
  color: #666;
  margin-bottom: 2px;
}

.price {
  font-size: 14px;
  color: #ff4d4f;
  font-weight: 500;
}
