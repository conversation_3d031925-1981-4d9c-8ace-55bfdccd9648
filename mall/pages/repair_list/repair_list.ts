const app = getApp()

Page({
  data: {
    status: 'pending', // 默认显示待接单
    orders: [],
    openId: '',
    statusText: {
      'pending': '待接单',
      'accepted': '待上门',
      'processing': '维修中',
      'completed': '已完成'
    },
    faultTypeMap: {
      'no_charging': '无法充电',
      'slow_charging': '充电慢',
      'error_code': '报错代码',
      'port_damage': '接口损坏',
      'not_starting': '无法启动',
      'overheating': '过热',
      'display_issue': '显示故障',
      'other': '其他故障'
    }
  },

  onLoad: function (options) {
    console.log('Repair list page loaded');

    // 如果有状态参数，设置当前状态
    if (options.status && this.data.statusText[options.status]) {
      this.setData({
        status: options.status
      });
    }

    // 获取openId
    this.getOpenId();
  },

  onShow: function () {
    // 如果已经有openId，则获取订单列表
    if (this.data.openId) {
      this.getOrderList();
    }
  },

  // 获取openId
  getOpenId: function() {
    // 从全局数据中获取openId
    if (app.globalData.openId) {
      this.setData({
        openId: app.globalData.openId
      });
      this.getOrderList();
    } else {
      // 从本地存储中获取
      tt.getStorage({
        key: 'userInfo',
        success: (res) => {
          if (res.data && res.data.openId) {
            this.setData({
              openId: res.data.openId
            });
            this.getOrderList();
          } else {
            // 尝试从openId存储中获取
            tt.getStorage({
              key: 'openId',
              success: (res) => {
                if (res.data) {
                  this.setData({
                    openId: res.data
                  });
                  this.getOrderList();
                } else {
                  this.showLoginTip();
                }
              },
              fail: () => {
                this.showLoginTip();
              }
            });
          }
        },
        fail: () => {
          // 尝试从openId存储中获取
          tt.getStorage({
            key: 'openId',
            success: (res) => {
              if (res.data) {
                this.setData({
                  openId: res.data
                });
                this.getOrderList();
              } else {
                this.showLoginTip();
              }
            },
            fail: () => {
              this.showLoginTip();
            }
          });
        }
      });
    }
  },
  
  // 显示登录提示
  showLoginTip: function() {
    // 如果没有openId，提示用户登录
    tt.showToast({
      title: '请先登录',
      icon: 'none'
    });
    
    // 跳转到用户中心页面
    tt.switchTab({
      url: '/pages/user_center/user_center'
    });
  },

  // 获取订单列表
  getOrderList: function() {
    if (!this.data.openId) {
      console.error('获取订单列表失败：openId为空');
      return;
    }

    tt.showLoading({
      title: '加载中...'
    });

    const api = require('../../utils/api');
    api.getRepairOrderList(this.data.openId, this.data.status).then(res => {
      tt.hideLoading();
      
      if (res.success) {
        // 格式化创建时间
        const orders = res.orders.map(order => {
          if (order.createdAt) {
            const date = new Date(order.createdAt);
            order.createdAtFormatted = `${date.getFullYear()}-${this.padZero(date.getMonth() + 1)}-${this.padZero(date.getDate())} ${this.padZero(date.getHours())}:${this.padZero(date.getMinutes())}`;
          } else {
            order.createdAtFormatted = '未知';
          }
          return order;
        });

        this.setData({
          orders: orders
        });
      } else {
        console.error('获取订单列表失败:', res.message);
        tt.showToast({
          title: '获取订单列表失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      tt.hideLoading();
      console.error('获取订单列表请求失败:', err);
      tt.showToast({
        title: '获取订单列表失败',
        icon: 'none'
      });
    });
  },

  // 切换标签
  switchTab: function(e) {
    const status = e.currentTarget.dataset.status;
    
    this.setData({
      status: status
    });
    
    this.getOrderList();
  },

  // 跳转到订单详情
  goToOrderDetail: function(e) {
    const id = e.currentTarget.dataset.id;
    
    tt.navigateTo({
      url: `/pages/repair_detail/repair_detail?id=${id}`
    });
  },

  // 取消订单
  cancelOrder: function(e) {
    const id = e.currentTarget.dataset.id;
    
    tt.showModal({
      title: '提示',
      content: '确定要取消该维修订单吗？',
      success: (res) => {
        if (res.confirm) {
          this.doCancelOrder(id);
        }
      }
    });
  },

  // 执行取消订单
  doCancelOrder: function(orderNo) {
    if (!this.data.openId) {
      console.error('取消订单失败：openId为空');
      return;
    }

    tt.showLoading({
      title: '取消中...'
    });

    const api = require('../../utils/api');
    api.cancelRepairOrder(orderNo, this.data.openId).then(res => {
      tt.hideLoading();
      
      if (res.success) {
        tt.showToast({
          title: '取消成功',
          icon: 'success'
        });
        
        // 刷新订单列表
        this.getOrderList();
      } else {
        console.error('取消订单失败:', res.message);
        tt.showToast({
          title: res.message || '取消失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      tt.hideLoading();
      console.error('取消订单请求失败:', err);
      tt.showToast({
        title: '取消失败',
        icon: 'none'
      });
    });
  },

  // 联系客服
  contactService: function() {
    // 跳转到客服页面
    tt.navigateTo({
      url: '/pages/customer_service/customer_service'
    });
  },

  // 去预约维修
  goToRepair: function() {
    tt.navigateTo({
      url: '/pages/repair_form/repair_form'
    });
  },

  // 数字补零
  padZero: function(num) {
    return num < 10 ? '0' + num : num;
  }
})
