.container {
  padding: 30rpx 30rpx 0;
}

.category-list {
  white-space: nowrap;
  margin-bottom: 30rpx;
}

.category-item {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  margin-right: 32rpx;
  width: 120rpx;
  transition: all 0.3s ease;
}

.category-icon {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 12rpx;
  border-radius: 50%;
  background-color: #f5f5f5;
  transition: all 0.3s ease;
}

.category-icon-image {
  width: 48rpx;
  height: 48rpx;
  display: block;
  transition: all 0.3s ease;
}

.category-item.active .category-icon {
  background-color: #e3f2fd;
  transform: scale(1.05);
}

.category-name {
  font-size: 24rpx;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  text-align: center;
}

.category-item.active .category-name {
  color: #1e88e5;
  font-weight: 500;
}

.loading-more, .no-more {
  text-align: center;
  padding: 30rpx 0;
  color: #757575;
  font-size: 28rpx;
}
