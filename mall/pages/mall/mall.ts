const app = getApp()
const api = require('../../utils/api.js')

Page({
  data: {
    currentCategory: 0, // 0表示全部
    sortType: 'default', // 排序方式：default, sales, price
    priceOrder: 'desc', // 价格排序：asc, desc
    hasMore: true, // 是否有更多商品
    page: 1, // 当前页码
    pageSize: 10, // 每页商品数量

    categories: [
      {
        id: 0,
        name: '全部',
        icon: '/images/icons/全部.png',
        selectedIcon: '/images/icons/全部_selected.png'
      }
    ],

    banners: [
      {
        id: 1,
        image: '/images/轮播图/轮播图4.png'
      },
      {
        id: 2,
        image: '/images/轮播图/轮播图3.png'
      }
    ],

    products: []
  },

  onLoad: function () {
    console.log('Mall page loaded');
    // 加载分类和商品数据
    this.loadCategories();
    this.loadProducts();
  },

  onShow: function () {
    // 页面显示时执行

    // 更新自定义tabBar的选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 1
      });
      console.log('产品页更新tabBar成功');
    } else {
      console.log('产品页获取tabBar失败');
    }
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    // 重置数据
    this.setData({
      page: 1,
      hasMore: true
    });

    // 重新加载商品
    this.loadProducts();

    // 停止下拉刷新
    tt.stopPullDownRefresh();
  },

  // 上拉加载更多
  onReachBottom: function() {
    if (this.data.hasMore) {
      this.loadMoreProducts();
    }
  },

  // 加载分类数据
  loadCategories: function() {
    api.getProductCategories().then(res => {
      if (res.success) {
        // 在分类列表前添加"全部"选项
        const allCategory = {
          id: 0,
          name: '全部',
          icon: '/images/icons/全部.png',
          selectedIcon: '/images/icons/全部_selected.png'
        };
        const categories = [allCategory, ...res.data];
        this.setData({
          categories: categories
        });
      } else {
        console.error('获取分类失败:', res.message);
      }
    }).catch(err => {
      console.error('获取分类失败:', err);
    });
  },

  // 加载商品
  loadProducts: function() {
    console.log('Loading products...');

    tt.showLoading({
      title: '加载中'
    });

    api.getProductList(this.data.currentCategory, this.data.sortType, this.data.priceOrder).then(res => {
      tt.hideLoading();

      if (res.success) {
        // 处理商品图片URL
        const processedProducts = res.data.map(product => {
          return {
            ...product,
            mainImage: this.processImageUrl(product.mainImage),
            detailImage: this.processImageUrl(product.detailImage),
            images: product.images ? product.images.split(',').map(url => this.processImageUrl(url.trim())) : []
          };
        });

        this.setData({
          products: processedProducts,
          hasMore: false // 暂时设为false，后续可以实现分页
        });

        console.log('商品列表加载成功，商品数量:', processedProducts.length);
        if (processedProducts.length > 0) {
          console.log('第一个商品的主图URL:', processedProducts[0].mainImage);
        }
      } else {
        console.error('获取商品列表失败:', res.message);
        tt.showToast({
          title: res.message || '获取商品列表失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      tt.hideLoading();
      console.error('获取商品列表失败:', err);
      tt.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
    });
  },

  // 加载更多商品
  loadMoreProducts: function() {
    // 模拟加载更多数据
    this.setData({
      page: this.data.page + 1
    });

    // 模拟网络请求
    tt.showLoading({
      title: '加载中'
    });

    setTimeout(() => {
      tt.hideLoading();

      // 示例中没有更多数据，实际开发中应该调用API获取更多商品
      this.setData({
        hasMore: false
      });
    }, 500);
  },

  // 排序商品
  sortProducts: function(products) {
    switch(this.data.sortType) {
      case 'sales':
        products.sort((a, b) => b.sales - a.sales);
        break;
      case 'price':
        if (this.data.priceOrder === 'asc') {
          products.sort((a, b) => a.price - b.price);
        } else {
          products.sort((a, b) => b.price - a.price);
        }
        break;
      default:
        // 默认排序，可以按照综合评分或其他因素
        break;
    }
    return products;
  },

  // 分类点击
  onCategoryTap: function(e) {
    const id = parseInt(e.currentTarget.dataset.id);

    if (id !== this.data.currentCategory) {
      this.setData({
        currentCategory: id,
        page: 1,
        hasMore: true
      });

      this.loadProducts();
    }
  },

  // 排序方式点击
  onSortTap: function(e) {
    const type = e.currentTarget.dataset.type;

    if (type === this.data.sortType) {
      // 如果点击的是当前排序方式，且是价格排序，则切换升降序
      if (type === 'price') {
        this.setData({
          priceOrder: this.data.priceOrder === 'asc' ? 'desc' : 'asc'
        });
        // 重新加载商品
        this.loadProducts();
      }
    } else {
      // 切换排序方式
      this.setData({
        sortType: type,
        priceOrder: type === 'price' ? 'desc' : this.data.priceOrder
      });
      // 重新加载商品
      this.loadProducts();
    }
  },

  // 显示筛选
  showFilter: function() {
    tt.showToast({
      title: '筛选功能开发中',
      icon: 'none'
    });
  },

  // 搜索框点击
  goToSearch: function() {
    tt.showToast({
      title: '搜索功能开发中',
      icon: 'none'
    });
  },

  // 跳转到产品详情页
  goToProductDetail: function(e) {
    const id = e.currentTarget.dataset.id;
    tt.navigateTo({
      url: `/pages/product_detail/product_detail?id=${id}`
    });
  },

  // 处理图片URL，确保是完整的HTTPS URL
  processImageUrl: function(imageUrl) {
    if (!imageUrl || imageUrl.trim() === '') {
      return '/images/products/充电线缆.png'; // 默认图片
    }

    // 如果已经是完整的HTTP/HTTPS URL，直接返回
    if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
      return imageUrl;
    }

    // 如果是相对路径，添加服务器地址
    if (imageUrl.startsWith('/uploads/')) {
      return `https://localhost:8443${imageUrl}`;
    }

    // 如果只是文件名，添加完整路径
    if (!imageUrl.startsWith('/')) {
      return `https://localhost:8443/uploads/${imageUrl}`;
    }

    // 其他情况，添加服务器地址
    return `https://localhost:8443${imageUrl}`;
  }
})
