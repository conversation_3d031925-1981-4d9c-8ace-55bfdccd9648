/* 全局样式 */
:root {
  --primary-color: #1e88e5; /* Blue theme color */
  --secondary-color: #64b5f6;
  --accent-color: #0d47a1;
  --text-color: #333333;
  --light-text: #757575;
  --background-color: #f5f5f5;
  --white: #ffffff;
  --border-color: #e0e0e0;
  --success-color: #4caf50;
  --warning-color: #ff9800;
  --error-color: #f44336;
}

page {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  background-color: #f5f5f5;
  color: #333333;
  line-height: 1.5;
  font-size: 14px;
}

/* 微信小程序tabBar样式 */
.wx_tabbar_container {
  height: 100rpx !important;
  padding-top: 10rpx !important;
  padding-bottom: 10rpx !important;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05) !important;
}

.wx_tabbar_item {
  padding-top: 20rpx !important;
  padding-bottom: 20rpx !important;
}

.wx_tabbar_icon {
  width: 1rpx !important;
  height: 1rpx !important;
  margin: 0 !important;
  opacity: 0 !important;
  position: absolute !important;
}

.wx_tabbar_text {
  font-size: 28rpx !important;
  line-height: 1.2 !important;
  font-weight: 400 !important;
  margin-top: 0 !important;
}

.wx_tabbar_item_active .wx_tabbar_text {
  font-weight: 600 !important;
  transform: scale(1.05) !important;
}

/* 添加底部指示器 */
.wx_tabbar_item_active::after {
  content: '' !important;
  position: absolute !important;
  bottom: 10rpx !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  width: 40rpx !important;
  height: 6rpx !important;
  background-color: #1e88e5 !important;
  border-radius: 3rpx !important;
}

/* 常用布局类 */
.flex {
  display: flex;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.gap-2 {
  gap: 16rpx;
}

.gap-3 {
  gap: 24rpx;
}

/* 间距类 */
.m-1 { margin: 8rpx; }
.m-2 { margin: 16rpx; }
.m-3 { margin: 24rpx; }
.m-4 { margin: 32rpx; }

.mt-1 { margin-top: 8rpx; }
.mt-2 { margin-top: 16rpx; }
.mt-3 { margin-top: 24rpx; }
.mt-4 { margin-top: 32rpx; }

.mb-1 { margin-bottom: 8rpx; }
.mb-2 { margin-bottom: 16rpx; }
.mb-3 { margin-bottom: 24rpx; }
.mb-4 { margin-bottom: 32rpx; }

.ml-1 { margin-left: 8rpx; }
.ml-2 { margin-left: 16rpx; }
.ml-3 { margin-left: 24rpx; }
.ml-4 { margin-left: 32rpx; }

.mr-1 { margin-right: 8rpx; }
.mr-2 { margin-right: 16rpx; }
.mr-3 { margin-right: 24rpx; }
.mr-4 { margin-right: 32rpx; }

.p-1 { padding: 8rpx; }
.p-2 { padding: 16rpx; }
.p-3 { padding: 24rpx; }
.p-4 { padding: 32rpx; }

.pt-1 { padding-top: 8rpx; }
.pt-2 { padding-top: 16rpx; }
.pt-3 { padding-top: 24rpx; }
.pt-4 { padding-top: 32rpx; }

.pb-1 { padding-bottom: 8rpx; }
.pb-2 { padding-bottom: 16rpx; }
.pb-3 { padding-bottom: 24rpx; }
.pb-4 { padding-bottom: 32rpx; }

.pl-1 { padding-left: 8rpx; }
.pl-2 { padding-left: 16rpx; }
.pl-3 { padding-left: 24rpx; }
.pl-4 { padding-left: 32rpx; }

.pr-1 { padding-right: 8rpx; }
.pr-2 { padding-right: 16rpx; }
.pr-3 { padding-right: 24rpx; }
.pr-4 { padding-right: 32rpx; }

/* 文字类 */
.text-xs { font-size: 20rpx; }
.text-sm { font-size: 24rpx; }
.text-base { font-size: 28rpx; }
.text-lg { font-size: 32rpx; }
.text-xl { font-size: 36rpx; }
.text-2xl { font-size: 40rpx; }

.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-bold { font-weight: 600; }

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-primary { color: #1e88e5; }
.text-secondary { color: #64b5f6; }
.text-muted { color: #757575; }
.text-success { color: #4caf50; }
.text-warning { color: #ff9800; }
.text-error { color: #f44336; }

/* 背景类 */
.bg-primary { background-color: #1e88e5; }
.bg-secondary { background-color: #64b5f6; }
.bg-white { background-color: #ffffff; }
.bg-light { background-color: #f5f5f5; }
.bg-success { background-color: #4caf50; }
.bg-warning { background-color: #ff9800; }
.bg-error { background-color: #f44336; }

/* 边框类 */
.border { border: 1rpx solid #e0e0e0; }
.border-primary { border: 1rpx solid #1e88e5; }
.border-light { border: 1rpx solid #f0f0f0; }

.rounded { border-radius: 8rpx; }
.rounded-lg { border-radius: 16rpx; }
.rounded-full { border-radius: 50%; }

/* 阴影类 */
.shadow-sm { box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05); }
.shadow { box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1); }
.shadow-lg { box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15); }

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx 32rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  text-align: center;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 80rpx;
  box-sizing: border-box;
}

.btn-primary {
  background-color: #1e88e5;
  color: white;
}

.btn-secondary {
  background-color: #64b5f6;
  color: white;
}

.btn-outline {
  background-color: transparent;
  color: #1e88e5;
  border: 2rpx solid #1e88e5;
}

.btn-ghost {
  background-color: rgba(30, 136, 229, 0.1);
  color: #1e88e5;
}

.btn-sm {
  padding: 16rpx 24rpx;
  font-size: 24rpx;
  min-height: 60rpx;
}

.btn-lg {
  padding: 32rpx 48rpx;
  font-size: 32rpx;
  min-height: 100rpx;
}

.btn-block {
  width: 100%;
  display: flex;
}

.btn-disabled {
  opacity: 0.5;
  pointer-events: none;
}

/* 卡片样式 */
.card {
  background-color: white;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 24rpx;
}

.card-header {
  padding-bottom: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
  margin-bottom: 24rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin: 0;
}

/* 网格布局 */
.grid {
  display: grid;
  gap: 24rpx;
}

.grid-2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid-3 {
  grid-template-columns: repeat(3, 1fr);
}

.grid-4 {
  grid-template-columns: repeat(4, 1fr);
}

/* 产品卡片 */
.product-card {
  background-color: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.product-card:active {
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
}

.product-card image {
  width: 100%;
  height: 300rpx;
  object-fit: cover;
}

.product-card .info {
  padding: 24rpx;
}

.product-card .title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-card .price {
  font-size: 32rpx;
  font-weight: 600;
  color: #1e88e5;
}

/* 搜索栏 */
.search-bar {
  display: flex;
  align-items: center;
  background-color: white;
  border-radius: 50rpx;
  padding: 20rpx 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.search-bar input {
  flex: 1;
  margin-left: 16rpx;
  font-size: 28rpx;
  color: #333;
}

.search-bar input::placeholder {
  color: #999;
}

/* 章节标题 */
.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
  padding: 0 8rpx;
}

.section-title text {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.section-title .more {
  font-size: 24rpx;
  color: #1e88e5;
  display: flex;
  align-items: center;
  gap: 8rpx;
}

/* 徽章 */
.badge {
  display: inline-flex;
  align-items: center;
  padding: 8rpx 16rpx;
  background-color: #1e88e5;
  color: white;
  font-size: 20rpx;
  border-radius: 20rpx;
  font-weight: 500;
}

.badge-success {
  background-color: #4caf50;
}

.badge-warning {
  background-color: #ff9800;
}

.badge-error {
  background-color: #f44336;
}

.badge-secondary {
  background-color: #64b5f6;
}

/* 响应式 */
@media (max-width: 750rpx) {
  .grid-4 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .grid-3 {
    grid-template-columns: repeat(2, 1fr);
  }
}
