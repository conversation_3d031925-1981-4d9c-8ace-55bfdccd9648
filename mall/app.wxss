/* 隐藏tabBar图标 */
.tt_tabbar_icon {
  width: 0 !important;
  height: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
  opacity: 0 !important;
  display: none !important;
}

.tt_tabbar_text {
  margin-top: 0 !important;
  padding-top: 0 !important;
  font-size: 30rpx !important;
  line-height: 1.2 !important;
}

.tt_tabbar_item {
  padding-top: 20rpx !important;
  padding-bottom: 20rpx !important;
}

.tt_tabbar_item_active .tt_tabbar_text {
  font-weight: 600 !important;
  transform: scale(1.05) !important;
}

.tt_tabbar_container {
  height: 100rpx !important;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05) !important;
}

/* 添加底部指示器 */
.tt_tabbar_item_active::after {
  content: '' !important;
  position: absolute !important;
  bottom: 10rpx !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  width: 40rpx !important;
  height: 6rpx !important;
  background-color: #1e88e5 !important;
  border-radius: 3rpx !important;
}
