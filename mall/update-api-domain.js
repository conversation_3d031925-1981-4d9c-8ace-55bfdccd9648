/**
 * API域名更新工具
 * 用于快速切换开发环境的API域名
 */

const fs = require('fs');
const path = require('path');

// 配置文件路径
const CONFIG_FILES = [
  'utils/api.js',
  'utils/config.ts',
  'pages/customer_service/customer_service.ts'
];

// 域名配置
const DOMAINS = {
  local: 'https://***********:8443',
  ngrok: '', // 需要手动输入ngrok域名
  localhost: 'https://localhost:8443'
};

function updateApiDomain(newDomain) {
  console.log(`🔄 更新API域名为: ${newDomain}`);
  
  CONFIG_FILES.forEach(filePath => {
    const fullPath = path.join(__dirname, filePath);
    
    if (!fs.existsSync(fullPath)) {
      console.log(`⚠️  文件不存在: ${filePath}`);
      return;
    }
    
    try {
      let content = fs.readFileSync(fullPath, 'utf8');
      
      // 替换不同格式的API地址
      const patterns = [
        /API_BASE_URL:\s*['"`]https?:\/\/[^'"`]+['"`]/g,
        /API_BASE\s*=\s*['"`]https?:\/\/[^'"`]+['"`]/g,
        /https?:\/\/[\d\.]+:\d+/g,
        /https?:\/\/[a-zA-Z0-9\-\.]+\.ngrok\.io/g,
        /https?:\/\/localhost:\d+/g
      ];
      
      patterns.forEach(pattern => {
        if (filePath.includes('api.js')) {
          content = content.replace(
            /API_BASE_URL:\s*['"`]https?:\/\/[^'"`]+['"`]/g,
            `API_BASE_URL: '${newDomain}/api'`
          );
        } else if (filePath.includes('config.ts')) {
          content = content.replace(
            /API_BASE_URL:\s*['"`]https?:\/\/[^'"`]+['"`]/g,
            `API_BASE_URL: '${newDomain}'`
          );
        } else if (filePath.includes('customer_service.ts')) {
          content = content.replace(
            /API_BASE\s*=\s*['"`]https?:\/\/[^'"`]+['"`]/g,
            `API_BASE = '${newDomain}/api'`
          );
        }
      });
      
      fs.writeFileSync(fullPath, content, 'utf8');
      console.log(`✅ 已更新: ${filePath}`);
      
    } catch (error) {
      console.error(`❌ 更新失败 ${filePath}:`, error.message);
    }
  });
}

function showUsage() {
  console.log('📋 使用方法:');
  console.log('node update-api-domain.js <domain>');
  console.log('');
  console.log('🌐 可用域名:');
  console.log('  local     - https://***********:8443 (局域网IP)');
  console.log('  localhost - https://localhost:8443 (本地)');
  console.log('  <ngrok>   - 直接输入ngrok域名');
  console.log('');
  console.log('💡 示例:');
  console.log('  node update-api-domain.js local');
  console.log('  node update-api-domain.js https://abc123.ngrok.io');
}

// 主函数
function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    showUsage();
    return;
  }
  
  const input = args[0];
  let targetDomain;
  
  if (DOMAINS[input]) {
    targetDomain = DOMAINS[input];
  } else if (input.startsWith('http')) {
    targetDomain = input;
  } else {
    console.error('❌ 无效的域名参数');
    showUsage();
    return;
  }
  
  if (!targetDomain) {
    console.error('❌ 域名不能为空');
    return;
  }
  
  updateApiDomain(targetDomain);
  console.log('');
  console.log('🎉 API域名更新完成！');
  console.log('📱 请重新编译小程序以应用更改');
}

if (require.main === module) {
  main();
}

module.exports = { updateApiDomain };
