# 🔐 抖音小程序HTTPS证书配置指南

## 🚨 问题描述

抖音小程序在真机调试时不信任自签名证书，导致API请求失败：
```
{errMsg: "request:fail url error", errNo: 10401, errorCode: 100029, errorType: "D"}
```

## 🛠️ 解决方案

### 方案1: 开发者工具设置（推荐）

#### 步骤1: 打开项目设置
1. 在抖音开发者工具中打开项目
2. 点击右上角 **"详情"** 按钮
3. 选择 **"项目配置"** 标签

#### 步骤2: 配置网络设置
1. 找到 **"网络设置"** 或 **"调试设置"**
2. 勾选以下选项：
   - ✅ **"不校验合法域名"**
   - ✅ **"不校验HTTPS证书"**
   - ✅ **"不校验TLS版本"**

#### 步骤3: 添加开发域名
1. 在 **"开发域名"** 中添加：
   ```
   ***********:8443
   ```
2. 保存设置并重新编译

### 方案2: 使用内网穿透

#### 使用 ngrok
```bash
# 1. 安装 ngrok
# 下载: https://ngrok.com/download

# 2. 启动隧道
ngrok http 8443

# 3. 获得公网域名
# 例如: https://abc123.ngrok.io
```

#### 使用 localtunnel
```bash
# 1. 安装
npm install -g localtunnel

# 2. 启动隧道
lt --port 8443 --subdomain zlz-mall

# 3. 获得域名
# https://zlz-mall.loca.lt
```

#### 更新API配置
```javascript
// mall/utils/api.js
const CONFIG = {
  API_BASE_URL: 'https://abc123.ngrok.io/api',  // 使用隧道域名
  DEBUG: true
};
```

### 方案3: 配置本地hosts（仅限开发）

#### 步骤1: 修改hosts文件
```bash
# macOS/Linux
sudo vim /etc/hosts

# Windows
# 编辑 C:\Windows\System32\drivers\etc\hosts

# 添加以下行
*********** zlz-mall.local
```

#### 步骤2: 重新生成证书
```bash
cd mall_server
keytool -genkeypair -alias zlz-mall -keyalg RSA -keysize 2048 \
  -storetype PKCS12 -keystore keystore.p12 -validity 365 \
  -storepass zlzmall123 -keypass zlzmall123 \
  -dname "CN=zlz-mall.local, OU=ZLZ Mall, O=成都桩郎中新能源技术有限公司, L=成都, ST=四川, C=CN"
```

#### 步骤3: 更新API配置
```javascript
// 使用域名而不是IP
const CONFIG = {
  API_BASE_URL: 'https://zlz-mall.local:8443/api',
  DEBUG: true
};
```

## 🔧 调试技巧

### 检查网络请求
1. 在抖音开发者工具中打开 **"调试器"**
2. 查看 **"Network"** 标签
3. 观察API请求的状态码和错误信息

### 验证证书
```bash
# 测试HTTPS连接
curl -k https://***********:8443/api/engineers/approved

# 查看证书信息
openssl s_client -connect ***********:8443 -servername ***********
```

### 日志调试
```javascript
// 在API调用前添加日志
console.log('API Request:', url, method, data);

// 在失败回调中添加详细日志
fail: (err) => {
  console.error('API Error Details:', {
    url: fullUrl,
    method: method,
    error: err,
    timestamp: new Date().toISOString()
  });
}
```

## 📱 真机调试注意事项

### 网络环境
- 确保手机和开发机在同一局域网
- 检查防火墙设置，确保8443端口开放
- 某些企业网络可能阻止自签名证书

### 小程序限制
- 抖音小程序对HTTPS要求更严格
- 真机环境不支持跳过证书验证
- 建议使用内网穿透获得真实域名

### 性能考虑
- 内网穿透可能增加延迟
- 免费服务通常有带宽限制
- 生产环境必须使用正式证书

## 🎯 最佳实践

### 开发阶段
1. 使用开发者工具的证书跳过功能
2. 配置本地域名映射
3. 使用HTTP进行快速开发（如需要）

### 测试阶段
1. 使用内网穿透获得公网域名
2. 测试真机环境下的完整流程
3. 验证所有API接口的可用性

### 生产阶段
1. 购买正式SSL证书
2. 配置CDN和负载均衡
3. 启用HSTS和其他安全措施

## 🔗 相关资源

- [抖音小程序开发文档](https://developer.open-douyin.com/docs/resource/zh-CN/mini-app/develop/guide/start/introduction)
- [ngrok官网](https://ngrok.com/)
- [Let's Encrypt免费证书](https://letsencrypt.org/)
- [SSL证书检测工具](https://www.ssllabs.com/ssltest/)
