{"compilerOptions": {"strictNullChecks": true, "noImplicitAny": true, "module": "CommonJS", "target": "ES2020", "allowJs": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "experimentalDecorators": true, "noImplicitThis": true, "noImplicitReturns": true, "alwaysStrict": true, "noFallthroughCasesInSwitch": true, "noUnusedLocals": true, "noUnusedParameters": true, "strict": true, "strictPropertyInitialization": true, "lib": ["es2020"], "types": ["@douyin-microapp/typings"]}, "include": ["./**/*.ts"], "exclude": ["node_modules"]}