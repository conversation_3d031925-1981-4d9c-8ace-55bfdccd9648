.nav-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 110rpx;
  background: white;
  z-index: 999;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  padding-bottom: env(safe-area-inset-bottom);
}

.nav-bar-border {
  background-color: #f0f0f0;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 1px;
  transform: scaleY(0.5);
}

.nav-bar-content {
  display: flex;
  height: 100%;
}

.nav-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  height: 100%;
}

.nav-item::after {
  content: '';
  position: absolute;
  top: 20rpx;
  right: 0;
  height: 40rpx;
  width: 1px;
  background-color: #f0f0f0;
  transform: scaleX(0.5);
}

.nav-item:last-child::after {
  display: none;
}

.nav-text {
  font-size: 30rpx;
  color: #757575;
  transition: all 0.3s ease;
}

.nav-item.active .nav-text {
  color: #1e88e5;
  font-weight: 600;
  transform: scale(1.05);
}

.nav-indicator {
  position: absolute;
  bottom: 15rpx;
  width: 48rpx;
  height: 6rpx;
  background-color: #1e88e5;
  border-radius: 3rpx;
  transition: all 0.3s ease;
}

.nav-bar-placeholder {
  height: 110rpx;
  width: 100%;
}
