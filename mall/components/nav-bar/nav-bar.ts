Component({
  properties: {
    active: {
      type: String,
      value: 'index'
    }
  },

  methods: {
    switchTab(e) {
      const page = e.currentTarget.dataset.page;

      if (page === this.data.active) {
        return; // 已经在当前页面，不需要切换
      }

      let url = '';

      switch (page) {
        case 'index':
          url = '/pages/index/index';
          break;
        case 'mall':
          url = '/pages/mall/mall';
          break;
        case 'repair':
          url = '/pages/repair/repair';
          break;
        case 'user':
          url = '/pages/user_center/user_center';
          break;
      }

      if (url) {
        // 对于主导航页面，直接使用reLaunch方法
        // 这样可以关闭所有其他页面，避免页面堆栈过多
        tt.reLaunch({
          url: url,
          success: () => {
            // 更新选中状态
            this.setData({
              active: page
            });
          },
          fail: (err) => {
          }
        });
      }
    }
  }
})
