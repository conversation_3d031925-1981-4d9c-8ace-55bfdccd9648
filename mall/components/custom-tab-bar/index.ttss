.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120rpx;
  background: white;
  display: flex;
  padding-bottom: env(safe-area-inset-bottom);
  z-index: 999;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.tab-bar-border {
  background-color: #f0f0f0;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 1px;
  transform: scaleY(0.5);
}

.tab-bar-item {
  flex: 1;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  position: relative;
}

.tab-icon-container {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 8rpx;
  transition: all 0.3s ease;
}

.tab-bar-item.active .tab-icon-container {
  transform: scale(1.1);
}

.tab-icon {
  width: 44rpx;
  height: 44rpx;
  display: block;
  object-fit: contain; /* 确保图标不会被拉伸变形 */
}

.tab-text {
  font-size: 22rpx;
  color: #757575;
  line-height: 1.2;
}

.tab-bar-item.active .tab-text {
  color: #1e88e5;
  font-weight: 500;
}

.tab-indicator {
  position: absolute;
  bottom: -10rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 10rpx;
  height: 10rpx;
  border-radius: 50%;
  background-color: #1e88e5;
}
