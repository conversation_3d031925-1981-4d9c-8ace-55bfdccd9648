<view class="tab-bar">
  <view class="tab-bar-border"></view>
  <view tt:for="{{list}}" tt:key="index" class="tab-bar-item {{selected === index ? 'active' : ''}}" data-path="{{item.pagePath}}" data-index="{{index}}" bindtap="switchTab">
    <view class="tab-icon-container">
      <image src="{{selected === index ? item.selectedIconPath : item.iconPath}}" class="tab-icon"></image>
      <view class="tab-indicator" tt:if="{{selected === index}}"></view>
    </view>
    <view class="tab-text">{{item.text}}</view>
  </view>
</view>
