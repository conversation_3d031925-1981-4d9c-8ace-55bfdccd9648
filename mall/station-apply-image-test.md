# 🧪 服务网点申请图片功能测试指南

## 📋 功能概述

服务网点申请页面包含三种类型的图片上传：
1. **营业执照** - 单张图片上传
2. **资质证书** - 最多9张图片上传
3. **网点照片** - 最多9张图片上传

## 🔍 测试流程

### 1. 小程序端测试

#### 📱 服务网点申请测试
1. **打开服务网点申请页面**
   - 进入小程序
   - 找到服务网点申请入口
   - 填写基本信息

2. **测试营业执照上传**
   - 点击"上传营业执照"按钮
   - 选择图片（相册或拍照）
   - 验证图片预览功能
   - 确认上传成功提示

3. **测试资质证书上传**
   - 点击"上传资质证书"按钮
   - 选择多张图片（最多9张）
   - 验证批量上传功能
   - 测试删除功能

4. **测试网点照片上传**
   - 点击"上传网点照片"按钮
   - 选择多张图片（最多9张）
   - 验证批量上传功能
   - 测试删除功能

5. **提交申请**
   - 填写完整信息
   - 点击提交申请
   - 观察上传进度
   - 确认提交成功

### 2. 管理端测试

#### 🖥️ 管理端图片显示测试
1. **登录管理后台**
   - 访问 `https://localhost:8443/admin/login`
   - 使用管理员账号登录

2. **查看服务网点列表**
   - 进入服务网点管理页面
   - 找到包含图片的申请记录

3. **查看申请详情**
   - 点击"查看详情"按钮
   - 验证营业执照显示
   - 验证资质证书图片显示
   - 验证网点照片显示
   - 测试图片点击放大功能

4. **测试专用页面**
   - 访问 `https://localhost:8443/admin/test-station-images`
   - 验证各种图片显示功能

## ✅ 验证要点

### 小程序端验证
- [ ] 营业执照上传功能正常
- [ ] 资质证书批量上传功能正常
- [ ] 网点照片批量上传功能正常
- [ ] 图片预览功能正常
- [ ] 图片删除功能正常
- [ ] 上传进度提示正常
- [ ] 申请提交成功
- [ ] 图片数量限制生效

### 管理端验证
- [ ] 营业执照正确显示
- [ ] 资质证书图片正确显示
- [ ] 网点照片正确显示
- [ ] 图片点击放大功能正常
- [ ] 图片加载速度合理
- [ ] 图片路径处理正确

### 后端验证
- [ ] 图片文件成功保存到服务器
- [ ] 数据库中正确存储图片URL
- [ ] 图片访问路径正确
- [ ] 批量上传接口正常工作

## 🐛 常见问题排查

### 问题1: 小程序端图片上传失败
**可能原因:**
- 网络连接问题
- 图片格式不支持
- 图片大小超限
- 服务器存储空间不足

**排查步骤:**
1. 检查网络连接
2. 验证图片格式和大小
3. 查看小程序控制台错误
4. 检查服务器日志

### 问题2: 管理端图片不显示
**可能原因:**
- 图片路径错误
- 静态资源访问配置问题
- 图片文件丢失
- JSON数据格式错误

**排查步骤:**
1. 检查图片URL是否正确
2. 验证静态资源配置
3. 确认图片文件存在
4. 检查浏览器控制台错误
5. 验证JSON数据格式

### 问题3: 批量上传功能异常
**可能原因:**
- API接口问题
- 并发上传限制
- 文件大小总和超限

**排查步骤:**
1. 检查API响应
2. 测试单个上传
3. 减少上传数量测试
4. 查看服务器资源使用情况

## 📊 测试数据

### 测试图片要求
- **格式**: JPG, PNG, GIF, BMP, WEBP
- **大小**: 单张不超过5MB
- **数量**: 
  - 营业执照: 1张
  - 资质证书: 最多9张
  - 网点照片: 最多9张

### 测试场景
1. **正常场景**: 上传符合要求的图片
2. **边界场景**: 上传最大数量图片
3. **异常场景**: 上传不支持格式或超大文件
4. **网络场景**: 弱网环境下上传

## 🔧 调试工具

### 小程序调试
```javascript
// 在station_apply.ts中添加调试日志
console.log('📸 开始上传图片:', filePaths);
console.log('✅ 图片上传完成:', uploadedUrls);
```

### 管理端调试
```javascript
// 在浏览器控制台中检查
console.log('🖼️ 图片数据:', document.querySelectorAll('.certificates-gallery'));
console.log('📋 JSON数据:', gallery.getAttribute('data-certificates'));
```

### 后端调试
```bash
# 查看上传文件
ls -la uploads/

# 查看服务器日志
tail -f logs/spring.log

# 测试图片访问
curl -k https://localhost:8443/uploads/filename.jpg
```

## 📈 性能测试

### 上传性能
- 单张图片上传时间 < 5秒
- 9张图片批量上传时间 < 30秒
- 上传成功率 > 95%

### 显示性能
- 图片加载时间 < 3秒
- 缩略图生成时间 < 1秒
- 详情页面打开时间 < 2秒

## 🎯 测试结果记录

### 测试环境
- **小程序版本**: 
- **管理端浏览器**: 
- **服务器环境**: 
- **测试时间**: 

### 测试结果
| 功能 | 状态 | 备注 |
|------|------|------|
| 营业执照上传 | ✅/❌ |  |
| 资质证书上传 | ✅/❌ |  |
| 网点照片上传 | ✅/❌ |  |
| 管理端显示 | ✅/❌ |  |
| 图片预览 | ✅/❌ |  |

### 发现的问题
1. 
2. 
3. 

### 改进建议
1. 
2. 
3. 

## 🚀 部署检查清单

### 生产环境部署前
- [ ] 配置正确的上传路径
- [ ] 设置合适的文件大小限制
- [ ] 配置图片压缩
- [ ] 设置CDN加速
- [ ] 配置HTTPS证书
- [ ] 测试所有功能

### 监控指标
- 图片上传成功率
- 图片加载速度
- 存储空间使用情况
- 用户申请完成率

## 🔗 相关链接

- 测试页面: `https://localhost:8443/admin/test-station-images`
- 管理端: `https://localhost:8443/admin/service-centers`
- 上传测试: `https://localhost:8443/admin/test-upload`
- 图片测试: `https://localhost:8443/admin/test-images`
