# 🔧 抖音开发者工具配置指南

## 🚨 问题描述
真机调试时出现网络请求错误：
```
errMsg: "request:fail url error"
errNo: 10401
errorCode: 100029
```

这是因为抖音小程序不信任自签名SSL证书导致的。

## 🛠️ 解决方案

### 方案1: 开发者工具设置（推荐用于开发）

#### 步骤1: 打开项目详情
1. 在抖音开发者工具中打开你的项目
2. 点击右上角的 **"详情"** 按钮
3. 选择 **"项目配置"** 标签页

#### 步骤2: 配置网络设置
在项目配置中找到以下选项并勾选：

✅ **不校验合法域名、web-view（业务域名）、TLS 版本以及 HTTPS 证书**

如果找不到上述选项，尝试以下设置：
- ✅ **不校验合法域名**
- ✅ **不校验HTTPS证书** 
- ✅ **不校验TLS版本**
- ✅ **跳过域名校验**

#### 步骤3: 添加开发域名
在 **"开发环境域名"** 或 **"request合法域名"** 中添加：
```
***********:8443
localhost:8443
```

#### 步骤4: 重新编译
1. 保存设置
2. 重新编译项目
3. 重新进行真机调试

### 方案2: 使用内网穿透（推荐用于真机测试）

#### 使用 ngrok
```bash
# 1. 安装 ngrok
# 访问 https://ngrok.com/download 下载

# 2. 配置 authtoken
ngrok config add-authtoken YOUR_AUTHTOKEN

# 3. 启动隧道
ngrok http 8443

# 4. 获得公网域名
# 例如: https://abc123.ngrok.io
```

#### 更新API配置
获得ngrok域名后，运行更新脚本：
```bash
cd mall
node update-api-domain.js https://abc123.ngrok.io
```

或手动更新以下文件：
- `utils/api.js`
- `utils/config.ts` 
- `pages/customer_service/customer_service.ts`

### 方案3: 使用 localtunnel（免费替代方案）

```bash
# 1. 安装
npm install -g localtunnel

# 2. 启动隧道
lt --port 8443 --subdomain zlz-mall

# 3. 获得域名
# https://zlz-mall.loca.lt

# 4. 更新API配置
node update-api-domain.js https://zlz-mall.loca.lt
```

## 🔍 验证配置

### 检查网络请求
1. 在抖音开发者工具中打开 **"调试器"**
2. 切换到 **"Network"** 标签
3. 进行登录操作
4. 查看请求状态：
   - ✅ 状态码 200: 请求成功
   - ❌ 状态码 0 或错误: 证书/域名问题

### 测试API连接
在开发者工具控制台中运行：
```javascript
// 测试API连接
tt.request({
  url: 'https://***********:8443/api/user/login',
  method: 'POST',
  data: { test: true },
  success: (res) => console.log('✅ 连接成功', res),
  fail: (err) => console.log('❌ 连接失败', err)
});
```

## 🚨 常见问题

### Q1: 开发者工具中找不到"不校验证书"选项
**A:** 不同版本的抖音开发者工具界面可能不同，尝试：
- 查看 **"工具"** → **"项目设置"**
- 查看 **"设置"** → **"通用设置"**
- 查看项目根目录的 `project.config.json` 文件

### Q2: 真机调试仍然失败
**A:** 真机环境更严格，建议：
1. 使用内网穿透获得真实域名
2. 确保手机和开发机在同一网络
3. 检查防火墙设置

### Q3: ngrok 连接不稳定
**A:** 免费版ngrok有限制，可以：
1. 使用 localtunnel 替代
2. 购买ngrok付费版
3. 使用其他内网穿透工具

### Q4: 域名更新后仍然报错
**A:** 确保：
1. 重新编译小程序
2. 清除开发者工具缓存
3. 重启开发者工具
4. 检查所有配置文件都已更新

## 📱 project.config.json 配置

如果开发者工具界面找不到选项，可以直接编辑配置文件：

```json
{
  "setting": {
    "urlCheck": false,
    "es6": true,
    "enhance": true,
    "postcss": true,
    "preloadBackgroundData": false,
    "minified": true,
    "newFeature": false,
    "coverView": true,
    "nodeModules": false,
    "autoAudits": false,
    "showShadowRootInWxmlPanel": true,
    "scopeDataCheck": false,
    "uglifyFileName": false,
    "checkInvalidKey": true,
    "checkSiteMap": true,
    "uploadWithSourceMap": true,
    "compileHotReLoad": false,
    "lazyloadPlaceholderEnable": false,
    "useMultiFrameRuntime": true,
    "useApiHook": true,
    "useApiHostProcess": true,
    "babelSetting": {
      "ignore": [],
      "disablePlugins": [],
      "outputPath": ""
    },
    "enableEngineNative": false,
    "useIsolateContext": false,
    "userConfirmedBundleSwitch": false,
    "packNpmManually": false,
    "packNpmRelationList": [],
    "minifyWXSS": true,
    "disableUseStrict": false,
    "minifyWXML": true,
    "showES6CompileOption": false,
    "useCompilerPlugins": false,
    "ignoreUploadUnusedFiles": true
  },
  "appid": "your-app-id",
  "projectname": "zlz-mall",
  "debugOptions": {
    "hidedInDevtools": []
  },
  "scripts": {},
  "staticServerOptions": {
    "baseURL": "",
    "servePath": ""
  },
  "isGameTourist": false,
  "condition": {
    "search": {
      "list": []
    },
    "conversation": {
      "list": []
    },
    "game": {
      "list": []
    },
    "plugin": {
      "list": []
    },
    "gamePlugin": {
      "list": []
    },
    "miniprogram": {
      "list": []
    }
  }
}
```

关键配置项：
- `"urlCheck": false` - 不校验域名
- 在开发环境中添加你的域名到白名单

## 🎯 推荐流程

### 开发阶段
1. ✅ 配置开发者工具跳过证书验证
2. ✅ 使用局域网IP进行开发
3. ✅ 定期测试真机调试

### 测试阶段  
1. ✅ 使用内网穿透获得公网域名
2. ✅ 在真机上进行完整测试
3. ✅ 验证所有API功能

### 生产阶段
1. ✅ 购买正式SSL证书
2. ✅ 配置真实域名
3. ✅ 在抖音开发者后台配置域名白名单
