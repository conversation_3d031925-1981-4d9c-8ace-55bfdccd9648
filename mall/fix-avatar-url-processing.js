/**
 * 批量修复头像URL处理函数
 * 将所有页面中硬编码的域名替换为统一的API工具函数调用
 */

const fs = require('fs');
const path = require('path');

// 需要修复的文件列表
const filesToFix = [
  'pages/repair_success/repair_success.ts',
  'pages/repair_list_processing/repair_list_processing.ts',
  'pages/repair_detail/repair_detail.ts',
  'pages/repair_list_completed/repair_list_completed.ts',
  'pages/repair_list_accepted/repair_list_accepted.ts',
  'pages/engineer_list/engineer_list.ts'
];

// 旧的processAvatarUrl函数模式
const oldFunctionPattern = /\/\/ 处理头像URL\s*\n\s*processAvatarUrl:\s*function\(avatarUrl\)\s*\{[\s\S]*?\},/g;

// 新的processAvatarUrl函数
const newFunction = `  // 处理头像URL
  processAvatarUrl: function(avatarUrl) {
    // 使用统一的API工具函数
    const api = require('../../utils/api.js');
    return api.processAvatarUrl(avatarUrl);
  },`;

console.log('🔧 开始修复头像URL处理函数...\n');

filesToFix.forEach(filePath => {
  const fullPath = path.join(__dirname, filePath);
  
  if (!fs.existsSync(fullPath)) {
    console.log(`⚠️  文件不存在: ${filePath}`);
    return;
  }
  
  try {
    let content = fs.readFileSync(fullPath, 'utf8');
    
    // 检查是否包含processAvatarUrl函数
    if (!content.includes('processAvatarUrl')) {
      console.log(`ℹ️  跳过（无processAvatarUrl函数）: ${filePath}`);
      return;
    }
    
    // 替换processAvatarUrl函数
    const updatedContent = content.replace(oldFunctionPattern, newFunction);
    
    // 检查是否有变化
    if (updatedContent === content) {
      console.log(`ℹ️  无需修改: ${filePath}`);
      return;
    }
    
    // 写入更新后的内容
    fs.writeFileSync(fullPath, updatedContent, 'utf8');
    console.log(`✅ 已修复: ${filePath}`);
    
  } catch (error) {
    console.error(`❌ 修复失败 ${filePath}:`, error.message);
  }
});

console.log('\n🎉 头像URL处理函数修复完成！');
console.log('\n📋 修复内容：');
console.log('- 移除硬编码的域名 (https://localhost:8443)');
console.log('- 使用统一的 api.processAvatarUrl() 工具函数');
console.log('- 自动适配当前配置的服务器地址');
console.log('\n🔧 配置位置：mall/utils/api.js 中的 CONFIG.SERVER_BASE_URL');
