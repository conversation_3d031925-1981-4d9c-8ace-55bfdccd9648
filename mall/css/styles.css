/* Base styles for the mini-program */
:root {
  --primary-color: #1e88e5; /* Blue theme color */
  --secondary-color: #64b5f6;
  --accent-color: #0d47a1;
  --text-color: #333333;
  --light-text: #757575;
  --background-color: #f5f5f5;
  --white: #ffffff;
  --border-color: #e0e0e0;
  --success-color: #4caf50;
  --warning-color: #ff9800;
  --error-color: #f44336;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  margin: 0;
  padding: 0;
  background-color: var(--background-color);
  color: var(--text-color);
  line-height: 1.5;
}

.mini-program-container {
  max-width: 414px; /* iPhone 12/13 width */
  margin: 0 auto;
  background-color: var(--white);
  min-height: 100vh;
  position: relative;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  border-radius: 30px;
  overflow: hidden;
}

.status-bar {
  height: 44px;
  background-color: var(--white);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px;
  font-size: 12px;
  color: var(--text-color);
  border-bottom: 1px solid var(--border-color);
}

.status-bar .time {
  font-weight: 600;
}

.status-bar .icons {
  display: flex;
  gap: 5px;
}

.nav-bar {
  height: 44px;
  background-color: var(--white);
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  border-bottom: 1px solid var(--border-color);
}

.nav-bar .title {
  font-size: 17px;
  font-weight: 600;
}

.nav-bar .back-button {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
}

.tab-bar {
  height: 50px;
  background-color: var(--white);
  display: flex;
  justify-content: space-around;
  align-items: center;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  max-width: 414px;
  margin: 0 auto;
  border-top: 1px solid var(--border-color);
}

.tab-bar .tab {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  height: 100%;
  color: var(--light-text);
  text-decoration: none;
  font-size: 10px;
}

.tab-bar .tab.active {
  color: var(--primary-color);
}

.tab-bar .tab i {
  font-size: 20px;
  margin-bottom: 2px;
}

.content {
  padding: 16px;
  padding-bottom: 66px; /* Account for tab bar */
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-title .more {
  font-size: 14px;
  color: var(--light-text);
  font-weight: normal;
}

.card {
  background-color: var(--white);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.btn {
  display: inline-block;
  padding: 10px 20px;
  background-color: var(--primary-color);
  color: var(--white);
  border-radius: 8px;
  text-align: center;
  font-weight: 500;
  border: none;
  cursor: pointer;
  text-decoration: none;
}

.btn-block {
  display: block;
  width: 100%;
}

.btn-outline {
  background-color: transparent;
  border: 1px solid var(--primary-color);
  color: var(--primary-color);
}

.btn-sm {
  padding: 6px 12px;
  font-size: 14px;
}

.form-group {
  margin-bottom: 16px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.form-control {
  width: 100%;
  padding: 12px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  font-size: 16px;
  box-sizing: border-box;
}

.form-control:focus {
  border-color: var(--primary-color);
  outline: none;
}

.grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.grid-3 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
}

.product-card {
  background-color: var(--white);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.product-card img {
  width: 100%;
  height: 120px;
  object-fit: cover;
}

.product-card .info {
  padding: 10px;
}

.product-card .title {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-card .price {
  color: var(--error-color);
  font-weight: 600;
  font-size: 16px;
}

.badge {
  display: inline-block;
  padding: 2px 8px;
  background-color: var(--primary-color);
  color: var(--white);
  border-radius: 4px;
  font-size: 12px;
  margin-right: 4px;
}

.badge-outline {
  background-color: transparent;
  border: 1px solid var(--primary-color);
  color: var(--primary-color);
}

.divider {
  height: 1px;
  background-color: var(--border-color);
  margin: 16px 0;
}

.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.avatar-lg {
  width: 60px;
  height: 60px;
}

.flex {
  display: flex;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.gap-2 {
  gap: 8px;
}

.gap-3 {
  gap: 12px;
}

.mt-2 {
  margin-top: 8px;
}

.mb-2 {
  margin-bottom: 8px;
}

.mt-3 {
  margin-top: 12px;
}

.mb-3 {
  margin-bottom: 12px;
}

.text-primary {
  color: var(--primary-color);
}

.text-success {
  color: var(--success-color);
}

.text-warning {
  color: var(--warning-color);
}

.text-error {
  color: var(--error-color);
}

.text-light {
  color: var(--light-text);
}

.text-sm {
  font-size: 14px;
}

.text-xs {
  font-size: 12px;
}

.text-center {
  text-align: center;
}

.font-bold {
  font-weight: 600;
}

.search-bar {
  display: flex;
  background-color: #f0f0f0;
  border-radius: 8px;
  padding: 8px 12px;
  align-items: center;
  margin-bottom: 16px;
}

.search-bar input {
  border: none;
  background-color: transparent;
  flex: 1;
  margin-left: 8px;
  font-size: 14px;
}

.search-bar input:focus {
  outline: none;
}

.banner {
  width: 100%;
  height: 150px;
  border-radius: 12px;
  overflow: hidden;
  margin-bottom: 16px;
}

.banner img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.category-list {
  display: flex;
  overflow-x: auto;
  gap: 16px;
  padding-bottom: 12px;
  margin-bottom: 16px;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 60px;
}

.category-icon {
  width: 50px;
  height: 50px;
  background-color: #e3f2fd;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 8px;
  color: var(--primary-color);
}

.category-name {
  font-size: 12px;
  text-align: center;
}

/* Hide scrollbar for Chrome, Safari and Opera */
.category-list::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.category-list {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.rating {
  display: flex;
  gap: 2px;
  color: #ffc107;
  font-size: 14px;
}

.steps {
  display: flex;
  margin-bottom: 24px;
}

.step {
  flex: 1;
  text-align: center;
  position: relative;
}

.step:not(:last-child):after {
  content: '';
  position: absolute;
  top: 12px;
  right: -50%;
  width: 100%;
  height: 2px;
  background-color: var(--border-color);
  z-index: 1;
}

.step.active:not(:last-child):after {
  background-color: var(--primary-color);
}

.step-number {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: var(--border-color);
  color: var(--white);
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto 8px;
  position: relative;
  z-index: 2;
  font-size: 12px;
}

.step.active .step-number {
  background-color: var(--primary-color);
}

.step-label {
  font-size: 12px;
  color: var(--light-text);
}

.step.active .step-label {
  color: var(--primary-color);
  font-weight: 500;
}

.quantity-selector {
  display: flex;
  align-items: center;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  overflow: hidden;
  width: fit-content;
}

.quantity-btn {
  width: 28px;
  height: 28px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;
  border: none;
  cursor: pointer;
}

.quantity-input {
  width: 40px;
  height: 28px;
  border: none;
  text-align: center;
  font-size: 14px;
}

.quantity-input:focus {
  outline: none;
}

.checkbox {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 1px solid var(--border-color);
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.checkbox.checked {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: var(--white);
}

.map-container {
  height: 200px;
  border-radius: 12px;
  overflow: hidden;
  margin-bottom: 16px;
  background-color: #e0e0e0;
}

.map-container img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.list-item {
  display: flex;
  padding: 12px 0;
  border-bottom: 1px solid var(--border-color);
}

.list-item:last-child {
  border-bottom: none;
}

.list-item .content {
  flex: 1;
  padding: 0 12px;
}

.list-item .title {
  font-weight: 500;
  margin-bottom: 4px;
}

.list-item .description {
  font-size: 14px;
  color: var(--light-text);
}

.list-item .right {
  display: flex;
  align-items: center;
  color: var(--light-text);
}

.tab-content {
  display: none;
}

.tab-content.active {
  display: block;
}

.tabs {
  display: flex;
  border-bottom: 1px solid var(--border-color);
  margin-bottom: 16px;
}

.tab {
  padding: 12px 16px;
  font-size: 14px;
  cursor: pointer;
  position: relative;
}

.tab.active {
  color: var(--primary-color);
  font-weight: 500;
}

.tab.active:after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: var(--primary-color);
}
