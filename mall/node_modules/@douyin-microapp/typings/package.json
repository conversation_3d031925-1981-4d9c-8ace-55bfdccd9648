{"_from": "@douyin-microapp/typings@latest", "_id": "@douyin-microapp/typings@1.0.4", "_inBundle": false, "_integrity": "sha512-RcfvymirXV+KQawRtKY4TaMA6oN0j8SJqkst8FiZbduVcuE6i8s3fciovcWW32MjzF69t+h8ci8uogfaTcv9Gg==", "_location": "/@douyin-microapp/typings", "_phantomChildren": {}, "_requested": {"type": "tag", "registry": true, "raw": "@douyin-microapp/typings@latest", "name": "@douyin-microapp/typings", "escapedName": "@douyin-microapp%2ftypings", "scope": "@douyin-microapp", "rawSpec": "latest", "saveSpec": null, "fetchSpec": "latest"}, "_requiredBy": ["#DEV:/"], "_resolved": "https://registry.npmjs.org/@douyin-microapp/typings/-/typings-1.0.4.tgz", "_shasum": "b7a616cd3b99bad8d4ef552c14b20e4cd6cb6de8", "_spec": "@douyin-microapp/typings@latest", "_where": "/Users/<USER>/ide/miniapp-toolkit/packages/miniprogram-ide/src/applications/microapp/assets/templates/typescript/app-ts/template", "author": "", "bundleDependencies": false, "deprecated": false, "description": "Type definitions for APIs of Douyin Mini Program in TypeScript", "license": "MIT", "main": "index.d.ts", "name": "@douyin-microapp/typings", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "version": "1.0.4"}