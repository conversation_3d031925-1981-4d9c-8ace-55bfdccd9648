# 🔄 微信小程序适配方案

## 📋 问题分析

### 当前抖音小程序架构
```
用户登录 → 抖音code → 后端调用抖音API → 获取抖音openId → 用户识别
```

### 微信小程序需要的架构
```
用户登录 → 微信code → 后端调用微信API → 获取微信openId → 用户识别
```

### 核心挑战
- **不同平台的openId不同**：同一用户在抖音和微信有不同的openId
- **API接口差异**：抖音和微信的登录API不同
- **用户数据隔离**：需要统一用户身份识别

## 🛠️ 解决方案

### 方案一：多平台统一用户系统（推荐）

#### 1. 数据库结构调整

**用户表增加平台字段**：
```sql
ALTER TABLE users ADD COLUMN platform VARCHAR(20) DEFAULT 'douyin';
ALTER TABLE users ADD COLUMN platform_open_id VARCHAR(100);
ALTER TABLE users ADD COLUMN unified_user_id VARCHAR(100);

-- 创建索引
CREATE INDEX idx_platform_openid ON users(platform, platform_open_id);
CREATE INDEX idx_unified_user_id ON users(unified_user_id);
```

**用户绑定表**（可选，用于跨平台账号绑定）：
```sql
CREATE TABLE user_platform_bindings (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    unified_user_id VARCHAR(100) NOT NULL,
    platform VARCHAR(20) NOT NULL,
    platform_open_id VARCHAR(100) NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_platform_openid (platform, platform_open_id),
    KEY idx_unified_user_id (unified_user_id)
);
```

#### 2. 后端适配

**新增微信API工具类**：
```java
@Component
public class WxApiUtil {
    @Value("${wx.appid}")
    private String appid;
    
    @Value("${wx.secret}")
    private String secret;
    
    public Map<String, Object> code2Session(String code) {
        String url = "https://api.weixin.qq.com/sns/jscode2session" +
                "?appid=" + appid +
                "&secret=" + secret +
                "&js_code=" + code +
                "&grant_type=authorization_code";
        
        // 调用微信API
        // 返回包含openid和session_key的结果
    }
}
```

**统一用户服务**：
```java
@Service
public class UnifiedUserService {
    
    public Map<String, Object> platformLogin(String code, String platform, Map<String, Object> userInfo) {
        String openId;
        String sessionKey;
        
        // 根据平台调用不同的API
        if ("douyin".equals(platform)) {
            Map<String, Object> result = ttApiUtil.code2Session(code);
            openId = (String) result.get("openid");
            sessionKey = (String) result.get("session_key");
        } else if ("wechat".equals(platform)) {
            Map<String, Object> result = wxApiUtil.code2Session(code);
            openId = (String) result.get("openid");
            sessionKey = (String) result.get("session_key");
        } else {
            throw new IllegalArgumentException("不支持的平台: " + platform);
        }
        
        // 查找或创建用户
        User user = findOrCreateUser(platform, openId, userInfo);
        
        return buildLoginResult(user, openId, sessionKey);
    }
    
    private User findOrCreateUser(String platform, String openId, Map<String, Object> userInfo) {
        // 先查找是否存在该平台的用户
        User user = userMapper.findByPlatformAndOpenId(platform, openId);
        
        if (user == null) {
            // 创建新用户
            user = new User();
            user.setPlatform(platform);
            user.setPlatformOpenId(openId);
            user.setUnifiedUserId(generateUnifiedUserId());
            
            // 设置用户信息
            if (userInfo != null) {
                user.setNickname((String) userInfo.get("nickName"));
                user.setAvatarUrl((String) userInfo.get("avatarUrl"));
            }
            
            userMapper.insert(user);
        }
        
        return user;
    }
}
```

#### 3. 前端适配

**创建平台适配层**：
```javascript
// utils/platform.js
const getPlatform = () => {
  // 检测当前运行环境
  if (typeof tt !== 'undefined') {
    return 'douyin';
  } else if (typeof wx !== 'undefined') {
    return 'wechat';
  } else {
    return 'unknown';
  }
};

const getPlatformAPI = () => {
  const platform = getPlatform();
  if (platform === 'douyin') {
    return tt;
  } else if (platform === 'wechat') {
    return wx;
  } else {
    throw new Error('不支持的平台');
  }
};

module.exports = {
  getPlatform,
  getPlatformAPI
};
```

**统一登录逻辑**：
```javascript
// utils/unified-auth.js
const platform = require('./platform');

const login = (userInfo = null) => {
  return new Promise((resolve, reject) => {
    const platformAPI = platform.getPlatformAPI();
    const currentPlatform = platform.getPlatform();
    
    platformAPI.login({
      success: (res) => {
        if (res.code) {
          // 调用统一登录接口
          const api = require('./api');
          api.platformLogin(res.code, currentPlatform, userInfo)
            .then(resolve)
            .catch(reject);
        } else {
          reject(new Error('获取登录code失败'));
        }
      },
      fail: reject
    });
  });
};

const getUserProfile = (desc = '用于完善会员资料') => {
  return new Promise((resolve, reject) => {
    const platformAPI = platform.getPlatformAPI();
    
    if (platform.getPlatform() === 'douyin') {
      platformAPI.getUserProfile({
        desc: desc,
        success: resolve,
        fail: reject
      });
    } else if (platform.getPlatform() === 'wechat') {
      platformAPI.getUserProfile({
        desc: desc,
        success: resolve,
        fail: reject
      });
    }
  });
};

module.exports = {
  login,
  getUserProfile
};
```

### 方案二：双平台独立部署

#### 1. 复制项目结构
```
mall-douyin/     # 抖音小程序版本
mall-wechat/     # 微信小程序版本
mall-server/     # 共享后端服务
```

#### 2. 后端支持多平台
```java
@PostMapping("/login/{platform}")
public ResponseEntity<Map<String, Object>> platformLogin(
    @PathVariable String platform,
    @RequestBody Map<String, Object> params) {
    
    String code = (String) params.get("code");
    Map<String, Object> userInfo = (Map<String, Object>) params.get("userInfo");
    
    Map<String, Object> result;
    if ("douyin".equals(platform)) {
        result = douyinUserService.login(code, userInfo);
    } else if ("wechat".equals(platform)) {
        result = wechatUserService.login(code, userInfo);
    } else {
        throw new IllegalArgumentException("不支持的平台");
    }
    
    return ResponseEntity.ok(result);
}
```

## 🎯 推荐实施步骤

### 第一阶段：后端适配
1. **添加微信API工具类**
2. **修改用户表结构**
3. **创建统一登录接口**
4. **测试微信登录流程**

### 第二阶段：前端适配
1. **创建平台检测工具**
2. **统一登录逻辑**
3. **适配微信小程序API**
4. **测试双平台兼容性**

### 第三阶段：数据迁移
1. **现有抖音用户数据标记**
2. **测试用户绑定功能**
3. **验证跨平台数据一致性**

## 🔧 具体实施代码

### 1. 微信小程序配置
```json
// app.json
{
  "appid": "你的微信小程序appid",
  "pages": [
    // 页面列表保持不变
  ],
  "window": {
    "navigationBarTitleText": "桩郎中充电桩维修"
  }
}
```

### 2. 统一API接口
```javascript
// utils/api.js 新增
const platformLogin = (code, platform, userInfo) => {
  return request(`/user/login/${platform}`, 'POST', { code, userInfo });
};
```

### 3. 应用启动适配
```javascript
// app.ts
App({
  onLaunch: function () {
    const platform = require('./utils/platform');
    const currentPlatform = platform.getPlatform();
    
    console.log('当前平台:', currentPlatform);
    
    // 根据平台初始化
    if (currentPlatform === 'douyin') {
      this.initDouyinApp();
    } else if (currentPlatform === 'wechat') {
      this.initWechatApp();
    }
  }
});
```

## ✅ 优势对比

| 方案 | 优势 | 劣势 |
|------|------|------|
| **统一用户系统** | 用户数据统一、维护简单 | 需要改动较大 |
| **独立部署** | 改动最小、风险较低 | 数据分离、维护复杂 |

## 🎉 推荐方案

建议采用 **方案一：多平台统一用户系统**，因为：

1. **长期维护性好**：一套代码支持多平台
2. **用户体验统一**：用户数据可以跨平台
3. **扩展性强**：未来支持更多平台容易
4. **数据一致性**：避免数据分散问题

需要我帮你实施具体的适配代码吗？
