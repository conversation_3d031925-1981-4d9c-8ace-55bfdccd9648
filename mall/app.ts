const auth = require('./utils/auth');

App({
  globalData: {
    userInfo: null,
    userId: null,
    openId: null,
    sessionKey: null,
    sessionToken: null,
    isLoggedIn: false,
    cartItems: [],
    repairInfo: null,
    primaryColor: '#1e88e5'
  },
  onLaunch: function () {
    // 初始化应用时执行
    console.log('🚀 App launched');

    // 初始化登录状态
    const loginState = auth.initLoginState();

    // 检查抖音小程序的session状态
    tt.checkSession({
      success: () => {
        // 登录态有效
        console.log('✅ 抖音Session valid');

        // 如果本地有登录状态，直接使用
        if (loginState.isLoggedIn) {
          console.log('✅ 使用本地登录状态:', loginState.userInfo?.nickName);
        } else {
          // 本地没有登录状态，但抖音session有效，尝试静默登录
          console.log('🔄 尝试静默登录...');
          this.silentLogin();
        }
      },
      fail: () => {
        // 登录态过期，清除本地状态
        console.log('⚠️ 抖音Session过期，清除本地登录状态');
        auth.clearLoginState();
      }
    });
  },

  // 静默登录（不需要用户授权）
  silentLogin: function() {
    const api = require('./utils/api');

    tt.login({
      success: (res) => {
        if (res.code) {
          console.log('🔄 静默登录获取code:', res.code);

          // 只发送code到后端，不获取用户信息
          api.code2Session(res.code).then(loginRes => {
            if (loginRes.success && loginRes.openId) {
              // 保存基本登录状态
              const basicUserInfo = {
                nickName: '用户',
                avatarUrl: ''
              };

              auth.saveLoginState(loginRes.openId, basicUserInfo, loginRes.sessionToken);
              console.log('✅ 静默登录成功，openId:', loginRes.openId);
            } else {
              console.log('⚠️ 静默登录失败，需要用户主动登录');
            }
          }).catch(err => {
            console.error('❌ 静默登录请求失败:', err);
          });
        }
      },
      fail: (err) => {
        console.error('❌ 静默登录失败:', err);
      }
    });
  },

  // 完整登录（需要用户授权）
  login: function() {
    const api = require('./utils/api');

    tt.login({
      success: (res) => {
        if (res.code) {
          console.log('🔐 开始完整登录, code:', res.code);

          // 获取用户信息
          tt.getUserProfile({
            desc: '用于完善会员资料',
            success: (userRes) => {
              // 发送 code 和用户信息到后端
              api.login(res.code, userRes.userInfo).then(loginRes => {
                if (loginRes.success) {
                  // 使用新的认证系统保存登录状态
                  const success = auth.saveLoginState(
                    loginRes.openId,
                    loginRes.userInfo || userRes.userInfo,
                    loginRes.sessionToken
                  );

                  if (success) {
                    console.log('✅ 完整登录成功，用户:', loginRes.userInfo?.nickName);

                    // 显示成功提示
                    tt.showToast({
                      title: '登录成功',
                      icon: 'success'
                    });
                  }
                } else {
                  console.error('❌ 登录失败:', loginRes.message);
                  tt.showToast({
                    title: '登录失败',
                    icon: 'none'
                  });
                }
              }).catch(err => {
                console.error('❌ 登录请求失败:', err);
                tt.showToast({
                  title: '登录失败，请重试',
                  icon: 'none'
                });
              });
            },
            fail: (err) => {
              console.error('❌ 获取用户信息失败:', err);
              tt.showModal({
                title: '提示',
                content: '需要您的授权才能正常使用小程序功能',
                showCancel: false
              });
            }
          });
        } else {
          console.log('❌ 获取登录code失败:', res.errMsg);
          tt.showToast({
            title: '登录失败，请重试',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error('❌ 登录失败:', err);
        tt.showToast({
          title: '登录失败，请重试',
          icon: 'none'
        });
      }
    });
  },

  // 获取用户信息
  getUserInfo: function(cb) {
    // 使用新的认证系统获取用户信息
    const loginState = auth.getLoginState();

    if (loginState.isLoggedIn && loginState.userInfo) {
      // 更新全局状态
      this.globalData.userInfo = loginState.userInfo;
      this.globalData.openId = loginState.openId;
      this.globalData.isLoggedIn = true;
      this.globalData.sessionToken = loginState.sessionToken;

      typeof cb === 'function' && cb(loginState.userInfo);
      return loginState.userInfo;
    } else {
      // 没有登录状态，返回null
      typeof cb === 'function' && cb(null);
      return null;
    }
  },

  // 检查登录状态
  checkLoginStatus: function() {
    return auth.isLoggedIn();
  },

  // 退出登录
  logout: function() {
    auth.clearLoginState();

    // 显示提示
    tt.showToast({
      title: '已退出登录',
      icon: 'success'
    });

    // 可以跳转到首页或登录页
    tt.reLaunch({
      url: '/pages/index/index'
    });
  },

  // 添加商品到购物车
  addToCart: function(product, quantity = 1) {
    let cartItems = this.globalData.cartItems;
    let found = false;

    // 检查商品是否已在购物车中
    for (let i = 0; i < cartItems.length; i++) {
      if (cartItems[i].id === product.id) {
        cartItems[i].quantity += quantity;
        found = true;
        break;
      }
    }

    // 如果是新商品，添加到购物车
    if (!found) {
      product.quantity = quantity;
      cartItems.push(product);
    }

    // 更新全局购物车数据
    this.globalData.cartItems = cartItems;
    return cartItems;
  },

  // 从购物车移除商品
  removeFromCart: function(productId) {
    let cartItems = this.globalData.cartItems;
    this.globalData.cartItems = cartItems.filter(item => item.id !== productId);
    return this.globalData.cartItems;
  },

  // 更新购物车商品数量
  updateCartItemQuantity: function(productId, quantity) {
    let cartItems = this.globalData.cartItems;
    for (let i = 0; i < cartItems.length; i++) {
      if (cartItems[i].id === productId) {
        cartItems[i].quantity = quantity;
        break;
      }
    }
    this.globalData.cartItems = cartItems;
    return cartItems;
  },

  // 保存维修信息
  saveRepairInfo: function(repairInfo) {
    this.globalData.repairInfo = repairInfo;
  }
})
