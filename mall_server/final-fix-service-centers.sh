#!/bin/bash

echo "🔧 最终修复服务网点显示问题"
echo "============================"

# 服务器地址
SERVER_URL="https://localhost:8443"

echo ""
echo "📋 步骤1: 检查当前状态"
echo "API: $SERVER_URL/api/debug/service-centers/all"

echo "获取详细调试信息..."
DEBUG_RESPONSE=$(curl -k -s "$SERVER_URL/api/debug/service-centers/all")
echo "调试信息:"
echo "$DEBUG_RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$DEBUG_RESPONSE"

echo ""
echo "📋 步骤2: 批量审核通过并激活所有服务网点"
echo "API: $SERVER_URL/api/debug/service-centers/approve-and-activate-all"

echo "执行批量处理..."
PROCESS_RESPONSE=$(curl -k -s -X POST "$SERVER_URL/api/debug/service-centers/approve-and-activate-all")
echo "处理结果:"
echo "$PROCESS_RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$PROCESS_RESPONSE"

echo ""
echo "📋 步骤3: 验证最终结果"
echo "API: $SERVER_URL/api/service-centers/approved"

echo "验证已审核通过的服务网点..."
FINAL_RESPONSE=$(curl -k -s "$SERVER_URL/api/service-centers/approved")
echo "最终结果:"
echo "$FINAL_RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$FINAL_RESPONSE"

# 统计最终的服务网点数量
FINAL_COUNT=$(echo "$FINAL_RESPONSE" | grep -o '"id":[0-9]*' | wc -l | tr -d ' ')

echo ""
echo "📊 修复结果总结"
echo "================"
echo "✅ 最终已审核通过的服务网点数量: $FINAL_COUNT"

if [ "$FINAL_COUNT" -ge 6 ]; then
    echo "🎉 修复成功！现在有 $FINAL_COUNT 个服务网点可以在小程序端显示"
    echo ""
    echo "🧪 测试步骤:"
    echo "1. 重新打开小程序服务网点页面"
    echo "2. 下拉刷新数据"
    echo "3. 检查是否显示了所有新增的服务网点"
    echo ""
    echo "📱 小程序端应该显示的服务网点:"
    echo "$FINAL_RESPONSE" | grep -o '"name":"[^"]*"' | sed 's/"name":"//g' | sed 's/"//g' | nl
elif [ "$FINAL_COUNT" -gt 1 ]; then
    echo "⚠️  部分修复成功，有 $FINAL_COUNT 个服务网点"
    echo "如果还有服务网点没有显示，请检查:"
    echo "1. 服务网点的status是否为'approved'"
    echo "2. 服务网点的isActive是否为true"
    echo "3. 小程序端是否正确调用了API"
else
    echo "❌ 修复可能失败，只有 $FINAL_COUNT 个服务网点"
    echo "请检查:"
    echo "1. 服务器是否正常运行"
    echo "2. 数据库连接是否正常"
    echo "3. API接口是否正确响应"
fi

echo ""
echo "🔗 相关链接"
echo "==========="
echo "管理后台: $SERVER_URL/admin/service-centers"
echo "调试页面: $SERVER_URL/admin/quick-approve"
echo "API测试: $SERVER_URL/admin/test-miniprogram-api"
echo "调试API: $SERVER_URL/api/debug/service-centers/all"

echo ""
echo "💡 如果小程序端仍然没有显示新增的服务网点，请检查:"
echo "1. 小程序端网络连接是否正常"
echo "2. 小程序端API调用是否成功"
echo "3. 小程序端是否有缓存需要清理"
echo "4. 小程序端的服务网点页面是否正确调用了getApprovedServiceCenters API"
