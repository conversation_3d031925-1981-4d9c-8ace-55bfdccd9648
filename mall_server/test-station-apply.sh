#!/bin/bash

echo "🧪 测试服务网点申请功能"
echo "========================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 服务器地址
SERVER_URL="https://localhost:8443"

echo ""
echo -e "${BLUE}📋 测试步骤:${NC}"
echo "1. 测试服务网点申请接口"
echo "2. 验证数据库存储"
echo "3. 测试管理端显示"
echo ""

# 测试数据
TEST_DATA='{
  "name": "测试充电站",
  "contactPerson": "张三",
  "phone": "***********",
  "email": "<EMAIL>",
  "address": "北京市朝阳区测试路123号",
  "province": "北京市",
  "city": "北京市",
  "district": "朝阳区",
  "latitude": "39.9042",
  "longitude": "116.4074",
  "businessHours": "08:00-22:00",
  "serviceTypes": "[\"充电桩维修\", \"充电桩安装\"]",
  "equipmentTypes": "[\"直流充电桩\", \"交流充电桩\"]",
  "serviceDescription": "专业的充电桩维修和安装服务",
  "facilities": "[\"停车场\", \"休息区\"]",
  "serviceFee": "100.00",
  "parkingInfo": "免费停车2小时",
  "businessLicense": "/uploads/20250610_143914_aea5df47.png",
  "qualificationCertificates": "[\"/uploads/20250610_143914_aea5df47.png\", \"/uploads/20250610_143914_4a433ed2.png\"]",
  "images": "[\"/uploads/20250610_143914_aea5df47.png\", \"/uploads/20250610_143914_4a433ed2.png\"]"
}'

echo -e "${YELLOW}🔍 测试1: 检查服务器状态${NC}"
if curl -k -s "$SERVER_URL/api/upload/image" -X OPTIONS > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 服务器运行正常${NC}"
else
    echo -e "${RED}❌ 服务器未运行或无法访问${NC}"
    echo "请确保服务器已启动: mvn spring-boot:run"
    exit 1
fi

echo ""
echo -e "${YELLOW}🔍 测试2: 测试服务网点申请接口${NC}"
echo "发送POST请求到: $SERVER_URL/api/applications/station"

RESPONSE=$(curl -k -s -X POST \
  "$SERVER_URL/api/applications/station" \
  -H "Content-Type: application/json" \
  -d "$TEST_DATA")

echo "响应结果:"
echo "$RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$RESPONSE"

# 检查响应是否成功
if echo "$RESPONSE" | grep -q '"success":true'; then
    echo -e "${GREEN}✅ 申请提交成功${NC}"
    
    # 提取申请ID
    APPLICATION_ID=$(echo "$RESPONSE" | grep -o '"applicationId":[0-9]*' | grep -o '[0-9]*')
    if [ -n "$APPLICATION_ID" ]; then
        echo -e "${GREEN}📋 申请ID: $APPLICATION_ID${NC}"
    fi
else
    echo -e "${RED}❌ 申请提交失败${NC}"
    echo "请检查:"
    echo "1. 服务器是否正常运行"
    echo "2. 数据库连接是否正常"
    echo "3. 接口路径是否正确"
fi

echo ""
echo -e "${YELLOW}🔍 测试3: 测试图片访问${NC}"
TEST_IMAGES=(
    "/uploads/20250610_143914_aea5df47.png"
    "/uploads/20250610_143914_4a433ed2.png"
)

for img in "${TEST_IMAGES[@]}"; do
    echo "测试图片: $img"
    if curl -k -s -I "$SERVER_URL$img" | grep -q "200 OK"; then
        echo -e "${GREEN}✅ 图片访问正常${NC}"
    else
        echo -e "${RED}❌ 图片访问失败${NC}"
        echo "请检查图片文件是否存在: .$img"
    fi
done

echo ""
echo -e "${YELLOW}🔍 测试4: 测试管理端页面${NC}"
ADMIN_PAGES=(
    "/admin/service-centers"
    "/admin/test-station-images"
)

for page in "${ADMIN_PAGES[@]}"; do
    echo "测试页面: $page"
    if curl -k -s "$SERVER_URL$page" | grep -q "<html"; then
        echo -e "${GREEN}✅ 页面访问正常${NC}"
    else
        echo -e "${RED}❌ 页面访问失败${NC}"
    fi
done

echo ""
echo -e "${BLUE}📊 测试总结${NC}"
echo "===================="
echo "✅ 如果所有测试都通过，说明功能正常"
echo "❌ 如果有测试失败，请根据提示进行排查"
echo ""
echo -e "${BLUE}🔗 相关链接${NC}"
echo "管理端: $SERVER_URL/admin/service-centers"
echo "测试页面: $SERVER_URL/admin/test-station-images"
echo "API文档: $SERVER_URL/api/applications/station"
echo ""
echo -e "${BLUE}💡 调试建议${NC}"
echo "1. 查看服务器日志: tail -f logs/spring.log"
echo "2. 检查数据库: SELECT * FROM service_centers ORDER BY id DESC LIMIT 5;"
echo "3. 测试图片上传: curl -k -X POST -F 'file=@test.jpg' $SERVER_URL/api/upload/image"
echo "4. 检查网络连接: ping localhost"
