-- 修复图片路径的SQL脚本

-- 1. 查看当前的图片路径格式
SELECT id, images, 
       CASE 
           WHEN images LIKE '%/uploads/%' THEN '包含/uploads/'
           WHEN images LIKE '%uploads/%' THEN '包含uploads/'
           ELSE '其他格式'
       END as path_type
FROM repair_orders 
WHERE images IS NOT NULL AND images != '';

-- 2. 如果路径中包含重复的uploads，进行修复
-- 注意：执行前请先备份数据！

-- 修复重复的/uploads/路径
UPDATE repair_orders 
SET images = REPLACE(images, '/uploads//uploads/', '/uploads/')
WHERE images LIKE '%/uploads//uploads/%';

-- 修复重复的uploads/路径（无前导斜杠）
UPDATE repair_orders 
SET images = REPLACE(images, 'uploads/uploads/', 'uploads/')
WHERE images LIKE '%uploads/uploads/%';

-- 3. 标准化路径格式 - 确保所有路径都以/uploads/开头
UPDATE repair_orders 
SET images = CONCAT('[', 
    GROUP_CONCAT(
        CONCAT('"', 
            CASE 
                WHEN JSON_UNQUOTE(JSON_EXTRACT(images, CONCAT('$[', idx, ']'))) LIKE '/uploads/%' 
                THEN JSON_UNQUOTE(JSON_EXTRACT(images, CONCAT('$[', idx, ']')))
                WHEN JSON_UNQUOTE(JSON_EXTRACT(images, CONCAT('$[', idx, ']'))) LIKE 'uploads/%' 
                THEN CONCAT('/', JSON_UNQUOTE(JSON_EXTRACT(images, CONCAT('$[', idx, ']'))))
                ELSE CONCAT('/uploads/', JSON_UNQUOTE(JSON_EXTRACT(images, CONCAT('$[', idx, ']'))))
            END,
        '"')
    ),
']')
WHERE images IS NOT NULL 
  AND images != '' 
  AND JSON_VALID(images);

-- 4. 验证修复结果
SELECT id, images, 
       JSON_LENGTH(images) as image_count,
       JSON_EXTRACT(images, '$[0]') as first_image
FROM repair_orders 
WHERE images IS NOT NULL AND images != ''
ORDER BY id DESC
LIMIT 10;
