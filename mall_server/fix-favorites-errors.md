# 🔧 修复收藏功能错误

## 📋 问题分析

遇到了两个主要错误：

### 1. 数据库表不存在错误
```
Table 'mall.product_favorites' doesn't exist
```

### 2. API参数缺失错误
```
Required request parameter 'openId' for method parameter type String is not present
```

## ✅ 解决方案

### 1. 创建数据库表

#### 方法一：使用API接口创建（推荐）
```bash
# 访问以下URL来创建表
GET http://localhost:8443/api/database/init-favorites

# 检查表状态
GET http://localhost:8443/api/database/check-favorites
```

#### 方法二：手动执行SQL
```sql
-- 在数据库中手动执行以下SQL
CREATE TABLE IF NOT EXISTS `product_favorites` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '收藏ID',
  `open_id` varchar(100) NOT NULL COMMENT '用户openId',
  `product_id` bigint(20) NOT NULL COMMENT '商品ID',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_product` (`open_id`, `product_id`),
  KEY `idx_open_id` (`open_id`),
  KEY `idx_product_id` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品收藏表';
```

### 2. 修复API参数问题

已修复后端控制器，现在支持JSON格式的请求体：

#### 修复前（有问题）
```java
@PostMapping("/add")
public ResponseEntity<Map<String, Object>> addFavorite(@RequestParam String openId, 
                                                      @RequestParam Long productId)
```

#### 修复后（正确）
```java
@PostMapping("/add")
public ResponseEntity<Map<String, Object>> addFavorite(@RequestBody Map<String, Object> requestBody) {
    String openId = (String) requestBody.get("openId");
    Long productId = Long.valueOf(requestBody.get("productId").toString());
    // ...
}
```

## 🧪 测试步骤

### 1. 重启后端服务
```bash
# 重启Spring Boot应用以加载新的控制器
```

### 2. 创建数据库表
```bash
# 方法一：使用浏览器访问
https://localhost:8443/api/database/init-favorites

# 方法二：使用curl
curl -k https://localhost:8443/api/database/init-favorites
```

### 3. 验证表创建成功
```bash
# 检查表状态
curl -k https://localhost:8443/api/database/check-favorites
```

期望返回：
```json
{
  "success": true,
  "tableExists": true,
  "recordCount": 0,
  "message": "product_favorites表存在，包含 0 条记录"
}
```

### 4. 测试收藏功能

#### 测试添加收藏
```bash
curl -k -X POST https://localhost:8443/api/favorites/add \
  -H "Content-Type: application/json" \
  -d '{"openId": "test_user", "productId": 1}'
```

期望返回：
```json
{
  "success": true,
  "message": "收藏成功"
}
```

#### 测试检查收藏状态
```bash
curl -k "https://localhost:8443/api/favorites/check?openId=test_user&productId=1"
```

期望返回：
```json
{
  "success": true,
  "isFavorite": true
}
```

#### 测试获取收藏列表
```bash
curl -k "https://localhost:8443/api/favorites/list?openId=test_user"
```

期望返回：
```json
{
  "success": true,
  "products": [...],
  "count": 1
}
```

### 5. 测试前端功能

1. **确保用户已登录**
   - 检查 `app.globalData.openId` 是否存在

2. **测试商品详情页收藏**
   - 进入商品详情页
   - 点击收藏按钮
   - 验证收藏状态变化

3. **测试收藏列表页**
   - 从user_center进入收藏页面
   - 验证收藏商品显示
   - 测试取消收藏功能

## 🔍 故障排除

### 1. 如果表创建失败

#### 检查数据库连接
```bash
# 检查数据库是否可访问
curl -k https://localhost:8443/api/database/check-favorites
```

#### 手动创建表
```sql
-- 连接到MySQL数据库
mysql -u root -p

-- 选择数据库
USE mall;

-- 创建表
CREATE TABLE IF NOT EXISTS `product_favorites` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '收藏ID',
  `open_id` varchar(100) NOT NULL COMMENT '用户openId',
  `product_id` bigint(20) NOT NULL COMMENT '商品ID',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_product` (`open_id`, `product_id`),
  KEY `idx_open_id` (`open_id`),
  KEY `idx_product_id` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品收藏表';

-- 验证表创建
SHOW TABLES LIKE 'product_favorites';
DESCRIBE product_favorites;
```

### 2. 如果API调用仍然失败

#### 检查请求格式
确保前端发送的是JSON格式：
```javascript
// 正确的API调用
api.addFavorite(openId, productId)
  .then(res => {
    console.log('收藏成功:', res);
  })
  .catch(err => {
    console.error('收藏失败:', err);
  });
```

#### 检查Content-Type
确保请求头包含：
```
Content-Type: application/json
```

### 3. 如果前端仍然报错

#### 检查登录状态
```javascript
// 在收藏操作前检查
const openId = app.globalData.openId;
if (!openId) {
  console.error('用户未登录');
  return;
}
```

#### 检查商品ID
```javascript
// 确保商品ID是有效的数字
const productId = parseInt(this.data.id);
if (!productId) {
  console.error('商品ID无效');
  return;
}
```

## 🎯 验证清单

- [ ] 后端服务已重启
- [ ] product_favorites表已创建
- [ ] 表结构正确（包含id, open_id, product_id, created_at字段）
- [ ] API接口可以正常访问
- [ ] 添加收藏功能正常
- [ ] 检查收藏状态功能正常
- [ ] 获取收藏列表功能正常
- [ ] 取消收藏功能正常
- [ ] 前端收藏按钮状态正确
- [ ] 收藏列表页面显示正常

## 🎉 完成后的功能

修复完成后，你将拥有：

1. **完整的数据库支持**
   - product_favorites表正确创建
   - 支持用户收藏数据持久化

2. **正常的API接口**
   - 添加收藏：POST /api/favorites/add
   - 取消收藏：POST /api/favorites/remove
   - 检查状态：GET /api/favorites/check
   - 获取列表：GET /api/favorites/list

3. **完善的前端功能**
   - 商品详情页收藏按钮
   - 收藏列表页面
   - 实时状态同步

按照这个指南操作，收藏功能应该就能正常工作了！🚀
