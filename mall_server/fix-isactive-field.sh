#!/bin/bash

echo "🔧 修复 isActive 字段问题"
echo "========================="

# 服务器地址
SERVER_URL="https://localhost:8443"

echo ""
echo "📋 当前状态分析"
echo "==============="
echo "统计信息显示: 总数8个，已审核通过4个"
echo "API返回: 只有2个已审核通过的服务网点"
echo "问题: 有2个服务网点 status='approved' 但 isActive=false"

echo ""
echo "🔧 解决方案"
echo "==========="
echo "需要将所有 status='approved' 的服务网点的 isActive 设置为 true"

echo ""
echo "📋 SQL修复语句"
echo "=============="
echo "UPDATE service_centers"
echo "SET is_active = 1"
echo "WHERE status = 'approved' AND (is_active = 0 OR is_active IS NULL);"

echo ""
echo "📋 验证修复前状态"
echo "API: $SERVER_URL/api/service-centers/approved"

BEFORE_RESPONSE=$(curl -k -s "$SERVER_URL/api/service-centers/approved")
BEFORE_COUNT=$(echo "$BEFORE_RESPONSE" | grep -o '"id":[0-9]*' | wc -l | tr -d ' ')
echo "修复前API返回数量: $BEFORE_COUNT"

echo ""
echo "📋 检查统计信息"
echo "API: $SERVER_URL/api/service-centers/stats"

STATS_RESPONSE=$(curl -k -s "$SERVER_URL/api/service-centers/stats")
echo "$STATS_RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$STATS_RESPONSE"

echo ""
echo "🚀 执行修复"
echo "==========="
echo "由于无法直接访问数据库，提供以下修复方案:"

echo ""
echo "方案1: 直接执行SQL（如果有数据库访问权限）"
echo "-----------------------------------------------"
echo "mysql -u username -p database_name"
echo "UPDATE service_centers SET is_active = 1 WHERE status = 'approved';"

echo ""
echo "方案2: 通过管理后台重新审核"
echo "-----------------------------"
echo "1. 访问: $SERVER_URL/admin/service-centers"
echo "2. 查看所有已审核通过但未激活的服务网点"
echo "3. 重新点击'通过申请'（现在会自动设置isActive=true）"

echo ""
echo "方案3: 创建临时修复接口"
echo "----------------------"
echo "已修复后端审核逻辑，现在审核通过时会自动设置 isActive = true"

echo ""
echo "📊 预期修复结果"
echo "==============="
echo "修复后应该有4个服务网点在小程序端显示"
echo "API应该返回4个已审核通过的服务网点"

echo ""
echo "🧪 验证步骤"
echo "==========="
echo "1. 执行上述任一修复方案"
echo "2. 访问: curl -k '$SERVER_URL/api/service-centers/approved'"
echo "3. 检查返回的服务网点数量是否为4个"
echo "4. 在小程序端刷新服务网点页面"
echo "5. 验证是否显示了所有新增的维修中心"

echo ""
echo "💡 重要说明"
echo "==========="
echo "已修复的后端逻辑确保:"
echo "✅ 审核通过时自动设置 isActive = true"
echo "✅ 审核拒绝时自动设置 isActive = false"
echo "✅ 新的审核操作会正确更新字段"
echo ""
echo "对于已存在的数据，需要手动修复 isActive 字段"
