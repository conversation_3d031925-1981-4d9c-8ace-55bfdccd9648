# 👥 后端用户登录系统说明

## 📋 系统概述

你的后端有两套独立的用户系统：

### 1. 🎯 小程序用户系统 (`users` 表)
- **用途**：普通用户使用小程序
- **登录方式**：抖音小程序授权登录
- **创建方式**：**自动创建**（用户首次登录时）
- **标识**：基于抖音 `openId`

### 2. 🔐 管理员用户系统 (`admin_users` 表)
- **用途**：后台管理系统
- **登录方式**：用户名 + 密码
- **创建方式**：**手动创建**（需要管理员权限）
- **标识**：基于用户名

## 🚀 如何添加新用户

### 方案一：小程序用户（自动创建）

**无需手动操作**，用户使用小程序时会自动创建：

1. 用户在小程序中点击登录
2. 小程序获取抖音授权 code
3. 后端调用抖音API获取 openId
4. 如果用户不存在，自动创建新用户记录

**代码逻辑**：
```java
// UserController.code2Session 方法
if (user == null) {
    // 自动创建新用户
    user = new User();
    user.setOpenId(openId);
    user.setStatus(1);
    user.setCreatedAt(LocalDateTime.now());
    userService.save(user);
}
```

### 方案二：管理员用户（手动创建）

#### 🔧 方法1：使用新增的API接口（推荐）

**API地址**：`POST /admin/api/admin-users`

**请求示例**：
```bash
curl -k -X POST "https://localhost:8443/admin/api/admin-users" \
  -H "Content-Type: application/json" \
  -H "Cookie: JSESSIONID=你的session" \
  -d '{
    "username": "newadmin",
    "password": "admin123",
    "realName": "新管理员",
    "email": "<EMAIL>",
    "phone": "13800000003",
    "role": "admin",
    "permissions": "[\"engineer_management\", \"order_management\"]"
  }'
```

**权限要求**：
- ✅ 必须是已登录的超级管理员
- ✅ 只有 `super_admin` 角色可以创建新管理员

**字段说明**：
- `username`：用户名（必填，唯一）
- `password`：密码（必填，会自动加密）
- `realName`：真实姓名（必填）
- `email`：邮箱（可选）
- `phone`：手机号（可选）
- `role`：角色（可选，默认 `admin`）
- `permissions`：权限列表（可选，JSON数组格式）

#### 🔧 方法2：直接数据库操作

**SQL语句**：
```sql
-- 添加新管理员（密码：admin123）
INSERT INTO `admin_users` (
    `username`, 
    `password`, 
    `real_name`, 
    `email`, 
    `phone`, 
    `role`, 
    `permissions`, 
    `status`, 
    `created_at`, 
    `updated_at`, 
    `password_updated_at`
) VALUES (
    'newadmin',  -- 用户名
    '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXIGfkPNtmismLQdQIG.ZjUON4C',  -- 密码：admin123
    '新管理员',   -- 真实姓名
    '<EMAIL>',  -- 邮箱
    '13800000003',  -- 手机号
    'admin',  -- 角色
    '["engineer_management", "service_center_management", "order_management"]',  -- 权限
    1,  -- 状态：启用
    NOW(), 
    NOW(), 
    NOW()
);
```

## 📊 现有账户信息

### 默认管理员账户

| 用户名 | 密码 | 角色 | 权限 |
|--------|------|------|------|
| `admin` | `admin123` | `super_admin` | 全部权限 |
| `operator` | `admin123` | `admin` | 部分权限 |

### 登录地址
- **管理后台**：`https://localhost:8443/admin/login`
- **小程序**：通过抖音小程序授权

## 🔐 权限系统

### 角色类型
- **super_admin**：超级管理员（全部权限）
- **admin**：普通管理员（部分权限）
- **operator**：操作员（基础权限）

### 权限列表
```json
[
  "user_management",           // 用户管理
  "engineer_management",       // 工程师管理
  "service_center_management", // 服务网点管理
  "order_management",          // 订单管理
  "review_management",         // 审核管理
  "system_management"          // 系统管理
]
```

## 🛠️ 使用步骤

### 添加新管理员的完整流程

#### 1. 登录超级管理员账户
```bash
# 访问管理后台
https://localhost:8443/admin/login

# 使用默认账户登录
用户名：admin
密码：admin123
```

#### 2. 调用创建API
```bash
# 获取登录后的 session cookie
# 然后调用创建API
curl -k -X POST "https://localhost:8443/admin/api/admin-users" \
  -H "Content-Type: application/json" \
  -H "Cookie: JSESSIONID=你的session" \
  -d '{
    "username": "manager001",
    "password": "manager123",
    "realName": "业务经理",
    "email": "<EMAIL>",
    "phone": "13800000004",
    "role": "admin"
  }'
```

#### 3. 验证创建结果
```bash
# 成功响应
{
  "success": true,
  "message": "管理员创建成功",
  "data": {
    "id": 3,
    "username": "manager001",
    "realName": "业务经理",
    "role": "admin"
  }
}
```

#### 4. 测试新账户登录
```bash
# 使用新账户登录
用户名：manager001
密码：manager123
```

## 🔍 常见问题

### Q1: 小程序用户如何查看？
**A**: 小程序用户存储在 `users` 表中，可以通过数据库查询：
```sql
SELECT * FROM users ORDER BY created_at DESC;
```

### Q2: 忘记管理员密码怎么办？
**A**: 可以直接更新数据库：
```sql
-- 重置密码为 admin123
UPDATE admin_users 
SET password = '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXIGfkPNtmismLQdQIG.ZjUON4C',
    password_updated_at = NOW()
WHERE username = '用户名';
```

### Q3: 如何修改用户权限？
**A**: 更新 permissions 字段：
```sql
UPDATE admin_users 
SET permissions = '["engineer_management", "order_management"]',
    updated_at = NOW()
WHERE username = '用户名';
```

### Q4: 如何禁用用户？
**A**: 设置 status 为 0：
```sql
UPDATE admin_users 
SET status = 0,
    updated_at = NOW()
WHERE username = '用户名';
```

## 🎯 推荐使用方式

### 对于普通用户
- ✅ **使用小程序登录**：让用户通过抖音小程序授权登录
- ✅ **自动创建账户**：无需手动干预，系统自动处理

### 对于管理员用户
- ✅ **使用API创建**：通过新增的API接口创建管理员
- ✅ **权限控制**：根据需要分配不同的角色和权限
- ✅ **安全管理**：定期更换密码，合理分配权限

现在你可以通过API或数据库操作来添加新的管理员用户了！🎉
