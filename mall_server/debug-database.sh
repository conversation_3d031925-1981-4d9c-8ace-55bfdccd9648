#!/bin/bash

echo "🔍 调试数据库中的服务网点数据"
echo "================================"

# 服务器地址
SERVER_URL="https://localhost:8443"

echo ""
echo "📋 步骤1: 检查API返回的数据"
echo "API: $SERVER_URL/api/service-centers/approved"

APPROVED_RESPONSE=$(curl -k -s "$SERVER_URL/api/service-centers/approved")
echo "API响应:"
echo "$APPROVED_RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$APPROVED_RESPONSE"

echo ""
echo "📋 步骤2: 检查统计信息"
echo "API: $SERVER_URL/api/service-centers/stats"

STATS_RESPONSE=$(curl -k -s "$SERVER_URL/api/service-centers/stats")
echo "统计信息:"
echo "$STATS_RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$STATS_RESPONSE"

echo ""
echo "📋 步骤3: 测试不同的查询条件"
echo "创建测试查询接口..."

# 创建测试查询的curl命令
echo ""
echo "🔧 建议的调试步骤:"
echo "1. 检查数据库表结构:"
echo "   DESCRIBE service_centers;"
echo ""
echo "2. 查看所有服务网点的状态和激活状态:"
echo "   SELECT id, name, status, is_active, created_at FROM service_centers ORDER BY id;"
echo ""
echo "3. 查看符合条件的服务网点:"
echo "   SELECT * FROM service_centers WHERE status = 'approved';"
echo ""
echo "4. 查看is_active字段的数据类型和值:"
echo "   SELECT id, name, status, is_active, TYPEOF(is_active) as type FROM service_centers;"
echo ""
echo "5. 测试不同的查询条件:"
echo "   SELECT * FROM service_centers WHERE status = 'approved' AND is_active = true;"
echo "   SELECT * FROM service_centers WHERE status = 'approved' AND is_active = 1;"
echo "   SELECT * FROM service_centers WHERE status = 'approved' AND is_active IS NOT NULL;"

echo ""
echo "📊 问题分析"
echo "============"

# 分析API响应
APPROVED_COUNT=$(echo "$APPROVED_RESPONSE" | grep -o '"id":[0-9]*' | wc -l | tr -d ' ')
echo "当前API返回的已审核通过服务网点数量: $APPROVED_COUNT"

if [ "$APPROVED_COUNT" -eq 0 ]; then
    echo ""
    echo "❌ 问题: API没有返回任何已审核通过的服务网点"
    echo "可能原因:"
    echo "1. 数据库中没有 status = 'approved' 的记录"
    echo "2. is_active 字段的值不是预期的格式"
    echo "3. SQL查询条件有问题"
    echo "4. 数据库字段名不匹配"
elif [ "$APPROVED_COUNT" -eq 1 ]; then
    echo ""
    echo "⚠️  问题: 只返回了1个服务网点，但应该有更多"
    echo "可能原因:"
    echo "1. 其他服务网点的 status 不是 'approved'"
    echo "2. 其他服务网点的 is_active 不是 1 或 true"
    echo "3. 审核操作没有正确更新数据库"
else
    echo ""
    echo "✅ API返回了 $APPROVED_COUNT 个服务网点，看起来正常"
fi

echo ""
echo "🔧 修复建议"
echo "============"
echo "1. 直接查看数据库数据:"
echo "   登录数据库，执行上述SQL查询"
echo ""
echo "2. 检查审核操作是否成功:"
echo "   确认管理后台的审核操作确实更新了数据库"
echo ""
echo "3. 修复数据库数据:"
echo "   UPDATE service_centers SET status = 'approved', is_active = 1 WHERE id > 1;"
echo ""
echo "4. 检查字段映射:"
echo "   确认Java实体类的字段名与数据库字段名匹配"
echo ""
echo "5. 测试修复后的API:"
echo "   curl -k '$SERVER_URL/api/service-centers/approved'"
