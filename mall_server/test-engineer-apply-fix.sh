#!/bin/bash

echo "🔧 测试工程师申请接口修复"
echo "=========================="

# 服务器地址
SERVER_URL="https://localhost:8443"

echo ""
echo "📋 问题分析"
echo "==========="
echo "问题: 小程序端工程师入驻返回提交失败null"
echo "可能原因:"
echo "1. 缺少openId字段"
echo "2. 必要字段验证失败"
echo "3. 数据库保存失败"
echo "4. 后端异常未正确处理"

echo ""
echo "🧪 测试1: 基本API连通性"
echo "========================"

echo "GET $SERVER_URL/api/engineers/approved"
BASIC_RESPONSE=$(curl -k -s "$SERVER_URL/api/engineers/approved")
if echo "$BASIC_RESPONSE" | grep -q '"success"'; then
    echo "✅ 基本API连通正常"
else
    echo "❌ 基本API连通失败"
    echo "响应: $BASIC_RESPONSE"
fi

echo ""
echo "🧪 测试2: 缺少openId的申请"
echo "=========================="

TEST_DATA_NO_OPENID='{
  "name": "测试工程师",
  "phone": "13800138000",
  "email": "<EMAIL>",
  "gender": "男",
  "age": "30",
  "specialties": "[\"充电桩安装\"]",
  "experienceYears": "5",
  "education": "大专",
  "certifications": "[\"电工证\"]",
  "skills": "[\"电路维修\"]",
  "workAreas": "[\"成都市\"]"
}'

echo "POST $SERVER_URL/api/engineers/apply (无openId)"
NO_OPENID_RESPONSE=$(curl -k -s -X POST \
  -H "Content-Type: application/json" \
  -d "$TEST_DATA_NO_OPENID" \
  "$SERVER_URL/api/engineers/apply")

echo "响应:"
echo "$NO_OPENID_RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$NO_OPENID_RESPONSE"

echo ""
echo "🧪 测试3: 包含openId的完整申请"
echo "============================="

TEST_DATA_WITH_OPENID='{
  "openId": "test_openid_123456",
  "name": "测试工程师",
  "phone": "13800138000",
  "email": "<EMAIL>",
  "gender": "男",
  "age": "30",
  "idCard": "123456789012345678",
  "specialties": "[\"充电桩安装\", \"充电桩维修\"]",
  "experienceYears": "5",
  "education": "大专",
  "certifications": "[\"电工证\", \"技能证书\"]",
  "skills": "[\"电路维修\", \"设备安装\"]",
  "workAreas": "[\"成都市\", \"绵阳市\"]",
  "workTime": "周一至周五 9:00-18:00",
  "hourlyRate": "100",
  "serviceFee": "50",
  "bio": "专业充电桩维修工程师",
  "introduction": "拥有5年充电桩维修经验",
  "workPhotos": "[\"https://example.com/photo1.jpg\"]"
}'

echo "POST $SERVER_URL/api/engineers/apply (包含openId)"
WITH_OPENID_RESPONSE=$(curl -k -s -X POST \
  -H "Content-Type: application/json" \
  -d "$TEST_DATA_WITH_OPENID" \
  "$SERVER_URL/api/engineers/apply")

echo "响应:"
echo "$WITH_OPENID_RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$WITH_OPENID_RESPONSE"

echo ""
echo "🧪 测试4: 缺少必要字段的申请"
echo "=========================="

TEST_DATA_MISSING_FIELDS='{
  "openId": "test_openid_123456"
}'

echo "POST $SERVER_URL/api/engineers/apply (缺少必要字段)"
MISSING_FIELDS_RESPONSE=$(curl -k -s -X POST \
  -H "Content-Type: application/json" \
  -d "$TEST_DATA_MISSING_FIELDS" \
  "$SERVER_URL/api/engineers/apply")

echo "响应:"
echo "$MISSING_FIELDS_RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$MISSING_FIELDS_RESPONSE"

echo ""
echo "📊 测试结果分析"
echo "==============="

# 分析测试结果
if echo "$WITH_OPENID_RESPONSE" | grep -q '"success":true'; then
    echo "✅ 包含openId的完整申请成功"
    APPLICATION_ID=$(echo "$WITH_OPENID_RESPONSE" | grep -o '"applicationId":[0-9]*' | grep -o '[0-9]*')
    if [ -n "$APPLICATION_ID" ]; then
        echo "📝 申请ID: $APPLICATION_ID"
    fi
elif echo "$WITH_OPENID_RESPONSE" | grep -q '"success":false'; then
    echo "⚠️  包含openId的申请返回失败"
    ERROR_MSG=$(echo "$WITH_OPENID_RESPONSE" | grep -o '"message":"[^"]*"' | cut -d'"' -f4)
    echo "错误信息: $ERROR_MSG"
else
    echo "❌ 包含openId的申请无响应或格式错误"
fi

if echo "$MISSING_FIELDS_RESPONSE" | grep -q '"success":false'; then
    echo "✅ 缺少必要字段的验证正常工作"
else
    echo "⚠️  缺少必要字段的验证可能有问题"
fi

echo ""
echo "🔧 修复说明"
echo "============"
echo "已完成的修复:"
echo "1. ✅ 后端添加了openId字段处理"
echo "2. ✅ 后端添加了必要字段验证"
echo "3. ✅ 后端添加了详细错误日志"
echo "4. ✅ 小程序端添加了openId获取逻辑"
echo "5. ✅ 小程序端添加了提交前验证"

echo ""
echo "📱 小程序端测试步骤"
echo "=================="
echo "1. 重新启动小程序开发工具"
echo "2. 清除小程序缓存和存储"
echo "3. 重新进入工程师申请页面"
echo "4. 检查控制台是否输出openId"
echo "5. 填写完整表单并提交"
echo "6. 查看控制台的提交数据日志"

echo ""
echo "🖥️ 后端日志检查"
echo "==============="
echo "查看服务器控制台输出:"
echo "- 🚀 收到工程师申请数据: ..."
echo "- 如果有异常，会打印详细错误信息"

echo ""
echo "💡 常见问题解决"
echo "==============="
echo "如果仍然失败，请检查:"
echo "1. 小程序是否成功获取到openId"
echo "2. 提交的数据格式是否正确"
echo "3. 服务器数据库连接是否正常"
echo "4. 后端控制台是否有错误日志"

echo ""
echo "✅ 修复完成"
echo "============"
echo "现在工程师申请应该可以正常工作了！"
