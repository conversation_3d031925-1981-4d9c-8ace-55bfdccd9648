# 🔧 工程师服务次数自增功能实现

## 🎯 功能目标

实现工程师服务次数自动统计功能：
- **接单时**：工程师总接单数 +1
- **完成订单时**：工程师完成订单数 +1，自动更新成功率

## 📊 数据库字段说明

### Engineers表相关字段
```sql
-- 评价信息
`rating` decimal(3,2) DEFAULT '5.00' COMMENT '平均评分',
`total_orders` int(11) DEFAULT '0' COMMENT '总接单数',
`completed_orders` int(11) DEFAULT '0' COMMENT '完成订单数',
`success_rate` decimal(5,2) DEFAULT '100.00' COMMENT '成功率',
```

### RepairOrders表相关字段
```sql
`status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '订单状态（pending-待接单，accepted-待上门，processing-维修中，completed-已完成）',
`engineer_id` bigint(20) DEFAULT NULL COMMENT '工程师ID',
`engineer_name` varchar(50) DEFAULT NULL COMMENT '工程师姓名',
`engineer_phone` varchar(20) DEFAULT NULL COMMENT '工程师电话',
`completed_at` datetime DEFAULT NULL COMMENT '完成时间',
```

## 🛠️ 实现方案

### 1. EngineerMapper新增方法 ✅

```java
/**
 * 增加工程师总接单数
 */
@Update("UPDATE engineers SET total_orders = total_orders + 1, updated_at = NOW() WHERE id = #{engineerId}")
int incrementTotalOrders(@Param("engineerId") Long engineerId);

/**
 * 增加工程师完成订单数
 */
@Update("UPDATE engineers SET completed_orders = completed_orders + 1, updated_at = NOW() WHERE id = #{engineerId}")
int incrementCompletedOrders(@Param("engineerId") Long engineerId);

/**
 * 更新工程师成功率
 */
@Update("UPDATE engineers SET success_rate = CASE WHEN total_orders > 0 THEN ROUND((completed_orders * 100.0 / total_orders), 2) ELSE 100.00 END, updated_at = NOW() WHERE id = #{engineerId}")
int updateSuccessRate(@Param("engineerId") Long engineerId);
```

### 2. EngineerService新增方法 ✅

```java
/**
 * 增加工程师服务次数
 * @param engineerId 工程师ID
 * @return 是否成功
 */
boolean incrementServiceCount(Long engineerId);

/**
 * 更新工程师订单统计
 * @param engineerId 工程师ID
 * @param isCompleted 是否完成订单
 * @return 是否成功
 */
boolean updateOrderStats(Long engineerId, boolean isCompleted);
```

### 3. EngineerServiceImpl实现 ✅

```java
@Override
public boolean incrementServiceCount(Long engineerId) {
    if (engineerId == null) return false;
    
    try {
        // 增加完成订单数
        int result = baseMapper.incrementCompletedOrders(engineerId);
        
        // 更新成功率
        if (result > 0) {
            baseMapper.updateSuccessRate(engineerId);
        }
        
        return result > 0;
    } catch (Exception e) {
        e.printStackTrace();
        return false;
    }
}

@Override
public boolean updateOrderStats(Long engineerId, boolean isCompleted) {
    if (engineerId == null) return false;
    
    try {
        // 增加总接单数
        int result = baseMapper.incrementTotalOrders(engineerId);
        
        // 如果订单完成，增加完成订单数
        if (isCompleted && result > 0) {
            baseMapper.incrementCompletedOrders(engineerId);
        }
        
        // 更新成功率
        if (result > 0) {
            baseMapper.updateSuccessRate(engineerId);
        }
        
        return result > 0;
    } catch (Exception e) {
        e.printStackTrace();
        return false;
    }
}
```

### 4. RepairOrderServiceImpl集成 ✅

#### 分配工程师时增加总接单数
```java
@Override
public boolean assignEngineer(Long orderId, Long engineerId, String engineerName, String engineerPhone) {
    // ... 原有逻辑
    
    boolean result = updateById(order);
    
    // 如果是首次分配工程师，增加工程师的总接单数
    if (result && !wasAssigned && engineerId != null) {
        try {
            log.info("为订单 {} 分配工程师 {}，增加总接单数", order.getOrderNo(), engineerId);
            boolean incrementResult = engineerService.updateOrderStats(engineerId, false);
            if (incrementResult) {
                log.info("工程师 {} 总接单数增加成功", engineerId);
            } else {
                log.warn("工程师 {} 总接单数增加失败", engineerId);
            }
        } catch (Exception e) {
            log.error("更新工程师接单统计时发生错误: ", e);
        }
    }
    
    return result;
}
```

#### 完成订单时增加完成订单数
```java
@Override
public boolean updateOrderStatus(Long orderId, String status, String remark) {
    // ... 原有逻辑
    
    boolean result = updateById(order);
    
    // 如果订单状态变更为已完成，且有分配工程师，则增加工程师服务次数
    if (result && "completed".equals(status) && !"completed".equals(oldStatus) && order.getEngineerId() != null) {
        try {
            log.info("订单 {} 已完成，增加工程师 {} 的服务次数", order.getOrderNo(), order.getEngineerId());
            boolean incrementResult = engineerService.incrementServiceCount(order.getEngineerId());
            if (incrementResult) {
                log.info("工程师 {} 服务次数增加成功", order.getEngineerId());
            } else {
                log.warn("工程师 {} 服务次数增加失败", order.getEngineerId());
            }
        } catch (Exception e) {
            log.error("更新工程师服务次数时发生错误: ", e);
        }
    }
    
    return result;
}
```

### 5. AdminApiController优化 ✅

```java
// 使用service方法更新订单状态，自动处理工程师统计
String oldStatus = order.getStatus();
boolean updated = repairOrderService.updateOrderStatus(id, status, remark);

// 使用service方法分配工程师，自动处理工程师统计
boolean updated = repairOrderService.assignEngineer(id, engineerId, engineer.getName(), engineer.getPhone());
```

## 🔄 业务流程

### 分配工程师流程
```
1. 管理员选择订单和工程师
2. 调用 assignEngineer() 方法
3. 更新订单状态为 "accepted"
4. 自动增加工程师总接单数 ****. 自动更新工程师成功率
6. 记录操作日志
```

### 完成订单流程
```
1. 管理员将订单状态改为 "completed"
2. 调用 updateOrderStatus() 方法
3. 设置订单完成时间
4. 自动增加工程师完成订单数 ****. 自动更新工程师成功率
6. 记录操作日志
```

## 📊 统计计算逻辑

### 成功率计算公式
```sql
success_rate = CASE 
    WHEN total_orders > 0 THEN 
        ROUND((completed_orders * 100.0 / total_orders), 2) 
    ELSE 
        100.00 
END
```

### 统计字段说明
- **total_orders**: 工程师接到的总订单数
- **completed_orders**: 工程师完成的订单数
- **success_rate**: 完成率 = (完成订单数 / 总订单数) × 100%

## 🧪 测试验证

### 测试页面
创建了专门的测试页面：`/admin/test-engineer-service-count`

### 测试场景
1. **分配工程师测试**
   - 选择未分配工程师的订单
   - 分配工程师
   - 验证工程师总接单数增加

2. **完成订单测试**
   - 选择已分配工程师的订单
   - 将状态改为已完成
   - 验证工程师完成订单数增加

3. **成功率计算测试**
   - 验证成功率自动更新
   - 验证计算公式正确性

### 测试步骤
```
1. 访问测试页面
2. 输入工程师ID和订单ID
3. 加载当前统计数据
4. 执行分配工程师操作
5. 验证总接单数增加
6. 执行完成订单操作
7. 验证完成订单数增加
8. 验证成功率更新
```

## 🔍 日志监控

### 关键日志
```java
log.info("为订单 {} 分配工程师 {}，增加总接单数", orderNo, engineerId);
log.info("工程师 {} 总接单数增加成功", engineerId);
log.info("订单 {} 已完成，增加工程师 {} 的服务次数", orderNo, engineerId);
log.info("工程师 {} 服务次数增加成功", engineerId);
```

### 错误处理
```java
log.warn("工程师 {} 总接单数增加失败", engineerId);
log.warn("工程师 {} 服务次数增加失败", engineerId);
log.error("更新工程师接单统计时发生错误: ", e);
log.error("更新工程师服务次数时发生错误: ", e);
```

## 🚀 使用说明

### 管理员操作
1. **分配工程师**：在订单管理页面选择工程师分配给订单
2. **完成订单**：将订单状态更新为"已完成"
3. **查看统计**：在工程师管理页面查看服务统计

### 自动化处理
- 无需手动更新统计数据
- 系统自动维护数据一致性
- 实时更新工程师排名

## 📈 效果展示

### 工程师列表显示
- 总接单数：实时显示
- 完成订单数：实时显示  
- 成功率：自动计算显示
- 评分：用户评价平均分

### 排序优化
- 按完成订单数排序
- 按成功率排序
- 按评分排序

现在工程师服务次数自增功能已经完全实现！每次完成订单后，该工程师的服务次数会自动+1，同时更新相关统计数据。🎉
