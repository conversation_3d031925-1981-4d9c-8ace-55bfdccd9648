# 🔧 工程师管理页面编辑功能实现

## 🎯 功能目标

为工程师管理页面添加编辑功能，模仿产品管理页面的编辑按钮和模态框，让管理员可以直接更新工程师信息。

## 🛠️ 实现内容

### 1. 前端界面优化 ✅

#### 添加编辑按钮
```html
<button class="action-btn btn-outline-primary"
        onclick="showEditEngineerModal(this)"
        th:data-engineer-id="${engineer.id}">
    <i class="bi bi-pencil me-1"></i>编辑
</button>
```

#### 编辑模态框设计
- **标签化界面**：基本信息、专业信息、服务信息、状态设置
- **响应式布局**：适配不同屏幕尺寸
- **数据预填充**：自动加载现有工程师数据到表单
- **动态字段管理**：专业领域、技能标签、服务区域的动态添加/删除

### 2. 编辑表单结构 ✅

#### 基本信息标签
```html
<div class="tab-pane fade show active" id="edit-basic-info">
    <div class="row">
        <div class="col-md-6">
            <input type="text" id="editEngineerName" name="name" required>
            <input type="tel" id="editEngineerPhone" name="phone" required>
            <input type="email" id="editEngineerEmail" name="email">
            <select id="editEngineerGender" name="gender">
                <option value="男">男</option>
                <option value="女">女</option>
            </select>
        </div>
        <div class="col-md-6">
            <input type="number" id="editEngineerAge" name="age">
            <select id="editEngineerEducation" name="education">
                <option value="初中">初中</option>
                <option value="高中">高中</option>
                <!-- 更多学历选项 -->
            </select>
            <input type="number" id="editEngineerExperience" name="experienceYears">
            <input type="text" id="editEngineerWorkTime" name="workTime">
        </div>
    </div>
    <textarea id="editEngineerIntroduction" name="introduction"></textarea>
</div>
```

#### 专业信息标签
```html
<div class="tab-pane fade" id="edit-professional-info">
    <!-- 动态专业领域输入 -->
    <div id="editSpecialtiesContainer"></div>
    <button onclick="addEditSpecialty()">添加专业领域</button>
    
    <!-- 动态技能标签输入 -->
    <div id="editSkillsContainer"></div>
    <button onclick="addEditSkill()">添加技能标签</button>
    
    <!-- 动态服务区域输入 -->
    <div id="editWorkAreasContainer"></div>
    <button onclick="addEditWorkArea()">添加服务区域</button>
</div>
```

#### 服务信息标签
```html
<div class="tab-pane fade" id="edit-service-info">
    <input type="number" id="editEngineerHourlyRate" name="hourlyRate">
    <input type="number" id="editEngineerServiceFee" name="serviceFee">
    <input type="number" id="editEngineerRating" name="rating">
    
    <!-- 只读的服务统计 -->
    <input type="text" id="editEngineerTotalOrders" readonly>
    <input type="text" id="editEngineerCompletedOrders" readonly>
    <input type="text" id="editEngineerSuccessRate" readonly>
</div>
```

#### 状态设置标签
```html
<div class="tab-pane fade" id="edit-status-info">
    <select id="editEngineerStatus" name="status">
        <option value="pending">待审核</option>
        <option value="approved">已通过</option>
        <option value="rejected">已拒绝</option>
        <option value="suspended">已暂停</option>
        <option value="active">活跃中</option>
    </select>
    
    <input type="checkbox" id="editEngineerIsOnline" name="isOnline">
    <input type="checkbox" id="editEngineerIsAvailable" name="isAvailable">
    <textarea id="editEngineerReviewNotes" name="reviewNotes"></textarea>
</div>
```

### 3. JavaScript功能实现 ✅

#### 显示编辑模态框
```javascript
function showEditEngineerModal(button) {
    const engineerId = button.dataset.engineerId;
    
    // 获取工程师详情
    fetch(`/admin/api/engineers/${engineerId}/detail`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                populateEditEngineerForm(data.data);
                const editModal = new bootstrap.Modal(document.getElementById('editEngineerModal'));
                editModal.show();
            }
        });
}
```

#### 数据预填充
```javascript
function populateEditEngineerForm(engineer) {
    // 基本信息
    document.getElementById('editEngineerName').value = engineer.name || '';
    document.getElementById('editEngineerPhone').value = engineer.phone || '';
    // ... 更多字段
    
    // 服务统计（只读）
    document.getElementById('editEngineerTotalOrders').value = (engineer.totalOrders || 0) + ' 单';
    document.getElementById('editEngineerCompletedOrders').value = (engineer.completedOrders || 0) + ' 单';
    document.getElementById('editEngineerSuccessRate').value = (engineer.successRate || 100) + '%';
    
    // 状态设置
    document.getElementById('editEngineerStatus').value = engineer.status || 'pending';
    document.getElementById('editEngineerIsOnline').checked = engineer.isOnline || false;
    document.getElementById('editEngineerIsAvailable').checked = engineer.isAvailable || false;
    
    // 处理JSON字段
    populateEditJsonFields(engineer);
}
```

#### 动态字段管理
```javascript
function addEditSpecialty(value = '') {
    const container = document.getElementById('editSpecialtiesContainer');
    const div = document.createElement('div');
    div.className = 'input-group mb-2';
    div.innerHTML = `
        <input type="text" class="form-control" placeholder="输入专业领域" value="${value}" onchange="updateEditSpecialties()">
        <button class="btn btn-outline-danger" type="button" onclick="removeEditSpecialty(this)">
            <i class="bi bi-trash"></i>
        </button>
    `;
    container.appendChild(div);
    updateEditSpecialties();
}

function updateEditSpecialties() {
    const container = document.getElementById('editSpecialtiesContainer');
    const inputs = container.querySelectorAll('input');
    const specialties = Array.from(inputs)
        .map(input => input.value)
        .filter(value => value && value.trim() !== '');
    
    document.getElementById('editEngineerSpecialties').value = JSON.stringify(specialties);
}
```

#### 表单提交处理
```javascript
document.getElementById('confirmEditEngineerBtn').addEventListener('click', function() {
    // 更新JSON字段
    updateEditSpecialties();
    updateEditSkills();
    updateEditWorkAreas();
    
    const form = document.getElementById('editEngineerForm');
    const formData = new FormData(form);
    const engineerId = document.getElementById('editEngineerId').value;
    
    // 验证必填字段
    const requiredFields = ['name', 'phone'];
    // ... 验证逻辑
    
    // 处理复选框值
    formData.set('isOnline', document.getElementById('editEngineerIsOnline').checked ? 'true' : 'false');
    formData.set('isAvailable', document.getElementById('editEngineerIsAvailable').checked ? 'true' : 'false');
    
    // 提交数据
    fetch(`/admin/api/engineers/${engineerId}`, {
        method: 'PUT',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('工程师信息编辑成功！');
            location.reload();
        }
    });
});
```

### 4. 后端API实现 ✅

#### 编辑工程师API
```java
@PutMapping("/engineers/{id}")
public Map<String, Object> editEngineer(@PathVariable Long id,
                                      @RequestParam Map<String, String> params,
                                      HttpServletRequest request) {
    // 权限验证
    AdminUser admin = getAdminFromSession(request);
    if (admin == null) {
        return errorResult("请先登录");
    }
    
    // 获取现有工程师信息
    Engineer engineer = engineerService.getById(id);
    if (engineer == null) {
        return errorResult("工程师不存在");
    }
    
    // 更新基本信息
    if (params.containsKey("name")) {
        engineer.setName(params.get("name"));
    }
    if (params.containsKey("phone")) {
        engineer.setPhone(params.get("phone"));
    }
    // ... 更多字段更新
    
    // 更新专业信息
    if (params.containsKey("specialties")) {
        engineer.setSpecialties(params.get("specialties"));
    }
    if (params.containsKey("skills")) {
        engineer.setSkills(params.get("skills"));
    }
    if (params.containsKey("workAreas")) {
        engineer.setWorkAreas(params.get("workAreas"));
    }
    
    // 更新服务信息
    if (params.containsKey("hourlyRate")) {
        engineer.setHourlyRate(new BigDecimal(params.get("hourlyRate")));
    }
    if (params.containsKey("serviceFee")) {
        engineer.setServiceFee(new BigDecimal(params.get("serviceFee")));
    }
    if (params.containsKey("rating")) {
        engineer.setRating(new BigDecimal(params.get("rating")));
    }
    
    // 更新状态信息
    if (params.containsKey("status")) {
        engineer.setStatus(params.get("status"));
    }
    if (params.containsKey("isOnline")) {
        engineer.setIsOnline("true".equals(params.get("isOnline")));
    }
    if (params.containsKey("isAvailable")) {
        engineer.setIsAvailable("true".equals(params.get("isAvailable")));
    }
    
    // 保存更新
    boolean updated = engineerService.updateById(engineer);
    
    return updated ? successResult("工程师信息更新成功", engineer) : errorResult("更新失败");
}
```

## 📊 功能特性对比

### 编辑功能对比
| 功能 | 产品管理 | 工程师管理 |
|------|----------|------------|
| **编辑按钮** | ✅ 有 | ✅ 新增 |
| **模态框编辑** | ✅ 有 | ✅ 新增 |
| **标签化界面** | ✅ 有 | ✅ 新增 |
| **数据预填充** | ✅ 有 | ✅ 新增 |
| **动态字段** | ✅ 有 | ✅ 新增 |
| **表单验证** | ✅ 有 | ✅ 新增 |

### 字段类型对比
| 字段类型 | 产品管理 | 工程师管理 |
|---------|----------|------------|
| **基本信息** | 名称、品牌、型号等 | 姓名、电话、邮箱等 |
| **JSON字段** | 特点、规格、服务 | 专业领域、技能、区域 |
| **数值字段** | 价格、库存、重量 | 时薪、服务费、评分 |
| **状态字段** | 上架/下架 | 审核状态、在线状态 |
| **只读字段** | 创建时间、更新时间 | 服务统计、成功率 |

### 用户体验对比
| 体验方面 | 优化前 | 优化后 |
|---------|--------|--------|
| **编辑方式** | ❌ 无法编辑 | ✅ 模态框编辑 |
| **操作流程** | ❌ 需要其他页面 | ✅ 同页面操作 |
| **数据展示** | ❌ 只能查看 | ✅ 可编辑所有字段 |
| **界面一致性** | ❌ 与产品页面不一致 | ✅ 保持一致风格 |

## 🔧 支持的编辑字段

### 基本信息字段
- ✅ **姓名**：必填字段，工程师姓名
- ✅ **联系电话**：必填字段，联系方式
- ✅ **邮箱**：可选字段，邮箱地址
- ✅ **性别**：下拉选择，男/女
- ✅ **年龄**：数值输入，18-65岁
- ✅ **学历**：下拉选择，初中到博士
- ✅ **工作经验**：数值输入，工作年数
- ✅ **工作时间**：文本输入，工作时间说明
- ✅ **个人介绍**：文本域，个人简介

### 专业信息字段
- ✅ **专业领域**：动态添加/删除，JSON存储
- ✅ **技能标签**：动态添加/删除，JSON存储
- ✅ **服务区域**：动态添加/删除，JSON存储

### 服务信息字段
- ✅ **时薪**：数值输入，元/小时
- ✅ **上门服务费**：数值输入，元/次
- ✅ **评分**：数值输入，0-5分
- ✅ **服务统计**：只读显示，总接单数、完成订单数、成功率

### 状态设置字段
- ✅ **工程师状态**：下拉选择，待审核/已通过/已拒绝/已暂停/活跃中
- ✅ **当前在线**：复选框，是否在线
- ✅ **可接单**：复选框，是否可接单
- ✅ **审核备注**：文本域，审核意见

## 🚀 使用说明

### 管理员操作步骤
1. **进入工程师管理页面**：访问 `/admin/engineers`
2. **选择要编辑的工程师**：在工程师卡片中点击"编辑"按钮
3. **编辑工程师信息**：
   - 在"基本信息"标签中修改个人信息
   - 在"专业信息"标签中管理专业领域、技能、服务区域
   - 在"服务信息"标签中设置费用和评分
   - 在"状态设置"标签中管理工程师状态
4. **保存修改**：点击"保存修改"按钮提交更改
5. **查看结果**：页面自动刷新显示最新信息

### 动态字段管理
- **添加字段**：点击"添加XXX"按钮新增输入框
- **删除字段**：点击输入框右侧的垃圾桶图标删除
- **自动保存**：输入内容会自动转换为JSON格式保存

### 数据验证
- **必填字段**：姓名和联系电话为必填
- **格式验证**：邮箱、电话号码格式验证
- **数值范围**：年龄、评分等有合理范围限制

现在工程师管理页面已经具备完整的编辑功能，管理员可以直接在页面上编辑工程师的所有信息！🎉
