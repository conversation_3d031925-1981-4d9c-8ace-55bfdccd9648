#!/bin/bash

echo "🔍 检查uploads目录配置"
echo "========================"

# 检查当前目录
echo "📍 当前目录: $(pwd)"

# 检查uploads目录是否存在
if [ -d "./uploads" ]; then
    echo "✅ uploads目录存在"
    echo "📂 目录路径: $(realpath ./uploads)"
    echo "📊 目录权限: $(ls -ld ./uploads)"
    
    # 检查目录内容
    file_count=$(find ./uploads -type f | wc -l)
    echo "📁 文件数量: $file_count"
    
    if [ $file_count -gt 0 ]; then
        echo "📋 最近的文件:"
        ls -la ./uploads | head -10
    fi
else
    echo "❌ uploads目录不存在，正在创建..."
    mkdir -p ./uploads
    chmod 755 ./uploads
    echo "✅ uploads目录创建完成"
fi

echo ""
echo "🌐 测试静态资源访问"
echo "===================="

# 创建测试图片
test_file="./uploads/test.txt"
echo "This is a test file for static resource access" > $test_file

echo "✅ 测试文件创建: $test_file"
echo "🔗 访问URL: https://localhost:8443/uploads/test.txt"

echo ""
echo "🔧 配置检查"
echo "============"

# 检查application.yml配置
if [ -f "src/main/resources/application.yml" ]; then
    echo "📄 application.yml 配置:"
    grep -A 3 "file:" src/main/resources/application.yml
else
    echo "❌ application.yml 文件不存在"
fi

echo ""
echo "💡 故障排查建议"
echo "================"
echo "1. 确保服务器已启动: mvn spring-boot:run"
echo "2. 测试静态资源: curl -k https://localhost:8443/uploads/test.txt"
echo "3. 检查浏览器控制台是否有CORS错误"
echo "4. 验证图片URL格式: /uploads/filename (不要重复路径)"
echo "5. 检查防火墙是否阻止8443端口"

echo ""
echo "🧪 测试命令"
echo "============"
echo "# 测试文件上传"
echo "curl -k -X POST -F 'file=@test-image.jpg' https://localhost:8443/api/upload/image"
echo ""
echo "# 测试静态资源访问"
echo "curl -k https://localhost:8443/uploads/test.txt"
echo ""
echo "# 检查服务器日志"
echo "tail -f logs/spring.log"
