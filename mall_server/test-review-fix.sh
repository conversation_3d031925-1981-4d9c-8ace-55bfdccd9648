#!/bin/bash

echo "🧪 测试审核逻辑修复"
echo "==================="

# 服务器地址
SERVER_URL="https://localhost:8443"

echo ""
echo "📋 步骤1: 检查当前状态"
echo "API: $SERVER_URL/api/service-centers/approved"

BEFORE_RESPONSE=$(curl -k -s "$SERVER_URL/api/service-centers/approved")
echo "修复前的已审核通过服务网点:"
echo "$BEFORE_RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$BEFORE_RESPONSE"

BEFORE_COUNT=$(echo "$BEFORE_RESPONSE" | grep -o '"id":[0-9]*' | wc -l | tr -d ' ')
echo "修复前数量: $BEFORE_COUNT"

echo ""
echo "📋 步骤2: 检查统计信息"
echo "API: $SERVER_URL/api/service-centers/stats"

STATS_RESPONSE=$(curl -k -s "$SERVER_URL/api/service-centers/stats")
echo "统计信息:"
echo "$STATS_RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$STATS_RESPONSE"

echo ""
echo "📋 步骤3: 模拟审核操作"
echo "现在需要通过管理后台手动审核待审核的服务网点"
echo ""
echo "🔧 操作步骤:"
echo "1. 访问管理后台: $SERVER_URL/admin/login"
echo "2. 进入服务网点管理: $SERVER_URL/admin/service-centers"
echo "3. 查看待审核的服务网点"
echo "4. 点击'通过申请'按钮"
echo "5. 填写审核备注（可选）"
echo "6. 确认审核"
echo ""
echo "修复后的审核逻辑会自动设置 isActive = true"

echo ""
echo "📋 步骤4: 等待手动审核..."
echo "请按任意键继续验证结果..."
read -n 1 -s

echo ""
echo "📋 步骤5: 验证修复结果"
echo "API: $SERVER_URL/api/service-centers/approved"

AFTER_RESPONSE=$(curl -k -s "$SERVER_URL/api/service-centers/approved")
echo "修复后的已审核通过服务网点:"
echo "$AFTER_RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$AFTER_RESPONSE"

AFTER_COUNT=$(echo "$AFTER_RESPONSE" | grep -o '"id":[0-9]*' | wc -l | tr -d ' ')
echo "修复后数量: $AFTER_COUNT"

echo ""
echo "📊 修复结果对比"
echo "================"
echo "修复前数量: $BEFORE_COUNT"
echo "修复后数量: $AFTER_COUNT"

if [ "$AFTER_COUNT" -gt "$BEFORE_COUNT" ]; then
    DIFF=$((AFTER_COUNT - BEFORE_COUNT))
    echo "🎉 修复成功！新增了 $DIFF 个可显示的服务网点"
    echo ""
    echo "📱 小程序端验证:"
    echo "1. 重新打开小程序服务网点页面"
    echo "2. 下拉刷新数据"
    echo "3. 检查是否显示了新增的服务网点"
elif [ "$AFTER_COUNT" -eq "$BEFORE_COUNT" ]; then
    echo "⚠️  数量没有变化，可能没有待审核的服务网点"
    echo "请检查是否有状态为 'pending' 的服务网点需要审核"
else
    echo "❌ 数量减少了，可能出现了问题"
fi

echo ""
echo "🔗 相关链接"
echo "==========="
echo "管理后台: $SERVER_URL/admin/service-centers"
echo "小程序API: $SERVER_URL/api/service-centers/approved"
echo "统计信息: $SERVER_URL/api/service-centers/stats"

echo ""
echo "💡 修复说明"
echo "============"
echo "已修复的问题:"
echo "1. 审核通过时自动设置 isActive = true"
echo "2. 审核拒绝时自动设置 isActive = false"
echo "3. 确保审核通过的服务网点能在小程序端显示"
echo ""
echo "现在审核操作会正确更新 isActive 字段！"
