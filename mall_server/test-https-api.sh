#!/bin/bash

echo "🔐 测试HTTPS API接口"
echo "===================="

# 基础URL
BASE_URL="https://localhost:8443"

# 测试函数
test_api() {
    local method=$1
    local endpoint=$2
    local data=$3
    local description=$4
    
    echo "📡 测试: $description"
    echo "   $method $BASE_URL$endpoint"
    
    if [ -n "$data" ]; then
        curl -k -s -X $method "$BASE_URL$endpoint" \
             -H "Content-Type: application/json" \
             -d "$data" \
             -w "\n状态码: %{http_code}\n" \
             --connect-timeout 5
    else
        curl -k -s -X $method "$BASE_URL$endpoint" \
             -w "\n状态码: %{http_code}\n" \
             --connect-timeout 5
    fi
    
    echo "--------------------"
}

# 检查服务器是否运行
echo "🔍 检查HTTPS服务器状态..."
if curl -k -s --connect-timeout 3 "$BASE_URL/admin" > /dev/null; then
    echo "✅ HTTPS服务器运行正常"
else
    echo "❌ HTTPS服务器未运行，请先启动服务器"
    echo "启动命令: ./mvnw spring-boot:run"
    exit 1
fi

echo ""

# 测试各个API接口
test_api "GET" "/admin" "" "管理后台首页"

test_api "GET" "/admin/customer-service" "" "客服管理页面"

test_api "POST" "/api/user/login" '{"code":"test123","userInfo":{"nickName":"测试用户"}}' "用户登录接口"

test_api "POST" "/api/customer-service/session" '{"openId":"test_openid"}' "创建客服会话"

test_api "GET" "/api/engineers/approved" "" "获取工程师列表"

test_api "GET" "/api/service-centers/approved" "" "获取服务网点列表"

echo "🎉 HTTPS API测试完成！"
echo ""
echo "📝 说明："
echo "- 使用 -k 参数跳过自签名证书验证"
echo "- 状态码 200 表示请求成功"
echo "- 状态码 302 表示重定向（HTTP→HTTPS）"
echo "- 状态码 404 表示接口不存在"
echo "- 状态码 500 表示服务器内部错误"
