# 🔧 管理端服务网点管理页面添加服务网点功能

## 📋 功能概述

模仿工程师页面的添加工程师方法，在服务网点管理页面添加了完整的添加服务网点功能：
1. **添加按钮**：在筛选区域添加"添加服务网点"按钮
2. **模态框表单**：完整的服务网点信息录入表单
3. **后端API**：创建服务网点的后端接口
4. **数据验证**：前后端数据验证和错误处理

## ✅ 主要实现内容

### 1. 前端界面优化

#### 添加按钮布局
```html
<!-- 在筛选标签区域添加按钮 -->
<div class="d-flex flex-wrap justify-content-between align-items-center">
    <div class="d-flex flex-wrap">
        <!-- 原有的筛选标签 -->
        <button class="filter-tab">全部网点</button>
        <button class="filter-tab">待审核</button>
        <!-- ... 其他筛选标签 -->
    </div>
    <div class="filter-actions">
        <button class="btn-add-service-center" onclick="showAddServiceCenterModal()">
            <i class="bi bi-building-add me-1"></i>添加服务网点
        </button>
    </div>
</div>
```

#### 按钮样式设计
```css
.btn-add-service-center {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    text-decoration: none;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    border: none;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    cursor: pointer;
}

.btn-add-service-center:hover {
    background: linear-gradient(135deg, #218838 0%, #1ea085 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
    color: white;
}
```

### 2. 模态框表单设计

#### 表单结构
```html
<div class="modal fade" id="addServiceCenterModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-building-add me-2"></i>添加服务网点
                </h5>
            </div>
            <div class="modal-body">
                <form id="addServiceCenterForm" enctype="multipart/form-data">
                    <!-- 基本信息、位置信息、营业信息、服务信息、其他设置 -->
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-success" id="confirmAddServiceCenterBtn">
                    <i class="bi bi-building-add me-1"></i>添加网点
                </button>
            </div>
        </div>
    </div>
</div>
```

#### 表单字段分组

**基本信息**
- 网点名称 (必填)
- 联系人 (必填)
- 联系电话 (必填)
- 邮箱
- 服务费用

**位置信息**
- 省份 (必填)
- 城市 (必填)
- 区县 (必填)
- 详细地址 (必填)
- 经度/纬度

**营业信息**
- 营业时间
- 营业日期
- 24小时营业选项

**服务信息**
- 服务类型 (多选)
- 服务描述

**其他设置**
- 启用网点
- 推荐网点
- 初始状态

### 3. JavaScript功能实现

#### 显示模态框
```javascript
function showAddServiceCenterModal() {
    // 清空表单
    document.getElementById('addServiceCenterForm').reset();
    
    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('addServiceCenterModal'));
    modal.show();
}
```

#### 表单提交处理
```javascript
document.getElementById('confirmAddServiceCenterBtn').addEventListener('click', function() {
    const form = document.getElementById('addServiceCenterForm');
    const formData = new FormData(form);

    // 收集服务类型
    const serviceTypes = [];
    document.querySelectorAll('.service-types-checkboxes input[type="checkbox"]:checked').forEach(checkbox => {
        serviceTypes.push(checkbox.value);
    });
    formData.set('serviceTypes', JSON.stringify(serviceTypes));

    // 验证必填字段
    const requiredFields = ['name', 'contactPerson', 'phone', 'province', 'city', 'district', 'address'];
    let isValid = true;
    
    for (const field of requiredFields) {
        const input = form.querySelector(`[name="${field}"]`);
        if (!input.value.trim()) {
            input.classList.add('is-invalid');
            isValid = false;
        } else {
            input.classList.remove('is-invalid');
        }
    }

    if (!isValid) {
        alert('请填写所有必填字段');
        return;
    }

    // 提交数据
    fetch('/admin/api/service-centers', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('服务网点添加成功！');
            bootstrap.Modal.getInstance(document.getElementById('addServiceCenterModal')).hide();
            location.reload();
        } else {
            alert('添加失败：' + (data.message || '未知错误'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('网络错误，请稍后重试');
    });
});
```

### 4. 后端API实现

#### 控制器方法
```java
@PostMapping("/service-centers")
public Map<String, Object> addServiceCenter(@RequestParam Map<String, String> params,
                                           jakarta.servlet.http.HttpServletRequest request) {
    Map<String, Object> result = new HashMap<>();
    
    try {
        // 验证必填字段
        String[] requiredFields = {"name", "contactPerson", "phone", "province", "city", "district", "address"};
        for (String field : requiredFields) {
            if (params.get(field) == null || params.get(field).trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "缺少必填字段：" + field);
                return result;
            }
        }
        
        // 检查电话号码是否已存在
        ServiceCenter existingServiceCenter = serviceCenterService.getOne(
            new QueryWrapper<ServiceCenter>().eq("phone", params.get("phone"))
        );
        if (existingServiceCenter != null) {
            result.put("success", false);
            result.put("message", "该电话号码已被使用");
            return result;
        }
        
        // 创建服务网点对象
        ServiceCenter serviceCenter = new ServiceCenter();
        
        // 设置各种字段...
        
        // 保存到数据库
        boolean saved = serviceCenterService.save(serviceCenter);
        
        if (saved) {
            result.put("success", true);
            result.put("message", "服务网点添加成功");
            result.put("data", serviceCenter);
        } else {
            result.put("success", false);
            result.put("message", "服务网点添加失败");
        }
        
    } catch (Exception e) {
        log.error("添加服务网点失败", e);
        result.put("success", false);
        result.put("message", "添加失败：" + e.getMessage());
    }
    
    return result;
}
```

#### 数据处理逻辑
```java
// 基本信息
serviceCenter.setName(params.get("name"));
serviceCenter.setContactPerson(params.get("contactPerson"));
serviceCenter.setPhone(params.get("phone"));
serviceCenter.setEmail(params.get("email"));
serviceCenter.setAddress(params.get("address"));
serviceCenter.setProvince(params.get("province"));
serviceCenter.setCity(params.get("city"));
serviceCenter.setDistrict(params.get("district"));

// 位置信息
if (params.get("longitude") != null && !params.get("longitude").trim().isEmpty()) {
    serviceCenter.setLongitude(new BigDecimal(params.get("longitude")));
}
if (params.get("latitude") != null && !params.get("latitude").trim().isEmpty()) {
    serviceCenter.setLatitude(new BigDecimal(params.get("latitude")));
}

// 营业信息
serviceCenter.setBusinessHours(params.get("businessHours"));
serviceCenter.setBusinessDays(params.get("businessDays"));
serviceCenter.setIs24Hours("on".equals(params.get("is24Hours")));

// 服务信息
serviceCenter.setServiceTypes(params.get("serviceTypes"));
serviceCenter.setServiceDescription(params.get("serviceDescription"));

// 费用信息
if (params.get("serviceFee") != null && !params.get("serviceFee").trim().isEmpty()) {
    serviceCenter.setServiceFee(new BigDecimal(params.get("serviceFee")));
} else {
    serviceCenter.setServiceFee(new BigDecimal("0.00"));
}

// 状态信息
serviceCenter.setStatus(params.getOrDefault("status", "approved"));
serviceCenter.setIsActive("on".equals(params.get("isActive")));
serviceCenter.setIsFeatured("on".equals(params.get("isFeatured")));

// 默认评价信息
serviceCenter.setRating(new BigDecimal("5.00"));
serviceCenter.setReviewCount(0);
serviceCenter.setTotalServices(0);

// 时间信息
LocalDateTime now = LocalDateTime.now();
serviceCenter.setCreatedAt(now);
serviceCenter.setUpdatedAt(now);
serviceCenter.setApplicationTime(now);
if ("approved".equals(serviceCenter.getStatus())) {
    serviceCenter.setReviewTime(now);
}
```

## 🎨 界面效果对比

### 优化前的筛选区域
```
┌─────────────────────────────────────┐
│ [全部网点] [待审核] [已通过] [已拒绝] │
│ [已暂停] [已关闭]                   │
└─────────────────────────────────────┘
```

### 优化后的筛选区域
```
┌─────────────────────────────────────┐
│ [全部网点] [待审核] [已通过] [已拒绝] │ [🏢 添加服务网点]
│ [已暂停] [已关闭]                   │
└─────────────────────────────────────┘
```

### 添加服务网点模态框
```
┌─────────────────────────────────────┐
│ 🏢 添加服务网点                  [×] │
├─────────────────────────────────────┤
│ 📋 基本信息          📍 位置信息     │
│ • 网点名称 *         • 省份 *       │
│ • 联系人 *           • 城市 *       │
│ • 联系电话 *         • 区县 *       │
│ • 邮箱               • 详细地址 *   │
│ • 服务费用           • 经度/纬度    │
│                                     │
│ 🕒 营业信息          🔧 服务信息     │
│ • 营业时间           • 服务类型     │
│ • 营业日期           • 服务描述     │
│ • 24小时营业                       │
│                                     │
│ ⚙️ 其他设置                         │
│ • 启用网点  • 推荐网点  • 初始状态  │
├─────────────────────────────────────┤
│                    [取消] [添加网点] │
└─────────────────────────────────────┘
```

## 🎯 功能特性

### 1. 用户体验
- ✅ **直观操作**：按钮位置合理，操作流程清晰
- ✅ **表单分组**：信息分类明确，填写体验良好
- ✅ **实时验证**：必填字段验证，错误提示明确
- ✅ **操作反馈**：成功/失败提示，状态反馈及时

### 2. 数据完整性
- ✅ **必填验证**：前后端双重验证必填字段
- ✅ **唯一性检查**：电话号码唯一性验证
- ✅ **数据类型**：数值字段类型验证和转换
- ✅ **默认值**：合理的默认值设置

### 3. 系统集成
- ✅ **权限控制**：管理员权限验证
- ✅ **数据一致性**：与现有数据结构保持一致
- ✅ **状态管理**：完整的状态流转支持
- ✅ **审核流程**：支持直接通过或待审核状态

### 4. 扩展性
- ✅ **模块化设计**：前后端分离，易于维护
- ✅ **配置灵活**：支持各种配置选项
- ✅ **字段扩展**：易于添加新的字段
- ✅ **功能复用**：可复用的组件和方法

## 🧪 测试建议

### 1. 功能测试
1. **按钮显示**：检查"添加服务网点"按钮是否正确显示
2. **模态框打开**：点击按钮是否正确打开模态框
3. **表单填写**：测试各种字段的填写和验证
4. **数据提交**：测试表单提交和数据保存

### 2. 验证测试
1. **必填字段**：测试必填字段的验证
2. **数据格式**：测试数值字段的格式验证
3. **唯一性**：测试电话号码的唯一性验证
4. **边界值**：测试各种边界值情况

### 3. 集成测试
1. **权限验证**：测试未登录用户的访问控制
2. **数据一致性**：验证添加的数据在列表中正确显示
3. **状态流转**：测试不同初始状态的设置
4. **页面刷新**：测试添加成功后的页面刷新

## 📝 总结

通过这次功能添加：

1. **完善了管理功能**：管理员可以直接在后台添加服务网点
2. **提升了操作效率**：无需通过前端申请流程
3. **保持了设计一致性**：与工程师管理页面保持相同的设计风格
4. **增强了数据完整性**：完善的验证和错误处理机制

现在管理端的服务网点管理页面具有了完整的CRUD功能，管理员可以方便地添加、查看、审核和管理服务网点！🚀
