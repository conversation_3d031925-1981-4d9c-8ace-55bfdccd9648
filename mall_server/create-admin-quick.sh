#!/bin/bash

# 快速创建管理员账号脚本
# 适用于生产环境投产前

echo "🚀 桩郎中充电桩维修 - 快速创建管理员账号"
echo "=========================================="

# 配置信息
SERVER_URL="https://www.zhuanglz.cn:8443"
# 如果是本地测试，使用：SERVER_URL="https://localhost:8443"

echo "📋 将创建以下管理员账号："
echo "1. 超级管理员：zhuanglz_admin (密码: admin123)"
echo "2. 运营管理员：operation_manager (密码: admin123)"
echo "3. 客服管理员：customer_service (密码: admin123)"
echo ""

read -p "确认创建这些账号吗？(y/n): " confirm

if [ "$confirm" != "y" ] && [ "$confirm" != "Y" ]; then
    echo "❌ 操作已取消"
    exit 1
fi

echo ""
echo "🔐 请先使用现有管理员账号登录..."
echo "默认账号: admin, 密码: admin123"
echo ""

# 获取登录session
echo "🔑 正在获取登录session..."
LOGIN_RESPONSE=$(curl -k -s -c cookies.txt -X POST "${SERVER_URL}/admin/login" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=admin&password=admin123")

if [[ $LOGIN_RESPONSE == *"success"* ]]; then
    echo "✅ 登录成功"
else
    echo "❌ 登录失败，请检查服务器状态和默认账号"
    echo "响应: $LOGIN_RESPONSE"
    exit 1
fi

echo ""
echo "👥 开始创建管理员账号..."

# 创建超级管理员
echo "1️⃣ 创建超级管理员..."
RESPONSE1=$(curl -k -s -b cookies.txt -X POST "${SERVER_URL}/admin/api/admin-users" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "zhuanglz_admin",
    "password": "admin123",
    "realName": "桩郎中超级管理员",
    "email": "<EMAIL>",
    "phone": "13800000000",
    "role": "super_admin",
    "permissions": "[\"user_management\", \"engineer_management\", \"service_center_management\", \"order_management\", \"review_management\", \"system_management\"]"
  }')

if [[ $RESPONSE1 == *"success\":true"* ]]; then
    echo "✅ 超级管理员创建成功"
else
    echo "⚠️ 超级管理员创建失败: $RESPONSE1"
fi

# 创建运营管理员
echo "2️⃣ 创建运营管理员..."
RESPONSE2=$(curl -k -s -b cookies.txt -X POST "${SERVER_URL}/admin/api/admin-users" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "operation_manager",
    "password": "admin123",
    "realName": "运营管理员",
    "email": "<EMAIL>",
    "phone": "13800000001",
    "role": "admin",
    "permissions": "[\"engineer_management\", \"service_center_management\", \"order_management\", \"review_management\"]"
  }')

if [[ $RESPONSE2 == *"success\":true"* ]]; then
    echo "✅ 运营管理员创建成功"
else
    echo "⚠️ 运营管理员创建失败: $RESPONSE2"
fi

# 创建客服管理员
echo "3️⃣ 创建客服管理员..."
RESPONSE3=$(curl -k -s -b cookies.txt -X POST "${SERVER_URL}/admin/api/admin-users" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "customer_service",
    "password": "admin123",
    "realName": "客服管理员",
    "email": "<EMAIL>",
    "phone": "13800000002",
    "role": "operator",
    "permissions": "[\"order_management\", \"review_management\"]"
  }')

if [[ $RESPONSE3 == *"success\":true"* ]]; then
    echo "✅ 客服管理员创建成功"
else
    echo "⚠️ 客服管理员创建失败: $RESPONSE3"
fi

# 清理临时文件
rm -f cookies.txt

echo ""
echo "🎉 管理员账号创建完成！"
echo "=========================================="
echo "📋 账号信息："
echo "1. 超级管理员"
echo "   用户名: zhuanglz_admin"
echo "   密码: admin123"
echo "   权限: 全部权限"
echo ""
echo "2. 运营管理员"
echo "   用户名: operation_manager"
echo "   密码: admin123"
echo "   权限: 运营相关权限"
echo ""
echo "3. 客服管理员"
echo "   用户名: customer_service"
echo "   密码: admin123"
echo "   权限: 客服相关权限"
echo ""
echo "🔗 登录地址: ${SERVER_URL}/admin/login"
echo ""
echo "⚠️ 重要提醒："
echo "1. 投产后请立即修改所有账号密码"
echo "2. 请修改邮箱和手机号为实际联系方式"
echo "3. 建议删除默认的 admin 和 operator 账号"
echo ""
echo "🚀 现在可以投产了！"
