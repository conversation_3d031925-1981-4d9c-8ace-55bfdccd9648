<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>故障类型转换测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .fault-type {
            background: #e3f2fd;
            padding: 8px 12px;
            margin: 5px;
            border-radius: 4px;
            display: inline-block;
            border: 1px solid #2196f3;
        }
        .fault-type-text {
            background: #f3e5f5;
            padding: 8px 12px;
            margin: 5px;
            border-radius: 4px;
            display: inline-block;
            border: 1px solid #9c27b0;
        }
        .log {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 10px;
        }
        button {
            background: #2196f3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1976d2;
        }
    </style>
</head>
<body>
    <h1>🔧 故障类型转换测试页面</h1>
    
    <div class="test-container">
        <h3>测试用例 - 列表中的故障类型 (.fault-type)</h3>
        <div>
            <span class="fault-type" data-fault-type="no_charging">no_charging</span>
            <span class="fault-type" data-fault-type="slow_charging">slow_charging</span>
            <span class="fault-type" data-fault-type="error_code">error_code</span>
            <span class="fault-type" data-fault-type="port_damage">port_damage</span>
            <span class="fault-type" data-fault-type="not_starting">not_starting</span>
            <span class="fault-type" data-fault-type="overheating">overheating</span>
            <span class="fault-type" data-fault-type="display_issue">display_issue</span>
            <span class="fault-type" data-fault-type="other">other</span>
        </div>
    </div>

    <div class="test-container">
        <h3>测试用例 - 详情中的故障类型 (.fault-type-text)</h3>
        <div>
            <span class="fault-type-text" data-fault-type="no_charging">no_charging</span>
            <span class="fault-type-text" data-fault-type="slow_charging">slow_charging</span>
            <span class="fault-type-text" data-fault-type="error_code">error_code</span>
            <span class="fault-type-text" data-fault-type="port_damage">port_damage</span>
        </div>
    </div>

    <div class="test-container">
        <h3>控制按钮</h3>
        <button onclick="testTranslation()">手动执行转换</button>
        <button onclick="addNewElement()">动态添加元素</button>
        <button onclick="clearLog()">清空日志</button>
    </div>

    <div class="test-container">
        <h3>转换日志</h3>
        <div id="logContainer" class="log"></div>
    </div>

    <script>
        // 故障类型映射表
        const faultTypeMap = {
            'no_charging': '无法充电',
            'slow_charging': '充电慢',
            'error_code': '报错代码',
            'port_damage': '接口损坏',
            'not_starting': '无法启动',
            'overheating': '过热',
            'display_issue': '显示故障',
            'other': '其他故障'
        };

        // 日志函数
        function log(message) {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            logContainer.innerHTML += `[${timestamp}] ${message}<br>`;
            logContainer.scrollTop = logContainer.scrollHeight;
            console.log(message);
        }

        // 初始化故障类型转换
        function initFaultTypeTranslation() {
            log('🔧 开始初始化故障类型转换');
            
            // 转换列表中的故障类型
            const faultTypeElements = document.querySelectorAll('.fault-type');
            log(`📋 找到故障类型元素数量: ${faultTypeElements.length}`);
            
            faultTypeElements.forEach((element, index) => {
                const faultType = element.getAttribute('data-fault-type');
                const chineseName = faultTypeMap[faultType] || faultType;
                log(`🔄 转换${index}: ${faultType} -> ${chineseName}`);
                element.textContent = chineseName;
            });

            // 转换详情弹窗中的故障类型
            const faultTypeTextElements = document.querySelectorAll('.fault-type-text');
            log(`📋 找到详情故障类型元素数量: ${faultTypeTextElements.length}`);
            
            faultTypeTextElements.forEach((element, index) => {
                const faultType = element.getAttribute('data-fault-type');
                const chineseName = faultTypeMap[faultType] || faultType;
                log(`🔄 详情转换${index}: ${faultType} -> ${chineseName}`);
                element.textContent = chineseName;
            });
            
            log('✅ 故障类型转换完成');
        }

        // 获取故障类型中文名称
        function getFaultTypeName(faultType) {
            return faultTypeMap[faultType] || faultType;
        }

        // 手动测试转换
        function testTranslation() {
            log('🧪 手动执行转换测试');
            initFaultTypeTranslation();
        }

        // 动态添加元素
        function addNewElement() {
            const container = document.querySelector('.test-container');
            const newElement = document.createElement('span');
            newElement.className = 'fault-type';
            newElement.setAttribute('data-fault-type', 'no_charging');
            newElement.textContent = 'no_charging';
            container.appendChild(newElement);
            
            log('➕ 动态添加了新元素，需要重新转换');
            initFaultTypeTranslation();
        }

        // 清空日志
        function clearLog() {
            document.getElementById('logContainer').innerHTML = '';
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 页面加载完成，开始初始化');
            initFaultTypeTranslation();
            
            // 延迟再次执行，确保所有元素都已渲染
            setTimeout(function() {
                log('🔄 延迟执行故障类型转换');
                initFaultTypeTranslation();
            }, 500);
        });

        // 页面完全加载后再次执行
        window.addEventListener('load', function() {
            log('🎯 页面完全加载，再次执行故障类型转换');
            initFaultTypeTranslation();
        });
    </script>
</body>
</html>
