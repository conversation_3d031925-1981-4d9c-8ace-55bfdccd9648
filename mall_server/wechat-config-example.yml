# 微信小程序配置示例
# 请将此文件内容添加到 application.yml 中

# 微信小程序配置
wx:
  appid: your_wechat_appid_here          # 微信小程序AppID
  secret: your_wechat_secret_here        # 微信小程序AppSecret

# 现有抖音配置保持不变
tt:
  appid: ${TT_APPID:your_douyin_appid}
  secret: ${TT_SECRET:your_douyin_secret}

# 数据库配置（如果需要修改表结构）
spring:
  datasource:
    url: ********************************************************************************************************************
    username: your_username
    password: your_password
    driver-class-name: com.mysql.cj.jdbc.Driver

# 多平台支持配置
platform:
  supported:
    - douyin
    - wechat
  default: douyin

# 用户系统配置
user:
  # 是否启用跨平台用户绑定
  cross-platform-binding: true
  # 用户数据迁移模式
  migration-mode: auto  # auto, manual, disabled
