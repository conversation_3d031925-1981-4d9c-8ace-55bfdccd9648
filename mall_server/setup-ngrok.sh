#!/bin/bash

echo "🌐 设置 ngrok 内网穿透解决小程序HTTPS问题"
echo "============================================"

# 检查是否安装了ngrok
if ! command -v ngrok &> /dev/null; then
    echo "❌ ngrok 未安装"
    echo ""
    echo "📥 安装步骤："
    echo "1. 访问 https://ngrok.com/download"
    echo "2. 下载适合你系统的版本"
    echo "3. 解压并将 ngrok 添加到 PATH"
    echo ""
    echo "💡 macOS 快速安装："
    echo "brew install ngrok/ngrok/ngrok"
    echo ""
    echo "💡 或者直接下载："
    echo "curl -s https://ngrok-agent.s3.amazonaws.com/ngrok.asc | sudo tee /etc/apt/trusted.gpg.d/ngrok.asc >/dev/null"
    exit 1
fi

echo "✅ ngrok 已安装"

# 检查是否配置了authtoken
if ! ngrok config check &> /dev/null; then
    echo "⚠️  需要配置 ngrok authtoken"
    echo ""
    echo "📝 配置步骤："
    echo "1. 访问 https://dashboard.ngrok.com/get-started/your-authtoken"
    echo "2. 复制你的 authtoken"
    echo "3. 运行: ngrok config add-authtoken YOUR_AUTHTOKEN"
    echo ""
    read -p "请输入你的 ngrok authtoken: " authtoken
    if [ -n "$authtoken" ]; then
        ngrok config add-authtoken $authtoken
        echo "✅ authtoken 配置完成"
    else
        echo "❌ authtoken 不能为空"
        exit 1
    fi
fi

echo ""
echo "🚀 启动 ngrok 隧道..."
echo "将会为 https://localhost:8443 创建公网隧道"
echo ""
echo "⚠️  注意：启动后请不要关闭此终端窗口"
echo "📋 获得公网域名后，需要更新小程序API配置"
echo ""

# 启动ngrok
ngrok http 8443 --log=stdout
