-- 快速添加管理员账号脚本
-- 适用于生产环境投产前快速创建管理员

-- 1. 添加主管理员账号（超级管理员）
INSERT INTO `admin_users` (
    `username`, 
    `password`, 
    `real_name`, 
    `email`, 
    `phone`, 
    `role`, 
    `permissions`, 
    `status`, 
    `login_count`,
    `created_at`, 
    `updated_at`, 
    `password_updated_at`
) VALUES (
    'zhuanglz_admin',    -- 用户名：桩郎中管理员
    '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXIGfkPNtmismLQdQIG.ZjUON4C',  -- 密码：admin123
    '桩郎中超级管理员',   -- 真实姓名
    '<EMAIL>', -- 邮箱
    '13800000000',       -- 手机号（请修改为实际手机号）
    'super_admin',       -- 角色：超级管理员
    '["user_management", "engineer_management", "service_center_management", "order_management", "review_management", "system_management"]',  -- 全部权限
    1,                   -- 状态：启用
    0,                   -- 登录次数
    NOW(), 
    NOW(), 
    NOW()
);

-- 2. 添加运营管理员账号（普通管理员）
INSERT INTO `admin_users` (
    `username`, 
    `password`, 
    `real_name`, 
    `email`, 
    `phone`, 
    `role`, 
    `permissions`, 
    `status`, 
    `login_count`,
    `created_at`, 
    `updated_at`, 
    `password_updated_at`
) VALUES (
    'operation_manager', -- 用户名：运营管理员
    '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXIGfkPNtmismLQdQIG.ZjUON4C',  -- 密码：admin123
    '运营管理员',         -- 真实姓名
    '<EMAIL>', -- 邮箱
    '13800000001',       -- 手机号（请修改为实际手机号）
    'admin',             -- 角色：普通管理员
    '["engineer_management", "service_center_management", "order_management", "review_management"]',  -- 运营相关权限
    1,                   -- 状态：启用
    0,                   -- 登录次数
    NOW(), 
    NOW(), 
    NOW()
);

-- 3. 添加客服管理员账号（客服专用）
INSERT INTO `admin_users` (
    `username`, 
    `password`, 
    `real_name`, 
    `email`, 
    `phone`, 
    `role`, 
    `permissions`, 
    `status`, 
    `login_count`,
    `created_at`, 
    `updated_at`, 
    `password_updated_at`
) VALUES (
    'customer_service',  -- 用户名：客服
    '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXIGfkPNtmismLQdQIG.ZjUON4C',  -- 密码：admin123
    '客服管理员',         -- 真实姓名
    '<EMAIL>', -- 邮箱
    '13800000002',       -- 手机号（请修改为实际手机号）
    'operator',          -- 角色：操作员
    '["order_management", "review_management"]',  -- 客服相关权限
    1,                   -- 状态：启用
    0,                   -- 登录次数
    NOW(), 
    NOW(), 
    NOW()
);

-- 查询验证创建结果
SELECT 
    id,
    username,
    real_name,
    email,
    phone,
    role,
    status,
    created_at
FROM admin_users 
WHERE username IN ('zhuanglz_admin', 'operation_manager', 'customer_service')
ORDER BY created_at DESC;

-- 使用说明：
-- 1. 所有账号的默认密码都是：admin123
-- 2. 请及时修改邮箱和手机号为实际联系方式
-- 3. 投产后建议立即修改密码
-- 4. 登录地址：https://你的域名/admin/login
