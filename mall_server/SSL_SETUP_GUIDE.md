# 🔐 SSL/HTTPS 配置指南

## 📋 概述

本项目已配置自签名SSL证书，支持HTTPS访问。这提供了更高的安全性，特别是在处理用户数据和API通信时。

## 🔧 当前配置

### 证书信息
- **证书类型**: 自签名证书 (PKCS12)
- **证书别名**: zlz-mall
- **有效期**: 365天 (2025-06-06 至 2026-06-06)
- **密钥长度**: 2048位 RSA
- **签名算法**: SHA256withRSA

### 服务端口
- **HTTPS端口**: 8443
- **HTTP端口**: 8080 (自动重定向到HTTPS)

### 访问地址
- **管理后台**: https://localhost:8443/admin
- **API接口**: https://localhost:8443/api
- **客服页面**: https://localhost:8443/admin/customer-service

## 🚀 快速开始

### 1. 启动服务
```bash
cd mall_server
./mvnw spring-boot:run
```

### 2. 访问应用
打开浏览器访问: https://localhost:8443

⚠️ **注意**: 首次访问时浏览器会显示安全警告，这是因为使用了自签名证书。点击"高级" → "继续访问"即可。

## 🔒 证书管理

### 查看证书信息
```bash
keytool -list -v -keystore src/main/resources/keystore.p12 -storepass zlzmall123
```

### 导出证书（用于客户端信任）
```bash
keytool -export -alias zlz-mall -keystore src/main/resources/keystore.p12 -storepass zlzmall123 -file zlz-mall.crt
```

### 重新生成证书
```bash
keytool -genkeypair -alias zlz-mall -keyalg RSA -keysize 2048 \
  -storetype PKCS12 -keystore keystore.p12 -validity 365 \
  -storepass zlzmall123 -keypass zlzmall123 \
  -dname "CN=localhost, OU=ZLZ Mall, O=成都桩郎中新能源技术有限公司, L=成都, ST=四川, C=CN"
```

## 📱 小程序配置

小程序端已更新为使用HTTPS API：
- 配置文件: `mall/utils/config.ts`
- API基础地址: `https://localhost:8443`

### 开发环境证书信任

由于使用自签名证书，需要在开发环境中配置证书信任：

#### macOS
1. 导出证书: `keytool -export -alias zlz-mall -keystore keystore.p12 -storepass zlzmall123 -file zlz-mall.crt`
2. 双击 `zlz-mall.crt` 文件
3. 在钥匙串中找到证书，双击设置为"始终信任"

#### Windows
1. 导出证书文件
2. 双击证书文件 → 安装证书
3. 选择"本地计算机" → "受信任的根证书颁发机构"

## 🌐 生产环境部署

### 购买正式SSL证书

推荐的SSL证书提供商：
- **Let's Encrypt** (免费)
- **阿里云SSL证书**
- **腾讯云SSL证书**
- **DigiCert**
- **Symantec**

### 配置正式证书

1. **获取证书文件**
   - 通常包含: `.crt` (证书文件) 和 `.key` (私钥文件)

2. **转换为PKCS12格式**
   ```bash
   openssl pkcs12 -export -in your-cert.crt -inkey your-private.key \
     -out keystore.p12 -name your-alias -passout pass:your-password
   ```

3. **更新配置文件**
   ```yaml
   server:
     port: 443  # 标准HTTPS端口
     ssl:
       key-store: classpath:keystore.p12
       key-store-password: your-password
       key-alias: your-alias
   ```

4. **更新域名配置**
   - 修改 `mall/utils/config.ts` 中的 `API_BASE_URL`
   - 将 `localhost` 替换为实际域名

## 🔧 配置文件说明

### application.yml
```yaml
server:
  port: 8443                    # HTTPS端口
  ssl:
    enabled: true               # 启用SSL
    key-store: classpath:keystore.p12  # 证书文件路径
    key-store-password: zlzmall123     # 证书密码
    key-store-type: PKCS12      # 证书类型
    key-alias: zlz-mall         # 证书别名
  http:
    port: 8080                  # HTTP重定向端口
```

### HttpsConfig.java
- 配置HTTP到HTTPS的自动重定向
- 强制所有请求使用HTTPS
- 支持同时监听HTTP和HTTPS端口

## 🛠️ 故障排除

### 常见问题

1. **证书不被信任**
   - 解决方案: 在浏览器中手动信任证书，或安装到系统证书库

2. **端口冲突**
   - 检查8443和8080端口是否被占用
   - 使用 `lsof -i :8443` 查看端口占用

3. **证书过期**
   - 重新生成证书或更新正式证书
   - 系统会在启动时检查证书有效期

4. **小程序无法访问HTTPS**
   - 确保小程序配置了正确的HTTPS地址
   - 检查网络连接和防火墙设置

### 日志查看

应用启动时会显示SSL证书信息：
```
=== SSL证书信息 ===
证书主题: CN=localhost, OU=ZLZ Mall, O=成都桩郎中新能源技术有限公司
证书有效期: 2025-06-06 至 2026-06-06
✅ SSL证书有效，还有 364 天过期
```

## 📈 安全建议

1. **定期更新证书**: 在证书过期前30天更新
2. **使用强密码**: 证书密码应足够复杂
3. **备份证书**: 定期备份证书文件
4. **监控证书状态**: 设置证书过期提醒
5. **使用HSTS**: 在生产环境启用HTTP严格传输安全

## 🔄 升级到正式证书

当准备上线时，按以下步骤升级：

1. 购买SSL证书
2. 配置域名DNS
3. 替换自签名证书
4. 更新小程序API配置
5. 测试所有功能
6. 部署到生产环境

---

**注意**: 自签名证书仅适用于开发和测试环境，生产环境必须使用正式的SSL证书。
