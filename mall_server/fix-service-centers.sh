#!/bin/bash

echo "🔧 修复服务网点显示问题"
echo "========================"

# 服务器地址
SERVER_URL="https://localhost:8443"

echo ""
echo "📋 步骤1: 检查当前服务网点状态"
echo "API: $SERVER_URL/api/quick-test/service-centers-status"

STATUS_RESPONSE=$(curl -k -s "$SERVER_URL/api/quick-test/service-centers-status")
echo "当前状态:"
echo "$STATUS_RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$STATUS_RESPONSE"

echo ""
echo "📋 步骤2: 批量审核通过所有待审核的服务网点"
echo "API: $SERVER_URL/api/quick-test/approve-all-pending"

APPROVE_RESPONSE=$(curl -k -s -X POST "$SERVER_URL/api/quick-test/approve-all-pending")
echo "审核结果:"
echo "$APPROVE_RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$APPROVE_RESPONSE"

echo ""
echo "📋 步骤3: 验证修复结果"
echo "API: $SERVER_URL/api/service-centers/approved"

FINAL_RESPONSE=$(curl -k -s "$SERVER_URL/api/service-centers/approved")
echo "最终结果:"
echo "$FINAL_RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$FINAL_RESPONSE"

# 统计最终的服务网点数量
FINAL_COUNT=$(echo "$FINAL_RESPONSE" | grep -o '"id":[0-9]*' | wc -l | tr -d ' ')
echo ""
echo "✅ 修复完成！现在有 $FINAL_COUNT 个已审核通过的服务网点"

echo ""
echo "🧪 测试建议"
echo "============"
echo "1. 重新打开小程序服务网点页面"
echo "2. 下拉刷新数据"
echo "3. 检查是否显示了新增的服务网点"
echo ""
echo "如果小程序端仍然没有显示，请检查:"
echo "- 小程序端网络连接"
echo "- API调用是否成功"
echo "- 小程序端缓存是否需要清理"
