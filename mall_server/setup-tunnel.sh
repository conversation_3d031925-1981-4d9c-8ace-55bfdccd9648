#!/bin/bash

echo "🌐 设置内网穿透解决小程序HTTPS证书问题"
echo "============================================"

echo "📋 可选方案："
echo "1. 使用 ngrok (推荐)"
echo "2. 使用 frp"
echo "3. 使用花生壳"
echo "4. 使用 localtunnel"

echo ""
echo "🔧 方案1: 使用 ngrok"
echo "1. 安装 ngrok: https://ngrok.com/"
echo "2. 注册账号获取 authtoken"
echo "3. 运行命令:"
echo "   ngrok http 8443"
echo "4. 获得类似 https://abc123.ngrok.io 的域名"
echo "5. 更新小程序API配置为该域名"

echo ""
echo "🔧 方案2: 使用 localtunnel"
echo "1. 安装: npm install -g localtunnel"
echo "2. 运行: lt --port 8443 --subdomain zlz-mall"
echo "3. 获得: https://zlz-mall.loca.lt"

echo ""
echo "🔧 方案3: 临时解决方案 - 跳过证书验证"
echo "在抖音开发者工具中："
echo "1. 工具 → 项目设置 → 网络设置"
echo "2. 勾选'不校验合法域名、HTTPS证书'"
echo "3. 添加开发域名: 10.20.10.46:8443"

echo ""
echo "🎯 推荐流程："
echo "1. 开发阶段: 使用跳过证书验证"
echo "2. 测试阶段: 使用内网穿透"
echo "3. 生产阶段: 购买正式SSL证书"

echo ""
echo "📝 注意事项："
echo "- 内网穿透可能有速度限制"
echo "- 免费服务通常有流量限制"
echo "- 生产环境必须使用正式证书"
