#!/bin/bash

echo "🔍 调试工程师照片显示问题"
echo "=========================="

# 服务器地址
SERVER_URL="https://localhost:8443"

echo ""
echo "📋 问题描述"
echo "==========="
echo "管理端工程师审核页面："
echo "✅ 资质照片显示正确"
echo "❌ 工作照片显示不正确，无法显示"

echo ""
echo "🧪 测试1: 获取工程师列表数据"
echo "=========================="

echo "GET $SERVER_URL/api/engineers/approved"
ENGINEERS_RESPONSE=$(curl -k -s "$SERVER_URL/api/engineers/approved")

echo "工程师列表响应:"
echo "$ENGINEERS_RESPONSE" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    if data.get('success') and data.get('data'):
        engineers = data['data']
        print(f'总共 {len(engineers)} 个工程师')
        for i, engineer in enumerate(engineers[:3], 1):  # 只显示前3个
            print(f'\\n工程师 {i}: {engineer.get(\"name\", \"未知\")}')
            print(f'  ID: {engineer.get(\"id\")}')
            print(f'  资质证书: {engineer.get(\"certifications\", \"无\")}')
            print(f'  工作照片: {engineer.get(\"workPhotos\", \"无\")}')
            
            # 检查字段类型
            if 'certifications' in engineer:
                cert_type = type(engineer['certifications']).__name__
                print(f'  资质证书类型: {cert_type}')
            
            if 'workPhotos' in engineer:
                work_type = type(engineer['workPhotos']).__name__
                print(f'  工作照片类型: {work_type}')
    else:
        print('API返回格式异常')
        print(data)
except Exception as e:
    print(f'解析失败: {e}')
" 2>/dev/null || echo "无法解析JSON数据"

echo ""
echo "🧪 测试2: 获取单个工程师详情"
echo "=========================="

# 提取第一个工程师的ID
ENGINEER_ID=$(echo "$ENGINEERS_RESPONSE" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    if data.get('success') and data.get('data') and len(data['data']) > 0:
        print(data['data'][0]['id'])
except:
    pass
" 2>/dev/null)

if [ -n "$ENGINEER_ID" ]; then
    echo "GET $SERVER_URL/api/engineers/detail/$ENGINEER_ID"
    DETAIL_RESPONSE=$(curl -k -s "$SERVER_URL/api/engineers/detail/$ENGINEER_ID")
    
    echo "工程师详情响应:"
    echo "$DETAIL_RESPONSE" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    if data.get('success') and data.get('data'):
        engineer = data['data']
        print(f'工程师: {engineer.get(\"name\", \"未知\")}')
        print(f'ID: {engineer.get(\"id\")}')
        
        # 详细检查字段
        cert = engineer.get('certifications')
        work = engineer.get('workPhotos')
        
        print(f'\\n资质证书字段:')
        print(f'  值: {cert}')
        print(f'  类型: {type(cert).__name__}')
        print(f'  长度: {len(str(cert)) if cert else 0}')
        
        print(f'\\n工作照片字段:')
        print(f'  值: {work}')
        print(f'  类型: {type(work).__name__}')
        print(f'  长度: {len(str(work)) if work else 0}')
        
        # 尝试解析JSON
        if cert:
            try:
                import json
                cert_parsed = json.loads(cert) if isinstance(cert, str) else cert
                print(f'  资质证书解析后: {cert_parsed}')
                print(f'  资质证书数量: {len(cert_parsed) if isinstance(cert_parsed, list) else \"非数组\"}')
            except:
                print(f'  资质证书JSON解析失败')
        
        if work:
            try:
                import json
                work_parsed = json.loads(work) if isinstance(work, str) else work
                print(f'  工作照片解析后: {work_parsed}')
                print(f'  工作照片数量: {len(work_parsed) if isinstance(work_parsed, list) else \"非数组\"}')
            except:
                print(f'  工作照片JSON解析失败')
    else:
        print('详情API返回格式异常')
        print(data)
except Exception as e:
    print(f'解析失败: {e}')
" 2>/dev/null || echo "无法解析JSON数据"
else
    echo "❌ 无法获取工程师ID"
fi

echo ""
echo "🧪 测试3: 检查数据库字段映射"
echo "=========================="

echo "数据库字段名: work_photos (下划线)"
echo "Java实体字段: workPhotos (驼峰)"
echo "MyBatis配置: map-underscore-to-camel-case: true"

echo ""
echo "📊 可能的问题原因"
echo "================"
echo "1. 数据库中work_photos字段为空或null"
echo "2. JSON格式不正确"
echo "3. 字段映射问题"
echo "4. 前端JavaScript解析问题"

echo ""
echo "🔧 调试建议"
echo "============"
echo "1. 检查数据库中的work_photos字段值"
echo "2. 对比certifications和work_photos字段的数据格式"
echo "3. 检查前端renderImageGallery函数的处理逻辑"
echo "4. 添加更多调试日志"

echo ""
echo "📝 SQL查询建议"
echo "=============="
echo "SELECT id, name, certifications, work_photos FROM engineers WHERE id = $ENGINEER_ID;"
echo ""
echo "检查字段值是否为空、格式是否正确"

echo ""
echo "🎯 下一步操作"
echo "=============="
echo "1. 登录数据库查看work_photos字段的实际值"
echo "2. 在管理端页面打开浏览器开发者工具"
echo "3. 查看控制台是否有JavaScript错误"
echo "4. 检查网络请求中的工程师数据"
