#!/bin/bash

echo "🔍 检查服务网点状态"
echo "===================="

# 服务器地址
SERVER_URL="https://localhost:8443"

echo ""
echo "📋 检查已审核通过的服务网点"
echo "API: $SERVER_URL/api/service-centers/approved"

APPROVED_RESPONSE=$(curl -k -s "$SERVER_URL/api/service-centers/approved")
echo "响应结果:"
echo "$APPROVED_RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$APPROVED_RESPONSE"

# 统计已审核通过的数量
APPROVED_COUNT=$(echo "$APPROVED_RESPONSE" | grep -o '"id":[0-9]*' | wc -l | tr -d ' ')
echo "✅ 已审核通过的服务网点数量: $APPROVED_COUNT"

echo ""
echo "📋 检查服务网点统计信息"
echo "API: $SERVER_URL/api/service-centers/stats"

STATS_RESPONSE=$(curl -k -s "$SERVER_URL/api/service-centers/stats")
echo "响应结果:"
echo "$STATS_RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$STATS_RESPONSE"

echo ""
echo "📊 问题分析"
echo "===================="

if [ "$APPROVED_COUNT" -eq 0 ]; then
    echo "❌ 问题: 没有已审核通过的服务网点"
elif [ "$APPROVED_COUNT" -eq 1 ]; then
    echo "⚠️  问题: 只有1个已审核通过的服务网点"
    echo "新增的服务网点可能还在待审核状态"
else
    echo "✅ 已审核通过的服务网点数量正常: $APPROVED_COUNT"
fi

echo ""
echo "🔧 解决方案"
echo "===================="
echo "1. 登录管理后台: $SERVER_URL/admin/service-centers"
echo "2. 检查新增服务网点的状态"
echo "3. 如果状态是 'pending'，点击'通过申请'"
echo "4. 如果状态是 'approved'，检查 isActive 字段"
echo "5. 小程序端重新刷新数据"
