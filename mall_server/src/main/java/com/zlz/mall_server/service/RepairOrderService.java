package com.zlz.mall_server.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zlz.mall_server.model.RepairOrder;

import java.util.List;
import java.util.Map;

public interface RepairOrderService extends IService<RepairOrder> {

    /**
     * 创建维修订单
     * @param repairOrder 维修订单信息
     * @return 创建后的维修订单
     */
    RepairOrder createRepairOrder(RepairOrder repairOrder);

    /**
     * 根据openId查询用户维修订单列表
     * @param openId 用户openId
     * @return 维修订单列表
     */
    List<RepairOrder> findByOpenId(String openId);

    /**
     * 根据openId和状态查询用户维修订单列表
     * @param openId 用户openId
     * @param status 订单状态
     * @return 维修订单列表
     */
    List<RepairOrder> findByOpenIdAndStatus(String openId, String status);

    /**
     * 根据订单编号查询维修订单
     * @param orderNo 订单编号
     * @return 维修订单
     */
    RepairOrder findByOrderNo(String orderNo);

    /**
     * 取消维修订单
     * @param orderNo 订单编号
     * @param openId 用户openId（用于验证权限）
     * @return 是否取消成功
     */
    boolean cancelRepairOrder(String orderNo, String openId);

    /**
     * 更新维修订单状态
     * @param orderNo 订单编号
     * @param status 新状态
     * @return 是否更新成功
     */
    boolean updateRepairOrderStatus(String orderNo, String status);

    /**
     * 获取用户维修订单统计
     * @param openId 用户openId
     * @return 各状态订单数量
     */
    Map<String, Integer> getRepairOrderStats(String openId);

    /**
     * 获取订单状态统计
     */
    Map<String, Long> getStatusStatistics();

    /**
     * 更新订单状态
     */
    boolean updateOrderStatus(Long orderId, String status, String remark);

    /**
     * 分配工程师
     */
    boolean assignEngineer(Long orderId, Long engineerId, String engineerName, String engineerPhone);
}
