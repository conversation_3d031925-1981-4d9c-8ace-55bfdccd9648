package com.zlz.mall_server.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zlz.mall_server.mapper.ProductReviewMapper;
import com.zlz.mall_server.model.ProductReview;
import com.zlz.mall_server.service.ProductReviewService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ProductReviewServiceImpl extends ServiceImpl<ProductReviewMapper, ProductReview> implements ProductReviewService {
    
    @Override
    public List<ProductReview> getReviewsByProductId(Long productId) {
        return baseMapper.findByProductId(productId);
    }
    
    @Override
    public Integer getReviewCountByProductId(Long productId) {
        return baseMapper.countByProductId(productId);
    }
    
    @Override
    public Double getAverageRatingByProductId(Long productId) {
        Double avgRating = baseMapper.getAverageRatingByProductId(productId);
        return avgRating != null ? avgRating : 0.0;
    }
}
