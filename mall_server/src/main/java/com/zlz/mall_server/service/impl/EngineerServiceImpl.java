package com.zlz.mall_server.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zlz.mall_server.mapper.EngineerMapper;
import com.zlz.mall_server.mapper.EngineerMapper.EngineerStats;
import com.zlz.mall_server.model.Engineer;
import com.zlz.mall_server.service.EngineerService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class EngineerServiceImpl extends ServiceImpl<EngineerMapper, Engineer> implements EngineerService {
    
    @Override
    public List<Engineer> getApprovedEngineers() {
        return baseMapper.findApprovedEngineers();
    }
    
    @Override
    public List<Engineer> getAvailableEngineers() {
        return baseMapper.findAvailableEngineers();
    }
    
    @Override
    public List<Engineer> getEngineersBySpecialty(String specialty) {
        return baseMapper.findBySpecialty(specialty);
    }
    
    @Override
    public List<Engineer> getEngineersByWorkArea(String area) {
        return baseMapper.findByWorkArea(area);
    }
    
    @Override
    public EngineerStats getEngineerStats() {
        return baseMapper.getEngineerStats();
    }

    @Override
    public Engineer getByPhone(String phone) {
        return baseMapper.findByPhone(phone);
    }

    @Override
    public boolean incrementServiceCount(Long engineerId) {
        if (engineerId == null) {
            System.out.println("❌ 工程师ID为空，无法增加服务次数");
            return false;
        }

        try {
            System.out.println("🔧 开始为工程师 " + engineerId + " 增加完成订单数");

            // 增加完成订单数
            int result = baseMapper.incrementCompletedOrders(engineerId);
            System.out.println("📊 完成订单数更新结果: " + result + " 行受影响");

            // 更新成功率
            if (result > 0) {
                int successRateResult = baseMapper.updateSuccessRate(engineerId);
                System.out.println("📈 成功率更新结果: " + successRateResult + " 行受影响");
            }

            System.out.println("✅ 工程师 " + engineerId + " 服务次数增加" + (result > 0 ? "成功" : "失败"));
            return result > 0;
        } catch (Exception e) {
            System.out.println("💥 增加工程师服务次数时发生异常: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    @Override
    public boolean updateOrderStats(Long engineerId, boolean isCompleted) {
        if (engineerId == null) {
            return false;
        }

        try {
            // 增加总接单数
            int result = baseMapper.incrementTotalOrders(engineerId);

            // 如果订单完成，增加完成订单数
            if (isCompleted && result > 0) {
                baseMapper.incrementCompletedOrders(engineerId);
            }

            // 更新成功率
            if (result > 0) {
                baseMapper.updateSuccessRate(engineerId);
            }

            return result > 0;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
}
