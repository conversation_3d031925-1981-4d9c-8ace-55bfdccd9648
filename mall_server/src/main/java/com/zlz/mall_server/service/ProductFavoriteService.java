package com.zlz.mall_server.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zlz.mall_server.model.ProductFavorite;
import com.zlz.mall_server.model.Products;

import java.util.List;

public interface ProductFavoriteService extends IService<ProductFavorite> {
    
    /**
     * 添加商品收藏
     * @param openId 用户openId
     * @param productId 商品ID
     * @return 是否成功
     */
    boolean addFavorite(String openId, Long productId);
    
    /**
     * 取消商品收藏
     * @param openId 用户openId
     * @param productId 商品ID
     * @return 是否成功
     */
    boolean removeFavorite(String openId, Long productId);
    
    /**
     * 检查是否已收藏
     * @param openId 用户openId
     * @param productId 商品ID
     * @return 是否已收藏
     */
    boolean isFavorite(String openId, Long productId);
    
    /**
     * 获取用户收藏的商品列表
     * @param openId 用户openId
     * @return 收藏的商品列表
     */
    List<Products> getFavoriteProducts(String openId);
    
    /**
     * 获取用户收藏数量
     * @param openId 用户openId
     * @return 收藏数量
     */
    int getFavoriteCount(String openId);
}
