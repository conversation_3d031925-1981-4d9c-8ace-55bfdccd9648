package com.zlz.mall_server.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zlz.mall_server.model.User;
import java.util.Map;

public interface UserService extends IService<User> {

    /**
     * 用户登录
     * @param code 抖音登录code
     * @param userInfo 用户信息
     * @return 登录结果
     */
    Map<String, Object> login(String code, Map<String, Object> userInfo);

    /**
     * 根据openId查询用户
     * @param openId 抖音openId
     * @return 用户对象
     */
    User findByOpenId(String openId);

    /**
     * 创建或更新用户
     * @param user 用户对象
     * @return 用户对象
     */
    User createOrUpdateUser(User user);
}
