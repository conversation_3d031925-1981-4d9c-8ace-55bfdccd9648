package com.zlz.mall_server.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zlz.mall_server.model.Products;

import java.util.List;

public interface ProductService extends IService<Products> {
    
    /**
     * 根据分类ID查询商品列表
     * @param categoryId 分类ID，0表示查询所有
     * @return 商品列表
     */
    List<Products> getProductsByCategoryId(Integer categoryId);
    
    /**
     * 根据分类ID和排序方式查询商品列表
     * @param categoryId 分类ID
     * @param sortType 排序方式：default, sales, price, sortOrder
     * @param priceOrder 价格排序：asc, desc
     * @return 商品列表
     */
    List<Products> getProductsByCategoryIdWithSort(Integer categoryId, String sortType, String priceOrder);

    /**
     * 根据分类ID获取商品列表（带排序和限制数量）
     * @param categoryId 分类ID
     * @param sortType 排序方式：default, sales, price, sortOrder
     * @param priceOrder 价格排序：asc, desc
     * @param limit 限制数量，0表示不限制
     * @return 商品列表
     */
    List<Products> getProductsByCategoryIdWithSortAndLimit(Integer categoryId, String sortType, String priceOrder, Integer limit);

    /**
     * 根据商品ID获取商品详情
     * @param id 商品ID
     * @return 商品详情
     */
    Products getProductDetail(Long id);
}
