package com.zlz.mall_server.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zlz.mall_server.model.AdminUser;

public interface AdminUserService extends IService<AdminUser> {
    
    /**
     * 管理员登录
     * @param username 用户名
     * @param password 密码
     * @param ip IP地址
     * @return 登录结果
     */
    AdminUser login(String username, String password, String ip);
    
    /**
     * 验证密码
     * @param rawPassword 原始密码
     * @param encodedPassword 加密密码
     * @return 是否匹配
     */
    boolean verifyPassword(String rawPassword, String encodedPassword);
    
    /**
     * 加密密码
     * @param rawPassword 原始密码
     * @return 加密后的密码
     */
    String encodePassword(String rawPassword);

    /**
     * 创建管理员用户
     * @param adminUser 管理员信息
     * @return 创建结果
     */
    boolean createAdminUser(AdminUser adminUser);

    /**
     * 检查用户名是否存在
     * @param username 用户名
     * @return 是否存在
     */
    boolean existsByUsername(String username);
}
