package com.zlz.mall_server.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zlz.mall_server.mapper.UserMapper;
import com.zlz.mall_server.model.User;
import com.zlz.mall_server.service.UserService;
import com.zlz.mall_server.utils.TtApiUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@Service
@Slf4j
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private TtApiUtil ttApiUtil;

    @Override
    public Map<String, Object> login(String code, Map<String, Object> userInfo) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 调用抖音API获取openId和sessionKey
            Map<String, Object> ttResult = ttApiUtil.code2Session(code);
            
            log.info("抖音登录API响应: {}", ttResult);

//            if (ttResult.containsKey("error")) {
//                // 抖音API调用失败
//                Object errorMsg = ttResult.get("errmsg");
//                result.put("success", false);
//                result.put("message", "抖音登录失败: " + (errorMsg != null ? errorMsg : "未知错误"));
//                return result;
//            }
            
            // 检查是否包含openid
            if (!ttResult.containsKey("openid")) {
                result.put("success", false);
                result.put("message", "抖音登录失败: 响应中没有openid");
                return result;
            }

            String openId = (String) ttResult.get("openid");
            String sessionKey = (String) ttResult.get("session_key");

            // 查询用户是否存在
            User user = findByOpenId(openId);

            if (user == null) {
                // 用户不存在，创建新用户
                user = new User();
                user.setOpenId(openId);
                user.setCreatedAt(LocalDateTime.now());
            }

            // 更新用户信息
            if (userInfo != null) {
                user.setNickname((String) userInfo.get("nickName"));
                user.setAvatarUrl((String) userInfo.get("avatarUrl"));
                Object genderObj = userInfo.get("gender");
                if (genderObj != null) {
                    user.setGender(String.valueOf(genderObj));
                }
            }

            user.setLastLoginTime(LocalDateTime.now());
            user.setUpdatedAt(LocalDateTime.now());
            user.setStatus(1); // 正常状态

            // 保存用户信息
            createOrUpdateUser(user);

            // 返回登录结果
            result.put("success", true);
            result.put("openId", openId);
            result.put("sessionKey", sessionKey);
            result.put("userId", user.getId());

            // 不返回敏感信息
            user.setOpenId(null);
            result.put("userInfo", user);

        } catch (Exception e) {
            log.error("用户登录异常", e);
            result.put("success", false);
            result.put("message", "登录异常: " + e.getMessage());
        }

        return result;
    }

    @Override
    public User findByOpenId(String openId) {
        return userMapper.findByOpenId(openId);
    }

    @Override
    public User createOrUpdateUser(User user) {
        if (user.getId() == null) {
            // 新用户，插入
            save(user);
        } else {
            // 更新用户
            updateById(user);
        }
        return user;
    }
}
