package com.zlz.mall_server.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zlz.mall_server.model.ServiceCenter;
import com.zlz.mall_server.mapper.ServiceCenterMapper.ServiceCenterStats;

import java.math.BigDecimal;
import java.util.List;

public interface ServiceCenterService extends IService<ServiceCenter> {
    
    /**
     * 查询所有已审核通过的服务网点
     * @return 服务网点列表
     */
    List<ServiceCenter> getApprovedServiceCenters();
    
    /**
     * 查询推荐的服务网点
     * @return 服务网点列表
     */
    List<ServiceCenter> getFeaturedServiceCenters();
    
    /**
     * 根据城市查询服务网点
     * @param city 城市
     * @return 服务网点列表
     */
    List<ServiceCenter> getServiceCentersByCity(String city);
    
    /**
     * 根据服务类型查询服务网点
     * @param serviceType 服务类型
     * @return 服务网点列表
     */
    List<ServiceCenter> getServiceCentersByServiceType(String serviceType);
    
    /**
     * 根据关键词搜索服务网点
     * @param keyword 关键词
     * @return 服务网点列表
     */
    List<ServiceCenter> searchServiceCenters(String keyword);
    
    /**
     * 根据位置范围查询服务网点
     * @param minLat 最小纬度
     * @param maxLat 最大纬度
     * @param minLng 最小经度
     * @param maxLng 最大经度
     * @return 服务网点列表
     */
    List<ServiceCenter> getServiceCentersByLocationRange(BigDecimal minLat, BigDecimal maxLat, BigDecimal minLng, BigDecimal maxLng);
    
    /**
     * 获取服务网点统计信息
     * @return 统计信息
     */
    ServiceCenterStats getServiceCenterStats();
}
