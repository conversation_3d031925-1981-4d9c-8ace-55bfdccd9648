package com.zlz.mall_server.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zlz.mall_server.model.UserAddress;

import java.util.List;

public interface UserAddressService extends IService<UserAddress> {
    
    /**
     * 根据openId查询用户地址列表
     * @param openId 用户openId
     * @return 地址列表
     */
    List<UserAddress> findByOpenId(String openId);
    
    /**
     * 根据openId查询用户默认地址
     * @param openId 用户openId
     * @return 默认地址
     */
    UserAddress findDefaultByOpenId(String openId);
    
    /**
     * 保存地址
     * @param address 地址信息
     * @return 保存后的地址
     */
    UserAddress saveAddress(UserAddress address);
    
    /**
     * 删除地址
     * @param id 地址ID
     * @param openId 用户openId（用于验证权限）
     * @return 是否删除成功
     */
    boolean deleteAddress(Long id, String openId);
}
