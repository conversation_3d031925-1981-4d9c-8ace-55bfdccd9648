package com.zlz.mall_server.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zlz.mall_server.mapper.ProductFavoriteMapper;
import com.zlz.mall_server.model.ProductFavorite;
import com.zlz.mall_server.model.Products;
import com.zlz.mall_server.service.ProductFavoriteService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@Service
public class ProductFavoriteServiceImpl extends ServiceImpl<ProductFavoriteMapper, ProductFavorite> implements ProductFavoriteService {
    
    @Override
    public boolean addFavorite(String openId, Long productId) {
        try {
            // 检查是否已经收藏
            if (isFavorite(openId, productId)) {
                log.warn("用户{}已经收藏了商品{}", openId, productId);
                return false;
            }
            
            // 创建收藏记录
            ProductFavorite favorite = new ProductFavorite();
            favorite.setOpenId(openId);
            favorite.setProductId(productId);
            favorite.setCreatedAt(LocalDateTime.now());
            
            return save(favorite);
        } catch (Exception e) {
            log.error("添加收藏失败: openId={}, productId={}", openId, productId, e);
            return false;
        }
    }
    
    @Override
    public boolean removeFavorite(String openId, Long productId) {
        try {
            int result = baseMapper.deleteFavorite(openId, productId);
            return result > 0;
        } catch (Exception e) {
            log.error("取消收藏失败: openId={}, productId={}", openId, productId, e);
            return false;
        }
    }
    
    @Override
    public boolean isFavorite(String openId, Long productId) {
        try {
            int count = baseMapper.checkFavoriteExists(openId, productId);
            return count > 0;
        } catch (Exception e) {
            log.error("检查收藏状态失败: openId={}, productId={}", openId, productId, e);
            return false;
        }
    }
    
    @Override
    public List<Products> getFavoriteProducts(String openId) {
        try {
            return baseMapper.findFavoriteProductsByOpenId(openId);
        } catch (Exception e) {
            log.error("获取收藏商品列表失败: openId={}", openId, e);
            return List.of();
        }
    }
    
    @Override
    public int getFavoriteCount(String openId) {
        try {
            return baseMapper.countFavoritesByOpenId(openId);
        } catch (Exception e) {
            log.error("获取收藏数量失败: openId={}", openId, e);
            return 0;
        }
    }
}
