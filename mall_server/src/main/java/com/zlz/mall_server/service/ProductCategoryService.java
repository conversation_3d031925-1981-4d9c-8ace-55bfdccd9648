package com.zlz.mall_server.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zlz.mall_server.model.ProductCategory;

import java.util.List;

public interface ProductCategoryService extends IService<ProductCategory> {
    
    /**
     * 查询所有启用的分类
     * @return 分类列表
     */
    List<ProductCategory> getAllEnabledCategories();
    
    /**
     * 根据父分类ID查询子分类
     * @param parentId 父分类ID
     * @return 子分类列表
     */
    List<ProductCategory> getCategoriesByParentId(Integer parentId);
}
