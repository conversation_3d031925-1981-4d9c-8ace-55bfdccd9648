package com.zlz.mall_server.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zlz.mall_server.mapper.AdminUserMapper;
import com.zlz.mall_server.model.AdminUser;
import com.zlz.mall_server.service.AdminUserService;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;

@Service
public class AdminUserServiceImpl extends ServiceImpl<AdminUserMapper, AdminUser> implements AdminUserService {
    
    private final BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
    
    @Override
    public AdminUser login(String username, String password, String ip) {
        // 根据用户名查询管理员
        AdminUser admin = baseMapper.findByUsername(username);
        if (admin == null) {
            return null;
        }
        
        // 验证密码
        if (!verifyPassword(password, admin.getPassword())) {
            return null;
        }
        
        // 更新登录信息
        baseMapper.updateLastLoginInfo(admin.getId(), ip);
        
        // 清除密码字段，不返回给前端
        admin.setPassword(null);
        
        return admin;
    }
    
    @Override
    public boolean verifyPassword(String rawPassword, String encodedPassword) {
        return passwordEncoder.matches(rawPassword, encodedPassword);
    }
    
    @Override
    public String encodePassword(String rawPassword) {
        return passwordEncoder.encode(rawPassword);
    }
}
