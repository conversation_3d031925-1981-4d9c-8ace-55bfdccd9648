package com.zlz.mall_server.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zlz.mall_server.mapper.AdminUserMapper;
import com.zlz.mall_server.model.AdminUser;
import com.zlz.mall_server.service.AdminUserService;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;

@Service
public class AdminUserServiceImpl extends ServiceImpl<AdminUserMapper, AdminUser> implements AdminUserService {
    
    private final BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
    
    @Override
    public AdminUser login(String username, String password, String ip) {
        // 根据用户名查询管理员
        AdminUser admin = baseMapper.findByUsername(username);
        if (admin == null) {
            return null;
        }
        
        // 验证密码
        if (!verifyPassword(password, admin.getPassword())) {
            return null;
        }
        
        // 更新登录信息
        baseMapper.updateLastLoginInfo(admin.getId(), ip);
        
        // 清除密码字段，不返回给前端
        admin.setPassword(null);
        
        return admin;
    }
    
    @Override
    public boolean verifyPassword(String rawPassword, String encodedPassword) {
        return passwordEncoder.matches(rawPassword, encodedPassword);
    }
    
    @Override
    public String encodePassword(String rawPassword) {
        return passwordEncoder.encode(rawPassword);
    }

    @Override
    public boolean createAdminUser(AdminUser adminUser) {
        try {
            // 检查用户名是否已存在
            if (existsByUsername(adminUser.getUsername())) {
                return false;
            }

            // 加密密码
            adminUser.setPassword(encodePassword(adminUser.getPassword()));

            // 设置默认值
            if (adminUser.getRole() == null) {
                adminUser.setRole("admin");
            }
            if (adminUser.getStatus() == null) {
                adminUser.setStatus(1);
            }
            if (adminUser.getLoginCount() == null) {
                adminUser.setLoginCount(0);
            }

            // 设置时间
            adminUser.setCreatedAt(java.time.LocalDateTime.now());
            adminUser.setUpdatedAt(java.time.LocalDateTime.now());
            adminUser.setPasswordUpdatedAt(java.time.LocalDateTime.now());

            // 保存到数据库
            return save(adminUser);
        } catch (Exception e) {
            log.error("创建管理员用户失败", e);
            return false;
        }
    }

    @Override
    public boolean existsByUsername(String username) {
        AdminUser admin = baseMapper.findByUsername(username);
        return admin != null;
    }
}
