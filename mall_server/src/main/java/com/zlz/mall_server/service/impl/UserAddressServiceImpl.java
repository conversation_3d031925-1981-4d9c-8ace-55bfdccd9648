package com.zlz.mall_server.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zlz.mall_server.mapper.UserAddressMapper;
import com.zlz.mall_server.model.UserAddress;
import com.zlz.mall_server.service.UserAddressService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Service
public class UserAddressServiceImpl extends ServiceImpl<UserAddressMapper, UserAddress> implements UserAddressService {

    @Autowired
    private UserAddressMapper userAddressMapper;

    @Override
    public List<UserAddress> findByOpenId(String openId) {
        return userAddressMapper.findByOpenId(openId);
    }

    @Override
    public UserAddress findDefaultByOpenId(String openId) {
        return userAddressMapper.findDefaultByOpenId(openId);
    }

    @Override
    @Transactional
    public UserAddress saveAddress(UserAddress address) {
        LocalDateTime now = LocalDateTime.now();
        
        // 如果是新增地址
        if (address.getId() == null) {
            address.setCreatedAt(now);
            address.setUpdatedAt(now);
            
            // 如果设置为默认地址，需要将该用户的其他地址设为非默认
            if (address.getIsDefault()) {
                userAddressMapper.clearDefaultAddress(address.getOpenId());
            }
            
            // 如果是用户的第一个地址，自动设为默认地址
            QueryWrapper<UserAddress> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("open_id", address.getOpenId());
            int count = (int)count(queryWrapper);
            if (count == 0) {
                address.setIsDefault(true);
            }
            
            save(address);
        } else {
            // 更新地址
            address.setUpdatedAt(now);
            
            // 如果设置为默认地址，需要将该用户的其他地址设为非默认
            if (address.getIsDefault()) {
                userAddressMapper.clearDefaultAddress(address.getOpenId());
            }
            
            updateById(address);
        }
        
        return address;
    }

    @Override
    public boolean deleteAddress(Long id, String openId) {
        // 查询地址是否存在且属于该用户
        QueryWrapper<UserAddress> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id).eq("open_id", openId);
        UserAddress address = getOne(queryWrapper);
        
        if (address == null) {
            return false;
        }
        
        // 删除地址
        boolean result = removeById(id);
        
        // 如果删除的是默认地址，且用户还有其他地址，则将最新的一个地址设为默认地址
        if (result && address.getIsDefault()) {
            List<UserAddress> addresses = findByOpenId(openId);
            if (!addresses.isEmpty()) {
                UserAddress newDefault = addresses.get(0);
                newDefault.setIsDefault(true);
                newDefault.setUpdatedAt(LocalDateTime.now());
                updateById(newDefault);
            }
        }
        
        return result;
    }
}
