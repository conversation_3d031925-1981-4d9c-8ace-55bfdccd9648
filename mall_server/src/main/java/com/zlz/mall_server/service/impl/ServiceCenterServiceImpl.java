package com.zlz.mall_server.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zlz.mall_server.mapper.ServiceCenterMapper;
import com.zlz.mall_server.mapper.ServiceCenterMapper.ServiceCenterStats;
import com.zlz.mall_server.model.ServiceCenter;
import com.zlz.mall_server.service.ServiceCenterService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

@Service
public class ServiceCenterServiceImpl extends ServiceImpl<ServiceCenterMapper, ServiceCenter> implements ServiceCenterService {
    
    @Override
    public List<ServiceCenter> getApprovedServiceCenters() {
        return baseMapper.findApprovedServiceCenters();
    }
    
    @Override
    public List<ServiceCenter> getFeaturedServiceCenters() {
        return baseMapper.findFeaturedServiceCenters();
    }
    
    @Override
    public List<ServiceCenter> getServiceCentersByCity(String city) {
        return baseMapper.findByCity(city);
    }
    
    @Override
    public List<ServiceCenter> getServiceCentersByServiceType(String serviceType) {
        return baseMapper.findByServiceType(serviceType);
    }
    
    @Override
    public List<ServiceCenter> searchServiceCenters(String keyword) {
        return baseMapper.searchByKeyword(keyword);
    }
    
    @Override
    public List<ServiceCenter> getServiceCentersByLocationRange(BigDecimal minLat, BigDecimal maxLat, BigDecimal minLng, BigDecimal maxLng) {
        return baseMapper.findByLocationRange(minLat, maxLat, minLng, maxLng);
    }
    
    @Override
    public ServiceCenterStats getServiceCenterStats() {
        return baseMapper.getServiceCenterStats();
    }
}
