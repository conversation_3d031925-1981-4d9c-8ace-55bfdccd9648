package com.zlz.mall_server.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zlz.mall_server.mapper.ProductCategoryMapper;
import com.zlz.mall_server.model.ProductCategory;
import com.zlz.mall_server.service.ProductCategoryService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ProductCategoryServiceImpl extends ServiceImpl<ProductCategoryMapper, ProductCategory> implements ProductCategoryService {
    
    @Override
    public List<ProductCategory> getAllEnabledCategories() {
        return baseMapper.findAllEnabled();
    }
    
    @Override
    public List<ProductCategory> getCategoriesByParentId(Integer parentId) {
        return baseMapper.findByParentId(parentId);
    }
}
