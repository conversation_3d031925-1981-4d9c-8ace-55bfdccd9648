package com.zlz.mall_server.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zlz.mall_server.model.Engineer;
import com.zlz.mall_server.mapper.EngineerMapper.EngineerStats;

import java.util.List;

public interface EngineerService extends IService<Engineer> {
    
    /**
     * 查询所有已审核通过的工程师
     * @return 工程师列表
     */
    List<Engineer> getApprovedEngineers();
    
    /**
     * 查询在线且可接单的工程师
     * @return 工程师列表
     */
    List<Engineer> getAvailableEngineers();
    
    /**
     * 根据专业领域查询工程师
     * @param specialty 专业领域
     * @return 工程师列表
     */
    List<Engineer> getEngineersBySpecialty(String specialty);
    
    /**
     * 根据服务区域查询工程师
     * @param area 服务区域
     * @return 工程师列表
     */
    List<Engineer> getEngineersByWorkArea(String area);
    
    /**
     * 获取工程师统计信息
     * @return 统计信息
     */
    EngineerStats getEngineerStats();

    /**
     * 根据手机号查询工程师
     * @param phone 手机号
     * @return 工程师对象
     */
    Engineer getByPhone(String phone);

    /**
     * 增加工程师服务次数
     * @param engineerId 工程师ID
     * @return 是否成功
     */
    boolean incrementServiceCount(Long engineerId);

    /**
     * 更新工程师订单统计
     * @param engineerId 工程师ID
     * @param isCompleted 是否完成订单
     * @return 是否成功
     */
    boolean updateOrderStats(Long engineerId, boolean isCompleted);
}
