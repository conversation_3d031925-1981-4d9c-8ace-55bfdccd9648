package com.zlz.mall_server.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zlz.mall_server.mapper.CustomerServiceSessionMapper;
import com.zlz.mall_server.mapper.CustomerServiceMessageMapper;
import com.zlz.mall_server.mapper.CustomerServiceQuickReplyMapper;
import com.zlz.mall_server.mapper.UserMapper;
import com.zlz.mall_server.model.CustomerServiceSession;
import com.zlz.mall_server.model.CustomerServiceMessage;
import com.zlz.mall_server.model.CustomerServiceQuickReply;
import com.zlz.mall_server.model.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Service
public class CustomerServiceService {
    
    @Autowired
    private CustomerServiceSessionMapper sessionMapper;
    
    @Autowired
    private CustomerServiceMessageMapper messageMapper;
    
    @Autowired
    private CustomerServiceQuickReplyMapper quickReplyMapper;
    
    @Autowired
    private UserMapper userMapper;
    
    /**
     * 获取或创建用户会话
     */
    @Transactional
    public CustomerServiceSession getOrCreateSession(String openId) {
        // 查找用户的活跃会话
        CustomerServiceSession session = sessionMapper.findActiveSessionByOpenId(openId);
        
        if (session == null) {
            // 创建新会话
            User user = userMapper.selectOne(new QueryWrapper<User>().eq("open_id", openId));
            
            session = new CustomerServiceSession();
            session.setSessionId(UUID.randomUUID().toString());
            session.setOpenId(openId);
            session.setUserNickname(user != null ? user.getNickname() : "用户");
            session.setUserAvatar(user != null ? user.getAvatarUrl() : null);
            session.setStatus("waiting");
            session.setUnreadCountUser(0);
            session.setUnreadCountAdmin(0);
            session.setCreatedAt(LocalDateTime.now());
            session.setUpdatedAt(LocalDateTime.now());
            
            sessionMapper.insert(session);
        }
        
        return session;
    }
    
    /**
     * 发送消息
     */
    @Transactional
    public CustomerServiceMessage sendMessage(String sessionId, String senderType, String senderId, 
                                            String senderName, String senderAvatar, String content) {
        // 创建消息
        CustomerServiceMessage message = new CustomerServiceMessage();
        message.setSessionId(sessionId);
        message.setSenderType(senderType);
        message.setSenderId(senderId);
        message.setSenderName(senderName);
        message.setSenderAvatar(senderAvatar);
        message.setMessageType("text");
        message.setContent(content);
        message.setIsRead(false);
        message.setCreatedAt(LocalDateTime.now());
        
        messageMapper.insert(message);
        
        // 更新会话信息
        CustomerServiceSession session = sessionMapper.selectOne(
            new QueryWrapper<CustomerServiceSession>().eq("session_id", sessionId));
        
        if (session != null) {
            session.setLastMessageTime(LocalDateTime.now());
            session.setLastMessageContent(content);
            session.setUpdatedAt(LocalDateTime.now());
            
            // 更新未读计数
            if ("user".equals(senderType)) {
                session.setUnreadCountAdmin(session.getUnreadCountAdmin() + 1);
            } else {
                session.setUnreadCountUser(session.getUnreadCountUser() + 1);
            }
            
            sessionMapper.updateById(session);
        }
        
        return message;
    }
    
    /**
     * 获取会话消息列表
     */
    public List<CustomerServiceMessage> getSessionMessages(String sessionId) {
        return messageMapper.findMessagesBySessionId(sessionId);
    }
    
    /**
     * 标记消息为已读
     */
    @Transactional
    public void markMessagesAsRead(String sessionId, String readerType) {
        // 标记消息为已读
        String senderType = "user".equals(readerType) ? "admin" : "user";
        messageMapper.markMessagesAsRead(sessionId, senderType);
        
        // 更新会话未读计数
        if ("user".equals(readerType)) {
            sessionMapper.markUserMessagesAsRead(sessionId);
        } else {
            sessionMapper.markAdminMessagesAsRead(sessionId);
        }
    }
    
    /**
     * 分配客服
     */
    @Transactional
    public void assignAdmin(String sessionId, Long adminId, String adminName) {
        CustomerServiceSession session = sessionMapper.selectOne(
            new QueryWrapper<CustomerServiceSession>().eq("session_id", sessionId));
        
        if (session != null) {
            session.setAdminId(adminId);
            session.setAdminName(adminName);
            session.setStatus("active");
            session.setUpdatedAt(LocalDateTime.now());
            
            sessionMapper.updateById(session);
        }
    }
    
    /**
     * 关闭会话
     */
    @Transactional
    public void closeSession(String sessionId) {
        CustomerServiceSession session = sessionMapper.selectOne(
            new QueryWrapper<CustomerServiceSession>().eq("session_id", sessionId));
        
        if (session != null) {
            session.setStatus("closed");
            session.setClosedAt(LocalDateTime.now());
            session.setUpdatedAt(LocalDateTime.now());
            
            sessionMapper.updateById(session);
        }
    }
    
    /**
     * 获取管理员的活跃会话
     */
    public List<CustomerServiceSession> getAdminActiveSessions(Long adminId) {
        return sessionMapper.findActiveSessionsByAdminId(adminId);
    }
    
    /**
     * 获取等待接入的会话
     */
    public List<CustomerServiceSession> getWaitingSessions() {
        return sessionMapper.findWaitingSessions();
    }
    
    /**
     * 获取快捷回复
     */
    public List<CustomerServiceQuickReply> getQuickReplies(Long adminId) {
        return quickReplyMapper.findByAdminId(adminId);
    }

    /**
     * 根据openId获取活跃会话
     */
    public CustomerServiceSession getActiveSessionByOpenId(String openId) {
        return sessionMapper.findActiveSessionByOpenId(openId);
    }

    /**
     * 创建新会话
     */
    @Transactional
    public CustomerServiceSession createSession(String sessionId, String openId, String userNickname, String userAvatar) {
        CustomerServiceSession session = new CustomerServiceSession();
        session.setSessionId(sessionId);
        session.setOpenId(openId);
        session.setUserNickname(userNickname);
        session.setUserAvatar(userAvatar);
        session.setStatus("waiting");
        session.setUnreadCountUser(0);
        session.setUnreadCountAdmin(0);
        session.setCreatedAt(LocalDateTime.now());
        session.setUpdatedAt(LocalDateTime.now());

        sessionMapper.insert(session);
        return session;
    }

    /**
     * 根据会话ID获取会话
     */
    public CustomerServiceSession getSessionById(String sessionId) {
        return sessionMapper.selectOne(new QueryWrapper<CustomerServiceSession>().eq("session_id", sessionId));
    }
}
