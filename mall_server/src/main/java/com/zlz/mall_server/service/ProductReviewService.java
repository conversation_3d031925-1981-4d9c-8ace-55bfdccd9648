package com.zlz.mall_server.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zlz.mall_server.model.ProductReview;

import java.util.List;

public interface ProductReviewService extends IService<ProductReview> {
    
    /**
     * 根据商品ID查询评价列表
     * @param productId 商品ID
     * @return 评价列表
     */
    List<ProductReview> getReviewsByProductId(Long productId);
    
    /**
     * 根据商品ID统计评价数量
     * @param productId 商品ID
     * @return 评价数量
     */
    Integer getReviewCountByProductId(Long productId);
    
    /**
     * 根据商品ID计算平均评分
     * @param productId 商品ID
     * @return 平均评分
     */
    Double getAverageRatingByProductId(Long productId);
}
