package com.zlz.mall_server.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zlz.mall_server.mapper.RepairOrderMapper;
import com.zlz.mall_server.model.RepairOrder;
import com.zlz.mall_server.model.Engineer;
import com.zlz.mall_server.service.RepairOrderService;
import com.zlz.mall_server.service.EngineerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class RepairOrderServiceImpl extends ServiceImpl<RepairOrderMapper, RepairOrder> implements RepairOrderService {

    @Autowired
    private RepairOrderMapper repairOrderMapper;

    @Autowired
    private EngineerService engineerService;

    @Override
    public RepairOrder createRepairOrder(RepairOrder repairOrder) {
        // 生成订单编号：R + 时间戳
        String orderNo = "R" + System.currentTimeMillis();
        repairOrder.setOrderNo(orderNo);

        // 设置默认值
        if (repairOrder.getStatus() == null) {
            repairOrder.setStatus("pending"); // 默认为"待接单"状态
        }

        if (repairOrder.getRepairFee() == null) {
            repairOrder.setRepairFee(new BigDecimal("0.00"));
        }

        if (repairOrder.getPartsFee() == null) {
            repairOrder.setPartsFee(new BigDecimal("0.00"));
        }

        // 计算总费用
        BigDecimal totalFee = repairOrder.getRepairFee().add(repairOrder.getPartsFee());
        repairOrder.setTotalFee(totalFee);

        // 设置创建时间和更新时间
        LocalDateTime now = LocalDateTime.now();
        repairOrder.setCreatedAt(now);
        repairOrder.setUpdatedAt(now);

        // 如果appointmentDate是字符串格式，需要转换为LocalDate
        if (repairOrder.getAppointmentDate() == null && repairOrder.getAppointmentTime() != null) {
            String[] timeParts = repairOrder.getAppointmentTime().split(" ");
            if (timeParts.length > 0) {
                try {
                    LocalDate appointmentDate = LocalDate.parse(timeParts[0], DateTimeFormatter.ISO_DATE);
                    repairOrder.setAppointmentDate(appointmentDate);
                } catch (Exception e) {
                    // 解析失败，使用当前日期
                    repairOrder.setAppointmentDate(LocalDate.now());
                }
            }
        }

        // 保存订单
        save(repairOrder);

        return repairOrder;
    }

    @Override
    public List<RepairOrder> findByOpenId(String openId) {
        return repairOrderMapper.findByOpenId(openId);
    }

    @Override
    public List<RepairOrder> findByOpenIdAndStatus(String openId, String status) {
        return repairOrderMapper.findByOpenIdAndStatus(openId, status);
    }

    @Override
    public RepairOrder findByOrderNo(String orderNo) {
        return repairOrderMapper.findByOrderNo(orderNo);
    }

    @Override
    public boolean cancelRepairOrder(String orderNo, String openId) {
        // 查询订单是否存在且属于该用户
        QueryWrapper<RepairOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("order_no", orderNo).eq("open_id", openId);
        RepairOrder repairOrder = getOne(queryWrapper);

        if (repairOrder == null) {
            return false;
        }

        // 只有待接单状态的订单才能取消
        if (!"pending".equals(repairOrder.getStatus())) {
            return false;
        }

        // 更新订单状态为已取消
        repairOrder.setStatus("cancelled");
        repairOrder.setUpdatedAt(LocalDateTime.now());

        return updateById(repairOrder);
    }

    @Override
    public boolean updateRepairOrderStatus(String orderNo, String status) {
        // 查询订单是否存在
        RepairOrder repairOrder = findByOrderNo(orderNo);

        if (repairOrder == null) {
            return false;
        }

        // 验证状态是否有效
        if (!isValidStatus(status)) {
            return false;
        }

        // 更新订单状态
        repairOrder.setStatus(status);
        repairOrder.setUpdatedAt(LocalDateTime.now());

        return updateById(repairOrder);
    }

    // 验证状态是否有效
    private boolean isValidStatus(String status) {
        return "pending".equals(status) || // 待接单
               "accepted".equals(status) || // 待上门
               "processing".equals(status) || // 维修中
               "completed".equals(status) || // 已完成
               "cancelled".equals(status); // 已取消
    }

    @Override
    public Map<String, Integer> getRepairOrderStats(String openId) {
        Map<String, Integer> stats = new HashMap<>();

        // 查询各状态订单数量
        QueryWrapper<RepairOrder> pendingQuery = new QueryWrapper<>();
        pendingQuery.eq("open_id", openId).eq("status", "pending");
        int pendingCount = (int) count(pendingQuery);

        QueryWrapper<RepairOrder> acceptedQuery = new QueryWrapper<>();
        acceptedQuery.eq("open_id", openId).eq("status", "accepted");
        int acceptedCount = (int)count(acceptedQuery);

        QueryWrapper<RepairOrder> processingQuery = new QueryWrapper<>();
        processingQuery.eq("open_id", openId).eq("status", "processing");
        int processingCount = (int) count(processingQuery);

        QueryWrapper<RepairOrder> completedQuery = new QueryWrapper<>();
        completedQuery.eq("open_id", openId).eq("status", "completed");
        int completedCount = (int) count(completedQuery);

        QueryWrapper<RepairOrder> cancelledQuery = new QueryWrapper<>();
        cancelledQuery.eq("open_id", openId).eq("status", "cancelled");
        int cancelledCount = (int) count(cancelledQuery);

        stats.put("pending", pendingCount);
        stats.put("accepted", acceptedCount);
        stats.put("processing", processingCount);
        stats.put("completed", completedCount);
        stats.put("cancelled", cancelledCount);
        stats.put("total", pendingCount + acceptedCount + processingCount + completedCount + cancelledCount);

        return stats;
    }

    @Override
    public Map<String, Long> getStatusStatistics() {
        Map<String, Long> stats = new HashMap<>();

        // 查询各状态订单数量
        QueryWrapper<RepairOrder> pendingQuery = new QueryWrapper<>();
        pendingQuery.eq("status", "pending");
        long pendingCount = count(pendingQuery);

        QueryWrapper<RepairOrder> acceptedQuery = new QueryWrapper<>();
        acceptedQuery.eq("status", "accepted");
        long acceptedCount = count(acceptedQuery);

        QueryWrapper<RepairOrder> processingQuery = new QueryWrapper<>();
        processingQuery.eq("status", "processing");
        long processingCount = count(processingQuery);

        QueryWrapper<RepairOrder> completedQuery = new QueryWrapper<>();
        completedQuery.eq("status", "completed");
        long completedCount = count(completedQuery);

        stats.put("pending", pendingCount);
        stats.put("accepted", acceptedCount);
        stats.put("processing", processingCount);
        stats.put("completed", completedCount);
        stats.put("total", pendingCount + acceptedCount + processingCount + completedCount);

        return stats;
    }

    @Override
    public boolean updateOrderStatus(Long orderId, String status, String remark) {
        RepairOrder order = getById(orderId);
        if (order == null) {
            return false;
        }

        String oldStatus = order.getStatus();
        order.setStatus(status);
        if (remark != null && !remark.trim().isEmpty()) {
            order.setRemark(remark);
        }

        // 如果状态变更为已完成，设置完成时间
        if ("completed".equals(status) && !"completed".equals(oldStatus)) {
            order.setCompletedAt(LocalDateTime.now());
        }

        order.setUpdatedAt(LocalDateTime.now());

        boolean result = updateById(order);

        // 如果订单状态变更为已完成，且有分配工程师，则增加工程师服务次数
        if (result && "completed".equals(status) && !"completed".equals(oldStatus) && order.getEngineerId() != null) {
            try {
                log.info("🎯 订单状态变更检测: 订单 {} 状态从 {} 变更为 {}", order.getOrderNo(), oldStatus, status);
                log.info("🔧 订单 {} 已完成，开始增加工程师 {} 的服务次数", order.getOrderNo(), order.getEngineerId());

                boolean incrementResult = engineerService.incrementServiceCount(order.getEngineerId());
                if (incrementResult) {
                    log.info("✅ 工程师 {} 服务次数增加成功", order.getEngineerId());
                } else {
                    log.warn("❌ 工程师 {} 服务次数增加失败", order.getEngineerId());
                }
            } catch (Exception e) {
                log.error("💥 更新工程师服务次数时发生错误: ", e);
            }
        } else {
            log.info("🔍 订单状态更新条件检查: result={}, status={}, oldStatus={}, engineerId={}",
                    result, status, oldStatus, order.getEngineerId());
        }

        return result;
    }

    @Override
    public boolean assignEngineer(Long orderId, Long engineerId, String engineerName, String engineerPhone) {
        RepairOrder order = getById(orderId);
        if (order == null) {
            return false;
        }

        // 检查是否已经分配过工程师
        boolean wasAssigned = order.getEngineerId() != null;

        order.setEngineerId(engineerId);
        order.setEngineerName(engineerName);
        order.setEngineerPhone(engineerPhone);
        order.setStatus("accepted"); // 分配工程师后状态变为已接单
        order.setUpdatedAt(LocalDateTime.now());

        boolean result = updateById(order);

        // 如果是首次分配工程师，增加工程师的总接单数
        if (result && !wasAssigned && engineerId != null) {
            try {
                log.info("为订单 {} 分配工程师 {}，增加总接单数", order.getOrderNo(), engineerId);
                boolean incrementResult = engineerService.updateOrderStats(engineerId, false);
                if (incrementResult) {
                    log.info("工程师 {} 总接单数增加成功", engineerId);
                } else {
                    log.warn("工程师 {} 总接单数增加失败", engineerId);
                }
            } catch (Exception e) {
                log.error("更新工程师接单统计时发生错误: ", e);
            }
        }

        return result;
    }
}
