package com.zlz.mall_server.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/admin/test-db")
public class DatabaseTestController {
    
    @Autowired
    private DataSource dataSource;
    
    @GetMapping("/tables")
    public Map<String, Object> checkTables() {
        Map<String, Object> result = new HashMap<>();
        List<String> tables = new ArrayList<>();
        
        try (Connection connection = dataSource.getConnection()) {
            DatabaseMetaData metaData = connection.getMetaData();
            ResultSet rs = metaData.getTables(null, null, "customer_service_%", new String[]{"TABLE"});
            
            while (rs.next()) {
                tables.add(rs.getString("TABLE_NAME"));
            }
            
            result.put("success", true);
            result.put("customerServiceTables", tables);
            result.put("message", "数据库连接正常");
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("message", "数据库连接失败");
        }
        
        return result;
    }
    
    @GetMapping("/create-tables")
    public Map<String, Object> createTables() {
        Map<String, Object> result = new HashMap<>();
        
        try (Connection connection = dataSource.getConnection()) {
            // 创建客服会话表
            String createSessionsTable = """
                CREATE TABLE IF NOT EXISTS `customer_service_sessions` (
                  `id` bigint(20) NOT NULL AUTO_INCREMENT,
                  `session_id` varchar(100) NOT NULL,
                  `open_id` varchar(100) NOT NULL,
                  `user_nickname` varchar(50) DEFAULT NULL,
                  `user_avatar` varchar(255) DEFAULT NULL,
                  `admin_id` bigint(20) DEFAULT NULL,
                  `admin_name` varchar(50) DEFAULT NULL,
                  `status` varchar(20) DEFAULT 'waiting',
                  `last_message_time` datetime DEFAULT NULL,
                  `last_message_content` text,
                  `unread_count_user` int(11) DEFAULT '0',
                  `unread_count_admin` int(11) DEFAULT '0',
                  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
                  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                  `closed_at` datetime DEFAULT NULL,
                  PRIMARY KEY (`id`),
                  UNIQUE KEY `idx_session_id` (`session_id`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
                """;
            
            // 创建消息表
            String createMessagesTable = """
                CREATE TABLE IF NOT EXISTS `customer_service_messages` (
                  `id` bigint(20) NOT NULL AUTO_INCREMENT,
                  `session_id` varchar(100) NOT NULL,
                  `sender_type` varchar(20) NOT NULL,
                  `sender_id` varchar(100) NOT NULL,
                  `sender_name` varchar(50) DEFAULT NULL,
                  `sender_avatar` varchar(255) DEFAULT NULL,
                  `message_type` varchar(20) DEFAULT 'text',
                  `content` text NOT NULL,
                  `extra_data` text,
                  `is_read` tinyint(1) DEFAULT '0',
                  `read_at` datetime DEFAULT NULL,
                  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
                  PRIMARY KEY (`id`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
                """;
            
            // 创建快捷回复表
            String createQuickRepliesTable = """
                CREATE TABLE IF NOT EXISTS `customer_service_quick_replies` (
                  `id` bigint(20) NOT NULL AUTO_INCREMENT,
                  `admin_id` bigint(20) DEFAULT NULL,
                  `category` varchar(50) DEFAULT NULL,
                  `title` varchar(100) NOT NULL,
                  `content` text NOT NULL,
                  `sort_order` int(11) DEFAULT '0',
                  `is_active` tinyint(1) DEFAULT '1',
                  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
                  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                  PRIMARY KEY (`id`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
                """;
            
            connection.createStatement().execute(createSessionsTable);
            connection.createStatement().execute(createMessagesTable);
            connection.createStatement().execute(createQuickRepliesTable);
            
            // 插入默认快捷回复
            String insertQuickReplies = """
                INSERT IGNORE INTO `customer_service_quick_replies` (`category`, `title`, `content`, `sort_order`) VALUES
                ('greeting', '欢迎语', '您好！欢迎使用成都桩郎中新能源技术有限公司客服服务，我是您的专属客服，有什么可以帮助您的吗？', 1),
                ('greeting', '稍等回复', '好的，请稍等，我马上为您查询处理。', 2),
                ('service', '维修服务', '我们提供专业的充电桩维修服务，包括上门维修和远程指导。您可以通过小程序提交维修申请，我们会安排专业工程师为您服务。', 3),
                ('common', '感谢语', '感谢您的咨询，如果还有其他问题，随时联系我们。祝您生活愉快！', 4)
                """;
            
            connection.createStatement().execute(insertQuickReplies);
            
            result.put("success", true);
            result.put("message", "客服系统表创建成功");
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("message", "表创建失败");
        }
        
        return result;
    }
}
