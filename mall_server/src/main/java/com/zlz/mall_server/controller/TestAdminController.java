package com.zlz.mall_server.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zlz.mall_server.model.AdminUser;
import com.zlz.mall_server.model.RepairOrder;
import com.zlz.mall_server.service.AdminUserService;
import com.zlz.mall_server.service.RepairOrderService;
import com.zlz.mall_server.util.PasswordUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.HashMap;
import java.util.Map;

@Controller
@RequestMapping("/test")
public class TestAdminController {

    @Autowired
    private AdminUserService adminUserService;

    @Autowired
    private RepairOrderService repairOrderService;

    /**
     * 测试页面
     */
    @GetMapping("/admin")
    @ResponseBody
    public Map<String, Object> testAdmin() {
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("message", "管理后台测试页面");
        result.put("info", "如果看到这个页面，说明基本配置正确");
        return result;
    }

    /**
     * 测试登录页面
     */
    @GetMapping("/login")
    public String testLogin() {
        return "admin/login";
    }

    /**
     * 测试密码加密
     */
    @GetMapping("/password")
    @ResponseBody
    public Map<String, Object> testPassword(@RequestParam(defaultValue = "admin123") String password) {
        Map<String, Object> result = new HashMap<>();

        // 生成新的密码哈希
        String newHash = PasswordUtil.encode(password);

        // 测试数据库中的哈希
        String dbHash = "$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXIGfkPNtmismLQdQIG.ZjUON4C";
        boolean dbHashValid = PasswordUtil.matches(password, dbHash);

        // 测试新生成的哈希
        boolean newHashValid = PasswordUtil.matches(password, newHash);

        result.put("password", password);
        result.put("newHash", newHash);
        result.put("newHashValid", newHashValid);
        result.put("dbHash", dbHash);
        result.put("dbHashValid", dbHashValid);

        return result;
    }

    /**
     * 测试登录功能
     */
    @GetMapping("/testLogin")
    @ResponseBody
    public Map<String, Object> testLoginFunction(@RequestParam(defaultValue = "admin") String username,
                                                @RequestParam(defaultValue = "admin123") String password) {
        Map<String, Object> result = new HashMap<>();

        try {
            AdminUser admin = adminUserService.login(username, password, "127.0.0.1");

            if (admin != null) {
                result.put("success", true);
                result.put("message", "登录成功");
                result.put("admin", admin);
            } else {
                result.put("success", false);
                result.put("message", "登录失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "登录异常: " + e.getMessage());
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 快速创建管理员账号
     */
    @GetMapping("/createAdmin")
    @ResponseBody
    public Map<String, Object> createAdmin(@RequestParam String username,
                                         @RequestParam(defaultValue = "admin123") String password,
                                         @RequestParam(defaultValue = "管理员") String realName,
                                         @RequestParam(defaultValue = "<EMAIL>") String email,
                                         @RequestParam(defaultValue = "13800000000") String phone,
                                         @RequestParam(defaultValue = "super_admin") String role) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 检查用户名是否已存在
            QueryWrapper<AdminUser> query = new QueryWrapper<>();
            query.eq("username", username);
            AdminUser existingAdmin = adminUserService.getOne(query);
            if (existingAdmin != null) {
                result.put("success", false);
                result.put("message", "用户名已存在");
                return result;
            }

            // 创建新管理员
            AdminUser newAdmin = new AdminUser();
            newAdmin.setUsername(username);
            newAdmin.setPassword(PasswordUtil.encode(password)); // 加密密码
            newAdmin.setRealName(realName);
            newAdmin.setEmail(email);
            newAdmin.setPhone(phone);
            newAdmin.setRole(role);

            // 设置权限
            if ("super_admin".equals(role)) {
                newAdmin.setPermissions("[\"user_management\", \"engineer_management\", \"service_center_management\", \"order_management\", \"review_management\", \"system_management\"]");
            } else if ("admin".equals(role)) {
                newAdmin.setPermissions("[\"engineer_management\", \"service_center_management\", \"order_management\", \"review_management\"]");
            } else {
                newAdmin.setPermissions("[\"order_management\", \"review_management\"]");
            }

            newAdmin.setStatus(1); // 启用状态
            newAdmin.setLoginCount(0);
            newAdmin.setCreatedAt(java.time.LocalDateTime.now());
            newAdmin.setUpdatedAt(java.time.LocalDateTime.now());
            newAdmin.setPasswordUpdatedAt(java.time.LocalDateTime.now());

            // 保存到数据库
            boolean saved = adminUserService.save(newAdmin);

            if (saved) {
                result.put("success", true);
                result.put("message", "管理员创建成功");
                result.put("data", Map.of(
                    "id", newAdmin.getId(),
                    "username", username,
                    "realName", realName,
                    "email", email,
                    "phone", phone,
                    "role", role,
                    "password", password
                ));
            } else {
                result.put("success", false);
                result.put("message", "管理员创建失败");
            }

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "创建管理员异常: " + e.getMessage());
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 重置管理员密码
     */
    @GetMapping("/resetPassword")
    @ResponseBody
    public Map<String, Object> resetPassword(@RequestParam(defaultValue = "admin") String username,
                                           @RequestParam(defaultValue = "admin123") String newPassword) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 生成新密码哈希
            String encodedPassword = PasswordUtil.encode(newPassword);

            // 查找管理员
            QueryWrapper<AdminUser> query = new QueryWrapper<>();
            query.eq("username", username);
            AdminUser admin = adminUserService.getOne(query);
            if (admin == null) {
                result.put("success", false);
                result.put("message", "用户不存在");
                return result;
            }

            // 更新密码
            admin.setPassword(encodedPassword);
            boolean updated = adminUserService.updateById(admin);

            if (updated) {
                result.put("success", true);
                result.put("message", "密码重置成功");
                result.put("username", username);
                result.put("newPassword", newPassword);
                result.put("encodedPassword", encodedPassword);
            } else {
                result.put("success", false);
                result.put("message", "密码更新失败");
            }

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "重置密码异常: " + e.getMessage());
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 检查数据库中的管理员数据
     */
    @GetMapping("/checkAdmin")
    @ResponseBody
    public Map<String, Object> checkAdmin(@RequestParam(defaultValue = "admin") String username) {
        Map<String, Object> result = new HashMap<>();

        try {
            QueryWrapper<AdminUser> query = new QueryWrapper<>();
            query.eq("username", username);
            AdminUser admin = adminUserService.getOne(query);

            if (admin != null) {
                result.put("success", true);
                result.put("message", "找到用户");
                result.put("username", admin.getUsername());
                result.put("realName", admin.getRealName());
                result.put("status", admin.getStatus());
                result.put("role", admin.getRole());
                result.put("passwordLength", admin.getPassword() != null ? admin.getPassword().length() : 0);
                result.put("passwordPrefix", admin.getPassword() != null ? admin.getPassword().substring(0, Math.min(10, admin.getPassword().length())) : "null");

                // 测试密码验证
                boolean passwordValid = PasswordUtil.matches("admin123", admin.getPassword());
                result.put("passwordValid", passwordValid);
            } else {
                result.put("success", false);
                result.put("message", "用户不存在");
            }

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "查询异常: " + e.getMessage());
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 测试订单服务
     */
    @GetMapping("/orders")
    @ResponseBody
    public Map<String, Object> testOrders() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 测试订单统计
            Map<String, Long> stats = repairOrderService.getStatusStatistics();

            // 测试订单查询
            long totalOrders = repairOrderService.count();

            result.put("success", true);
            result.put("message", "订单服务测试成功");
            result.put("totalOrders", totalOrders);
            result.put("statusStats", stats);

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "订单服务测试失败: " + e.getMessage());
            e.printStackTrace();
        }

        return result;
    }
}
