package com.zlz.mall_server.controller;

import com.zlz.mall_server.model.Engineer;
import com.zlz.mall_server.model.ServiceCenter;
import com.zlz.mall_server.service.EngineerService;
import com.zlz.mall_server.service.ServiceCenterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/applications")
@CrossOrigin(origins = "*")
public class ApplicationController {
    
    @Autowired
    private ServiceCenterService serviceCenterService;
    
    @Autowired
    private EngineerService engineerService;
    
    /**
     * 提交充电站入驻申请
     */
    @PostMapping("/station")
    public Map<String, Object> submitStationApplication(@RequestBody Map<String, Object> applicationData) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 创建服务网点对象
            ServiceCenter serviceCenter = new ServiceCenter();
            
            // 基本信息
            serviceCenter.setName((String) applicationData.get("name"));
            serviceCenter.setContactPerson((String) applicationData.get("contactPerson"));
            serviceCenter.setPhone((String) applicationData.get("phone"));
            serviceCenter.setEmail((String) applicationData.get("email"));
            serviceCenter.setAddress((String) applicationData.get("address"));
            serviceCenter.setProvince((String) applicationData.get("province"));
            serviceCenter.setCity((String) applicationData.get("city"));
            serviceCenter.setDistrict((String) applicationData.get("district"));
            
            // 位置信息
            if (applicationData.get("latitude") != null) {
                serviceCenter.setLatitude(new BigDecimal(applicationData.get("latitude").toString()));
            }
            if (applicationData.get("longitude") != null) {
                serviceCenter.setLongitude(new BigDecimal(applicationData.get("longitude").toString()));
            }
            
            // 营业信息
            serviceCenter.setBusinessHours((String) applicationData.get("businessHours"));
            serviceCenter.setBusinessDays("周一至周日"); // 默认值
            serviceCenter.setIs24Hours(false); // 默认值
            
            // 服务信息
            serviceCenter.setServiceTypes((String) applicationData.get("serviceTypes"));
            serviceCenter.setEquipmentTypes((String) applicationData.get("equipmentTypes"));
            serviceCenter.setServiceDescription((String) applicationData.get("serviceDescription"));
            serviceCenter.setFacilities((String) applicationData.get("facilities"));
            
            // 费用信息
            if (applicationData.get("serviceFee") != null) {
                serviceCenter.setServiceFee(new BigDecimal(applicationData.get("serviceFee").toString()));
            }
            if (applicationData.get("inspectionFee") != null) {
                serviceCenter.setInspectionFee(new BigDecimal(applicationData.get("inspectionFee").toString()));
            }
            serviceCenter.setParkingInfo((String) applicationData.get("parkingInfo"));
            
            // 状态管理
            serviceCenter.setStatus("pending"); // 待审核
            serviceCenter.setIsActive(false); // 未激活
            serviceCenter.setIsFeatured(false); // 非推荐
            
            // 审核信息
            serviceCenter.setApplicationTime(LocalDateTime.now());
            
            // 资质信息
            serviceCenter.setBusinessLicense((String) applicationData.get("businessLicense"));
            serviceCenter.setQualificationCertificates((String) applicationData.get("qualificationCertificates"));
            serviceCenter.setImages((String) applicationData.get("images"));
            
            // 系统字段
            serviceCenter.setSortOrder(0);
            serviceCenter.setCreatedAt(LocalDateTime.now());
            serviceCenter.setUpdatedAt(LocalDateTime.now());
            
            // 默认评价信息
            serviceCenter.setRating(new BigDecimal("5.00"));
            serviceCenter.setReviewCount(0);
            serviceCenter.setTotalServices(0);
            
            // 保存到数据库
            serviceCenterService.save(serviceCenter);
            
            result.put("success", true);
            result.put("data", Map.of("applicationId", serviceCenter.getId()));
            result.put("message", "充电站入驻申请提交成功，我们将在3-5个工作日内完成审核");
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "申请提交失败：" + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 提交工程师入驻申请
     */
    @PostMapping("/engineer")
    public Map<String, Object> submitEngineerApplication(@RequestBody Map<String, Object> applicationData) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 创建工程师对象
            Engineer engineer = new Engineer();
            
            // 基本信息
            engineer.setName((String) applicationData.get("name"));
            engineer.setPhone((String) applicationData.get("phone"));
            engineer.setEmail((String) applicationData.get("email"));
            engineer.setGender((String) applicationData.get("gender"));
            if (applicationData.get("age") != null) {
                engineer.setAge(Integer.parseInt(applicationData.get("age").toString()));
            }
            engineer.setIdCard((String) applicationData.get("idCard"));
            
            // 专业信息
            engineer.setSpecialties((String) applicationData.get("specialties"));
            if (applicationData.get("experienceYears") != null) {
                engineer.setExperienceYears(Integer.parseInt(applicationData.get("experienceYears").toString()));
            }
            engineer.setEducation((String) applicationData.get("education"));
            engineer.setCertifications((String) applicationData.get("certifications"));
            engineer.setSkills((String) applicationData.get("skills"));
            
            // 工作信息
            engineer.setWorkAreas((String) applicationData.get("workAreas"));
            engineer.setWorkTime((String) applicationData.get("workTime"));
            if (applicationData.get("hourlyRate") != null) {
                engineer.setHourlyRate(new BigDecimal(applicationData.get("hourlyRate").toString()));
            }
            if (applicationData.get("serviceFee") != null) {
                engineer.setServiceFee(new BigDecimal(applicationData.get("serviceFee").toString()));
            }
            
            // 个人介绍
            engineer.setBio((String) applicationData.get("bio"));
            engineer.setIntroduction((String) applicationData.get("introduction"));
            engineer.setWorkPhotos((String) applicationData.get("workPhotos"));
            
            // 状态管理
            engineer.setStatus("pending"); // 待审核
            engineer.setIsOnline(false); // 离线
            engineer.setIsAvailable(false); // 不可接单
            
            // 审核信息
            engineer.setApplicationTime(LocalDateTime.now());
            
            // 系统字段
            engineer.setSortOrder(0);
            engineer.setCreatedAt(LocalDateTime.now());
            engineer.setUpdatedAt(LocalDateTime.now());
            
            // 默认评价信息
            engineer.setRating(new BigDecimal("5.00"));
            engineer.setTotalOrders(0);
            engineer.setCompletedOrders(0);
            engineer.setSuccessRate(new BigDecimal("100.00"));
            
            // 保存到数据库
            engineerService.save(engineer);
            
            result.put("success", true);
            result.put("data", Map.of("applicationId", engineer.getId()));
            result.put("message", "工程师入驻申请提交成功，我们将在3-5个工作日内完成审核");
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "申请提交失败：" + e.getMessage());
        }
        
        return result;
    }
}
