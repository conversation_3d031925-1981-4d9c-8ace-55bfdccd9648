package com.zlz.mall_server.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zlz.mall_server.model.*;
import com.zlz.mall_server.service.*;
import com.zlz.mall_server.model.RepairOrder;
import com.zlz.mall_server.util.StatusUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpSession;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping("/admin")
public class AdminController {

    @Autowired
    private AdminUserService adminUserService;

    @Autowired
    private EngineerService engineerService;

    @Autowired
    private ServiceCenterService serviceCenterService;

    @Autowired
    private RepairOrderService repairOrderService;

    @Autowired
    private ProductService productService;

    /**
     * 管理后台首页
     */
    @GetMapping({"", "/", "/index"})
    public String index(HttpServletRequest request, Model model) {
        HttpSession session = request.getSession(false);
        if (session == null) {
            return "redirect:/admin/login";
        }

        AdminUser admin = (AdminUser) session.getAttribute("admin");
        if (admin == null) {
            return "redirect:/admin/login";
        }

        // 获取统计数据
        Map<String, Object> stats = new HashMap<>();

        // 工程师统计
        QueryWrapper<Engineer> engineerQuery = new QueryWrapper<>();
        stats.put("totalEngineers", engineerService.count());
        engineerQuery.eq("status", "pending");
        stats.put("pendingEngineers", engineerService.count(engineerQuery));
        engineerQuery.clear();
        engineerQuery.eq("status", "approved");
        stats.put("approvedEngineers", engineerService.count(engineerQuery));

        // 服务网点统计
        QueryWrapper<ServiceCenter> centerQuery = new QueryWrapper<>();
        stats.put("totalServiceCenters", serviceCenterService.count());
        centerQuery.eq("status", "pending");
        stats.put("pendingServiceCenters", serviceCenterService.count(centerQuery));
        centerQuery.clear();
        centerQuery.eq("status", "approved");
        stats.put("approvedServiceCenters", serviceCenterService.count(centerQuery));

        // 维修订单统计
        QueryWrapper<RepairOrder> orderQuery = new QueryWrapper<>();
        stats.put("totalOrders", repairOrderService.count());
        orderQuery.eq("status", "pending");
        stats.put("pendingOrders", repairOrderService.count(orderQuery));
        orderQuery.clear();
        orderQuery.eq("status", "processing");
        stats.put("processingOrders", repairOrderService.count(orderQuery));
        orderQuery.clear();
        orderQuery.eq("status", "completed");
        stats.put("completedOrders", repairOrderService.count(orderQuery));

        model.addAttribute("admin", admin);
        model.addAttribute("stats", stats);

        return "admin/index";
    }

    /**
     * 登录页面
     */
    @GetMapping("/login")
    public String loginPage(HttpServletRequest request) {
        HttpSession session = request.getSession(false);
        if (session != null) {
            AdminUser admin = (AdminUser) session.getAttribute("admin");
            if (admin != null) {
                return "redirect:/admin/index";
            }
        }
        return "admin/login";
    }

    /**
     * 处理登录
     */
    @PostMapping("/login")
    @ResponseBody
    public Map<String, Object> login(@RequestParam String username,
                                   @RequestParam String password,
                                   HttpServletRequest request) {
        Map<String, Object> result = new HashMap<>();

        try {
            String ip = getClientIpAddress(request);
            AdminUser admin = adminUserService.login(username, password, ip);

            if (admin != null) {
                HttpSession session = request.getSession();
                session.setAttribute("admin", admin);
                result.put("success", true);
                result.put("message", "登录成功");
            } else {
                result.put("success", false);
                result.put("message", "用户名或密码错误");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "登录失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 退出登录
     */
    @GetMapping("/logout")
    public String logout(HttpServletRequest request) {
        HttpSession session = request.getSession(false);
        if (session != null) {
            session.removeAttribute("admin");
            session.invalidate();
        }
        return "redirect:/admin/login";
    }

    /**
     * 工程师管理页面
     */
    @GetMapping("/engineers")
    public String engineers(HttpServletRequest request, Model model,
                          @RequestParam(defaultValue = "1") int page,
                          @RequestParam(defaultValue = "10") int size,
                          @RequestParam(defaultValue = "") String status) {
        HttpSession session = request.getSession(false);
        if (session == null) {
            return "redirect:/admin/login";
        }

        AdminUser admin = (AdminUser) session.getAttribute("admin");
        if (admin == null) {
            return "redirect:/admin/login";
        }

        // 分页查询工程师
        Page<Engineer> pageObj = new Page<>(page, size);
        QueryWrapper<Engineer> query = new QueryWrapper<>();
        if (!status.isEmpty()) {
            query.eq("status", status);
        }
        query.orderByDesc("created_at");

        IPage<Engineer> engineerPage = engineerService.page(pageObj, query);

        model.addAttribute("admin", admin);
        model.addAttribute("engineerPage", engineerPage);
        model.addAttribute("currentStatus", status);

        return "admin/engineers";
    }

    /**
     * 添加工程师页面
     */
    @GetMapping("/add-engineer")
    public String addEngineer(HttpServletRequest request) {
        HttpSession session = request.getSession(false);
        if (session == null) {
            return "redirect:/admin/login";
        }

        AdminUser admin = (AdminUser) session.getAttribute("admin");
        if (admin == null) {
            return "redirect:/admin/login";
        }

        return "admin/add-engineer";
    }

    /**
     * 服务网点管理页面
     */
    @GetMapping("/service-centers")
    public String serviceCenters(HttpServletRequest request, Model model,
                                @RequestParam(defaultValue = "1") int page,
                                @RequestParam(defaultValue = "10") int size,
                                @RequestParam(defaultValue = "") String status) {
        HttpSession session = request.getSession(false);
        if (session == null) {
            return "redirect:/admin/login";
        }

        AdminUser admin = (AdminUser) session.getAttribute("admin");
        if (admin == null) {
            return "redirect:/admin/login";
        }

        // 分页查询服务网点
        Page<ServiceCenter> pageObj = new Page<>(page, size);
        QueryWrapper<ServiceCenter> query = new QueryWrapper<>();
        if (!status.isEmpty()) {
            query.eq("status", status);
        }
        query.orderByDesc("created_at");

        IPage<ServiceCenter> centerPage = serviceCenterService.page(pageObj, query);

        model.addAttribute("admin", admin);
        model.addAttribute("serviceCenterPage", centerPage);
        model.addAttribute("currentStatus", status);

        return "admin/service-centers";
    }

    /**
     * 产品管理页面
     */
    @GetMapping("/products")
    public String products(HttpServletRequest request, Model model,
                          @RequestParam(defaultValue = "1") int page,
                          @RequestParam(defaultValue = "10") int size,
                          @RequestParam(defaultValue = "") String status) {
        HttpSession session = request.getSession(false);
        if (session == null) {
            return "redirect:/admin/login";
        }

        AdminUser admin = (AdminUser) session.getAttribute("admin");
        if (admin == null) {
            return "redirect:/admin/login";
        }

        // 分页查询产品
        Page<Products> pageObj = new Page<>(page, size);
        QueryWrapper<Products> query = new QueryWrapper<>();
        if (!status.isEmpty()) {
            query.eq("status", Integer.parseInt(status));
        }
        query.orderByDesc("created_at");

        IPage<Products> productPage = productService.page(pageObj, query);

        model.addAttribute("admin", admin);
        model.addAttribute("productPage", productPage);
        model.addAttribute("currentStatus", status);

        return "admin/products";
    }

    /**
     * 维修订单管理页面
     */
    @GetMapping("/orders")
    public String orders(HttpServletRequest request, Model model,
                        @RequestParam(defaultValue = "1") int page,
                        @RequestParam(defaultValue = "10") int size,
                        @RequestParam(defaultValue = "") String status) {
        HttpSession session = request.getSession(false);
        if (session == null) {
            return "redirect:/admin/login";
        }

        AdminUser admin = (AdminUser) session.getAttribute("admin");
        if (admin == null) {
            return "redirect:/admin/login";
        }

        // 分页查询维修订单
        Page<RepairOrder> pageObj = new Page<>(page, size);
        QueryWrapper<RepairOrder> query = new QueryWrapper<>();
        if (!status.isEmpty()) {
            query.eq("status", status);
        }
        query.orderByDesc("created_at");

        IPage<RepairOrder> orderPage = repairOrderService.page(pageObj, query);

        model.addAttribute("admin", admin);
        model.addAttribute("orderPage", orderPage);
        model.addAttribute("currentStatus", status);

        return "admin/orders";
    }

    /**
     * 图片上传测试页面
     */
    @GetMapping("/test-upload")
    public String testUpload() {
        return "test-upload";
    }

    /**
     * 图片显示测试页面
     */
    @GetMapping("/test-images")
    public String testImages() {
        return "test-images";
    }

    /**
     * 服务网点图片测试页面
     */
    @GetMapping("/test-station-images")
    public String testStationImages() {
        return "test-station-images";
    }

    /**
     * 小程序API测试页面
     */
    @GetMapping("/test-miniprogram-api")
    public String testMiniprogramApi() {
        return "test-miniprogram-api";
    }

    /**
     * 服务网点详情测试页面
     */
    @GetMapping("/test-service-center-detail")
    public String testServiceCenterDetail() {
        return "test-service-center-detail";
    }

    /**
     * 快速审核服务网点页面
     */
    @GetMapping("/quick-approve")
    public String quickApprove() {
        return "quick-approve";
    }

    /**
     * 服务网点申请页面图片预览优化演示
     */
    @GetMapping("/station-apply-preview-demo")
    public String stationApplyPreviewDemo() {
        return "station-apply-preview-demo";
    }

    /**
     * 商品图片显示测试页面
     */
    @GetMapping("/test-product-images")
    public String testProductImages() {
        return "test-product-images";
    }

    /**
     * 筛选按钮功能测试页面
     */
    @GetMapping("/test-filter-buttons")
    public String testFilterButtons() {
        return "test-filter-buttons";
    }

    /**
     * 模态框修复测试页面
     */
    @GetMapping("/test-modal-fix")
    public String testModalFix() {
        return "test-modal-fix";
    }

    /**
     * 商品详情页优化测试页面
     */
    @GetMapping("/test-product-detail-optimization")
    public String testProductDetailOptimization() {
        return "test-product-detail-optimization";
    }

    /**
     * 产品添加功能测试页面
     */
    @GetMapping("/test-product-add-fix")
    public String testProductAddFix() {
        return "test-product-add-fix";
    }

    /**
     * 工程师服务次数增加测试页面
     */
    @GetMapping("/test-engineer-increment")
    public String testEngineerIncrement() {
        return "test-engineer-increment";
    }

    /**
     * 工程师服务次数自增功能测试页面
     */
    @GetMapping("/test-engineer-service-count")
    public String testEngineerServiceCount() {
        return "test-engineer-service-count";
    }

    /**
     * 产品编辑功能测试页面
     */
    @GetMapping("/test-product-edit")
    public String testProductEdit() {
        return "test-product-edit";
    }

    /**
     * 热门产品API测试页面
     */
    @GetMapping("/test-hot-products")
    public String testHotProducts() {
        return "test-hot-products";
    }

    /**
     * 热门产品限制测试页面
     */
    @GetMapping("/test-limit-fix")
    public String testLimitFix() {
        return "test-limit-fix";
    }

    /**
     * 测试用的服务网点详情API（不需要登录）
     */
    @GetMapping("/api/test/service-center/{id}")
    @ResponseBody
    public Map<String, Object> getTestServiceCenterDetail(@PathVariable Long id) {
        Map<String, Object> result = new HashMap<>();
        try {
            // 创建测试数据
            Map<String, Object> serviceCenter = new HashMap<>();
            serviceCenter.put("id", id);
            serviceCenter.put("name", "测试充电站 " + id);
            serviceCenter.put("phone", "***********");
            serviceCenter.put("contactPerson", "张三");
            serviceCenter.put("email", "<EMAIL>");
            serviceCenter.put("address", "北京市朝阳区测试路123号");
            serviceCenter.put("businessHours", "08:00-22:00");
            serviceCenter.put("serviceDescription", "专业的充电桩维修和安装服务");
            serviceCenter.put("businessLicense", "/uploads/20250610_143914_aea5df47.png");
            serviceCenter.put("qualificationCertificates", "[\"/uploads/20250610_143914_aea5df47.png\", \"/uploads/20250610_143914_4a433ed2.png\"]");
            serviceCenter.put("images", "[\"/uploads/20250610_143914_aea5df47.png\", \"/uploads/20250610_143914_4a433ed2.png\"]");

            result.put("success", true);
            result.put("data", serviceCenter);
            result.put("message", "获取测试服务网点详情成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取测试服务网点详情失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0];
        }

        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }

        return request.getRemoteAddr();
    }
}
