package com.zlz.mall_server.controller;

import com.zlz.mall_server.model.UserAddress;
import com.zlz.mall_server.service.UserAddressService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/address")
@Slf4j
public class UserAddressController {

    @Autowired
    private UserAddressService userAddressService;

    /**
     * 获取用户地址列表
     * @param openId 用户openId
     * @return 地址列表
     */
    @GetMapping("/list")
    public ResponseEntity<Map<String, Object>> getAddressList(@RequestParam String openId) {
        log.info("获取用户地址列表: openId={}", openId);
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            List<UserAddress> addresses = userAddressService.findByOpenId(openId);
            
            result.put("success", true);
            result.put("addresses", addresses);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取用户地址列表异常", e);
            result.put("success", false);
            result.put("message", "获取地址列表失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(result);
        }
    }
    
    /**
     * 获取用户默认地址
     * @param openId 用户openId
     * @return 默认地址
     */
    @GetMapping("/default")
    public ResponseEntity<Map<String, Object>> getDefaultAddress(@RequestParam String openId) {
        log.info("获取用户默认地址: openId={}", openId);
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            UserAddress address = userAddressService.findDefaultByOpenId(openId);
            
            result.put("success", true);
            result.put("address", address);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取用户默认地址异常", e);
            result.put("success", false);
            result.put("message", "获取默认地址失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(result);
        }
    }
    
    /**
     * 保存地址
     * @param address 地址信息
     * @return 保存结果
     */
    @PostMapping("/save")
    public ResponseEntity<Map<String, Object>> saveAddress(@RequestBody UserAddress address) {
        log.info("保存地址: {}", address);
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 验证必要参数
            if (address.getOpenId() == null || address.getOpenId().isEmpty()) {
                result.put("success", false);
                result.put("message", "保存地址失败: openId不能为空");
                return ResponseEntity.badRequest().body(result);
            }
            
            if (address.getName() == null || address.getName().isEmpty()) {
                result.put("success", false);
                result.put("message", "保存地址失败: 收货人姓名不能为空");
                return ResponseEntity.badRequest().body(result);
            }
            
            if (address.getPhone() == null || address.getPhone().isEmpty()) {
                result.put("success", false);
                result.put("message", "保存地址失败: 收货人手机号不能为空");
                return ResponseEntity.badRequest().body(result);
            }
            
            if (address.getProvince() == null || address.getProvince().isEmpty() ||
                address.getCity() == null || address.getCity().isEmpty() ||
                address.getDistrict() == null || address.getDistrict().isEmpty()) {
                result.put("success", false);
                result.put("message", "保存地址失败: 所在地区不能为空");
                return ResponseEntity.badRequest().body(result);
            }
            
            if (address.getAddress() == null || address.getAddress().isEmpty()) {
                result.put("success", false);
                result.put("message", "保存地址失败: 详细地址不能为空");
                return ResponseEntity.badRequest().body(result);
            }
            
            // 保存地址
            UserAddress savedAddress = userAddressService.saveAddress(address);
            
            result.put("success", true);
            result.put("message", address.getId() == null ? "添加成功" : "修改成功");
            result.put("address", savedAddress);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("保存地址异常", e);
            result.put("success", false);
            result.put("message", "保存地址失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(result);
        }
    }
    
    /**
     * 删除地址
     * @param id 地址ID
     * @param openId 用户openId
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Map<String, Object>> deleteAddress(@PathVariable Long id, @RequestParam String openId) {
        log.info("删除地址: id={}, openId={}", id, openId);
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            boolean success = userAddressService.deleteAddress(id, openId);
            
            if (success) {
                result.put("success", true);
                result.put("message", "删除成功");
                return ResponseEntity.ok(result);
            } else {
                result.put("success", false);
                result.put("message", "删除失败: 地址不存在或无权限删除");
                return ResponseEntity.badRequest().body(result);
            }
        } catch (Exception e) {
            log.error("删除地址异常", e);
            result.put("success", false);
            result.put("message", "删除地址失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(result);
        }
    }
    
    /**
     * 设置默认地址
     * @param id 地址ID
     * @param openId 用户openId
     * @return 设置结果
     */
    @PostMapping("/{id}/default")
    public ResponseEntity<Map<String, Object>> setDefaultAddress(@PathVariable Long id, @RequestParam String openId) {
        log.info("设置默认地址: id={}, openId={}", id, openId);
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 查询地址是否存在且属于该用户
            UserAddress address = userAddressService.getById(id);
            
            if (address == null || !address.getOpenId().equals(openId)) {
                result.put("success", false);
                result.put("message", "设置失败: 地址不存在或无权限设置");
                return ResponseEntity.badRequest().body(result);
            }
            
            // 设置为默认地址
            address.setIsDefault(true);
            userAddressService.saveAddress(address);
            
            result.put("success", true);
            result.put("message", "设置成功");
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("设置默认地址异常", e);
            result.put("success", false);
            result.put("message", "设置默认地址失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(result);
        }
    }
}
