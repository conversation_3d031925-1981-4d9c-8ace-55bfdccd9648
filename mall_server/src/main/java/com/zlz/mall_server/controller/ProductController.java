package com.zlz.mall_server.controller;

import com.zlz.mall_server.model.ProductCategory;
import com.zlz.mall_server.model.ProductReview;
import com.zlz.mall_server.model.Products;
import com.zlz.mall_server.service.ProductCategoryService;
import com.zlz.mall_server.service.ProductReviewService;
import com.zlz.mall_server.service.ProductService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/products")
@CrossOrigin(origins = "*")
public class ProductController {
    
    @Autowired
    private ProductService productService;
    
    @Autowired
    private ProductCategoryService categoryService;
    
    @Autowired
    private ProductReviewService reviewService;
    
    /**
     * 获取商品分类列表
     */
    @GetMapping("/categories")
    public Map<String, Object> getCategories() {
        Map<String, Object> result = new HashMap<>();
        try {
            List<ProductCategory> categories = categoryService.getAllEnabledCategories();
            result.put("success", true);
            result.put("data", categories);
            result.put("message", "获取分类列表成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取分类列表失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 获取商品列表
     */
    @GetMapping("/list")
    public Map<String, Object> getProductList(
            @RequestParam(value = "categoryId", defaultValue = "0") Integer categoryId,
            @RequestParam(value = "sortType", defaultValue = "default") String sortType,
            @RequestParam(value = "priceOrder", defaultValue = "desc") String priceOrder,
            @RequestParam(value = "limit", defaultValue = "0") Integer limit,
            jakarta.servlet.http.HttpServletRequest request) {

        Map<String, Object> result = new HashMap<>();
        try {
            System.out.println("🔍 获取商品列表 - categoryId: " + categoryId + ", sortType: " + sortType + ", limit: " + limit);

            List<Products> products;
            if (limit > 0) {
                // 如果有限制数量，使用带limit的方法
                System.out.println("📊 使用限制查询，limit: " + limit);
                products = productService.getProductsByCategoryIdWithSortAndLimit(categoryId, sortType, priceOrder, limit);
                System.out.println("✅ 限制查询结果数量: " + (products != null ? products.size() : 0));
            } else if ("default".equals(sortType)) {
                System.out.println("📊 使用默认查询");
                products = productService.getProductsByCategoryId(categoryId);
            } else {
                System.out.println("📊 使用排序查询");
                products = productService.getProductsByCategoryIdWithSort(categoryId, sortType, priceOrder);
            }

            // 处理图片URL，转换为完整的HTTPS URL
            String baseUrl = getBaseUrl(request);
            products.forEach(product -> {
                if (product.getMainImage() != null && !product.getMainImage().startsWith("http")) {
                    product.setMainImage(baseUrl + product.getMainImage());
                }
                if (product.getDetailImage() != null && !product.getDetailImage().startsWith("http")) {
                    product.setDetailImage(baseUrl + product.getDetailImage());
                }
                // 处理多张图片
                if (product.getImages() != null && !product.getImages().isEmpty()) {
                    String[] imageUrls = product.getImages().split(",");
                    StringBuilder fullImageUrls = new StringBuilder();
                    for (int i = 0; i < imageUrls.length; i++) {
                        String imageUrl = imageUrls[i].trim();
                        if (!imageUrl.startsWith("http")) {
                            imageUrl = baseUrl + imageUrl;
                        }
                        fullImageUrls.append(imageUrl);
                        if (i < imageUrls.length - 1) {
                            fullImageUrls.append(",");
                        }
                    }
                    product.setImages(fullImageUrls.toString());
                }
            });

            result.put("success", true);
            result.put("data", products);
            result.put("message", "获取商品列表成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取商品列表失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 获取商品详情
     */
    @GetMapping("/detail/{id}")
    public Map<String, Object> getProductDetail(@PathVariable Long id,
                                               jakarta.servlet.http.HttpServletRequest request) {
        Map<String, Object> result = new HashMap<>();
        try {
            Products product = productService.getProductDetail(id);
            if (product == null) {
                result.put("success", false);
                result.put("message", "商品不存在");
                return result;
            }

            // 处理图片URL，转换为完整的HTTPS URL
            String baseUrl = getBaseUrl(request);
            if (product.getMainImage() != null && !product.getMainImage().startsWith("http")) {
                product.setMainImage(baseUrl + product.getMainImage());
            }
            if (product.getDetailImage() != null && !product.getDetailImage().startsWith("http")) {
                product.setDetailImage(baseUrl + product.getDetailImage());
            }
            // 处理多张图片
            if (product.getImages() != null && !product.getImages().isEmpty()) {
                String[] imageUrls = product.getImages().split(",");
                StringBuilder fullImageUrls = new StringBuilder();
                for (int i = 0; i < imageUrls.length; i++) {
                    String imageUrl = imageUrls[i].trim();
                    if (!imageUrl.startsWith("http")) {
                        imageUrl = baseUrl + imageUrl;
                    }
                    fullImageUrls.append(imageUrl);
                    if (i < imageUrls.length - 1) {
                        fullImageUrls.append(",");
                    }
                }
                product.setImages(fullImageUrls.toString());
            }

            // 获取商品评价
            List<ProductReview> reviews = reviewService.getReviewsByProductId(id);
            Integer reviewCount = reviewService.getReviewCountByProductId(id);
            Double avgRating = reviewService.getAverageRatingByProductId(id);

            Map<String, Object> productData = new HashMap<>();
            productData.put("product", product);
            productData.put("reviews", reviews);
            productData.put("reviewCount", reviewCount);
            productData.put("avgRating", avgRating);

            result.put("success", true);
            result.put("data", productData);
            result.put("message", "获取商品详情成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取商品详情失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 获取商品评价列表
     */
    @GetMapping("/{id}/reviews")
    public Map<String, Object> getProductReviews(@PathVariable Long id) {
        Map<String, Object> result = new HashMap<>();
        try {
            List<ProductReview> reviews = reviewService.getReviewsByProductId(id);
            Integer reviewCount = reviewService.getReviewCountByProductId(id);
            Double avgRating = reviewService.getAverageRatingByProductId(id);
            
            Map<String, Object> reviewData = new HashMap<>();
            reviewData.put("reviews", reviews);
            reviewData.put("reviewCount", reviewCount);
            reviewData.put("avgRating", avgRating);
            
            result.put("success", true);
            result.put("data", reviewData);
            result.put("message", "获取评价列表成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取评价列表失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 获取基础URL
     */
    private String getBaseUrl(jakarta.servlet.http.HttpServletRequest request) {
        String scheme = request.getScheme();
        String serverName = request.getServerName();
        int serverPort = request.getServerPort();

        // 构建基础URL
        StringBuilder baseUrl = new StringBuilder();
        baseUrl.append(scheme).append("://").append(serverName);

        // 只有在非标准端口时才添加端口号
        if ((scheme.equals("http") && serverPort != 80) ||
            (scheme.equals("https") && serverPort != 443)) {
            baseUrl.append(":").append(serverPort);
        }

        return baseUrl.toString();
    }
}
