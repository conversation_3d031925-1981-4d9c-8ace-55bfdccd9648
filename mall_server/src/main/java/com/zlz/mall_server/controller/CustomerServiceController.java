package com.zlz.mall_server.controller;

import com.zlz.mall_server.model.CustomerServiceSession;
import com.zlz.mall_server.model.CustomerServiceMessage;
import com.zlz.mall_server.model.User;
import com.zlz.mall_server.service.CustomerServiceService;
import com.zlz.mall_server.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/customer-service")
public class CustomerServiceController {
    
    @Autowired
    private CustomerServiceService customerServiceService;
    
    @Autowired
    private UserService userService;
    
    /**
     * 获取或创建客服会话
     */
    @PostMapping("/session")
    public Map<String, Object> getOrCreateSession(@RequestBody Map<String, String> request) {
        Map<String, Object> result = new HashMap<>();
        try {
            String openId = request.get("openId");
            
            if (openId == null || openId.trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "openId不能为空");
                return result;
            }
            
            CustomerServiceSession session = customerServiceService.getOrCreateSession(openId);
            
            result.put("success", true);
            result.put("data", session);
            result.put("message", "获取会话成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取会话失败：" + e.getMessage());
            e.printStackTrace();
        }
        return result;
    }
    
    /**
     * 发送消息
     */
    @PostMapping("/message")
    public Map<String, Object> sendMessage(@RequestBody Map<String, String> request) {
        Map<String, Object> result = new HashMap<>();
        try {
            String sessionId = request.get("sessionId");
            String openId = request.get("openId");
            String content = request.get("content");
            
            if (sessionId == null || content == null || content.trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "参数不完整");
                return result;
            }
            
            // 获取用户信息
            User user = userService.findByOpenId(openId);
            String senderName = user != null ? user.getNickname() : "用户";
            String senderAvatar = user != null ? user.getAvatarUrl() : null;
            
            CustomerServiceMessage message = customerServiceService.sendMessage(
                sessionId, "user", openId, senderName, senderAvatar, content);
            
            result.put("success", true);
            result.put("data", message);
            result.put("message", "发送成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "发送失败：" + e.getMessage());
            e.printStackTrace();
        }
        return result;
    }
    
    /**
     * 获取会话消息列表
     */
    @GetMapping("/messages/{sessionId}")
    public Map<String, Object> getMessages(@PathVariable String sessionId) {
        Map<String, Object> result = new HashMap<>();
        try {
            List<CustomerServiceMessage> messages = customerServiceService.getSessionMessages(sessionId);
            
            result.put("success", true);
            result.put("data", messages);
            result.put("message", "获取消息成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取消息失败：" + e.getMessage());
            e.printStackTrace();
        }
        return result;
    }
    
    /**
     * 标记消息为已读
     */
    @PostMapping("/read")
    public Map<String, Object> markAsRead(@RequestBody Map<String, String> request) {
        Map<String, Object> result = new HashMap<>();
        try {
            String sessionId = request.get("sessionId");

            if (sessionId == null) {
                result.put("success", false);
                result.put("message", "sessionId不能为空");
                return result;
            }

            customerServiceService.markMessagesAsRead(sessionId, "user");

            result.put("success", true);
            result.put("message", "标记已读成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "标记已读失败：" + e.getMessage());
            e.printStackTrace();
        }
        return result;
    }

    /**
     * 获取会话状态
     */
    @GetMapping("/status/{sessionId}")
    public Map<String, Object> getSessionStatus(@PathVariable String sessionId) {
        Map<String, Object> result = new HashMap<>();
        try {
            CustomerServiceSession session = customerServiceService.getSessionById(sessionId);

            if (session != null) {
                result.put("success", true);
                result.put("data", Map.of(
                    "status", session.getStatus(),
                    "adminName", session.getAdminName(),
                    "lastMessageTime", session.getLastMessageTime()
                ));
                result.put("message", "获取成功");
            } else {
                result.put("success", false);
                result.put("message", "会话不存在");
            }

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取失败：" + e.getMessage());
            e.printStackTrace();
        }
        return result;
    }
}
