package com.zlz.mall_server.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.*;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/init")
@CrossOrigin(origins = "*")
public class InitController {
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    /**
     * 初始化数据库表和数据
     */
    @PostMapping("/database")
    public Map<String, Object> initDatabase() {
        Map<String, Object> result = new HashMap<>();
        try {
            // 读取SQL文件
            ClassPathResource resource = new ClassPathResource("db/init_all.sql");
            StringBuilder sqlBuilder = new StringBuilder();
            
            try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(resource.getInputStream(), StandardCharsets.UTF_8))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    // 跳过注释行和空行
                    if (!line.trim().startsWith("--") && !line.trim().isEmpty()) {
                        sqlBuilder.append(line).append("\n");
                    }
                }
            }
            
            // 分割SQL语句（以分号分隔）
            String[] sqlStatements = sqlBuilder.toString().split(";");
            
            int executedCount = 0;
            for (String sql : sqlStatements) {
                sql = sql.trim();
                if (!sql.isEmpty()) {
                    try {
                        jdbcTemplate.execute(sql);
                        executedCount++;
                    } catch (Exception e) {
                        // 如果是表已存在的错误，忽略
                        if (!e.getMessage().contains("already exists") && 
                            !e.getMessage().contains("Duplicate entry")) {
                            throw e;
                        }
                    }
                }
            }
            
            result.put("success", true);
            result.put("message", "数据库初始化成功，执行了 " + executedCount + " 条SQL语句");
            result.put("executedCount", executedCount);
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "数据库初始化失败：" + e.getMessage());
            e.printStackTrace();
        }
        return result;
    }
    
    /**
     * 检查数据库状态
     */
    @GetMapping("/status")
    public Map<String, Object> checkStatus() {
        Map<String, Object> result = new HashMap<>();
        try {
            // 检查表是否存在
            int categoryCount = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'mall' AND table_name = 'product_categories'", 
                Integer.class);
            int productCount = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'mall' AND table_name = 'products'", 
                Integer.class);
            int reviewCount = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'mall' AND table_name = 'product_reviews'", 
                Integer.class);
            
            boolean tablesExist = categoryCount > 0 && productCount > 0 && reviewCount > 0;
            
            Map<String, Object> status = new HashMap<>();
            status.put("tablesExist", tablesExist);
            status.put("categoryTableExists", categoryCount > 0);
            status.put("productTableExists", productCount > 0);
            status.put("reviewTableExists", reviewCount > 0);
            
            if (tablesExist) {
                // 检查数据
                int categoryDataCount = jdbcTemplate.queryForObject("SELECT COUNT(*) FROM product_categories", Integer.class);
                int productDataCount = jdbcTemplate.queryForObject("SELECT COUNT(*) FROM products", Integer.class);
                int reviewDataCount = jdbcTemplate.queryForObject("SELECT COUNT(*) FROM product_reviews", Integer.class);
                
                status.put("categoryDataCount", categoryDataCount);
                status.put("productDataCount", productDataCount);
                status.put("reviewDataCount", reviewDataCount);
            }
            
            result.put("success", true);
            result.put("data", status);
            result.put("message", "状态检查完成");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "状态检查失败：" + e.getMessage());
        }
        return result;
    }
}
