package com.zlz.mall_server.controller;

import com.zlz.mall_server.mapper.ServiceCenterMapper.ServiceCenterStats;
import com.zlz.mall_server.model.ServiceCenter;
import com.zlz.mall_server.service.ServiceCenterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/service-centers")
@CrossOrigin(origins = "*")
public class ServiceCenterController {
    
    @Autowired
    private ServiceCenterService serviceCenterService;
    
    /**
     * 获取所有已审核通过的服务网点列表
     */
    @GetMapping("/approved")
    public Map<String, Object> getApprovedServiceCenters() {
        Map<String, Object> result = new HashMap<>();
        try {
            List<ServiceCenter> serviceCenters = serviceCenterService.getApprovedServiceCenters();
            
            result.put("success", true);
            result.put("data", serviceCenters);
            result.put("message", "获取服务网点列表成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取服务网点列表失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 获取推荐的服务网点列表
     */
    @GetMapping("/featured")
    public Map<String, Object> getFeaturedServiceCenters() {
        Map<String, Object> result = new HashMap<>();
        try {
            List<ServiceCenter> serviceCenters = serviceCenterService.getFeaturedServiceCenters();
            
            result.put("success", true);
            result.put("data", serviceCenters);
            result.put("message", "获取推荐服务网点列表成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取推荐服务网点列表失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 根据城市查询服务网点
     */
    @GetMapping("/city/{city}")
    public Map<String, Object> getServiceCentersByCity(@PathVariable String city) {
        Map<String, Object> result = new HashMap<>();
        try {
            List<ServiceCenter> serviceCenters = serviceCenterService.getServiceCentersByCity(city);
            
            result.put("success", true);
            result.put("data", serviceCenters);
            result.put("message", "获取城市服务网点列表成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取城市服务网点列表失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 根据服务类型查询服务网点
     */
    @GetMapping("/service-type/{serviceType}")
    public Map<String, Object> getServiceCentersByServiceType(@PathVariable String serviceType) {
        Map<String, Object> result = new HashMap<>();
        try {
            List<ServiceCenter> serviceCenters = serviceCenterService.getServiceCentersByServiceType(serviceType);
            
            result.put("success", true);
            result.put("data", serviceCenters);
            result.put("message", "获取专业服务网点列表成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取专业服务网点列表失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 搜索服务网点
     */
    @GetMapping("/search")
    public Map<String, Object> searchServiceCenters(@RequestParam String keyword) {
        Map<String, Object> result = new HashMap<>();
        try {
            List<ServiceCenter> serviceCenters = serviceCenterService.searchServiceCenters(keyword);
            
            result.put("success", true);
            result.put("data", serviceCenters);
            result.put("message", "搜索服务网点成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "搜索服务网点失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 根据位置范围查询服务网点
     */
    @GetMapping("/location")
    public Map<String, Object> getServiceCentersByLocation(
            @RequestParam BigDecimal minLat,
            @RequestParam BigDecimal maxLat,
            @RequestParam BigDecimal minLng,
            @RequestParam BigDecimal maxLng) {
        Map<String, Object> result = new HashMap<>();
        try {
            List<ServiceCenter> serviceCenters = serviceCenterService.getServiceCentersByLocationRange(minLat, maxLat, minLng, maxLng);
            
            result.put("success", true);
            result.put("data", serviceCenters);
            result.put("message", "获取附近服务网点成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取附近服务网点失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 获取服务网点统计信息
     */
    @GetMapping("/stats")
    public Map<String, Object> getServiceCenterStats() {
        Map<String, Object> result = new HashMap<>();
        try {
            ServiceCenterStats stats = serviceCenterService.getServiceCenterStats();
            
            result.put("success", true);
            result.put("data", stats);
            result.put("message", "获取服务网点统计信息成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取服务网点统计信息失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 获取服务网点详情
     */
    @GetMapping("/detail/{id}")
    public Map<String, Object> getServiceCenterDetail(@PathVariable Long id) {
        Map<String, Object> result = new HashMap<>();
        try {
            ServiceCenter serviceCenter = serviceCenterService.getById(id);
            if (serviceCenter == null) {
                result.put("success", false);
                result.put("message", "服务网点不存在");
                return result;
            }
            
            result.put("success", true);
            result.put("data", serviceCenter);
            result.put("message", "获取服务网点详情成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取服务网点详情失败：" + e.getMessage());
        }
        return result;
    }
}
