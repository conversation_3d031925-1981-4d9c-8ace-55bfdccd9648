package com.zlz.mall_server.controller;

import com.zlz.mall_server.model.ServiceCenter;
import com.zlz.mall_server.service.ServiceCenterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/debug/service-centers")
@CrossOrigin(origins = "*")
public class ServiceCenterDebugController {

    @Autowired
    private ServiceCenterService serviceCenterService;

    /**
     * 获取所有服务网点的详细状态信息（用于调试）
     */
    @GetMapping("/all")
    public Map<String, Object> getAllServiceCentersDebug() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 获取所有服务网点
            List<ServiceCenter> allServiceCenters = serviceCenterService.list();
            
            // 按状态分组
            Map<String, List<ServiceCenter>> groupedByStatus = allServiceCenters.stream()
                .collect(Collectors.groupingBy(ServiceCenter::getStatus));
            
            // 统计信息
            Map<String, Object> stats = new HashMap<>();
            stats.put("total", allServiceCenters.size());
            
            for (Map.Entry<String, List<ServiceCenter>> entry : groupedByStatus.entrySet()) {
                String status = entry.getKey();
                List<ServiceCenter> centers = entry.getValue();
                
                stats.put(status + "_count", centers.size());
                
                // 进一步分析每个状态下的isActive情况
                long activeCount = centers.stream().filter(c -> Boolean.TRUE.equals(c.getIsActive())).count();
                long inactiveCount = centers.size() - activeCount;
                
                stats.put(status + "_active", activeCount);
                stats.put(status + "_inactive", inactiveCount);
            }
            
            // 详细信息
            List<Map<String, Object>> detailList = allServiceCenters.stream()
                .map(center -> {
                    Map<String, Object> detail = new HashMap<>();
                    detail.put("id", center.getId());
                    detail.put("name", center.getName());
                    detail.put("status", center.getStatus());
                    detail.put("isActive", center.getIsActive());
                    detail.put("isFeatured", center.getIsFeatured());
                    detail.put("createdAt", center.getCreatedAt());
                    detail.put("updatedAt", center.getUpdatedAt());
                    detail.put("applicationTime", center.getApplicationTime());
                    detail.put("reviewTime", center.getReviewTime());
                    detail.put("reviewNotes", center.getReviewNotes());
                    return detail;
                })
                .collect(Collectors.toList());
            
            result.put("success", true);
            result.put("data", Map.of(
                "stats", stats,
                "groupedByStatus", groupedByStatus,
                "allDetails", detailList
            ));
            result.put("message", "获取所有服务网点调试信息成功");
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取调试信息失败：" + e.getMessage());
            e.printStackTrace();
        }
        
        return result;
    }

    /**
     * 批量激活所有已审核通过但未激活的服务网点
     */
    @PostMapping("/activate-approved")
    public Map<String, Object> activateApprovedServiceCenters() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            List<ServiceCenter> allServiceCenters = serviceCenterService.list();
            int activatedCount = 0;
            
            for (ServiceCenter center : allServiceCenters) {
                // 如果状态是已审核通过但未激活，则激活
                if ("approved".equals(center.getStatus()) && !Boolean.TRUE.equals(center.getIsActive())) {
                    center.setIsActive(true);
                    serviceCenterService.updateById(center);
                    activatedCount++;
                }
            }
            
            result.put("success", true);
            result.put("data", Map.of("activatedCount", activatedCount));
            result.put("message", "批量激活完成，共激活 " + activatedCount + " 个服务网点");
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "批量激活失败：" + e.getMessage());
            e.printStackTrace();
        }
        
        return result;
    }

    /**
     * 批量审核通过所有待审核的服务网点
     */
    @PostMapping("/approve-all-pending")
    public Map<String, Object> approveAllPendingServiceCenters() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            List<ServiceCenter> allServiceCenters = serviceCenterService.list();
            int approvedCount = 0;
            
            for (ServiceCenter center : allServiceCenters) {
                if ("pending".equals(center.getStatus())) {
                    center.setStatus("approved");
                    center.setIsActive(true);
                    center.setReviewNotes("批量审核通过");
                    serviceCenterService.updateById(center);
                    approvedCount++;
                }
            }
            
            result.put("success", true);
            result.put("data", Map.of("approvedCount", approvedCount));
            result.put("message", "批量审核完成，共审核通过 " + approvedCount + " 个服务网点");
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "批量审核失败：" + e.getMessage());
            e.printStackTrace();
        }
        
        return result;
    }

    /**
     * 批量审核通过并激活所有服务网点（除了ID=1的测试数据）
     */
    @PostMapping("/approve-and-activate-all")
    public Map<String, Object> approveAndActivateAll() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            List<ServiceCenter> allServiceCenters = serviceCenterService.list();
            int processedCount = 0;
            
            for (ServiceCenter center : allServiceCenters) {
                // 跳过ID=1的测试数据，处理其他所有数据
                if (center.getId() > 1) {
                    boolean updated = false;
                    
                    // 如果不是已审核通过状态，设置为已审核通过
                    if (!"approved".equals(center.getStatus())) {
                        center.setStatus("approved");
                        center.setReviewNotes("批量审核通过");
                        updated = true;
                    }
                    
                    // 如果未激活，设置为激活
                    if (!Boolean.TRUE.equals(center.getIsActive())) {
                        center.setIsActive(true);
                        updated = true;
                    }
                    
                    if (updated) {
                        serviceCenterService.updateById(center);
                        processedCount++;
                    }
                }
            }
            
            result.put("success", true);
            result.put("data", Map.of("processedCount", processedCount));
            result.put("message", "批量处理完成，共处理 " + processedCount + " 个服务网点");
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "批量处理失败：" + e.getMessage());
            e.printStackTrace();
        }
        
        return result;
    }
}
