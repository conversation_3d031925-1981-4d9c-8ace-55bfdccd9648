package com.zlz.mall_server.controller;

import com.zlz.mall_server.mapper.EngineerMapper.EngineerStats;
import com.zlz.mall_server.model.Engineer;
import com.zlz.mall_server.service.EngineerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/engineers")
@CrossOrigin(origins = "*")
public class EngineerController {
    
    @Autowired
    private EngineerService engineerService;
    
    /**
     * 获取所有已审核通过的工程师列表
     */
    @GetMapping("/approved")
    public Map<String, Object> getApprovedEngineers() {
        Map<String, Object> result = new HashMap<>();
        try {
            List<Engineer> engineers = engineerService.getApprovedEngineers();
            
            // 处理JSON字段
            engineers.forEach(engineer -> {
                processEngineerJson<PERSON>ields(engineer);
            });
            
            result.put("success", true);
            result.put("data", engineers);
            result.put("message", "获取工程师列表成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取工程师列表失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 获取在线且可接单的工程师列表
     */
    @GetMapping("/available")
    public Map<String, Object> getAvailableEngineers() {
        Map<String, Object> result = new HashMap<>();
        try {
            List<Engineer> engineers = engineerService.getAvailableEngineers();
            
            // 处理JSON字段
            engineers.forEach(engineer -> {
                processEngineerJsonFields(engineer);
            });
            
            result.put("success", true);
            result.put("data", engineers);
            result.put("message", "获取可接单工程师列表成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取可接单工程师列表失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 根据专业领域查询工程师
     */
    @GetMapping("/specialty/{specialty}")
    public Map<String, Object> getEngineersBySpecialty(@PathVariable String specialty) {
        Map<String, Object> result = new HashMap<>();
        try {
            List<Engineer> engineers = engineerService.getEngineersBySpecialty(specialty);
            
            // 处理JSON字段
            engineers.forEach(engineer -> {
                processEngineerJsonFields(engineer);
            });
            
            result.put("success", true);
            result.put("data", engineers);
            result.put("message", "获取专业工程师列表成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取专业工程师列表失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 获取工程师统计信息
     */
    @GetMapping("/stats")
    public Map<String, Object> getEngineerStats() {
        Map<String, Object> result = new HashMap<>();
        try {
            EngineerStats stats = engineerService.getEngineerStats();
            
            result.put("success", true);
            result.put("data", stats);
            result.put("message", "获取工程师统计信息成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取工程师统计信息失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 获取工程师详情
     */
    @GetMapping("/detail/{id}")
    public Map<String, Object> getEngineerDetail(@PathVariable Long id) {
        Map<String, Object> result = new HashMap<>();
        try {
            Engineer engineer = engineerService.getById(id);
            if (engineer == null) {
                result.put("success", false);
                result.put("message", "工程师不存在");
                return result;
            }
            
            // 处理JSON字段
            processEngineerJsonFields(engineer);
            
            result.put("success", true);
            result.put("data", engineer);
            result.put("message", "获取工程师详情成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取工程师详情失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 提交工程师入驻申请（兼容接口）
     */
    @PostMapping("/apply")
    public Map<String, Object> submitEngineerApplication(@RequestBody Map<String, Object> applicationData) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 添加调试日志
            System.out.println("🚀 收到工程师申请数据: " + applicationData);

            // 验证必要字段
            if (applicationData.get("name") == null || applicationData.get("name").toString().trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "姓名不能为空");
                return result;
            }

            if (applicationData.get("phone") == null || applicationData.get("phone").toString().trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "联系电话不能为空");
                return result;
            }
            // 创建工程师对象
            Engineer engineer = new Engineer();

            // 基本信息
            engineer.setOpenId((String) applicationData.get("openId")); // 添加openId字段
            engineer.setName((String) applicationData.get("name"));
            engineer.setPhone((String) applicationData.get("phone"));
            engineer.setEmail((String) applicationData.get("email"));
            engineer.setGender((String) applicationData.get("gender"));
            if (applicationData.get("age") != null) {
                engineer.setAge(Integer.parseInt(applicationData.get("age").toString()));
            }
            engineer.setIdCard((String) applicationData.get("idCard"));

            // 专业信息
            engineer.setSpecialties((String) applicationData.get("specialties"));
            if (applicationData.get("experienceYears") != null) {
                engineer.setExperienceYears(Integer.parseInt(applicationData.get("experienceYears").toString()));
            }
            engineer.setEducation((String) applicationData.get("education"));
            engineer.setCertifications((String) applicationData.get("certifications"));
            engineer.setSkills((String) applicationData.get("skills"));

            // 工作信息
            engineer.setWorkAreas((String) applicationData.get("workAreas"));
            engineer.setWorkTime((String) applicationData.get("workTime"));
            if (applicationData.get("hourlyRate") != null) {
                engineer.setHourlyRate(new BigDecimal(applicationData.get("hourlyRate").toString()));
            }
            if (applicationData.get("serviceFee") != null) {
                engineer.setServiceFee(new BigDecimal(applicationData.get("serviceFee").toString()));
            }

            // 个人介绍
            engineer.setBio((String) applicationData.get("bio"));
            engineer.setIntroduction((String) applicationData.get("introduction"));
            engineer.setWorkPhotos((String) applicationData.get("workPhotos"));

            // 状态管理
            engineer.setStatus("pending"); // 待审核
            engineer.setIsOnline(false); // 离线
            engineer.setIsAvailable(false); // 不可接单

            // 审核信息
            engineer.setApplicationTime(LocalDateTime.now());

            // 系统字段
            engineer.setSortOrder(0);
            engineer.setCreatedAt(LocalDateTime.now());
            engineer.setUpdatedAt(LocalDateTime.now());

            // 默认评价信息
            engineer.setRating(new BigDecimal("5.00"));
            engineer.setTotalOrders(0);
            engineer.setCompletedOrders(0);
            engineer.setSuccessRate(new BigDecimal("100.00"));

            // 保存到数据库
            engineerService.save(engineer);

            result.put("success", true);
            result.put("data", Map.of("applicationId", engineer.getId()));
            result.put("message", "工程师入驻申请提交成功，我们将在3-5个工作日内完成审核");

        } catch (Exception e) {
            e.printStackTrace(); // 打印详细错误信息
            result.put("success", false);
            result.put("message", "申请提交失败：" + e.getMessage());
            System.err.println("工程师申请提交失败，详细错误：" + e.toString());
        }

        return result;
    }

    /**
     * 处理工程师的JSON字段
     * 将JSON字符串转换为前端可用的格式
     */
    private void processEngineerJsonFields(Engineer engineer) {
        // 这里可以根据需要处理JSON字段
        // 暂时保持原样，前端自行解析JSON字符串
    }
}
