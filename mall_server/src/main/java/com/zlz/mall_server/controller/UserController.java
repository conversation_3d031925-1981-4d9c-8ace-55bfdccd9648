package com.zlz.mall_server.controller;

import com.zlz.mall_server.model.User;
import com.zlz.mall_server.service.UserService;
import com.zlz.mall_server.utils.TtApiUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/user")
@Slf4j
public class UserController {

    @Autowired
    private UserService userService;

    @Autowired
    private TtApiUtil ttApiUtil;

    /**
     * 用户登录
     * @param params 包含code和userInfo的参数，抖音小程序登录
     * @return 登录结果
     */
    @PostMapping("/login")
    public ResponseEntity<Map<String, Object>> login(@RequestBody Map<String, Object> params) {
        log.info("用户登录请求: {}", params);

        String code = (String) params.get("code");
        Map<String, Object> userInfo = (Map<String, Object>) params.get("userInfo");

        if (code == null || code.isEmpty()) {
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("message", "登录失败: code不能为空");
            return ResponseEntity.badRequest().body(error);
        }

        Map<String, Object> result = userService.login(code, userInfo);

        if ((Boolean) result.get("success")) {
            return ResponseEntity.ok(result);
        } else {
            return ResponseEntity.badRequest().body(result);
        }
    }

    /**
     * 获取用户信息
     * @param userId 用户ID
     * @return 用户信息
     */
    @GetMapping("/{userId}")
    public ResponseEntity<Map<String, Object>> getUserInfo(@PathVariable Long userId) {
        log.info("获取用户信息: userId={}", userId);

        Map<String, Object> result = new HashMap<>();

        try {
            // 根据ID获取用户
            com.zlz.mall_server.model.User user = userService.getById(userId);

            if (user == null) {
                result.put("success", false);
                result.put("message", "用户不存在");
                return ResponseEntity.badRequest().body(result);
            }

            // 不返回敏感信息
            user.setOpenId(null);

            result.put("success", true);
            result.put("userInfo", user);

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取用户信息异常", e);
            result.put("success", false);
            result.put("message", "获取用户信息异常: " + e.getMessage());
            return ResponseEntity.badRequest().body(result);
        }
    }

    /**
     * 更新用户手机号
     * @param params 包含userId和phone的参数
     * @return 更新结果
     */
    @PostMapping("/updatePhone")
    public ResponseEntity<Map<String, Object>> updatePhone(@RequestBody Map<String, Object> params) {
        log.info("更新用户手机号: {}", params);

        Long userId = Long.valueOf(params.get("userId").toString());
        String phone = (String) params.get("phone");

        Map<String, Object> result = new HashMap<>();

        try {
            // 根据ID获取用户
            com.zlz.mall_server.model.User user = userService.getById(userId);

            if (user == null) {
                result.put("success", false);
                result.put("message", "用户不存在");
                return ResponseEntity.badRequest().body(result);
            }

            // 更新手机号
            user.setPhone(phone);
            userService.updateById(user);

            result.put("success", true);
            result.put("message", "手机号更新成功");

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("更新用户手机号异常", e);
            result.put("success", false);
            result.put("message", "更新用户手机号异常: " + e.getMessage());
            return ResponseEntity.badRequest().body(result);
        }
    }

    /**
     * 简化的登录接口，只需要code就可以获取openId
     * @param params 包含code的参数
     * @return 登录结果，包含openId
     */
    @PostMapping("/code2Session")
    public ResponseEntity<Map<String, Object>> code2Session(@RequestBody Map<String, Object> params) {
        log.info("简化登录请求: {}", params);

        String code = (String) params.get("code");

        if (code == null || code.isEmpty()) {
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("message", "登录失败: code不能为空");
            return ResponseEntity.badRequest().body(error);
        }

        try {
            // 调用抖音API获取openId和sessionKey
            Map<String, Object> ttResult = ttApiUtil.code2Session(code);

            log.info("抖音code2Session响应: {}", ttResult);

            if (ttResult.containsKey("error")) {
                // 抖音API调用失败
                Object errorMsg = ttResult.get("errmsg");
                Map<String, Object> error = new HashMap<>();
                error.put("success", false);
                error.put("message", "抖音登录失败: " + (errorMsg != null ? errorMsg : "未知错误"));
                return ResponseEntity.badRequest().body(error);
            }

            // 检查是否包含openid
            if (!ttResult.containsKey("openid")) {
                Map<String, Object> error = new HashMap<>();
                error.put("success", false);
                error.put("message", "抖音登录失败: 响应中没有openid");
                return ResponseEntity.badRequest().body(error);
            }

            String openId = (String) ttResult.get("openid");
            String sessionKey = (String) ttResult.get("session_key");

            // 查询用户是否存在
            User user = userService.findByOpenId(openId);

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("openId", openId);
            result.put("sessionKey", sessionKey);

            if (user != null) {
                result.put("userId", user.getId());
                result.put("isNewUser", false);

                // 更新登录时间
                user.setLastLoginTime(LocalDateTime.now());
                user.setUpdatedAt(LocalDateTime.now());
                userService.updateById(user);
                
                // 返回用户信息
                user.setOpenId(openId); // 不返回敏感信息
                result.put("userInfo", user);
            } else {
                // 用户不存在，但我们不再需要注册功能
                // 直接创建一个新用户
                user = new User();
                user.setOpenId(openId);
                user.setLastLoginTime(LocalDateTime.now());
                user.setCreatedAt(LocalDateTime.now());
                user.setUpdatedAt(LocalDateTime.now());
                user.setStatus(1); // 正常状态
                
                // 如果有用户信息，则使用
                if (params.containsKey("userInfo")) {
                    Map<String, Object> userInfo = (Map<String, Object>) params.get("userInfo");
                    if (userInfo != null) {
                        user.setNickname((String) userInfo.get("nickName"));
                        user.setAvatarUrl((String) userInfo.get("avatarUrl"));
                        user.setGender((String) userInfo.get("gender"));
                    }
                }
                
                // 保存用户
                userService.save(user);
                
                result.put("userId", user.getId());
                result.put("isNewUser", true);
                
                // 返回用户信息
                user.setOpenId(null); // 不返回敏感信息
                result.put("userInfo", user);
            }

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("简化登录异常", e);
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("message", "登录异常: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
}
