package com.zlz.mall_server.controller;

import com.zlz.mall_server.model.ServiceCenter;
import com.zlz.mall_server.service.ServiceCenterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/quick-test")
@CrossOrigin(origins = "*")
public class QuickTestController {

    @Autowired
    private ServiceCenterService serviceCenterService;

    /**
     * 快速审核所有待审核的服务网点（仅用于测试）
     */
    @PostMapping("/approve-all-pending")
    public Map<String, Object> approveAllPendingServiceCenters() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 获取所有服务网点
            List<ServiceCenter> allServiceCenters = serviceCenterService.list();
            int approvedCount = 0;
            
            for (ServiceCenter serviceCenter : allServiceCenters) {
                // 如果状态是待审核，自动审核通过
                if ("pending".equals(serviceCenter.getStatus())) {
                    serviceCenter.setStatus("approved");
                    serviceCenter.setIsActive(true);
                    serviceCenter.setReviewTime(LocalDateTime.now());
                    serviceCenter.setReviewNotes("测试环境自动审核通过");
                    serviceCenter.setUpdatedAt(LocalDateTime.now());
                    
                    serviceCenterService.updateById(serviceCenter);
                    approvedCount++;
                }
            }
            
            result.put("success", true);
            result.put("message", "批量审核完成");
            result.put("data", Map.of(
                "approvedCount", approvedCount,
                "totalCount", allServiceCenters.size()
            ));
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "批量审核失败：" + e.getMessage());
        }
        
        return result;
    }

    /**
     * 获取所有服务网点的状态统计（包含详细信息）
     */
    @GetMapping("/service-centers-status")
    public Map<String, Object> getServiceCentersStatus() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            List<ServiceCenter> allServiceCenters = serviceCenterService.list();
            
            Map<String, Integer> statusCount = new HashMap<>();
            Map<String, Object> statusDetails = new HashMap<>();
            
            for (ServiceCenter serviceCenter : allServiceCenters) {
                String status = serviceCenter.getStatus();
                statusCount.put(status, statusCount.getOrDefault(status, 0) + 1);
                
                // 收集每个状态的详细信息
                if (!statusDetails.containsKey(status)) {
                    statusDetails.put(status, new java.util.ArrayList<>());
                }
                
                Map<String, Object> centerInfo = new HashMap<>();
                centerInfo.put("id", serviceCenter.getId());
                centerInfo.put("name", serviceCenter.getName());
                centerInfo.put("status", serviceCenter.getStatus());
                centerInfo.put("isActive", serviceCenter.getIsActive());
                centerInfo.put("createdAt", serviceCenter.getCreatedAt());
                centerInfo.put("applicationTime", serviceCenter.getApplicationTime());
                
                ((java.util.List<Object>) statusDetails.get(status)).add(centerInfo);
            }
            
            result.put("success", true);
            result.put("data", Map.of(
                "total", allServiceCenters.size(),
                "statusCount", statusCount,
                "statusDetails", statusDetails
            ));
            result.put("message", "获取服务网点状态统计成功");
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取状态统计失败：" + e.getMessage());
        }
        
        return result;
    }

    /**
     * 重置所有服务网点为待审核状态（仅用于测试）
     */
    @PostMapping("/reset-all-to-pending")
    public Map<String, Object> resetAllToPending() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            List<ServiceCenter> allServiceCenters = serviceCenterService.list();
            int resetCount = 0;
            
            for (ServiceCenter serviceCenter : allServiceCenters) {
                if (serviceCenter.getId() > 1) { // 保留第一个测试数据
                    serviceCenter.setStatus("pending");
                    serviceCenter.setIsActive(false);
                    serviceCenter.setReviewTime(null);
                    serviceCenter.setReviewNotes(null);
                    serviceCenter.setRejectionReason(null);
                    serviceCenter.setUpdatedAt(LocalDateTime.now());
                    
                    serviceCenterService.updateById(serviceCenter);
                    resetCount++;
                }
            }
            
            result.put("success", true);
            result.put("message", "重置完成");
            result.put("data", Map.of("resetCount", resetCount));
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "重置失败：" + e.getMessage());
        }
        
        return result;
    }
}
