package com.zlz.mall_server.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/database")
public class DatabaseInitController {
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    /**
     * 初始化product_favorites表
     */
    @GetMapping("/init-favorites")
    public Map<String, Object> initFavoritesTable() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 读取SQL文件
            ClassPathResource resource = new ClassPathResource("db/product_favorites.sql");
            StringBuilder sqlBuilder = new StringBuilder();
            
            try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(resource.getInputStream(), StandardCharsets.UTF_8))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    // 跳过注释行
                    if (!line.trim().startsWith("--") && !line.trim().isEmpty()) {
                        sqlBuilder.append(line).append("\n");
                    }
                }
            }
            
            String sql = sqlBuilder.toString();
            log.info("执行SQL: {}", sql);
            
            // 执行SQL
            jdbcTemplate.execute(sql);
            
            // 检查表是否创建成功
            Integer count = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'mall' AND table_name = 'product_favorites'", 
                Integer.class);
            
            if (count != null && count > 0) {
                result.put("success", true);
                result.put("message", "product_favorites表创建成功");
                log.info("product_favorites表创建成功");
            } else {
                result.put("success", false);
                result.put("message", "product_favorites表创建失败");
                log.error("product_favorites表创建失败");
            }
            
        } catch (Exception e) {
            log.error("初始化product_favorites表失败", e);
            result.put("success", false);
            result.put("message", "初始化失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 检查product_favorites表状态
     */
    @GetMapping("/check-favorites")
    public Map<String, Object> checkFavoritesTable() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 检查表是否存在
            Integer tableCount = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'mall' AND table_name = 'product_favorites'", 
                Integer.class);
            
            boolean tableExists = tableCount != null && tableCount > 0;
            result.put("tableExists", tableExists);
            
            if (tableExists) {
                // 检查表结构
                Integer recordCount = jdbcTemplate.queryForObject(
                    "SELECT COUNT(*) FROM product_favorites", 
                    Integer.class);
                result.put("recordCount", recordCount);
                result.put("message", "product_favorites表存在，包含 " + recordCount + " 条记录");
            } else {
                result.put("message", "product_favorites表不存在");
            }
            
            result.put("success", true);
            
        } catch (Exception e) {
            log.error("检查product_favorites表失败", e);
            result.put("success", false);
            result.put("message", "检查失败: " + e.getMessage());
        }
        
        return result;
    }
}
