package com.zlz.mall_server.controller;

import com.zlz.mall_server.service.CustomerServiceService;
import com.zlz.mall_server.model.CustomerServiceSession;
import com.zlz.mall_server.model.CustomerServiceMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/debug")
public class DebugController {
    
    @Autowired
    private CustomerServiceService customerServiceService;
    
    /**
     * 查看所有等待接入的会话
     */
    @GetMapping("/waiting-sessions")
    public Map<String, Object> getWaitingSessions() {
        Map<String, Object> result = new HashMap<>();
        try {
            List<CustomerServiceSession> sessions = customerServiceService.getWaitingSessions();
            result.put("success", true);
            result.put("count", sessions.size());
            result.put("data", sessions);
        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            e.printStackTrace();
        }
        return result;
    }
    
    /**
     * 查看指定会话的消息
     */
    @GetMapping("/session-messages/{sessionId}")
    public Map<String, Object> getSessionMessages(@PathVariable String sessionId) {
        Map<String, Object> result = new HashMap<>();
        try {
            List<CustomerServiceMessage> messages = customerServiceService.getSessionMessages(sessionId);
            CustomerServiceSession session = customerServiceService.getSessionById(sessionId);
            
            result.put("success", true);
            result.put("session", session);
            result.put("messageCount", messages.size());
            result.put("messages", messages);
        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            e.printStackTrace();
        }
        return result;
    }
    
    /**
     * 手动接入会话（用于测试）
     */
    @PostMapping("/assign-session/{sessionId}")
    public Map<String, Object> assignSession(@PathVariable String sessionId) {
        Map<String, Object> result = new HashMap<>();
        try {
            // 使用测试管理员ID和名称
            customerServiceService.assignAdmin(sessionId, 1L, "测试客服");
            
            result.put("success", true);
            result.put("message", "会话已接入");
        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            e.printStackTrace();
        }
        return result;
    }
    
    /**
     * 查看会话详情
     */
    @GetMapping("/session-detail/{sessionId}")
    public Map<String, Object> getSessionDetail(@PathVariable String sessionId) {
        Map<String, Object> result = new HashMap<>();
        try {
            CustomerServiceSession session = customerServiceService.getSessionById(sessionId);
            
            if (session != null) {
                result.put("success", true);
                result.put("session", session);
                result.put("status", session.getStatus());
                result.put("adminId", session.getAdminId());
                result.put("adminName", session.getAdminName());
                result.put("unreadCountAdmin", session.getUnreadCountAdmin());
            } else {
                result.put("success", false);
                result.put("message", "会话不存在");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            e.printStackTrace();
        }
        return result;
    }
}
