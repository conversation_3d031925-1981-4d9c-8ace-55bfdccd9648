package com.zlz.mall_server.controller.admin;

import com.zlz.mall_server.model.Engineer;
import com.zlz.mall_server.service.EngineerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/admin/engineers")
@CrossOrigin(origins = "*")
public class AdminEngineerController {
    
    @Autowired
    private EngineerService engineerService;
    
    /**
     * 获取所有工程师列表（管理端）
     */
    @GetMapping("/list")
    public Map<String, Object> getAllEngineers(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String keyword) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            // 这里可以添加分页和筛选逻辑
            List<Engineer> engineers = engineerService.list();
            
            result.put("success", true);
            result.put("data", engineers);
            result.put("total", engineers.size());
            result.put("message", "获取工程师列表成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取工程师列表失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 管理端直接添加工程师
     */
    @PostMapping("/add")
    public Map<String, Object> addEngineer(@RequestBody Map<String, Object> engineerData) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            System.out.println("🚀 管理端添加工程师数据: " + engineerData);
            
            // 验证必要字段
            if (engineerData.get("name") == null || engineerData.get("name").toString().trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "工程师姓名不能为空");
                return result;
            }
            
            if (engineerData.get("phone") == null || engineerData.get("phone").toString().trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "联系电话不能为空");
                return result;
            }
            
            // 检查手机号是否已存在
            Engineer existingEngineer = engineerService.getByPhone(engineerData.get("phone").toString());
            if (existingEngineer != null) {
                result.put("success", false);
                result.put("message", "该手机号已被注册");
                return result;
            }
            
            // 创建工程师对象
            Engineer engineer = new Engineer();
            
            // 基本信息
            engineer.setName(engineerData.get("name").toString().trim());
            engineer.setPhone(engineerData.get("phone").toString().trim());
            engineer.setEmail((String) engineerData.get("email"));
            engineer.setAvatar((String) engineerData.get("avatar"));
            engineer.setGender((String) engineerData.get("gender"));
            
            // 年龄处理
            engineer.setAge(safeGetInteger(engineerData, "age", null));

            engineer.setIdCard((String) engineerData.get("idCard"));

            // 专业信息
            engineer.setSpecialties((String) engineerData.get("specialties"));

            // 工作经验年数处理
            engineer.setExperienceYears(safeGetInteger(engineerData, "experienceYears", 0));

            engineer.setEducation((String) engineerData.get("education"));
            engineer.setCertifications((String) engineerData.get("certifications"));
            engineer.setSkills((String) engineerData.get("skills"));

            // 工作信息
            engineer.setWorkAreas((String) engineerData.get("workAreas"));
            engineer.setWorkTime((String) engineerData.get("workTime"));

            // 费用信息处理
            engineer.setHourlyRate(safeGetBigDecimal(engineerData, "hourlyRate", null));
            engineer.setServiceFee(safeGetBigDecimal(engineerData, "serviceFee", null));
            
            // 个人介绍
            engineer.setBio((String) engineerData.get("bio"));
            engineer.setIntroduction((String) engineerData.get("introduction"));
            engineer.setWorkPhotos((String) engineerData.get("workPhotos"));
            
            // 状态管理 - 管理端添加的工程师可以直接设置状态
            String status = (String) engineerData.get("status");
            engineer.setStatus(status != null ? status : "approved"); // 默认已通过

            // 在线状态处理
            engineer.setIsOnline(safeGetBoolean(engineerData, "isOnline", false));

            // 接单状态处理
            engineer.setIsAvailable(safeGetBoolean(engineerData, "isAvailable", true));

            // 审核信息
            engineer.setApplicationTime(LocalDateTime.now());
            if ("approved".equals(engineer.getStatus())) {
                engineer.setReviewTime(LocalDateTime.now());
                engineer.setReviewNotes("管理员直接添加");
            }

            // 系统字段 - 排序权重处理
            engineer.setSortOrder(safeGetInteger(engineerData, "sortOrder", 0));
            
            engineer.setCreatedAt(LocalDateTime.now());
            engineer.setUpdatedAt(LocalDateTime.now());
            engineer.setLastActiveAt(LocalDateTime.now());
            
            // 默认评价信息
            engineer.setRating(new BigDecimal("5.00"));
            engineer.setTotalOrders(0);
            engineer.setCompletedOrders(0);
            engineer.setSuccessRate(new BigDecimal("100.00"));
            
            // 保存到数据库
            engineerService.save(engineer);
            
            result.put("success", true);
            result.put("data", Map.of("engineerId", engineer.getId()));
            result.put("message", "工程师添加成功");
            
        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "添加工程师失败：" + e.getMessage());
            System.err.println("管理端添加工程师失败，详细错误：" + e.toString());
        }
        
        return result;
    }
    
    /**
     * 更新工程师信息
     */
    @PutMapping("/update/{id}")
    public Map<String, Object> updateEngineer(@PathVariable Long id, @RequestBody Map<String, Object> engineerData) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            Engineer engineer = engineerService.getById(id);
            if (engineer == null) {
                result.put("success", false);
                result.put("message", "工程师不存在");
                return result;
            }
            
            // 更新字段（类似添加逻辑，但保留原有的创建时间等）
            if (engineerData.get("name") != null) {
                engineer.setName(engineerData.get("name").toString().trim());
            }
            
            if (engineerData.get("phone") != null) {
                String newPhone = engineerData.get("phone").toString().trim();
                // 检查新手机号是否被其他工程师使用
                Engineer existingEngineer = engineerService.getByPhone(newPhone);
                if (existingEngineer != null && !existingEngineer.getId().equals(id)) {
                    result.put("success", false);
                    result.put("message", "该手机号已被其他工程师使用");
                    return result;
                }
                engineer.setPhone(newPhone);
            }
            
            // 更新其他字段...
            if (engineerData.get("email") != null) engineer.setEmail((String) engineerData.get("email"));
            if (engineerData.get("avatar") != null) engineer.setAvatar((String) engineerData.get("avatar"));
            if (engineerData.get("gender") != null) engineer.setGender((String) engineerData.get("gender"));
            if (engineerData.get("age") != null) {
                try {
                    engineer.setAge(Integer.parseInt(engineerData.get("age").toString()));
                } catch (NumberFormatException e) {
                    // 忽略无效的年龄值
                }
            }
            
            // 更新时间
            engineer.setUpdatedAt(LocalDateTime.now());
            
            // 保存更新
            engineerService.updateById(engineer);
            
            result.put("success", true);
            result.put("message", "工程师信息更新成功");
            
        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "更新工程师信息失败：" + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 删除工程师
     */
    @DeleteMapping("/delete/{id}")
    public Map<String, Object> deleteEngineer(@PathVariable Long id) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            Engineer engineer = engineerService.getById(id);
            if (engineer == null) {
                result.put("success", false);
                result.put("message", "工程师不存在");
                return result;
            }
            
            engineerService.removeById(id);
            
            result.put("success", true);
            result.put("message", "工程师删除成功");
            
        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "删除工程师失败：" + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 批量审核工程师
     */
    @PostMapping("/batch-review")
    public Map<String, Object> batchReviewEngineers(@RequestBody Map<String, Object> reviewData) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            @SuppressWarnings("unchecked")
            List<Long> engineerIds = (List<Long>) reviewData.get("engineerIds");
            String status = (String) reviewData.get("status");
            String reviewNotes = (String) reviewData.get("reviewNotes");
            
            if (engineerIds == null || engineerIds.isEmpty()) {
                result.put("success", false);
                result.put("message", "请选择要审核的工程师");
                return result;
            }
            
            int successCount = 0;
            for (Long engineerId : engineerIds) {
                try {
                    Engineer engineer = engineerService.getById(engineerId);
                    if (engineer != null) {
                        engineer.setStatus(status);
                        engineer.setReviewTime(LocalDateTime.now());
                        engineer.setReviewNotes(reviewNotes);
                        engineer.setUpdatedAt(LocalDateTime.now());
                        
                        if ("approved".equals(status)) {
                            engineer.setIsAvailable(true);
                        }
                        
                        engineerService.updateById(engineer);
                        successCount++;
                    }
                } catch (Exception e) {
                    System.err.println("审核工程师 " + engineerId + " 失败: " + e.getMessage());
                }
            }
            
            result.put("success", true);
            result.put("message", "批量审核完成，成功处理 " + successCount + " 个工程师");
            
        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "批量审核失败：" + e.getMessage());
        }
        
        return result;
    }

    /**
     * 安全地从Map中获取Integer值
     */
    private Integer safeGetInteger(Map<String, Object> data, String key, Integer defaultValue) {
        Object value = data.get(key);
        if (value == null) {
            return defaultValue;
        }

        try {
            if (value instanceof Integer) {
                return (Integer) value;
            } else if (value instanceof String) {
                String strValue = ((String) value).trim();
                if (strValue.isEmpty()) {
                    return defaultValue;
                }
                return Integer.parseInt(strValue);
            } else {
                return Integer.parseInt(value.toString().trim());
            }
        } catch (NumberFormatException e) {
            System.err.println("无法转换为Integer: " + key + " = " + value);
            return defaultValue;
        }
    }

    /**
     * 安全地从Map中获取Boolean值
     */
    private Boolean safeGetBoolean(Map<String, Object> data, String key, Boolean defaultValue) {
        Object value = data.get(key);
        if (value == null) {
            return defaultValue;
        }

        try {
            if (value instanceof Boolean) {
                return (Boolean) value;
            } else if (value instanceof String) {
                String strValue = ((String) value).trim().toLowerCase();
                return "true".equals(strValue) || "1".equals(strValue);
            } else {
                return Boolean.parseBoolean(value.toString().trim());
            }
        } catch (Exception e) {
            System.err.println("无法转换为Boolean: " + key + " = " + value);
            return defaultValue;
        }
    }

    /**
     * 安全地从Map中获取BigDecimal值
     */
    private BigDecimal safeGetBigDecimal(Map<String, Object> data, String key, BigDecimal defaultValue) {
        Object value = data.get(key);
        if (value == null) {
            return defaultValue;
        }

        try {
            if (value instanceof BigDecimal) {
                return (BigDecimal) value;
            } else if (value instanceof String) {
                String strValue = ((String) value).trim();
                if (strValue.isEmpty()) {
                    return defaultValue;
                }
                return new BigDecimal(strValue);
            } else {
                return new BigDecimal(value.toString().trim());
            }
        } catch (NumberFormatException e) {
            System.err.println("无法转换为BigDecimal: " + key + " = " + value);
            return defaultValue;
        }
    }
}
