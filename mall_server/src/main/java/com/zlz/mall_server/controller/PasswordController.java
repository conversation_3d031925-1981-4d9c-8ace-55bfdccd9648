package com.zlz.mall_server.controller;

import com.zlz.mall_server.util.PasswordUtil;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/password")
public class PasswordController {
    
    /**
     * 生成密码哈希
     */
    @GetMapping("/generate")
    public Map<String, Object> generatePassword(@RequestParam(defaultValue = "admin123") String password) {
        Map<String, Object> result = new HashMap<>();
        
        String hash = PasswordUtil.encode(password);
        boolean valid = PasswordUtil.matches(password, hash);
        
        result.put("password", password);
        result.put("hash", hash);
        result.put("valid", valid);
        result.put("message", "密码哈希生成成功");
        
        return result;
    }
    
    /**
     * 验证密码
     */
    @GetMapping("/verify")
    public Map<String, Object> verifyPassword(@RequestParam String password, 
                                            @RequestParam String hash) {
        Map<String, Object> result = new HashMap<>();
        
        boolean valid = PasswordUtil.matches(password, hash);
        
        result.put("password", password);
        result.put("hash", hash);
        result.put("valid", valid);
        result.put("message", valid ? "密码验证成功" : "密码验证失败");
        
        return result;
    }
}
