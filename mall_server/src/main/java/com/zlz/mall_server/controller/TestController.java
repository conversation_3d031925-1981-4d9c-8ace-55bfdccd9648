package com.zlz.mall_server.controller;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@Controller
@RequestMapping("/test")
public class TestController {

    /**
     * 服务网点详情测试页面
     */
    @GetMapping("/service-center-detail")
    public String testServiceCenterDetail() {
        return "test-service-center-detail";
    }

    /**
     * 测试用的服务网点详情API（不需要登录）
     */
    @GetMapping("/api/service-center/{id}")
    @ResponseBody
    public Map<String, Object> getTestServiceCenterDetail(@PathVariable Long id) {
        Map<String, Object> result = new HashMap<>();
        try {
            // 创建测试数据
            Map<String, Object> serviceCenter = new HashMap<>();
            serviceCenter.put("id", id);
            serviceCenter.put("name", "测试充电站 " + id);
            serviceCenter.put("phone", "***********");
            serviceCenter.put("contactPerson", "张三");
            serviceCenter.put("email", "<EMAIL>");
            serviceCenter.put("address", "北京市朝阳区测试路123号");
            serviceCenter.put("businessHours", "08:00-22:00");
            serviceCenter.put("serviceDescription", "专业的充电桩维修和安装服务");
            serviceCenter.put("businessLicense", "/uploads/20250610_143914_aea5df47.png");
            serviceCenter.put("qualificationCertificates", "[\"/uploads/20250610_143914_aea5df47.png\", \"/uploads/20250610_143914_4a433ed2.png\"]");
            serviceCenter.put("images", "[\"/uploads/20250610_143914_aea5df47.png\", \"/uploads/20250610_143914_4a433ed2.png\"]");
            
            result.put("success", true);
            result.put("data", serviceCenter);
            result.put("message", "获取测试服务网点详情成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取测试服务网点详情失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 测试图片显示页面
     */
    @GetMapping("/images")
    public String testImages() {
        return "test-images";
    }

    /**
     * 测试服务网点图片页面
     */
    @GetMapping("/station-images")
    public String testStationImages() {
        return "test-station-images";
    }

    /**
     * 测试小程序API页面
     */
    @GetMapping("/miniprogram-api")
    public String testMiniprogramApi() {
        return "test-miniprogram-api";
    }
}
