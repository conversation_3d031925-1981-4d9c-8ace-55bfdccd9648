package com.zlz.mall_server.controller;

import com.zlz.mall_server.model.AdminUser;
import com.zlz.mall_server.model.CustomerServiceSession;
import com.zlz.mall_server.model.CustomerServiceMessage;
import com.zlz.mall_server.model.CustomerServiceQuickReply;
import com.zlz.mall_server.service.CustomerServiceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpSession;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping("/admin/customer-service")
public class AdminCustomerServiceController {
    
    @Autowired
    private CustomerServiceService customerServiceService;
    
    /**
     * 客服工作台页面
     */
    @GetMapping("")
    public String customerServicePage(Model model, HttpSession session) {
        AdminUser admin = (AdminUser) session.getAttribute("admin");
        if (admin == null) {
            return "redirect:/admin/login";
        }

        try {
            // 获取等待接入的会话
            List<CustomerServiceSession> waitingSessions = customerServiceService.getWaitingSessions();

            // 获取所有活跃会话（允许管理员查看所有会话）
            List<CustomerServiceSession> activeSessions = customerServiceService.getAllActiveSessions();

            // 获取快捷回复
            List<CustomerServiceQuickReply> quickReplies = customerServiceService.getQuickReplies(admin.getId());

            model.addAttribute("admin", admin);
            model.addAttribute("waitingSessions", waitingSessions);
            model.addAttribute("activeSessions", activeSessions);
            model.addAttribute("quickReplies", quickReplies);
        } catch (Exception e) {
            // 如果数据库表不存在，使用空列表
            System.err.println("客服数据库表可能不存在，使用默认数据: " + e.getMessage());
            model.addAttribute("admin", admin);
            model.addAttribute("waitingSessions", new java.util.ArrayList<>());
            model.addAttribute("activeSessions", new java.util.ArrayList<>());
            model.addAttribute("quickReplies", new java.util.ArrayList<>());
        }

        return "admin/customer_service_optimized";
    }
    
    /**
     * 接入会话
     */
    @PostMapping("/api/assign")
    @ResponseBody
    public Map<String, Object> assignSession(@RequestBody Map<String, String> request, HttpSession session) {
        Map<String, Object> result = new HashMap<>();
        try {
            AdminUser admin = (AdminUser) session.getAttribute("admin");
            if (admin == null) {
                result.put("success", false);
                result.put("message", "请先登录");
                return result;
            }
            
            String sessionId = request.get("sessionId");
            
            customerServiceService.assignAdmin(sessionId, admin.getId(), admin.getRealName());
            
            result.put("success", true);
            result.put("message", "接入成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "接入失败：" + e.getMessage());
            e.printStackTrace();
        }
        return result;
    }
    
    /**
     * 发送消息
     */
    @PostMapping("/api/message")
    @ResponseBody
    public Map<String, Object> sendMessage(@RequestBody Map<String, String> request, HttpSession session) {
        Map<String, Object> result = new HashMap<>();
        try {
            AdminUser admin = (AdminUser) session.getAttribute("admin");
            if (admin == null) {
                result.put("success", false);
                result.put("message", "请先登录");
                return result;
            }
            
            String sessionId = request.get("sessionId");
            String content = request.get("content");
            
            CustomerServiceMessage message = customerServiceService.sendMessage(
                sessionId, "admin", admin.getId().toString(), admin.getRealName(), admin.getAvatar(), content);
            
            result.put("success", true);
            result.put("data", message);
            result.put("message", "发送成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "发送失败：" + e.getMessage());
            e.printStackTrace();
        }
        return result;
    }
    
    /**
     * 获取会话消息
     */
    @GetMapping("/api/messages/{sessionId}")
    @ResponseBody
    public Map<String, Object> getMessages(@PathVariable String sessionId) {
        Map<String, Object> result = new HashMap<>();
        try {
            List<CustomerServiceMessage> messages = customerServiceService.getSessionMessages(sessionId);
            
            result.put("success", true);
            result.put("data", messages);
            result.put("message", "获取成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取失败：" + e.getMessage());
            e.printStackTrace();
        }
        return result;
    }
    
    /**
     * 标记消息为已读
     */
    @PostMapping("/api/read")
    @ResponseBody
    public Map<String, Object> markAsRead(@RequestBody Map<String, String> request) {
        Map<String, Object> result = new HashMap<>();
        try {
            String sessionId = request.get("sessionId");
            
            customerServiceService.markMessagesAsRead(sessionId, "admin");
            
            result.put("success", true);
            result.put("message", "标记已读成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "标记已读失败：" + e.getMessage());
            e.printStackTrace();
        }
        return result;
    }
    
    /**
     * 关闭会话
     */
    @PostMapping("/api/close")
    @ResponseBody
    public Map<String, Object> closeSession(@RequestBody Map<String, String> request) {
        Map<String, Object> result = new HashMap<>();
        try {
            String sessionId = request.get("sessionId");
            
            customerServiceService.closeSession(sessionId);
            
            result.put("success", true);
            result.put("message", "会话已关闭");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "关闭失败：" + e.getMessage());
            e.printStackTrace();
        }
        return result;
    }
}
