package com.zlz.mall_server.controller;

import com.zlz.mall_server.model.AdminUser;
import com.zlz.mall_server.model.Engineer;
import com.zlz.mall_server.model.ServiceCenter;
import com.zlz.mall_server.model.RepairOrder;
import com.zlz.mall_server.model.Products;
import com.zlz.mall_server.service.AdminUserService;

import java.math.BigDecimal;
import com.zlz.mall_server.service.EngineerService;
import com.zlz.mall_server.service.ServiceCenterService;
import com.zlz.mall_server.service.RepairOrderService;
import com.zlz.mall_server.service.ProductService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import java.math.BigDecimal;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpSession;
import jakarta.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.List;
import java.util.ArrayList;
import java.util.Set;
import java.util.UUID;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import org.springframework.web.multipart.MultipartFile;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/admin/api")
public class AdminApiController {

    @Autowired
    private EngineerService engineerService;

    @Autowired
    private ServiceCenterService serviceCenterService;

    @Autowired
    private ProductService productService;

    @Autowired
    private RepairOrderService repairOrderService;

    @Autowired
    private AdminUserService adminUserService;

    /**
     * 审核工程师申请
     */
    @PutMapping("/engineers/{id}/review")
    public Map<String, Object> reviewEngineer(@PathVariable Long id,
                                            @RequestParam String action,
                                            @RequestParam(required = false) String notes,
                                            jakarta.servlet.http.HttpServletRequest request) {
        Map<String, Object> result = new HashMap<>();

        try {
            jakarta.servlet.http.HttpSession session = request.getSession(false);
            if (session == null) {
                result.put("success", false);
                result.put("message", "请先登录");
                return result;
            }

            AdminUser admin = (AdminUser) session.getAttribute("admin");
            if (admin == null) {
                result.put("success", false);
                result.put("message", "请先登录");
                return result;
            }

            Engineer engineer = engineerService.getById(id);
            if (engineer == null) {
                result.put("success", false);
                result.put("message", "工程师不存在");
                return result;
            }

            // 更新工程师状态
            if ("approve".equals(action)) {
                engineer.setStatus("approved");
                // 工程师实体没有isActive字段，使用status字段管理状态
                engineer.setReviewTime(LocalDateTime.now());
                engineer.setReviewerId(admin.getId());
                engineer.setReviewNotes(notes);
                result.put("message", "工程师申请已通过");
            } else if ("reject".equals(action)) {
                engineer.setStatus("rejected");
                engineer.setReviewTime(LocalDateTime.now());
                engineer.setReviewerId(admin.getId());
                engineer.setReviewNotes(notes);
                result.put("message", "工程师申请已拒绝");
            } else {
                result.put("success", false);
                result.put("message", "无效的操作");
                return result;
            }

            boolean updated = engineerService.updateById(engineer);
            result.put("success", updated);

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "操作失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 更新工程师状态
     */
    @PutMapping("/engineers/{id}/status")
    public Map<String, Object> updateEngineerStatus(@PathVariable Long id,
                                                   @RequestParam String status,
                                                   @RequestParam(required = false) String notes,
                                                   jakarta.servlet.http.HttpServletRequest request) {
        Map<String, Object> result = new HashMap<>();

        try {
            jakarta.servlet.http.HttpSession session = request.getSession(false);
            if (session == null) {
                result.put("success", false);
                result.put("message", "请先登录");
                return result;
            }

            AdminUser admin = (AdminUser) session.getAttribute("admin");
            if (admin == null) {
                result.put("success", false);
                result.put("message", "请先登录");
                return result;
            }

            Engineer engineer = engineerService.getById(id);
            if (engineer == null) {
                result.put("success", false);
                result.put("message", "工程师不存在");
                return result;
            }

            // 验证状态转换的合法性
            String currentStatus = engineer.getStatus();
            if (!isValidStatusTransition(currentStatus, status, "engineer")) {
                result.put("success", false);
                result.put("message", "无效的状态转换");
                return result;
            }

            engineer.setStatus(status);
            engineer.setUpdatedAt(LocalDateTime.now());
            if (notes != null && !notes.trim().isEmpty()) {
                engineer.setReviewNotes(notes);
            }

            boolean updated = engineerService.updateById(engineer);
            result.put("success", updated);
            result.put("message", updated ? "状态更新成功" : "状态更新失败");

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "操作失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 获取工程师详情
     */
    @GetMapping("/engineers/{id}/detail")
    public Map<String, Object> getEngineerDetail(@PathVariable Long id,
                                                jakarta.servlet.http.HttpServletRequest request) {
        Map<String, Object> result = new HashMap<>();

        try {
            jakarta.servlet.http.HttpSession session = request.getSession(false);
            if (session == null) {
                result.put("success", false);
                result.put("message", "请先登录");
                return result;
            }

            AdminUser admin = (AdminUser) session.getAttribute("admin");
            if (admin == null) {
                result.put("success", false);
                result.put("message", "请先登录");
                return result;
            }

            Engineer engineer = engineerService.getById(id);
            if (engineer == null) {
                result.put("success", false);
                result.put("message", "工程师不存在");
                return result;
            }

            result.put("success", true);
            result.put("data", engineer);
            result.put("message", "获取工程师详情成功");

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取详情失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 添加服务网点
     */
    @PostMapping("/service-centers")
    public Map<String, Object> addServiceCenter(@RequestParam Map<String, String> params,
                                               jakarta.servlet.http.HttpServletRequest request) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 验证必填字段
            String[] requiredFields = {"name", "contactPerson", "phone", "province", "city", "district", "address"};
            for (String field : requiredFields) {
                if (params.get(field) == null || params.get(field).trim().isEmpty()) {
                    result.put("success", false);
                    result.put("message", "缺少必填字段：" + field);
                    return result;
                }
            }

            // 检查电话号码是否已存在
            ServiceCenter existingServiceCenter = serviceCenterService.getOne(
                new QueryWrapper<ServiceCenter>().eq("phone", params.get("phone"))
            );
            if (existingServiceCenter != null) {
                result.put("success", false);
                result.put("message", "该电话号码已被使用");
                return result;
            }

            // 创建服务网点对象
            ServiceCenter serviceCenter = new ServiceCenter();

            // 基本信息
            serviceCenter.setName(params.get("name"));
            serviceCenter.setContactPerson(params.get("contactPerson"));
            serviceCenter.setPhone(params.get("phone"));
            serviceCenter.setEmail(params.get("email"));
            serviceCenter.setAddress(params.get("address"));
            serviceCenter.setProvince(params.get("province"));
            serviceCenter.setCity(params.get("city"));
            serviceCenter.setDistrict(params.get("district"));

            // 位置信息
            if (params.get("longitude") != null && !params.get("longitude").trim().isEmpty()) {
                serviceCenter.setLongitude(new BigDecimal(params.get("longitude")));
            }
            if (params.get("latitude") != null && !params.get("latitude").trim().isEmpty()) {
                serviceCenter.setLatitude(new BigDecimal(params.get("latitude")));
            }

            // 营业信息
            serviceCenter.setBusinessHours(params.get("businessHours"));
            serviceCenter.setBusinessDays(params.get("businessDays"));
            serviceCenter.setIs24Hours("on".equals(params.get("is24Hours")));

            // 服务信息
            serviceCenter.setServiceTypes(params.get("serviceTypes"));
            serviceCenter.setServiceDescription(params.get("serviceDescription"));

            // 费用信息
            if (params.get("serviceFee") != null && !params.get("serviceFee").trim().isEmpty()) {
                serviceCenter.setServiceFee(new BigDecimal(params.get("serviceFee")));
            } else {
                serviceCenter.setServiceFee(new BigDecimal("0.00"));
            }

            // 状态信息
            serviceCenter.setStatus(params.getOrDefault("status", "approved"));
            serviceCenter.setIsActive("on".equals(params.get("isActive")));
            serviceCenter.setIsFeatured("on".equals(params.get("isFeatured")));

            // 默认评价信息
            serviceCenter.setRating(new BigDecimal("5.00"));
            serviceCenter.setReviewCount(0);
            serviceCenter.setTotalServices(0);

            // 时间信息
            LocalDateTime now = LocalDateTime.now();
            serviceCenter.setCreatedAt(now);
            serviceCenter.setUpdatedAt(now);
            serviceCenter.setApplicationTime(now);
            if ("approved".equals(serviceCenter.getStatus())) {
                serviceCenter.setReviewTime(now);
            }

            // 保存到数据库
            boolean saved = serviceCenterService.save(serviceCenter);

            if (saved) {
                result.put("success", true);
                result.put("message", "服务网点添加成功");
                result.put("data", serviceCenter);
            } else {
                result.put("success", false);
                result.put("message", "服务网点添加失败");
            }

        } catch (Exception e) {
            log.error("添加服务网点失败", e);
            result.put("success", false);
            result.put("message", "添加失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 审核服务网点申请
     */
    @PutMapping("/service-centers/{id}/review")
    public Map<String, Object> reviewServiceCenter(@PathVariable Long id,
                                                  @RequestParam String action,
                                                  @RequestParam(required = false) String notes,
                                                  jakarta.servlet.http.HttpServletRequest request) {
        Map<String, Object> result = new HashMap<>();

        try {
            jakarta.servlet.http.HttpSession session = request.getSession(false);
            if (session == null) {
                result.put("success", false);
                result.put("message", "请先登录");
                return result;
            }

            AdminUser admin = (AdminUser) session.getAttribute("admin");
            if (admin == null) {
                result.put("success", false);
                result.put("message", "请先登录");
                return result;
            }

            ServiceCenter serviceCenter = serviceCenterService.getById(id);
            if (serviceCenter == null) {
                result.put("success", false);
                result.put("message", "服务网点不存在");
                return result;
            }

            // 更新服务网点状态
            if ("approve".equals(action)) {
                serviceCenter.setStatus("approved");
                serviceCenter.setIsActive(true); // 审核通过时激活服务网点
                serviceCenter.setReviewTime(LocalDateTime.now());
                serviceCenter.setReviewerId(admin.getId());
                serviceCenter.setReviewNotes(notes);
                result.put("message", "服务网点申请已通过");
            } else if ("reject".equals(action)) {
                serviceCenter.setStatus("rejected");
                serviceCenter.setIsActive(false); // 拒绝时设置为未激活
                serviceCenter.setReviewTime(LocalDateTime.now());
                serviceCenter.setReviewerId(admin.getId());
                serviceCenter.setReviewNotes(notes);
                serviceCenter.setRejectionReason(notes);
                result.put("message", "服务网点申请已拒绝");
            } else {
                result.put("success", false);
                result.put("message", "无效的操作");
                return result;
            }

            boolean updated = serviceCenterService.updateById(serviceCenter);
            result.put("success", updated);

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "操作失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 更新服务网点状态
     */
    @PutMapping("/service-centers/{id}/status")
    public Map<String, Object> updateServiceCenterStatus(@PathVariable Long id,
                                                        @RequestParam String status,
                                                        @RequestParam(required = false) String notes,
                                                        jakarta.servlet.http.HttpServletRequest request) {
        Map<String, Object> result = new HashMap<>();

        try {
            jakarta.servlet.http.HttpSession session = request.getSession(false);
            if (session == null) {
                result.put("success", false);
                result.put("message", "请先登录");
                return result;
            }

            AdminUser admin = (AdminUser) session.getAttribute("admin");
            if (admin == null) {
                result.put("success", false);
                result.put("message", "请先登录");
                return result;
            }

            ServiceCenter serviceCenter = serviceCenterService.getById(id);
            if (serviceCenter == null) {
                result.put("success", false);
                result.put("message", "服务网点不存在");
                return result;
            }

            // 验证状态转换的合法性
            String currentStatus = serviceCenter.getStatus();
            if (!isValidStatusTransition(currentStatus, status, "service_center")) {
                result.put("success", false);
                result.put("message", "无效的状态转换");
                return result;
            }

            serviceCenter.setStatus(status);
            serviceCenter.setUpdatedAt(LocalDateTime.now());
            if (notes != null && !notes.trim().isEmpty()) {
                serviceCenter.setReviewNotes(notes);
            }

            boolean updated = serviceCenterService.updateById(serviceCenter);
            result.put("success", updated);
            result.put("message", updated ? "状态更新成功" : "状态更新失败");

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "操作失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 获取服务网点详情
     */
    @GetMapping("/service-centers/{id}/detail")
    public Map<String, Object> getServiceCenterDetail(@PathVariable Long id,
                                                     jakarta.servlet.http.HttpServletRequest request) {
        Map<String, Object> result = new HashMap<>();

        try {
            jakarta.servlet.http.HttpSession session = request.getSession(false);
            if (session == null) {
                result.put("success", false);
                result.put("message", "请先登录");
                return result;
            }

            AdminUser admin = (AdminUser) session.getAttribute("admin");
            if (admin == null) {
                result.put("success", false);
                result.put("message", "请先登录");
                return result;
            }

            ServiceCenter serviceCenter = serviceCenterService.getById(id);
            if (serviceCenter == null) {
                result.put("success", false);
                result.put("message", "服务网点不存在");
                return result;
            }

            result.put("success", true);
            result.put("data", serviceCenter);
            result.put("message", "获取服务网点详情成功");

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取详情失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 验证状态转换的合法性
     */
    private boolean isValidStatusTransition(String currentStatus, String newStatus, String type) {
        if (currentStatus == null || newStatus == null) {
            return false;
        }

        // 工程师状态转换规则
        if ("engineer".equals(type)) {
            switch (currentStatus) {
                case "pending":
                    return "approved".equals(newStatus) || "rejected".equals(newStatus);
                case "approved":
                    return "suspended".equals(newStatus);
                case "suspended":
                    return "approved".equals(newStatus);
                case "rejected":
                    return "pending".equals(newStatus); // 允许重新申请
                default:
                    return false;
            }
        }

        // 服务网点状态转换规则
        if ("service_center".equals(type)) {
            switch (currentStatus) {
                case "pending":
                    return "approved".equals(newStatus) || "rejected".equals(newStatus);
                case "approved":
                    return "suspended".equals(newStatus) || "closed".equals(newStatus);
                case "suspended":
                    return "approved".equals(newStatus) || "closed".equals(newStatus);
                case "rejected":
                    return "pending".equals(newStatus); // 允许重新申请
                case "closed":
                    return "approved".equals(newStatus); // 允许重新开放
                default:
                    return false;
            }
        }

        return false;
    }

    /**
     * 删除工程师
     */
    @DeleteMapping("/engineers/{id}")
    public Map<String, Object> deleteEngineer(@PathVariable Long id, jakarta.servlet.http.HttpServletRequest request) {
        Map<String, Object> result = new HashMap<>();

        try {
            jakarta.servlet.http.HttpSession session = request.getSession(false);
            if (session == null) {
                result.put("success", false);
                result.put("message", "请先登录");
                return result;
            }

            AdminUser admin = (AdminUser) session.getAttribute("admin");
            if (admin == null) {
                result.put("success", false);
                result.put("message", "请先登录");
                return result;
            }

            boolean deleted = engineerService.removeById(id);
            if (deleted) {
                result.put("success", true);
                result.put("message", "删除成功");
            } else {
                result.put("success", false);
                result.put("message", "删除失败");
            }

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "删除失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 删除服务网点
     */
    @DeleteMapping("/service-centers/{id}")
    public Map<String, Object> deleteServiceCenter(@PathVariable Long id, jakarta.servlet.http.HttpServletRequest request) {
        Map<String, Object> result = new HashMap<>();

        try {
            jakarta.servlet.http.HttpSession session = request.getSession(false);
            if (session == null) {
                result.put("success", false);
                result.put("message", "请先登录");
                return result;
            }

            AdminUser admin = (AdminUser) session.getAttribute("admin");
            if (admin == null) {
                result.put("success", false);
                result.put("message", "请先登录");
                return result;
            }

            boolean deleted = serviceCenterService.removeById(id);
            if (deleted) {
                result.put("success", true);
                result.put("message", "删除成功");
            } else {
                result.put("success", false);
                result.put("message", "删除失败");
            }

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "删除失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 更新订单状态
     */
    @PutMapping("/orders/{id}/status")
    public Map<String, Object> updateOrderStatus(@PathVariable Long id,
                                                @RequestParam String status,
                                                @RequestParam(required = false) String remark,
                                                jakarta.servlet.http.HttpServletRequest request) {
        Map<String, Object> result = new HashMap<>();

        try {
            jakarta.servlet.http.HttpSession session = request.getSession(false);
            if (session == null) {
                result.put("success", false);
                result.put("message", "请先登录");
                return result;
            }

            AdminUser admin = (AdminUser) session.getAttribute("admin");
            if (admin == null) {
                result.put("success", false);
                result.put("message", "请先登录");
                return result;
            }

            RepairOrder order = repairOrderService.getById(id);
            if (order == null) {
                result.put("success", false);
                result.put("message", "订单不存在");
                return result;
            }

            // 验证状态转换的合法性
            if (!isValidStatusTransition(order.getStatus(), status)) {
                result.put("success", false);
                result.put("message", "无效的状态转换");
                return result;
            }

            // 使用service方法更新订单状态，这样会自动处理工程师统计
            String oldStatus = order.getStatus();
            boolean updated = repairOrderService.updateOrderStatus(id, status, remark);

            if (updated) {
                result.put("success", true);
                result.put("message", "状态更新成功");
                result.put("newStatus", getStatusText(status));
            } else {
                result.put("success", false);
                result.put("message", "状态更新失败");
            }

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "操作失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 分配工程师
     */
    @PutMapping("/orders/{id}/assign")
    public Map<String, Object> assignEngineer(@PathVariable Long id,
                                             @RequestParam Long engineerId,
                                             jakarta.servlet.http.HttpServletRequest request) {
        Map<String, Object> result = new HashMap<>();

        try {
            jakarta.servlet.http.HttpSession session = request.getSession(false);
            if (session == null) {
                result.put("success", false);
                result.put("message", "请先登录");
                return result;
            }

            AdminUser admin = (AdminUser) session.getAttribute("admin");
            if (admin == null) {
                result.put("success", false);
                result.put("message", "请先登录");
                return result;
            }

            RepairOrder order = repairOrderService.getById(id);
            if (order == null) {
                result.put("success", false);
                result.put("message", "订单不存在");
                return result;
            }

            Engineer engineer = engineerService.getById(engineerId);
            if (engineer == null) {
                result.put("success", false);
                result.put("message", "工程师不存在");
                return result;
            }

            // 使用service方法分配工程师，这样会自动处理工程师统计
            boolean updated = repairOrderService.assignEngineer(id, engineerId, engineer.getName(), engineer.getPhone());

            if (updated) {
                result.put("success", true);
                result.put("message", "工程师分配成功");
                result.put("engineerName", engineer.getName());
            } else {
                result.put("success", false);
                result.put("message", "分配失败");
            }

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "操作失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 获取可用工程师列表
     */
    @GetMapping("/engineers/available")
    public Map<String, Object> getAvailableEngineers(jakarta.servlet.http.HttpServletRequest request) {
        Map<String, Object> result = new HashMap<>();

        try {
            jakarta.servlet.http.HttpSession session = request.getSession(false);
            if (session == null) {
                result.put("success", false);
                result.put("message", "请先登录");
                return result;
            }

            AdminUser admin = (AdminUser) session.getAttribute("admin");
            if (admin == null) {
                result.put("success", false);
                result.put("message", "请先登录");
                return result;
            }

            // 查询已审核通过且可用的工程师
            QueryWrapper<Engineer> query = new QueryWrapper<>();
            query.eq("status", "approved")
                 .eq("is_available", true)
                 .orderByDesc("rating")
                 .orderByDesc("completed_orders");

            List<Engineer> engineers = engineerService.list(query);

            result.put("success", true);
            result.put("data", engineers);
            result.put("message", "获取工程师列表成功");

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取工程师列表失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 完成订单
     */
    @PutMapping("/orders/{id}/complete")
    public Map<String, Object> completeOrder(@PathVariable Long id,
                                            @RequestBody Map<String, Object> completeData,
                                            jakarta.servlet.http.HttpServletRequest request) {
        Map<String, Object> result = new HashMap<>();

        try {
            jakarta.servlet.http.HttpSession session = request.getSession(false);
            if (session == null) {
                result.put("success", false);
                result.put("message", "请先登录");
                return result;
            }

            AdminUser admin = (AdminUser) session.getAttribute("admin");
            if (admin == null) {
                result.put("success", false);
                result.put("message", "请先登录");
                return result;
            }

            RepairOrder order = repairOrderService.getById(id);
            if (order == null) {
                result.put("success", false);
                result.put("message", "订单不存在");
                return result;
            }

            // 先更新订单详细信息
            order.setRepairResult(completeData.get("repairResult").toString());
            order.setRepairTime(new BigDecimal(completeData.get("repairTime").toString()));
            order.setRepairDescription(completeData.get("repairDescription").toString());

            // 费用明细
            order.setLaborFee(new BigDecimal(completeData.get("laborFee").toString()));
            order.setServiceFee(new BigDecimal(completeData.get("serviceFee").toString()));
            order.setMaterialsFee(new BigDecimal(completeData.get("materialsFee").toString()));
            order.setTotalFee(new BigDecimal(completeData.get("totalAmount").toString()));

            // 耗材明细（JSON格式存储）
            order.setMaterialsDetail(convertMaterialsToJson(completeData.get("materials")));

            // 备注和账单状态
            order.setRemark(completeData.get("remark").toString());
            order.setBillSent(true);
            order.setBillSentTime(LocalDateTime.now());
            order.setUpdatedAt(LocalDateTime.now());

            // 先保存详细信息
            boolean detailUpdated = repairOrderService.updateById(order);

            // 然后使用updateOrderStatus方法更新状态，这样会触发工程师统计更新
            boolean statusUpdated = false;
            if (detailUpdated) {
                statusUpdated = repairOrderService.updateOrderStatus(id, "completed", order.getRemark());
            }

            boolean updated = detailUpdated && statusUpdated;

            if (updated) {
                // 这里可以发送账单给用户（短信、微信消息等）
                sendBillToUser(order, completeData);

                result.put("success", true);
                result.put("message", "订单完成成功，账单已发送给用户");
            } else {
                result.put("success", false);
                result.put("message", "订单完成失败");
            }

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "操作失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 发送账单给用户
     */
    private void sendBillToUser(RepairOrder order, Map<String, Object> completeData) {
        try {
            // 这里可以实现发送账单的逻辑
            // 1. 生成账单PDF
            // 2. 发送微信消息
            // 3. 发送短信通知
            // 4. 保存账单记录

            System.out.println("=== 维修账单 ===");
            System.out.println("订单编号：" + order.getOrderNo());
            System.out.println("客户姓名：" + order.getName());
            System.out.println("联系电话：" + order.getPhone());
            System.out.println("维修地址：" + order.getFullAddress());
            System.out.println("故障类型：" + order.getFaultType());
            System.out.println("设备型号：" + order.getModel());
            System.out.println("工程师：" + order.getEngineerName());
            System.out.println("维修结果：" + completeData.get("repairResult"));
            System.out.println("维修耗时：" + completeData.get("repairTime") + "小时");
            System.out.println("维修详情：" + completeData.get("repairDescription"));
            System.out.println("人工费：¥" + completeData.get("laborFee"));
            System.out.println("服务费：¥" + completeData.get("serviceFee"));
            System.out.println("耗材费：¥" + completeData.get("materialsFee"));
            System.out.println("总费用：¥" + completeData.get("totalAmount"));
            System.out.println("================");

        } catch (Exception e) {
            System.err.println("发送账单失败：" + e.getMessage());
        }
    }

    /**
     * 获取订单详情
     */
    @GetMapping("/orders/{id}/detail")
    public Map<String, Object> getOrderDetail(@PathVariable Long id, jakarta.servlet.http.HttpServletRequest request) {
        Map<String, Object> result = new HashMap<>();

        try {
            jakarta.servlet.http.HttpSession session = request.getSession(false);
            if (session == null) {
                result.put("success", false);
                result.put("message", "请先登录");
                return result;
            }

            AdminUser admin = (AdminUser) session.getAttribute("admin");
            if (admin == null) {
                result.put("success", false);
                result.put("message", "请先登录");
                return result;
            }

            RepairOrder order = repairOrderService.getById(id);
            if (order == null) {
                result.put("success", false);
                result.put("message", "订单不存在");
                return result;
            }

            result.put("success", true);
            result.put("data", order);
            result.put("message", "获取订单详情成功");

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取订单详情失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 将耗材列表转换为JSON字符串
     */
    private String convertMaterialsToJson(Object materials) {
        try {
            if (materials == null) {
                return "[]";
            }

            // 如果已经是字符串，直接返回
            if (materials instanceof String) {
                return (String) materials;
            }

            // 如果是List，转换为JSON
            if (materials instanceof List) {
                List<?> materialsList = (List<?>) materials;
                StringBuilder json = new StringBuilder("[");

                for (int i = 0; i < materialsList.size(); i++) {
                    if (i > 0) json.append(",");

                    Map<?, ?> material = (Map<?, ?>) materialsList.get(i);
                    json.append("{")
                        .append("\"name\":\"").append(material.get("name")).append("\",")
                        .append("\"specification\":\"").append(material.get("specification")).append("\",")
                        .append("\"quantity\":").append(material.get("quantity")).append(",")
                        .append("\"unitPrice\":").append(material.get("unitPrice")).append(",")
                        .append("\"subtotal\":").append(material.get("subtotal"))
                        .append("}");
                }

                json.append("]");
                return json.toString();
            }

            return "[]";
        } catch (Exception e) {
            System.err.println("转换耗材JSON失败：" + e.getMessage());
            return "[]";
        }
    }

    /**
     * 验证状态转换是否合法
     */
    private boolean isValidStatusTransition(String currentStatus, String newStatus) {
        // 定义合法的状态转换
        switch (currentStatus) {
            case "pending":
                return "accepted".equals(newStatus);
            case "accepted":
                return "processing".equals(newStatus);
            case "processing":
                return "completed".equals(newStatus);
            case "completed":
                return false; // 已完成的订单不能再改变状态
            default:
                return false;
        }
    }

    /**
     * 获取状态的中文描述
     */
    private String getStatusText(String status) {
        switch (status) {
            case "pending":
                return "待接单";
            case "accepted":
                return "待上门";
            case "processing":
                return "维修中";
            case "completed":
                return "已完成";
            default:
                return status;
        }
    }

    /**
     * 修复已审核通过但未激活的服务网点（临时修复接口）
     */
    @PostMapping("/service-centers/fix-active")
    public Map<String, Object> fixInactiveApprovedServiceCenters() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 获取所有已审核通过但未激活的服务网点
            List<ServiceCenter> allServiceCenters = serviceCenterService.list();
            int fixedCount = 0;

            for (ServiceCenter serviceCenter : allServiceCenters) {
                // 如果状态是已审核通过但未激活，则激活
                if ("approved".equals(serviceCenter.getStatus()) && !Boolean.TRUE.equals(serviceCenter.getIsActive())) {
                    serviceCenter.setIsActive(true);
                    serviceCenter.setUpdatedAt(LocalDateTime.now());
                    serviceCenterService.updateById(serviceCenter);
                    fixedCount++;
                }
            }

            result.put("success", true);
            result.put("message", "修复完成，共激活 " + fixedCount + " 个服务网点");
            result.put("data", Map.of("fixedCount", fixedCount));

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "修复失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 编辑工程师信息
     */
    @PutMapping("/engineers/{id}")
    public Map<String, Object> editEngineer(@PathVariable Long id,
                                          @RequestParam Map<String, String> params,
                                          HttpServletRequest request) {
        Map<String, Object> result = new HashMap<>();

        try {
            jakarta.servlet.http.HttpSession session = request.getSession(false);
            if (session == null) {
                result.put("success", false);
                result.put("message", "请先登录");
                return result;
            }

            AdminUser admin = (AdminUser) session.getAttribute("admin");
            if (admin == null) {
                result.put("success", false);
                result.put("message", "请先登录");
                return result;
            }

            // 获取现有工程师信息
            Engineer engineer = engineerService.getById(id);
            if (engineer == null) {
                result.put("success", false);
                result.put("message", "工程师不存在");
                return result;
            }

            // 更新基本信息
            if (params.containsKey("name")) {
                engineer.setName(params.get("name"));
            }
            if (params.containsKey("phone")) {
                engineer.setPhone(params.get("phone"));
            }
            if (params.containsKey("email")) {
                engineer.setEmail(params.get("email"));
            }
            if (params.containsKey("gender")) {
                engineer.setGender(params.get("gender"));
            }
            if (params.containsKey("age")) {
                try {
                    engineer.setAge(Integer.parseInt(params.get("age")));
                } catch (NumberFormatException e) {
                    // 忽略无效的年龄值
                }
            }
            if (params.containsKey("education")) {
                engineer.setEducation(params.get("education"));
            }
            if (params.containsKey("experienceYears")) {
                try {
                    engineer.setExperienceYears(Integer.parseInt(params.get("experienceYears")));
                } catch (NumberFormatException e) {
                    // 忽略无效的经验年数
                }
            }
            if (params.containsKey("workTime")) {
                engineer.setWorkTime(params.get("workTime"));
            }
            if (params.containsKey("introduction")) {
                engineer.setIntroduction(params.get("introduction"));
            }

            // 更新专业信息
            if (params.containsKey("specialties")) {
                engineer.setSpecialties(params.get("specialties"));
            }
            if (params.containsKey("skills")) {
                engineer.setSkills(params.get("skills"));
            }
            if (params.containsKey("workAreas")) {
                engineer.setWorkAreas(params.get("workAreas"));
            }

            // 更新服务信息
            if (params.containsKey("hourlyRate")) {
                try {
                    engineer.setHourlyRate(new BigDecimal(params.get("hourlyRate")));
                } catch (NumberFormatException e) {
                    // 忽略无效的时薪值
                }
            }
            if (params.containsKey("serviceFee")) {
                try {
                    engineer.setServiceFee(new BigDecimal(params.get("serviceFee")));
                } catch (NumberFormatException e) {
                    // 忽略无效的服务费值
                }
            }
            if (params.containsKey("rating")) {
                try {
                    engineer.setRating(new BigDecimal(params.get("rating")));
                } catch (NumberFormatException e) {
                    // 忽略无效的评分值
                }
            }

            // 更新状态信息
            if (params.containsKey("status")) {
                engineer.setStatus(params.get("status"));
            }
            if (params.containsKey("isOnline")) {
                engineer.setIsOnline("true".equals(params.get("isOnline")));
            }
            if (params.containsKey("isAvailable")) {
                engineer.setIsAvailable("true".equals(params.get("isAvailable")));
            }
            if (params.containsKey("reviewNotes")) {
                engineer.setReviewNotes(params.get("reviewNotes"));
            }

            // 设置更新时间
            engineer.setUpdatedAt(LocalDateTime.now());

            // 保存更新
            boolean updated = engineerService.updateById(engineer);

            if (updated) {
                result.put("success", true);
                result.put("message", "工程师信息更新成功");
                result.put("data", engineer);
            } else {
                result.put("success", false);
                result.put("message", "工程师信息更新失败");
            }

        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "更新失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 创建管理员用户
     */
    @PostMapping("/admin-users")
    public Map<String, Object> createAdminUser(@RequestBody Map<String, String> params,
                                             HttpServletRequest request) {
        Map<String, Object> result = new HashMap<>();

        try {
            jakarta.servlet.http.HttpSession session = request.getSession(false);
            if (session == null) {
                result.put("success", false);
                result.put("message", "请先登录");
                return result;
            }

            AdminUser currentAdmin = (AdminUser) session.getAttribute("admin");
            if (currentAdmin == null) {
                result.put("success", false);
                result.put("message", "请先登录");
                return result;
            }

            // 只有超级管理员可以创建新管理员
            if (!"super_admin".equals(currentAdmin.getRole())) {
                result.put("success", false);
                result.put("message", "权限不足，只有超级管理员可以创建新管理员");
                return result;
            }

            // 验证必填字段
            String username = params.get("username");
            String password = params.get("password");
            String realName = params.get("realName");

            if (username == null || username.trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "用户名不能为空");
                return result;
            }

            if (password == null || password.trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "密码不能为空");
                return result;
            }

            if (realName == null || realName.trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "真实姓名不能为空");
                return result;
            }

            // 检查用户名是否已存在
            if (adminUserService.existsByUsername(username)) {
                result.put("success", false);
                result.put("message", "用户名已存在");
                return result;
            }

            // 创建管理员对象
            AdminUser newAdmin = new AdminUser();
            newAdmin.setUsername(username);
            newAdmin.setPassword(password); // 服务层会自动加密
            newAdmin.setRealName(realName);
            newAdmin.setEmail(params.get("email"));
            newAdmin.setPhone(params.get("phone"));
            newAdmin.setRole(params.getOrDefault("role", "admin"));
            newAdmin.setPermissions(params.get("permissions"));
            newAdmin.setCreatedBy(currentAdmin.getId());

            // 创建管理员
            boolean created = adminUserService.createAdminUser(newAdmin);

            if (created) {
                result.put("success", true);
                result.put("message", "管理员创建成功");
                result.put("data", Map.of(
                    "id", newAdmin.getId(),
                    "username", newAdmin.getUsername(),
                    "realName", newAdmin.getRealName(),
                    "role", newAdmin.getRole()
                ));
            } else {
                result.put("success", false);
                result.put("message", "管理员创建失败");
            }

        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "创建失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 测试工程师服务次数增加功能
     */
    @PostMapping("/test/engineer/{engineerId}/increment-service")
    public Map<String, Object> testIncrementEngineerService(@PathVariable Long engineerId) {
        Map<String, Object> result = new HashMap<>();

        try {
            System.out.println("🧪 开始测试工程师 " + engineerId + " 服务次数增加功能");

            // 获取更新前的数据
            Engineer beforeEngineer = engineerService.getById(engineerId);
            if (beforeEngineer == null) {
                result.put("success", false);
                result.put("message", "工程师不存在");
                return result;
            }

            System.out.println("📊 更新前数据: 总接单数=" + beforeEngineer.getTotalOrders() +
                             ", 完成订单数=" + beforeEngineer.getCompletedOrders() +
                             ", 成功率=" + beforeEngineer.getSuccessRate());

            // 调用增加服务次数方法
            boolean incrementResult = engineerService.incrementServiceCount(engineerId);

            // 获取更新后的数据
            Engineer afterEngineer = engineerService.getById(engineerId);

            System.out.println("📊 更新后数据: 总接单数=" + afterEngineer.getTotalOrders() +
                             ", 完成订单数=" + afterEngineer.getCompletedOrders() +
                             ", 成功率=" + afterEngineer.getSuccessRate());

            result.put("success", incrementResult);
            result.put("message", incrementResult ? "测试成功" : "测试失败");
            result.put("beforeData", Map.of(
                "totalOrders", beforeEngineer.getTotalOrders(),
                "completedOrders", beforeEngineer.getCompletedOrders(),
                "successRate", beforeEngineer.getSuccessRate()
            ));
            result.put("afterData", Map.of(
                "totalOrders", afterEngineer.getTotalOrders(),
                "completedOrders", afterEngineer.getCompletedOrders(),
                "successRate", afterEngineer.getSuccessRate()
            ));

        } catch (Exception e) {
            System.out.println("💥 测试过程中发生异常: " + e.getMessage());
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "测试失败：" + e.getMessage());
        }

        return result;
    }

    // ========== 产品管理相关API ==========

    /**
     * 添加产品
     */
    @PostMapping("/products")
    public Map<String, Object> addProduct(@RequestParam Map<String, String> params,
                                         @RequestParam(value = "mainImageFile", required = false) MultipartFile mainImageFile,
                                         @RequestParam(value = "imageFiles", required = false) MultipartFile[] imageFiles,
                                         @RequestParam(value = "detailImageFile", required = false) MultipartFile detailImageFile,
                                         jakarta.servlet.http.HttpServletRequest request) {
        Map<String, Object> result = new HashMap<>();

        try {
            System.out.println("🚀 开始添加产品，接收到的参数：" + params);

            // 验证必填字段
            String[] requiredFields = {"name", "categoryId", "price", "stock"};
            for (String field : requiredFields) {
                if (params.get(field) == null || params.get(field).trim().isEmpty()) {
                    result.put("success", false);
                    result.put("message", "缺少必填字段：" + field);
                    return result;
                }
            }

            // 验证主图
            if (mainImageFile == null || mainImageFile.isEmpty()) {
                result.put("success", false);
                result.put("message", "请上传产品主图");
                return result;
            }

            // 创建产品对象
            Products product = new Products();

            // 基本信息
            product.setName(params.get("name"));
            product.setShortName(params.get("shortName")); // 新增：产品简称
            product.setBrand(params.get("brand"));
            product.setModel(params.get("model"));
            product.setCategoryId(Integer.parseInt(params.get("categoryId")));
            product.setDescription(params.get("description"));
            product.setCompatibleCars(params.get("compatibleCars")); // 新增：适用车型

            // 价格和库存
            product.setPrice(new BigDecimal(params.get("price")));
            if (params.get("originalPrice") != null && !params.get("originalPrice").trim().isEmpty()) {
                product.setOriginalPrice(new BigDecimal(params.get("originalPrice")));
            }
            product.setStock(Integer.parseInt(params.get("stock")));

            // 销量（新增）
            if (params.get("sales") != null && !params.get("sales").trim().isEmpty()) {
                product.setSales(Integer.parseInt(params.get("sales")));
            } else {
                product.setSales(0);
            }

            // 排序权重
            if (params.get("sortOrder") != null && !params.get("sortOrder").trim().isEmpty()) {
                product.setSortOrder(Integer.parseInt(params.get("sortOrder")));
            } else {
                product.setSortOrder(0);
            }

            // 物理属性（新增）
            if (params.get("weight") != null && !params.get("weight").trim().isEmpty()) {
                product.setWeight(new BigDecimal(params.get("weight")));
            }
            product.setDimensions(params.get("dimensions"));
            product.setWarrantyPeriod(params.get("warrantyPeriod"));

            // JSON字段处理
            product.setFeatures(params.get("features")); // JSON数组
            product.setSpecifications(params.get("specifications")); // JSON对象数组
            product.setSpecs(params.get("specs")); // JSON数组
            product.setServices(params.get("services")); // JSON数组

            // SEO字段（新增）
            product.setSeoTitle(params.get("seoTitle"));
            product.setSeoKeywords(params.get("seoKeywords"));
            product.setSeoDescription(params.get("seoDescription"));

            // 状态设置
            product.setStatus("1".equals(params.get("status")) ? 1 : 0);

            System.out.println("📋 产品对象创建完成：" + product.getName());

            // 时间信息
            LocalDateTime now = LocalDateTime.now();
            product.setCreatedAt(now);
            product.setUpdatedAt(now);

            // 上传主图
            String mainImageUrl = uploadProductImage(mainImageFile, "main");
            product.setMainImage(mainImageUrl);
            System.out.println("📸 主图上传成功：" + mainImageUrl);

            // 上传轮播图片
            if (imageFiles != null && imageFiles.length > 0) {
                List<String> imageUrls = new ArrayList<>();
                for (MultipartFile file : imageFiles) {
                    if (!file.isEmpty()) {
                        String imageUrl = uploadProductImage(file, "image");
                        imageUrls.add(imageUrl);
                    }
                }
                if (!imageUrls.isEmpty()) {
                    // 将图片URL列表转换为逗号分隔字符串存储到images字段
                    product.setImages(String.join(",", imageUrls));
                    System.out.println("🖼️ 轮播图片上传成功：" + imageUrls.size() + "张");
                }
            }

            // 上传详情图片
            if (detailImageFile != null && !detailImageFile.isEmpty()) {
                String detailImageUrl = uploadProductImage(detailImageFile, "detail");
                product.setDetailImage(detailImageUrl);
                System.out.println("📄 详情图片上传成功：" + detailImageUrl);
            }

            // 保存到数据库
            System.out.println("💾 开始保存产品到数据库...");
            System.out.println("📋 产品信息：" +
                "名称=" + product.getName() +
                ", 简称=" + product.getShortName() +
                ", 品牌=" + product.getBrand() +
                ", 型号=" + product.getModel() +
                ", 特点=" + product.getFeatures() +
                ", 规格=" + product.getSpecifications() +
                ", 服务=" + product.getServices());

            boolean saved = productService.save(product);

            if (saved) {
                System.out.println("✅ 产品保存成功，ID：" + product.getId());
                result.put("success", true);
                result.put("message", "产品添加成功");
                result.put("data", Map.of(
                    "productId", product.getId(),
                    "name", product.getName(),
                    "mainImage", product.getMainImage()
                ));
            } else {
                System.out.println("❌ 产品保存失败");
                result.put("success", false);
                result.put("message", "产品添加失败");
            }

        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("🚨 添加产品时发生异常：" + e.getMessage());
            result.put("success", false);
            result.put("message", "添加产品失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 切换产品状态
     */
    @PutMapping("/products/{id}/status")
    public Map<String, Object> toggleProductStatus(@PathVariable Long id, @RequestBody Map<String, Object> requestBody) {
        Map<String, Object> result = new HashMap<>();

        try {
            Products product = productService.getById(id);
            if (product == null) {
                result.put("success", false);
                result.put("message", "产品不存在");
                return result;
            }

            Integer newStatus = (Integer) requestBody.get("status");
            product.setStatus(newStatus);
            product.setUpdatedAt(LocalDateTime.now());

            boolean updated = productService.updateById(product);

            if (updated) {
                result.put("success", true);
                result.put("message", newStatus == 1 ? "产品上架成功" : "产品下架成功");
            } else {
                result.put("success", false);
                result.put("message", "状态更新失败");
            }

        } catch (Exception e) {
            log.error("切换产品状态失败", e);
            result.put("success", false);
            result.put("message", "操作失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 获取产品详情
     */
    @GetMapping("/products/{id}")
    public Map<String, Object> getProductDetail(@PathVariable Long id) {
        Map<String, Object> result = new HashMap<>();

        try {
            Products product = productService.getById(id);
            if (product == null) {
                result.put("success", false);
                result.put("message", "产品不存在");
                return result;
            }

            result.put("success", true);
            result.put("message", "获取产品详情成功");
            result.put("data", product);

        } catch (Exception e) {
            log.error("获取产品详情失败", e);
            result.put("success", false);
            result.put("message", "获取产品详情失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 编辑产品
     */
    @PutMapping("/products/{id}")
    public Map<String, Object> editProduct(@PathVariable Long id,
                                          @RequestParam Map<String, String> params,
                                          @RequestParam(value = "mainImageFile", required = false) MultipartFile mainImageFile,
                                          @RequestParam(value = "imageFiles", required = false) MultipartFile[] imageFiles,
                                          @RequestParam(value = "detailImageFile", required = false) MultipartFile detailImageFile,
                                          jakarta.servlet.http.HttpServletRequest request) {
        Map<String, Object> result = new HashMap<>();

        try {
            System.out.println("🔄 开始编辑产品，ID: " + id + "，参数：" + params);

            // 获取现有产品
            Products product = productService.getById(id);
            if (product == null) {
                result.put("success", false);
                result.put("message", "产品不存在");
                return result;
            }

            // 更新基本信息
            if (params.get("name") != null) {
                product.setName(params.get("name"));
            }
            if (params.get("shortName") != null) {
                product.setShortName(params.get("shortName"));
            }
            if (params.get("brand") != null) {
                product.setBrand(params.get("brand"));
            }
            if (params.get("model") != null) {
                product.setModel(params.get("model"));
            }
            if (params.get("categoryId") != null) {
                product.setCategoryId(Integer.parseInt(params.get("categoryId")));
            }
            if (params.get("description") != null) {
                product.setDescription(params.get("description"));
            }
            if (params.get("compatibleCars") != null) {
                product.setCompatibleCars(params.get("compatibleCars"));
            }

            // 更新价格和库存
            if (params.get("price") != null) {
                product.setPrice(new BigDecimal(params.get("price")));
            }
            if (params.get("originalPrice") != null && !params.get("originalPrice").trim().isEmpty()) {
                product.setOriginalPrice(new BigDecimal(params.get("originalPrice")));
            }
            if (params.get("stock") != null) {
                product.setStock(Integer.parseInt(params.get("stock")));
            }
            if (params.get("sales") != null) {
                product.setSales(Integer.parseInt(params.get("sales")));
            }

            // 更新其他属性
            if (params.get("sortOrder") != null) {
                product.setSortOrder(Integer.parseInt(params.get("sortOrder")));
            }
            if (params.get("weight") != null && !params.get("weight").trim().isEmpty()) {
                product.setWeight(new BigDecimal(params.get("weight")));
            }
            if (params.get("dimensions") != null) {
                product.setDimensions(params.get("dimensions"));
            }
            if (params.get("warrantyPeriod") != null) {
                product.setWarrantyPeriod(params.get("warrantyPeriod"));
            }

            // 更新JSON字段
            if (params.get("features") != null) {
                product.setFeatures(params.get("features"));
            }
            if (params.get("specifications") != null) {
                product.setSpecifications(params.get("specifications"));
            }
            if (params.get("specs") != null) {
                product.setSpecs(params.get("specs"));
            }
            if (params.get("services") != null) {
                product.setServices(params.get("services"));
            }

            // 更新SEO字段
            if (params.get("seoTitle") != null) {
                product.setSeoTitle(params.get("seoTitle"));
            }
            if (params.get("seoKeywords") != null) {
                product.setSeoKeywords(params.get("seoKeywords"));
            }
            if (params.get("seoDescription") != null) {
                product.setSeoDescription(params.get("seoDescription"));
            }

            // 更新状态
            if (params.get("status") != null) {
                product.setStatus("1".equals(params.get("status")) ? 1 : 0);
            }

            // 处理图片上传
            if (mainImageFile != null && !mainImageFile.isEmpty()) {
                String mainImageUrl = uploadProductImage(mainImageFile, "main");
                product.setMainImage(mainImageUrl);
                System.out.println("📸 更新主图：" + mainImageUrl);
            }

            if (imageFiles != null && imageFiles.length > 0) {
                List<String> imageUrls = new ArrayList<>();
                for (MultipartFile file : imageFiles) {
                    if (!file.isEmpty()) {
                        String imageUrl = uploadProductImage(file, "image");
                        imageUrls.add(imageUrl);
                    }
                }
                if (!imageUrls.isEmpty()) {
                    product.setImages(String.join(",", imageUrls));
                    System.out.println("🖼️ 更新轮播图：" + imageUrls.size() + "张");
                }
            }

            if (detailImageFile != null && !detailImageFile.isEmpty()) {
                String detailImageUrl = uploadProductImage(detailImageFile, "detail");
                product.setDetailImage(detailImageUrl);
                System.out.println("📄 更新详情图：" + detailImageUrl);
            }

            // 更新时间
            product.setUpdatedAt(LocalDateTime.now());

            // 保存到数据库
            boolean updated = productService.updateById(product);

            if (updated) {
                System.out.println("✅ 产品编辑成功，ID：" + product.getId());
                result.put("success", true);
                result.put("message", "产品编辑成功");
                result.put("data", Map.of(
                    "productId", product.getId(),
                    "name", product.getName()
                ));
            } else {
                System.out.println("❌ 产品编辑失败");
                result.put("success", false);
                result.put("message", "产品编辑失败");
            }

        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("🚨 编辑产品时发生异常：" + e.getMessage());
            result.put("success", false);
            result.put("message", "编辑产品失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 删除产品
     */
    @DeleteMapping("/products/{id}")
    public Map<String, Object> deleteProduct(@PathVariable Long id) {
        Map<String, Object> result = new HashMap<>();

        try {
            Products product = productService.getById(id);
            if (product == null) {
                result.put("success", false);
                result.put("message", "产品不存在");
                return result;
            }

            boolean deleted = productService.removeById(id);

            if (deleted) {
                result.put("success", true);
                result.put("message", "产品删除成功");
            } else {
                result.put("success", false);
                result.put("message", "产品删除失败");
            }

        } catch (Exception e) {
            log.error("删除产品失败", e);
            result.put("success", false);
            result.put("message", "删除失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 上传产品图片的辅助方法
     */
    private String uploadProductImage(MultipartFile file, String type) throws Exception {
        // 验证文件
        if (file.isEmpty()) {
            throw new Exception("文件不能为空");
        }

        if (file.getSize() > 5 * 1024 * 1024) { // 5MB
            throw new Exception("文件大小不能超过5MB");
        }

        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || originalFilename.isEmpty()) {
            throw new Exception("文件名不能为空");
        }

        String extension = getFileExtension(originalFilename).toLowerCase();
        Set<String> allowedExtensions = Set.of("jpg", "jpeg", "png", "gif", "bmp", "webp");
        if (!allowedExtensions.contains(extension)) {
            throw new Exception("只支持图片格式：jpg, jpeg, png, gif, bmp, webp");
        }

        // 创建上传目录
        String uploadPath = "./uploads/";
        Path uploadDir = Paths.get(uploadPath);
        if (!Files.exists(uploadDir)) {
            Files.createDirectories(uploadDir);
        }

        // 生成唯一文件名
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String randomStr = UUID.randomUUID().toString().substring(0, 8);
        String savedFileName = "product_" + type + "_" + timestamp + "_" + randomStr + "." + extension;

        // 保存文件
        Path filePath = Paths.get(uploadPath, savedFileName);
        Files.copy(file.getInputStream(), filePath);

        // 返回访问URL
        return "/uploads/" + savedFileName;
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex == -1) {
            return "";
        }
        return filename.substring(lastDotIndex + 1);
    }
}
