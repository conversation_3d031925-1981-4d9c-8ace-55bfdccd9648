package com.zlz.mall_server.controller;

import com.zlz.mall_server.model.Products;
import com.zlz.mall_server.service.ProductFavoriteService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/favorites")
public class ProductFavoriteController {
    
    @Autowired
    private ProductFavoriteService productFavoriteService;
    
    /**
     * 添加商品收藏
     * @param requestBody 请求体包含openId和productId
     * @return 操作结果
     */
    @PostMapping("/add")
    public ResponseEntity<Map<String, Object>> addFavorite(@RequestBody Map<String, Object> requestBody) {
        String openId = (String) requestBody.get("openId");
        Long productId = Long.valueOf(requestBody.get("productId").toString());

        log.info("添加收藏: openId={}, productId={}", openId, productId);

        Map<String, Object> result = new HashMap<>();

        try {
            boolean success = productFavoriteService.addFavorite(openId, productId);
            
            if (success) {
                result.put("success", true);
                result.put("message", "收藏成功");
            } else {
                result.put("success", false);
                result.put("message", "收藏失败，可能已经收藏过了");
            }
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("添加收藏异常", e);
            result.put("success", false);
            result.put("message", "收藏失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(result);
        }
    }
    
    /**
     * 取消商品收藏
     * @param requestBody 请求体包含openId和productId
     * @return 操作结果
     */
    @PostMapping("/remove")
    public ResponseEntity<Map<String, Object>> removeFavorite(@RequestBody Map<String, Object> requestBody) {
        String openId = (String) requestBody.get("openId");
        Long productId = Long.valueOf(requestBody.get("productId").toString());

        log.info("取消收藏: openId={}, productId={}", openId, productId);

        Map<String, Object> result = new HashMap<>();

        try {
            boolean success = productFavoriteService.removeFavorite(openId, productId);
            
            if (success) {
                result.put("success", true);
                result.put("message", "取消收藏成功");
            } else {
                result.put("success", false);
                result.put("message", "取消收藏失败");
            }
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("取消收藏异常", e);
            result.put("success", false);
            result.put("message", "取消收藏失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(result);
        }
    }
    
    /**
     * 检查商品是否已收藏
     * @param openId 用户openId
     * @param productId 商品ID
     * @return 收藏状态
     */
    @GetMapping("/check")
    public ResponseEntity<Map<String, Object>> checkFavorite(@RequestParam String openId, 
                                                            @RequestParam Long productId) {
        log.info("检查收藏状态: openId={}, productId={}", openId, productId);
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            boolean isFavorite = productFavoriteService.isFavorite(openId, productId);
            
            result.put("success", true);
            result.put("isFavorite", isFavorite);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("检查收藏状态异常", e);
            result.put("success", false);
            result.put("message", "检查收藏状态失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(result);
        }
    }
    
    /**
     * 获取用户收藏的商品列表
     * @param openId 用户openId
     * @return 收藏的商品列表
     */
    @GetMapping("/list")
    public ResponseEntity<Map<String, Object>> getFavoriteProducts(@RequestParam String openId,
                                                                  jakarta.servlet.http.HttpServletRequest request) {
        log.info("获取收藏商品列表: openId={}", openId);

        Map<String, Object> result = new HashMap<>();

        try {
            List<Products> favoriteProducts = productFavoriteService.getFavoriteProducts(openId);
            int favoriteCount = productFavoriteService.getFavoriteCount(openId);

            // 处理图片URL，转换为完整的HTTPS URL
            String baseUrl = getBaseUrl(request);
            favoriteProducts.forEach(product -> {
                if (product.getMainImage() != null && !product.getMainImage().startsWith("http")) {
                    product.setMainImage(baseUrl + product.getMainImage());
                }
                if (product.getDetailImage() != null && !product.getDetailImage().startsWith("http")) {
                    product.setDetailImage(baseUrl + product.getDetailImage());
                }
                // 处理多张图片
                if (product.getImages() != null && !product.getImages().isEmpty()) {
                    String[] imageUrls = product.getImages().split(",");
                    StringBuilder fullImageUrls = new StringBuilder();
                    for (int i = 0; i < imageUrls.length; i++) {
                        String imageUrl = imageUrls[i].trim();
                        if (!imageUrl.startsWith("http")) {
                            imageUrl = baseUrl + imageUrl;
                        }
                        fullImageUrls.append(imageUrl);
                        if (i < imageUrls.length - 1) {
                            fullImageUrls.append(",");
                        }
                    }
                    product.setImages(fullImageUrls.toString());
                }
            });

            log.info("✅ 收藏商品列表处理完成，数量: {}", favoriteProducts.size());

            result.put("success", true);
            result.put("products", favoriteProducts);
            result.put("count", favoriteCount);

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取收藏商品列表异常", e);
            result.put("success", false);
            result.put("message", "获取收藏列表失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(result);
        }
    }
    
    /**
     * 获取用户收藏数量
     * @param openId 用户openId
     * @return 收藏数量
     */
    @GetMapping("/count")
    public ResponseEntity<Map<String, Object>> getFavoriteCount(@RequestParam String openId) {
        log.info("获取收藏数量: openId={}", openId);
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            int count = productFavoriteService.getFavoriteCount(openId);
            
            result.put("success", true);
            result.put("count", count);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取收藏数量异常", e);
            result.put("success", false);
            result.put("message", "获取收藏数量失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(result);
        }
    }

    /**
     * 获取基础URL
     */
    private String getBaseUrl(jakarta.servlet.http.HttpServletRequest request) {
        String scheme = request.getScheme();
        String serverName = request.getServerName();
        int serverPort = request.getServerPort();

        // 构建基础URL
        StringBuilder baseUrl = new StringBuilder();
        baseUrl.append(scheme).append("://").append(serverName);

        // 只有在非标准端口时才添加端口号
        if ((scheme.equals("http") && serverPort != 80) ||
            (scheme.equals("https") && serverPort != 443)) {
            baseUrl.append(":").append(serverPort);
        }

        return baseUrl.toString();
    }
}
