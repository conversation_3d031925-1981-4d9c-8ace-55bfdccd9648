package com.zlz.mall_server.controller;

import com.zlz.mall_server.model.AdminUser;
import com.zlz.mall_server.service.CustomerServiceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import jakarta.servlet.http.HttpSession;
import java.util.ArrayList;

@Controller
@RequestMapping("/admin/test-customer-service")
public class TestCustomerServiceController {

    @Autowired
    private CustomerServiceService customerServiceService;
    
    /**
     * 测试客服工作台页面
     */
    @GetMapping("")
    public String testCustomerServicePage(Model model, HttpSession session) {
        // 创建一个测试用的admin对象
        AdminUser admin = new AdminUser();
        admin.setId(1L);
        admin.setRealName("测试管理员");
        admin.setRole("super_admin");

        // 使用空列表避免数据库调用
        model.addAttribute("admin", admin);
        model.addAttribute("waitingSessions", new ArrayList<>());
        model.addAttribute("activeSessions", new ArrayList<>());
        model.addAttribute("quickReplies", new ArrayList<>());

        return "admin/customer_service";
    }

    /**
     * 简单的文本测试
     */
    @GetMapping("/simple")
    public String simpleTest(Model model) {
        model.addAttribute("message", "客服页面测试成功！");
        return "admin/simple_test";
    }

    /**
     * API测试页面
     */
    @GetMapping("/api-test")
    public String apiTest() {
        return "test_customer_service";
    }

    /**
     * 调试版客服页面
     */
    @GetMapping("/debug")
    public String debugCustomerService(Model model, HttpSession session) {
        // 创建一个测试用的admin对象
        AdminUser admin = new AdminUser();
        admin.setId(1L);
        admin.setRealName("测试管理员");
        admin.setRole("super_admin");

        try {
            // 获取客服数据
            model.addAttribute("admin", admin);
            model.addAttribute("waitingSessions", customerServiceService.getWaitingSessions());
            model.addAttribute("activeSessions", customerServiceService.getAdminActiveSessions(admin.getId()));
            model.addAttribute("quickReplies", new ArrayList<>());
        } catch (Exception e) {
            // 如果出错，使用空数据
            model.addAttribute("admin", admin);
            model.addAttribute("waitingSessions", new ArrayList<>());
            model.addAttribute("activeSessions", new ArrayList<>());
            model.addAttribute("quickReplies", new ArrayList<>());
            model.addAttribute("error", e.getMessage());
        }

        return "admin/customer_service_debug";
    }

    /**
     * 优化版客服页面
     */
    @GetMapping("/optimized")
    public String optimizedCustomerService(Model model, HttpSession session) {
        // 创建一个测试用的admin对象
        AdminUser admin = new AdminUser();
        admin.setId(1L);
        admin.setRealName("测试管理员");
        admin.setRole("super_admin");

        try {
            // 获取客服数据
            model.addAttribute("admin", admin);
            model.addAttribute("waitingSessions", customerServiceService.getWaitingSessions());
            model.addAttribute("activeSessions", customerServiceService.getAdminActiveSessions(admin.getId()));
            model.addAttribute("quickReplies", customerServiceService.getQuickReplies(admin.getId()));
        } catch (Exception e) {
            // 如果出错，使用空数据
            model.addAttribute("admin", admin);
            model.addAttribute("waitingSessions", new ArrayList<>());
            model.addAttribute("activeSessions", new ArrayList<>());
            model.addAttribute("quickReplies", new ArrayList<>());
            model.addAttribute("error", e.getMessage());
        }

        return "admin/customer_service_optimized";
    }
}
