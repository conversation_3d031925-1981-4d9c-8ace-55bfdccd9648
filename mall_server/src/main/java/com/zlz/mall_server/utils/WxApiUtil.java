package com.zlz.mall_server.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * 微信小程序API工具类
 */
@Component
@Slf4j
public class WxApiUtil {

    @Value("${wx.appid:}")
    private String appid;

    @Value("${wx.secret:}")
    private String secret;

    private final RestTemplate restTemplate = new RestTemplate();
    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 微信小程序登录，code换取openId和sessionKey
     * @param code 微信登录code
     * @return 包含openId和sessionKey的Map
     */
    public Map<String, Object> code2Session(String code) {
        // 微信小程序的登录API
        String url = "https://api.weixin.qq.com/sns/jscode2session" +
                "?appid=" + appid +
                "&secret=" + secret +
                "&js_code=" + code +
                "&grant_type=authorization_code";

        try {
            log.info("调用微信登录API: {}", url);
            log.info("appid: {}, secret: {}", appid, secret);
            
            ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
            String responseBody = response.getBody();

            log.info("微信登录响应状态码: {}", response.getStatusCodeValue());
            log.info("微信登录响应头: {}", response.getHeaders());
            log.info("微信登录响应体: {}", responseBody);

            if (responseBody == null || responseBody.isEmpty()) {
                log.error("微信API返回空响应");
                Map<String, Object> error = new HashMap<>();
                error.put("error", -3);
                error.put("errmsg", "微信API返回空响应");
                return error;
            }

            Map<String, Object> result;
            try {
                result = objectMapper.readValue(responseBody, HashMap.class);
                log.info("解析后的响应: {}", result);
            } catch (Exception e) {
                log.error("解析响应失败: {}", e.getMessage());
                Map<String, Object> error = new HashMap<>();
                error.put("error", -4);
                error.put("errmsg", "解析响应失败: " + e.getMessage());
                return error;
            }

            // 检查响应中是否包含openid
            if (result.containsKey("openid")) {
                log.info("成功获取openid: {}", result.get("openid"));
                return result;
            } else if (result.containsKey("errcode")) {
                // 微信API返回错误
                Object errorCode = result.get("errcode");
                Object errorMsg = result.get("errmsg");
                
                log.error("微信API返回错误: {} - {}", errorCode, errorMsg);
                
                Map<String, Object> error = new HashMap<>();
                error.put("error", errorCode != null ? errorCode : -5);
                error.put("errmsg", errorMsg != null ? errorMsg : "微信API返回未知错误");
                return error;
            } else {
                // 响应不包含openid也不包含错误码，可能是格式不正确
                log.error("微信API响应格式不正确: {}", responseBody);
                Map<String, Object> error = new HashMap<>();
                error.put("error", -2);
                error.put("errmsg", "微信API响应格式不正确");
                return error;
            }

        } catch (Exception e) {
            log.error("调用微信API异常", e);
            Map<String, Object> error = new HashMap<>();
            error.put("error", -1);
            error.put("errmsg", "调用微信API异常: " + e.getMessage());
            return error;
        }
    }

    /**
     * 验证微信小程序数据签名
     * @param rawData 原始数据
     * @param signature 签名
     * @param sessionKey 会话密钥
     * @return 是否验证通过
     */
    public boolean verifySignature(String rawData, String signature, String sessionKey) {
        try {
            // 微信签名验证逻辑
            // 这里需要实现具体的签名验证算法
            // 暂时返回true，实际使用时需要实现
            return true;
        } catch (Exception e) {
            log.error("验证微信签名异常", e);
            return false;
        }
    }

    /**
     * 解密微信小程序数据
     * @param encryptedData 加密数据
     * @param iv 初始向量
     * @param sessionKey 会话密钥
     * @return 解密后的数据
     */
    public Map<String, Object> decryptData(String encryptedData, String iv, String sessionKey) {
        try {
            // 微信数据解密逻辑
            // 这里需要实现具体的解密算法
            // 暂时返回空Map，实际使用时需要实现
            return new HashMap<>();
        } catch (Exception e) {
            log.error("解密微信数据异常", e);
            return new HashMap<>();
        }
    }

    /**
     * 获取微信小程序access_token
     * @return access_token
     */
    public String getAccessToken() {
        String url = "https://api.weixin.qq.com/cgi-bin/token" +
                "?grant_type=client_credential" +
                "&appid=" + appid +
                "&secret=" + secret;

        try {
            log.info("获取微信access_token: {}", url);
            
            ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
            String responseBody = response.getBody();

            if (responseBody != null) {
                Map<String, Object> result = objectMapper.readValue(responseBody, HashMap.class);
                
                if (result.containsKey("access_token")) {
                    String accessToken = (String) result.get("access_token");
                    log.info("成功获取access_token");
                    return accessToken;
                } else {
                    log.error("获取access_token失败: {}", result);
                }
            }
        } catch (Exception e) {
            log.error("获取微信access_token异常", e);
        }
        
        return null;
    }

    /**
     * 检查配置是否完整
     * @return 是否配置完整
     */
    public boolean isConfigured() {
        return appid != null && !appid.isEmpty() && 
               secret != null && !secret.isEmpty();
    }

    /**
     * 获取配置信息（用于调试）
     * @return 配置信息
     */
    public Map<String, Object> getConfigInfo() {
        Map<String, Object> config = new HashMap<>();
        config.put("appid", appid != null && !appid.isEmpty() ? appid.substring(0, 8) + "..." : "未配置");
        config.put("secret", secret != null && !secret.isEmpty() ? "已配置" : "未配置");
        config.put("configured", isConfigured());
        return config;
    }
}
