package com.zlz.mall_server.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

@Component
@Slf4j
public class TtApiUtil {

    @Value("${tt.appid}")
    private String appid;

    @Value("${tt.secret}")
    private String secret;

    private final RestTemplate restTemplate = new RestTemplate();
    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 抖音小程序登录，code换取openId和sessionKey
     * @param code 抖音登录code
     * @return 包含openId和sessionKey的Map
     */
    public Map<String, Object> code2Session(String code) {
        // 抖音小程序的登录API - 使用最新的URL
        // 原URL: https://developer.toutiao.com/api/apps/jscode2session
        // 新URL: https://developer.open-douyin.com/api/apps/v2/jscode2session
        String url = "https://developer.toutiao.com/api/apps/jscode2session" +
                "?appid=" + appid +
                "&secret=" + secret +
                "&code=" + code;

        try {
            log.info("调用抖音登录API: {}", url);
            log.info("appid: {}, secret: {}", appid, secret);
            
            ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
            String responseBody = response.getBody();

            log.info("抖音登录响应状态码: {}", response.getStatusCodeValue());
            log.info("抖音登录响应头: {}", response.getHeaders());
            log.info("抖音登录响应体: {}", responseBody);

            if (responseBody == null || responseBody.isEmpty()) {
                log.error("抖音API返回空响应");
                Map<String, Object> error = new HashMap<>();
                error.put("error", -3);
                error.put("errmsg", "抖音API返回空响应");
                return error;
            }

            Map<String, Object> result;
            try {
                result = objectMapper.readValue(responseBody, HashMap.class);
                log.info("解析后的响应: {}", result);
            } catch (Exception e) {
                log.error("解析响应失败: {}", e.getMessage());
                Map<String, Object> error = new HashMap<>();
                error.put("error", -4);
                error.put("errmsg", "解析响应失败: " + e.getMessage());
                return error;
            }

            // 检查响应中是否包含openid
            if (result.containsKey("openid") || result.containsKey("data")) {
                // 新版API可能将结果包装在data字段中
                if (result.containsKey("data") && result.get("data") instanceof Map) {
                    Map<String, Object> data = (Map<String, Object>) result.get("data");
                    if (data.containsKey("openid")) {
                        log.info("成功获取openid(data): {}", data.get("openid"));
                        return data;
                    }
                }
                
                if (result.containsKey("openid")) {
                    log.info("成功获取openid: {}", result.get("openid"));
                    return result;
                }
                
                // 如果到这里，说明data中没有openid
                log.error("抖音API响应中没有openid: {}", result);
                Map<String, Object> error = new HashMap<>();
                error.put("error", -6);
                error.put("errmsg", "抖音API响应中没有openid");
                return error;
            } else if (result.containsKey("errcode") || result.containsKey("error_code") || result.containsKey("error")) {
                // 抖音API返回错误
                Object errorCode = null;
                if (result.containsKey("errcode")) {
                    errorCode = result.get("errcode");
                } else if (result.containsKey("error_code")) {
                    errorCode = result.get("error_code");
                } else if (result.containsKey("error")) {
                    errorCode = result.get("error");
                }
                
                Object errorMsg = null;
                if (result.containsKey("errmsg")) {
                    errorMsg = result.get("errmsg");
                } else if (result.containsKey("error_msg")) {
                    errorMsg = result.get("error_msg");
                } else if (result.containsKey("message")) {
                    errorMsg = result.get("message");
                }
                
                log.error("抖音API返回错误: {} - {}", errorCode, errorMsg);
                
                Map<String, Object> error = new HashMap<>();
                error.put("error", errorCode != null ? errorCode : -5);
                error.put("errmsg", errorMsg != null ? errorMsg : "抖音API返回未知错误");
                return error;
            } else {
                // 响应不包含openid也不包含错误码，可能是格式不正确
                log.error("抖音API响应格式不正确: {}", responseBody);
                Map<String, Object> error = new HashMap<>();
                error.put("error", -2);
                error.put("errmsg", "抖音API响应格式不正确");
                return error;
            }
        } catch (Exception e) {
            log.error("调用抖音API异常", e);
            Map<String, Object> error = new HashMap<>();
            error.put("error", -1);
            error.put("errmsg", "调用抖音API异常: " + e.getMessage());
            return error;
        }
    }
}
