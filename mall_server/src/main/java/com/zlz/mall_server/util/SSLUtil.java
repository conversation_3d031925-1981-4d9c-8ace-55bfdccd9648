package com.zlz.mall_server.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;
import jakarta.annotation.PostConstruct;
import java.io.InputStream;
import java.security.KeyStore;
import java.security.cert.Certificate;
import java.security.cert.X509Certificate;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * SSL证书工具类
 * 用于查看和验证SSL证书信息
 */
@Component
public class SSLUtil {

    private static final Logger logger = LoggerFactory.getLogger(SSLUtil.class);

    /**
     * 应用启动时打印证书信息
     */
    @PostConstruct
    public void printCertificateInfo() {
        try {
            // 加载证书
            ClassPathResource resource = new ClassPathResource("keystore.p12");
            if (!resource.exists()) {
                logger.warn("SSL证书文件不存在: keystore.p12");
                return;
            }

            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            try (InputStream inputStream = resource.getInputStream()) {
                keyStore.load(inputStream, "zlzmall123".toCharArray());
            }

            // 获取证书
            Certificate certificate = keyStore.getCertificate("zlz-mall");
            if (certificate instanceof X509Certificate) {
                X509Certificate x509Cert = (X509Certificate) certificate;
                
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                
                logger.info("=== SSL证书信息 ===");
                logger.info("证书主题: {}", x509Cert.getSubjectDN().getName());
                logger.info("证书颁发者: {}", x509Cert.getIssuerDN().getName());
                logger.info("证书序列号: {}", x509Cert.getSerialNumber());
                logger.info("证书生效时间: {}", dateFormat.format(x509Cert.getNotBefore()));
                logger.info("证书过期时间: {}", dateFormat.format(x509Cert.getNotAfter()));
                logger.info("证书签名算法: {}", x509Cert.getSigAlgName());
                
                // 检查证书是否即将过期（30天内）
                Date now = new Date();
                Date expiryDate = x509Cert.getNotAfter();
                long daysUntilExpiry = (expiryDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24);
                
                if (daysUntilExpiry <= 30) {
                    logger.warn("⚠️  SSL证书将在 {} 天后过期！", daysUntilExpiry);
                } else {
                    logger.info("✅ SSL证书有效，还有 {} 天过期", daysUntilExpiry);
                }
                
                logger.info("=== HTTPS服务信息 ===");
                logger.info("🔒 HTTPS端口: 8443");
                logger.info("🔄 HTTP重定向端口: 8080");
                logger.info("🌐 HTTPS访问地址: https://localhost:8443");
                logger.info("📱 小程序API地址: https://localhost:8443/api");
                logger.info("🖥️  管理后台地址: https://localhost:8443/admin");
                logger.info("==================");
                
            }
        } catch (Exception e) {
            logger.error("读取SSL证书信息失败: {}", e.getMessage());
        }
    }

    /**
     * 检查证书是否有效
     */
    public boolean isCertificateValid() {
        try {
            ClassPathResource resource = new ClassPathResource("keystore.p12");
            if (!resource.exists()) {
                return false;
            }

            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            try (InputStream inputStream = resource.getInputStream()) {
                keyStore.load(inputStream, "zlzmall123".toCharArray());
            }

            Certificate certificate = keyStore.getCertificate("zlz-mall");
            if (certificate instanceof X509Certificate) {
                X509Certificate x509Cert = (X509Certificate) certificate;
                x509Cert.checkValidity(); // 检查证书是否在有效期内
                return true;
            }
        } catch (Exception e) {
            logger.error("证书验证失败: {}", e.getMessage());
        }
        return false;
    }

    /**
     * 获取证书过期天数
     */
    public long getDaysUntilExpiry() {
        try {
            ClassPathResource resource = new ClassPathResource("keystore.p12");
            if (!resource.exists()) {
                return -1;
            }

            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            try (InputStream inputStream = resource.getInputStream()) {
                keyStore.load(inputStream, "zlzmall123".toCharArray());
            }

            Certificate certificate = keyStore.getCertificate("zlz-mall");
            if (certificate instanceof X509Certificate) {
                X509Certificate x509Cert = (X509Certificate) certificate;
                Date now = new Date();
                Date expiryDate = x509Cert.getNotAfter();
                return (expiryDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24);
            }
        } catch (Exception e) {
            logger.error("获取证书过期时间失败: {}", e.getMessage());
        }
        return -1;
    }
}
