package com.zlz.mall_server.util;

import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

public class PasswordUtil {
    
    private static final BCryptPasswordEncoder encoder = new BCryptPasswordEncoder();
    
    /**
     * 加密密码
     */
    public static String encode(String rawPassword) {
        return encoder.encode(rawPassword);
    }
    
    /**
     * 验证密码
     */
    public static boolean matches(String rawPassword, String encodedPassword) {
        return encoder.matches(rawPassword, encodedPassword);
    }
    
    /**
     * 测试方法 - 生成密码哈希
     */
    public static void main(String[] args) {
        String password = "admin123";
        String encoded = encode(password);
        System.out.println("原始密码: " + password);
        System.out.println("加密后: " + encoded);
        System.out.println("验证结果: " + matches(password, encoded));
        
        // 测试数据库中的哈希
        String dbHash = "$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXIGfkPNtmismLQdQIG.ZjUON4C";
        System.out.println("数据库哈希验证: " + matches(password, dbHash));
    }
}
