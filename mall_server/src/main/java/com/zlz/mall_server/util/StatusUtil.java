package com.zlz.mall_server.util;

import com.zlz.mall_server.model.Engineer;
import com.zlz.mall_server.model.ServiceCenter;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;

/**
 * 状态管理工具类
 * 用于判断工程师和服务网点的实际状态
 */
public class StatusUtil {

    /**
     * 判断工程师是否为活跃状态
     * 活跃状态的条件：
     * 1. 状态为已通过审核 (approved)
     * 2. 当前在线 (is_online = true)
     * 3. 可以接单 (is_available = true)
     * 4. 最后活跃时间在24小时内
     */
    public static boolean isEngineerActive(Engineer engineer) {
        if (engineer == null) {
            return false;
        }

        // 基础条件：必须是已通过审核的状态
        if (!"approved".equals(engineer.getStatus())) {
            return false;
        }

        // 必须在线且可接单
        if (!Boolean.TRUE.equals(engineer.getIsOnline()) || 
            !Boolean.TRUE.equals(engineer.getIsAvailable())) {
            return false;
        }

        // 检查最后活跃时间（24小时内）
        LocalDateTime lastActive = engineer.getLastActiveAt();
        if (lastActive == null) {
            return false;
        }

        LocalDateTime now = LocalDateTime.now();
        LocalDateTime threshold = now.minusHours(24);
        
        return lastActive.isAfter(threshold);
    }

    /**
     * 判断服务网点是否为活跃状态
     * 活跃状态的条件：
     * 1. 状态为已通过审核 (approved)
     * 2. 网点启用 (is_active = true)
     * 3. 当前时间在营业时间内
     * 4. 最后活跃时间在7天内
     */
    public static boolean isServiceCenterActive(ServiceCenter serviceCenter) {
        if (serviceCenter == null) {
            return false;
        }

        // 基础条件：必须是已通过审核的状态
        if (!"approved".equals(serviceCenter.getStatus())) {
            return false;
        }

        // 必须是启用状态
        if (!Boolean.TRUE.equals(serviceCenter.getIsActive())) {
            return false;
        }

        // 检查最后活跃时间（7天内）
        LocalDateTime lastActive = serviceCenter.getLastActiveAt();
        if (lastActive == null) {
            return false;
        }

        LocalDateTime now = LocalDateTime.now();
        LocalDateTime threshold = now.minusDays(7);
        
        if (!lastActive.isAfter(threshold)) {
            return false;
        }

        // 检查营业时间
        return isInBusinessHours(serviceCenter);
    }

    /**
     * 检查当前时间是否在服务网点的营业时间内
     */
    private static boolean isInBusinessHours(ServiceCenter serviceCenter) {
        // 如果是24小时营业
        if (Boolean.TRUE.equals(serviceCenter.getIs24Hours())) {
            return true;
        }

        String businessHours = serviceCenter.getBusinessHours();
        if (businessHours == null || businessHours.trim().isEmpty()) {
            // 如果没有设置营业时间，默认认为在营业时间内
            return true;
        }

        try {
            // 解析营业时间格式，例如："09:00-18:00"
            if (businessHours.contains("-")) {
                String[] times = businessHours.split("-");
                if (times.length == 2) {
                    LocalTime startTime = LocalTime.parse(times[0].trim(), DateTimeFormatter.ofPattern("HH:mm"));
                    LocalTime endTime = LocalTime.parse(times[1].trim(), DateTimeFormatter.ofPattern("HH:mm"));
                    LocalTime currentTime = LocalTime.now();

                    // 处理跨天的情况（如 22:00-06:00）
                    if (startTime.isAfter(endTime)) {
                        return currentTime.isAfter(startTime) || currentTime.isBefore(endTime);
                    } else {
                        return !currentTime.isBefore(startTime) && !currentTime.isAfter(endTime);
                    }
                }
            }
        } catch (Exception e) {
            // 解析失败，默认认为在营业时间内
            return true;
        }

        return true;
    }

    /**
     * 获取工程师的显示状态
     * 根据实际情况返回更准确的状态描述
     */
    public static String getEngineerDisplayStatus(Engineer engineer) {
        if (engineer == null) {
            return "unknown";
        }

        String status = engineer.getStatus();
        
        switch (status) {
            case "pending":
                return "待审核";
            case "approved":
                // 进一步判断是否活跃
                if (isEngineerActive(engineer)) {
                    return "活跃中";
                } else if (Boolean.TRUE.equals(engineer.getIsOnline())) {
                    return "在线";
                } else {
                    return "离线";
                }
            case "rejected":
                return "已拒绝";
            case "suspended":
                return "已暂停";
            default:
                return status;
        }
    }

    /**
     * 获取服务网点的显示状态
     * 根据实际情况返回更准确的状态描述
     */
    public static String getServiceCenterDisplayStatus(ServiceCenter serviceCenter) {
        if (serviceCenter == null) {
            return "unknown";
        }

        String status = serviceCenter.getStatus();
        
        switch (status) {
            case "pending":
                return "待审核";
            case "approved":
                // 进一步判断是否活跃
                if (isServiceCenterActive(serviceCenter)) {
                    return "营业中";
                } else if (Boolean.TRUE.equals(serviceCenter.getIsActive())) {
                    return "已开通";
                } else {
                    return "已关闭";
                }
            case "rejected":
                return "已拒绝";
            case "suspended":
                return "已暂停";
            case "closed":
                return "已关闭";
            default:
                return status;
        }
    }

    /**
     * 获取状态对应的CSS类名
     */
    public static String getStatusCssClass(String status) {
        switch (status) {
            case "pending":
            case "待审核":
                return "status-pending";
            case "approved":
            case "已通过":
            case "已开通":
                return "status-approved";
            case "active":
            case "活跃中":
            case "营业中":
                return "status-active";
            case "rejected":
            case "已拒绝":
                return "status-rejected";
            case "suspended":
            case "已暂停":
                return "status-suspended";
            case "closed":
            case "已关闭":
                return "status-closed";
            case "在线":
                return "status-online";
            case "离线":
                return "status-offline";
            default:
                return "status-unknown";
        }
    }

    /**
     * 检查工程师是否可以接单
     */
    public static boolean canEngineerTakeOrders(Engineer engineer) {
        return "approved".equals(engineer.getStatus()) && 
               Boolean.TRUE.equals(engineer.getIsOnline()) && 
               Boolean.TRUE.equals(engineer.getIsAvailable());
    }

    /**
     * 检查服务网点是否可以提供服务
     */
    public static boolean canServiceCenterProvideService(ServiceCenter serviceCenter) {
        return "approved".equals(serviceCenter.getStatus()) && 
               Boolean.TRUE.equals(serviceCenter.getIsActive()) &&
               isInBusinessHours(serviceCenter);
    }

    /**
     * 更新工程师的最后活跃时间
     */
    public static void updateEngineerLastActive(Engineer engineer) {
        if (engineer != null) {
            engineer.setLastActiveAt(LocalDateTime.now());
        }
    }

    /**
     * 更新服务网点的最后活跃时间
     */
    public static void updateServiceCenterLastActive(ServiceCenter serviceCenter) {
        if (serviceCenter != null) {
            serviceCenter.setLastActiveAt(LocalDateTime.now());
        }
    }
}
