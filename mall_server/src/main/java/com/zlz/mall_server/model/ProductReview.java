package com.zlz.mall_server.model;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@TableName("product_reviews")
public class ProductReview {
    private Long id;
    
    private Long productId;
    
    private Long orderId;
    
    private Long orderItemId;
    
    private String openId;
    
    private String userNickname;
    
    private String userAvatar;
    
    private Integer rating;
    
    private String content;
    
    private String images; // JSON数组字符串
    
    private String specInfo;
    
    private Boolean isAnonymous;
    
    private String replyContent;
    
    private LocalDateTime replyTime;
    
    private Boolean isTop;
    
    private Integer likeCount;
    
    private Integer status;
    
    private LocalDateTime createdAt;
    
    private LocalDateTime updatedAt;
}
