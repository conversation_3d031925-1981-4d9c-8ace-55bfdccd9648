package com.zlz.mall_server.model;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@TableName("engineers")
public class Engineer {
    private Long id;
    
    private String openId;
    
    private String name;
    
    private String phone;
    
    private String email;
    
    private String avatar;
    
    private String gender;
    
    private Integer age;
    
    private String idCard;
    
    // 专业信息
    private String specialties; // JSON数组字符串
    
    private Integer experienceYears;
    
    private String education;
    
    private String certifications; // JSON数组字符串
    
    private String skills; // JSON数组字符串
    
    // 工作信息
    private String workAreas; // JSON数组字符串
    
    private String workTime;
    
    private BigDecimal hourlyRate;
    
    private BigDecimal serviceFee;
    
    // 评价信息
    private BigDecimal rating;
    
    private Integer totalOrders;
    
    private Integer completedOrders;
    
    private BigDecimal successRate;
    
    // 状态信息
    private String status; // pending, approved, rejected, suspended, active
    
    private Boolean isOnline;
    
    private Boolean isAvailable;
    
    // 审核信息
    private LocalDateTime applicationTime;
    
    private LocalDateTime reviewTime;
    
    private Long reviewerId;
    
    private String reviewNotes;
    
    // 个人介绍
    private String bio;
    
    private String introduction;
    
    private String workPhotos; // JSON数组字符串
    
    // 系统字段
    private Integer sortOrder;
    
    private LocalDateTime createdAt;
    
    private LocalDateTime updatedAt;
    
    private LocalDateTime lastActiveAt;
}
