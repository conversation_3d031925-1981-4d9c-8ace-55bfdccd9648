package com.zlz.mall_server.model;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@TableName("user_addresses")
public class UserAddress {
    private Long id;
    
    private String openId; // 用户openId
    
    private String name; // 收货人姓名
    
    private String phone; // 收货人手机号
    
    private String province; // 省份
    
    private String city; // 城市
    
    private String district; // 区/县
    
    private String address; // 详细地址
    
    private String tag; // 地址标签（家、公司、学校等）
    
    private Boolean isDefault; // 是否默认地址
    
    private LocalDateTime createdAt; // 创建时间
    
    private LocalDateTime updatedAt; // 更新时间
}
