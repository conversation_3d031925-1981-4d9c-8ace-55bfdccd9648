package com.zlz.mall_server.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@TableName("service_centers")
public class ServiceCenter {
    private Long id;
    
    private String name;
    
    private String address;
    
    private String province;
    
    private String city;
    
    private String district;
    
    private String phone;
    
    private String contactPerson;
    
    private String email;
    
    // 位置信息
    private BigDecimal latitude;
    
    private BigDecimal longitude;
    
    private String locationAccuracy;
    
    // 营业信息
    private String businessHours;
    
    private String businessDays;

    @TableField("is_24_hours")
    private Boolean is24Hours;
    
    // 服务信息
    private String serviceTypes; // JSON数组字符串
    
    private String equipmentTypes; // JSON数组字符串
    
    private String serviceDescription;
    
    private String facilities; // JSON数组字符串
    
    // 评价信息
    private BigDecimal rating;
    
    private Integer reviewCount;
    
    private Integer totalServices;
    
    // 费用信息
    private BigDecimal serviceFee;
    
    private BigDecimal inspectionFee;
    
    private String parkingInfo;
    
    // 状态管理
    private String status; // pending, approved, rejected, suspended, closed
    
    private Boolean isActive;
    
    private Boolean isFeatured;
    
    // 审核信息
    private LocalDateTime applicationTime;
    
    private LocalDateTime reviewTime;
    
    private Long reviewerId;
    
    private String reviewNotes;
    
    private String rejectionReason;
    
    // 资质信息
    private String businessLicense;
    
    private String qualificationCertificates; // JSON数组字符串
    
    private String images; // JSON数组字符串
    
    // 系统字段
    private Integer sortOrder;
    
    private LocalDateTime createdAt;
    
    private LocalDateTime updatedAt;
    
    private LocalDateTime lastActiveAt;
}
