package com.zlz.mall_server.model;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@TableName("users")  // 指定表名为users
public class User {
    private Long id;

    private String openId; // 抖音小程序openId

    private String nickname;

    private String avatarUrl;

    private String gender;

    private String phone;

    private Integer status;

    private LocalDateTime lastLoginTime;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;
}
