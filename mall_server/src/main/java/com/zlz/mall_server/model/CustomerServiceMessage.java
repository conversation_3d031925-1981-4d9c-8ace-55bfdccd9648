package com.zlz.mall_server.model;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@TableName("customer_service_messages")
public class CustomerServiceMessage {
    private Long id;
    
    private String sessionId;
    
    private String senderType; // user, admin
    
    private String senderId;
    
    private String senderName;
    
    private String senderAvatar;
    
    private String messageType; // text, image, file
    
    private String content;
    
    private String extraData; // JSON格式的额外数据
    
    private Boolean isRead;
    
    private LocalDateTime readAt;
    
    private LocalDateTime createdAt;
}
