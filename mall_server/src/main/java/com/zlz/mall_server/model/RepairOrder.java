package com.zlz.mall_server.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@TableName("repair_orders")
public class RepairOrder {
    private Long id;

    private String orderNo; // 订单编号

    private String openId; // 用户openId

    private String faultType; // 故障类型

    private String model; // 充电桩型号

    private String description; // 故障描述

    private String name; // 联系人姓名

    private String phone; // 联系电话

    private Long addressId; // 地址ID

    private String fullAddress; // 完整地址

    private String serviceType; // 服务方式（home-上门维修，remote-远程指导）

    private String images; // 故障照片（JSON数组）

    private LocalDate appointmentDate; // 预约日期

    private String appointmentTime; // 预约时间段

    private String status; // 订单状态（pending-待接单，accepted-待上门，processing-维修中，completed-已完成）

    private Long engineerId; // 工程师ID

    private String engineerName; // 工程师姓名

    private String engineerPhone; // 工程师电话

    @TableField(exist = false)
    private String engineerAvatar; // 工程师头像（动态获取，不存储在数据库）

    private BigDecimal repairFee; // 维修费用

    private BigDecimal partsFee; // 配件费用

    private BigDecimal totalFee; // 总费用

    private String remark; // 备注

    // 维修详情相关字段
    private String repairResult; // 维修结果（success-维修成功，partial-部分修复，failed-维修失败，replacement-需要更换设备）

    private BigDecimal repairTime; // 维修耗时（小时）

    private String repairDescription; // 维修详情描述

    private String repairImages; // 维修过程照片（JSON数组）

    // 费用明细字段
    private BigDecimal laborFee; // 人工费

    private BigDecimal serviceFee; // 服务费

    private BigDecimal materialsFee; // 耗材费用

    // 耗材明细
    private String materialsDetail; // 耗材明细（JSON数组）

    // 完成时间
    private LocalDateTime completedAt; // 完成时间

    // 评价相关字段
    private BigDecimal rating; // 用户评分（1-5分）

    private String reviewContent; // 用户评价内容

    private String reviewImages; // 评价照片（JSON数组）

    private LocalDateTime reviewTime; // 评价时间

    // 账单相关字段
    private Boolean billSent; // 是否已发送账单

    private LocalDateTime billSentTime; // 账单发送时间

    private LocalDateTime createdAt; // 创建时间

    private LocalDateTime updatedAt; // 更新时间
}
