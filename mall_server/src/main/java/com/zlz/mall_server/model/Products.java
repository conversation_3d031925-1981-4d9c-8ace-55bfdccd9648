package com.zlz.mall_server.model;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@TableName("products")
public class Products {
    private Long id;

    private String name;

    private String shortName;

    private BigDecimal price;

    private BigDecimal originalPrice;

    private Integer sales;

    private Integer stock;

    private Integer categoryId;

    private String brand;

    private String model;

    private String mainImage;

    private String images; // JSON数组字符串

    private String detailImage;

    private String features; // JSON数组字符串

    private String compatibleCars;

    private String description;

    private String specifications; // JSON数组字符串

    private String specs; // JSON数组字符串

    private String services; // JSON数组字符串

    private BigDecimal weight;

    private String dimensions;

    private String warrantyPeriod;

    private Integer status;

    private Integer sortOrder;

    private String seoTitle;

    private String seoKeywords;

    private String seoDescription;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;
}
