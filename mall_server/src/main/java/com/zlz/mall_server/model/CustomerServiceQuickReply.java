package com.zlz.mall_server.model;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@TableName("customer_service_quick_replies")
public class CustomerServiceQuickReply {
    private Long id;
    
    private Long adminId;
    
    private String category;
    
    private String title;
    
    private String content;
    
    private Integer sortOrder;
    
    private Boolean isActive;
    
    private LocalDateTime createdAt;
    
    private LocalDateTime updatedAt;
}
