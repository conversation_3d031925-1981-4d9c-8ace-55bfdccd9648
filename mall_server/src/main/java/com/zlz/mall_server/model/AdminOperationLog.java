package com.zlz.mall_server.model;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@TableName("admin_operation_logs")
public class AdminOperationLog {
    private Long id;
    
    private Long adminId;
    
    private String adminUsername;
    
    private String operationType;
    
    private String operationDesc;
    
    private String targetType;
    
    private Long targetId;
    
    private String requestMethod;
    
    private String requestUrl;
    
    private String requestParams;
    
    private String responseResult;
    
    private String ipAddress;
    
    private String userAgent;
    
    private Integer executionTime;
    
    private Integer status; // 0-失败 1-成功
    
    private String errorMessage;
    
    private LocalDateTime createdAt;
}
