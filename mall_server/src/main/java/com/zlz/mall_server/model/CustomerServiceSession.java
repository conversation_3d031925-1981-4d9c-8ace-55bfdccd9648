package com.zlz.mall_server.model;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@TableName("customer_service_sessions")
public class CustomerServiceSession {
    private Long id;
    
    private String sessionId;
    
    private String openId;
    
    private String userNickname;
    
    private String userAvatar;
    
    private Long adminId;
    
    private String adminName;
    
    private String status; // waiting, active, closed
    
    private LocalDateTime lastMessageTime;
    
    private String lastMessageContent;
    
    private Integer unreadCountUser;
    
    private Integer unreadCountAdmin;
    
    private LocalDateTime createdAt;
    
    private LocalDateTime updatedAt;
    
    private LocalDateTime closedAt;
}
