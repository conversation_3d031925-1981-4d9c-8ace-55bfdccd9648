package com.zlz.mall_server.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@TableName("product_favorites")
public class ProductFavorite {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private String openId; // 用户openId
    
    private Long productId; // 商品ID
    
    private LocalDateTime createdAt; // 创建时间
}
