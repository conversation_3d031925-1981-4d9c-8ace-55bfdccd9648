package com.zlz.mall_server.model;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@TableName("product_categories")
public class ProductCategory {
    private Integer id;
    
    private String name;
    
    private String icon;
    
    private String selectedIcon;
    
    private Integer parentId;
    
    private Integer level;
    
    private Integer sortOrder;
    
    private Integer status;
    
    private LocalDateTime createdAt;
    
    private LocalDateTime updatedAt;
}
