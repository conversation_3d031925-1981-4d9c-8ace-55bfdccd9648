package com.zlz.mall_server.model;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@TableName("admin_users")
public class AdminUser {
    private Long id;
    
    private String username;
    
    private String password;
    
    private String realName;
    
    private String email;
    
    private String phone;
    
    private String avatar;
    
    // 权限相关
    private String role; // super_admin, admin, operator
    
    private String permissions; // JSON数组字符串
    
    private Integer status; // 0-禁用 1-启用
    
    // 登录相关
    private LocalDateTime lastLoginTime;
    
    private String lastLoginIp;
    
    private Integer loginCount;
    
    private LocalDateTime passwordUpdatedAt;
    
    // 系统字段
    private LocalDateTime createdAt;
    
    private LocalDateTime updatedAt;
    
    private Long createdBy;
}
