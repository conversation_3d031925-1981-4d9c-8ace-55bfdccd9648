package com.zlz.mall_server.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zlz.mall_server.model.Engineer;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface EngineerMapper extends BaseMapper<Engineer> {
    
    /**
     * 查询所有已审核通过的工程师
     * @return 工程师列表
     */
    @Select("SELECT * FROM engineers WHERE status = 'approved' ORDER BY sort_order DESC, rating DESC, total_orders DESC")
    List<Engineer> findApprovedEngineers();
    
    /**
     * 查询在线且可接单的工程师
     * @return 工程师列表
     */
    @Select("SELECT * FROM engineers WHERE status = 'approved' AND is_online = 1 AND is_available = 1 ORDER BY sort_order DESC, rating DESC")
    List<Engineer> findAvailableEngineers();
    
    /**
     * 根据专业领域查询工程师
     * @param specialty 专业领域
     * @return 工程师列表
     */
    @Select("SELECT * FROM engineers WHERE status = 'approved' AND specialties LIKE CONCAT('%', #{specialty}, '%') ORDER BY rating DESC, total_orders DESC")
    List<Engineer> findBySpecialty(@Param("specialty") String specialty);
    
    /**
     * 根据服务区域查询工程师
     * @param area 服务区域
     * @return 工程师列表
     */
    @Select("SELECT * FROM engineers WHERE status = 'approved' AND work_areas LIKE CONCAT('%', #{area}, '%') ORDER BY rating DESC")
    List<Engineer> findByWorkArea(@Param("area") String area);
    
    /**
     * 根据手机号查询工程师
     * @param phone 手机号
     * @return 工程师对象
     */
    @Select("SELECT * FROM engineers WHERE phone = #{phone} LIMIT 1")
    Engineer findByPhone(@Param("phone") String phone);

    /**
     * 增加工程师总接单数
     * @param engineerId 工程师ID
     * @return 影响行数
     */
    @Update("UPDATE engineers SET total_orders = total_orders + 1, updated_at = NOW() WHERE id = #{engineerId}")
    int incrementTotalOrders(@Param("engineerId") Long engineerId);

    /**
     * 增加工程师完成订单数
     * @param engineerId 工程师ID
     * @return 影响行数
     */
    @Update("UPDATE engineers SET completed_orders = completed_orders + 1, updated_at = NOW() WHERE id = #{engineerId}")
    int incrementCompletedOrders(@Param("engineerId") Long engineerId);

    /**
     * 更新工程师成功率
     * @param engineerId 工程师ID
     * @return 影响行数
     */
    @Update("UPDATE engineers SET success_rate = CASE WHEN total_orders > 0 THEN ROUND((completed_orders * 100.0 / total_orders), 2) ELSE 100.00 END, updated_at = NOW() WHERE id = #{engineerId}")
    int updateSuccessRate(@Param("engineerId") Long engineerId);

    /**
     * 统计工程师数量
     * @return 统计信息
     */
    @Select("SELECT " +
            "COUNT(*) as total, " +
            "COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved, " +
            "COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending, " +
            "COUNT(CASE WHEN is_online = 1 AND status = 'approved' THEN 1 END) as online " +
            "FROM engineers")
    EngineerStats getEngineerStats();
    
    /**
     * 工程师统计信息内部类
     */
    class EngineerStats {
        private Integer total;
        private Integer approved;
        private Integer pending;
        private Integer online;
        
        // getters and setters
        public Integer getTotal() { return total; }
        public void setTotal(Integer total) { this.total = total; }
        
        public Integer getApproved() { return approved; }
        public void setApproved(Integer approved) { this.approved = approved; }
        
        public Integer getPending() { return pending; }
        public void setPending(Integer pending) { this.pending = pending; }
        
        public Integer getOnline() { return online; }
        public void setOnline(Integer online) { this.online = online; }
    }
}
