package com.zlz.mall_server.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zlz.mall_server.model.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface UserMapper extends BaseMapper<User> {

    /**
     * 根据openId查询用户
     * @param openId 抖音openId
     * @return 用户对象
     */
    @Select("SELECT * FROM users WHERE open_id = #{openId}")
    User findByOpenId(@Param("openId") String openId);
}
