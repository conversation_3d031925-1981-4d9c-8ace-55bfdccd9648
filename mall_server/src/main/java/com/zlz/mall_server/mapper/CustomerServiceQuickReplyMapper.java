package com.zlz.mall_server.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zlz.mall_server.model.CustomerServiceQuickReply;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface CustomerServiceQuickReplyMapper extends BaseMapper<CustomerServiceQuickReply> {
    
    @Select("SELECT * FROM customer_service_quick_replies WHERE (admin_id = #{adminId} OR admin_id IS NULL) AND is_active = 1 ORDER BY sort_order ASC")
    List<CustomerServiceQuickReply> findByAdminId(@Param("adminId") Long adminId);
    
    @Select("SELECT * FROM customer_service_quick_replies WHERE category = #{category} AND is_active = 1 ORDER BY sort_order ASC")
    List<CustomerServiceQuickReply> findByCategory(@Param("category") String category);
}
