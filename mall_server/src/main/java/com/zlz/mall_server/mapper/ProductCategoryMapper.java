package com.zlz.mall_server.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zlz.mall_server.model.ProductCategory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface ProductCategoryMapper extends BaseMapper<ProductCategory> {
    
    /**
     * 查询所有启用的分类
     * @return 分类列表
     */
    @Select("SELECT * FROM product_categories WHERE status = 1 ORDER BY sort_order ASC, id ASC")
    List<ProductCategory> findAllEnabled();
    
    /**
     * 根据父分类ID查询子分类
     * @param parentId 父分类ID
     * @return 子分类列表
     */
    @Select("SELECT * FROM product_categories WHERE parent_id = #{parentId} AND status = 1 ORDER BY sort_order ASC, id ASC")
    List<ProductCategory> findByParentId(Integer parentId);
}
