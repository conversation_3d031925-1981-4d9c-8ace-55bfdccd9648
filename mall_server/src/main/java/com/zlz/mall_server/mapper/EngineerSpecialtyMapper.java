package com.zlz.mall_server.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zlz.mall_server.model.EngineerSpecialty;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface EngineerSpecialtyMapper extends BaseMapper<EngineerSpecialty> {
    
    /**
     * 查询所有启用的专业领域
     * @return 专业领域列表
     */
    @Select("SELECT * FROM engineer_specialties WHERE status = 1 ORDER BY sort_order ASC, id ASC")
    List<EngineerSpecialty> findAllEnabled();
}
