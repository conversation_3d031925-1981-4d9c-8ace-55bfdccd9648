package com.zlz.mall_server.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zlz.mall_server.model.CustomerServiceSession;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@Mapper
public interface CustomerServiceSessionMapper extends BaseMapper<CustomerServiceSession> {
    
    @Select("SELECT * FROM customer_service_sessions WHERE open_id = #{openId} AND status != 'closed' ORDER BY created_at DESC LIMIT 1")
    CustomerServiceSession findActiveSessionByOpenId(@Param("openId") String openId);
    
    @Select("SELECT * FROM customer_service_sessions WHERE admin_id = #{adminId} AND status = 'active' ORDER BY last_message_time DESC")
    List<CustomerServiceSession> findActiveSessionsByAdminId(@Param("adminId") Long adminId);
    
    @Select("SELECT * FROM customer_service_sessions WHERE status = 'waiting' ORDER BY created_at ASC")
    List<CustomerServiceSession> findWaitingSessions();
    
    @Update("UPDATE customer_service_sessions SET unread_count_user = 0 WHERE session_id = #{sessionId}")
    void markUserMessagesAsRead(@Param("sessionId") String sessionId);
    
    @Update("UPDATE customer_service_sessions SET unread_count_admin = 0 WHERE session_id = #{sessionId}")
    void markAdminMessagesAsRead(@Param("sessionId") String sessionId);
}
