package com.zlz.mall_server.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zlz.mall_server.model.AdminOperationLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface AdminOperationLogMapper extends BaseMapper<AdminOperationLog> {
    
    /**
     * 分页查询操作日志
     */
    @Select("SELECT * FROM admin_operation_logs ORDER BY created_at DESC")
    IPage<AdminOperationLog> selectPageLogs(Page<AdminOperationLog> page);
    
    /**
     * 根据管理员ID查询操作日志
     */
    @Select("SELECT * FROM admin_operation_logs WHERE admin_id = #{adminId} ORDER BY created_at DESC LIMIT #{limit}")
    List<AdminOperationLog> findByAdminId(@Param("adminId") Long adminId, @Param("limit") Integer limit);
}
