package com.zlz.mall_server.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zlz.mall_server.model.ServiceCenter;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

@Mapper
public interface ServiceCenterMapper extends BaseMapper<ServiceCenter> {
    
    /**
     * 查询所有已审核通过的服务网点
     * @return 服务网点列表
     */
    @Select("SELECT * FROM service_centers WHERE status = 'approved' AND is_active = 1 ORDER BY sort_order DESC, rating DESC")
    List<ServiceCenter> findApprovedServiceCenters();
    
    /**
     * 查询推荐的服务网点
     * @return 服务网点列表
     */
    @Select("SELECT * FROM service_centers WHERE status = 'approved' AND is_active = 1 AND is_featured = 1 ORDER BY sort_order DESC, rating DESC")
    List<ServiceCenter> findFeaturedServiceCenters();
    
    /**
     * 根据城市查询服务网点
     * @param city 城市
     * @return 服务网点列表
     */
    @Select("SELECT * FROM service_centers WHERE status = 'approved' AND is_active = 1 AND city = #{city} ORDER BY rating DESC")
    List<ServiceCenter> findByCity(@Param("city") String city);
    
    /**
     * 根据服务类型查询服务网点
     * @param serviceType 服务类型
     * @return 服务网点列表
     */
    @Select("SELECT * FROM service_centers WHERE status = 'approved' AND is_active = 1 AND service_types LIKE CONCAT('%', #{serviceType}, '%') ORDER BY rating DESC")
    List<ServiceCenter> findByServiceType(@Param("serviceType") String serviceType);
    
    /**
     * 根据关键词搜索服务网点
     * @param keyword 关键词
     * @return 服务网点列表
     */
    @Select("SELECT * FROM service_centers WHERE status = 'approved' AND is_active = 1 AND (name LIKE CONCAT('%', #{keyword}, '%') OR address LIKE CONCAT('%', #{keyword}, '%') OR service_description LIKE CONCAT('%', #{keyword}, '%')) ORDER BY rating DESC")
    List<ServiceCenter> searchByKeyword(@Param("keyword") String keyword);
    
    /**
     * 根据位置范围查询服务网点（简化版，实际应该使用地理位置函数）
     * @param minLat 最小纬度
     * @param maxLat 最大纬度
     * @param minLng 最小经度
     * @param maxLng 最大经度
     * @return 服务网点列表
     */
    @Select("SELECT * FROM service_centers WHERE status = 'approved' AND is_active = 1 AND latitude BETWEEN #{minLat} AND #{maxLat} AND longitude BETWEEN #{minLng} AND #{maxLng} ORDER BY rating DESC")
    List<ServiceCenter> findByLocationRange(@Param("minLat") BigDecimal minLat, @Param("maxLat") BigDecimal maxLat, @Param("minLng") BigDecimal minLng, @Param("maxLng") BigDecimal maxLng);
    
    /**
     * 统计服务网点数量
     * @return 统计信息
     */
    @Select("SELECT " +
            "COUNT(*) as total, " +
            "COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved, " +
            "COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending, " +
            "COUNT(CASE WHEN is_featured = 1 AND status = 'approved' THEN 1 END) as featured " +
            "FROM service_centers")
    ServiceCenterStats getServiceCenterStats();
    
    /**
     * 服务网点统计信息内部类
     */
    class ServiceCenterStats {
        private Integer total;
        private Integer approved;
        private Integer pending;
        private Integer featured;
        
        // getters and setters
        public Integer getTotal() { return total; }
        public void setTotal(Integer total) { this.total = total; }
        
        public Integer getApproved() { return approved; }
        public void setApproved(Integer approved) { this.approved = approved; }
        
        public Integer getPending() { return pending; }
        public void setPending(Integer pending) { this.pending = pending; }
        
        public Integer getFeatured() { return featured; }
        public void setFeatured(Integer featured) { this.featured = featured; }
    }
}
