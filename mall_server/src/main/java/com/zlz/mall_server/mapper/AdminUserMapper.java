package com.zlz.mall_server.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zlz.mall_server.model.AdminUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface AdminUserMapper extends BaseMapper<AdminUser> {
    
    /**
     * 根据用户名查询管理员
     */
    @Select("SELECT * FROM admin_users WHERE username = #{username} AND status = 1")
    AdminUser findByUsername(@Param("username") String username);
    
    /**
     * 更新最后登录信息
     */
    @Update("UPDATE admin_users SET last_login_time = NOW(), last_login_ip = #{ip}, login_count = login_count + 1 WHERE id = #{id}")
    void updateLastLoginInfo(@Param("id") Long id, @Param("ip") String ip);
}
