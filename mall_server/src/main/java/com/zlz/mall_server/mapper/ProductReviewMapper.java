package com.zlz.mall_server.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zlz.mall_server.model.ProductReview;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ProductReviewMapper extends BaseMapper<ProductReview> {
    
    /**
     * 根据商品ID查询评价列表
     * @param productId 商品ID
     * @return 评价列表
     */
    @Select("SELECT * FROM product_reviews WHERE product_id = #{productId} AND status = 1 ORDER BY is_top DESC, created_at DESC")
    List<ProductReview> findByProductId(@Param("productId") Long productId);
    
    /**
     * 根据商品ID统计评价数量
     * @param productId 商品ID
     * @return 评价数量
     */
    @Select("SELECT COUNT(*) FROM product_reviews WHERE product_id = #{productId} AND status = 1")
    Integer countByProductId(@Param("productId") Long productId);
    
    /**
     * 根据商品ID计算平均评分
     * @param productId 商品ID
     * @return 平均评分
     */
    @Select("SELECT AVG(rating) FROM product_reviews WHERE product_id = #{productId} AND status = 1")
    Double getAverageRatingByProductId(@Param("productId") Long productId);
}
