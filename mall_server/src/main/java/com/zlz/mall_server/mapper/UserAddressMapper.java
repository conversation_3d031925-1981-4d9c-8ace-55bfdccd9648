package com.zlz.mall_server.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zlz.mall_server.model.UserAddress;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@Mapper
public interface UserAddressMapper extends BaseMapper<UserAddress> {
    
    /**
     * 根据openId查询用户地址列表
     * @param openId 用户openId
     * @return 地址列表
     */
    @Select("SELECT * FROM user_addresses WHERE open_id = #{openId} ORDER BY is_default DESC, updated_at DESC")
    List<UserAddress> findByOpenId(String openId);
    
    /**
     * 根据openId查询用户默认地址
     * @param openId 用户openId
     * @return 默认地址
     */
    @Select("SELECT * FROM user_addresses WHERE open_id = #{openId} AND is_default = 1 LIMIT 1")
    UserAddress findDefaultByOpenId(String openId);
    
    /**
     * 取消用户所有默认地址
     * @param openId 用户openId
     * @return 影响行数
     */
    @Update("UPDATE user_addresses SET is_default = 0 WHERE open_id = #{openId}")
    int clearDefaultAddress(String openId);
}
