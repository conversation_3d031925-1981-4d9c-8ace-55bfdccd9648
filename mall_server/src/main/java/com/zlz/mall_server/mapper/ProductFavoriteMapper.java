package com.zlz.mall_server.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zlz.mall_server.model.ProductFavorite;
import com.zlz.mall_server.model.Products;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ProductFavoriteMapper extends BaseMapper<ProductFavorite> {
    
    /**
     * 查询用户收藏的商品列表
     * @param openId 用户openId
     * @return 收藏的商品列表
     */
    @Select("SELECT p.*, pf.created_at as favorite_time FROM products p " +
            "INNER JOIN product_favorites pf ON p.id = pf.product_id " +
            "WHERE pf.open_id = #{openId} " +
            "ORDER BY pf.created_at DESC")
    List<Products> findFavoriteProductsByOpenId(@Param("openId") String openId);
    
    /**
     * 检查用户是否收藏了某个商品
     * @param openId 用户openId
     * @param productId 商品ID
     * @return 收藏记录数量
     */
    @Select("SELECT COUNT(*) FROM product_favorites WHERE open_id = #{openId} AND product_id = #{productId}")
    int checkFavoriteExists(@Param("openId") String openId, @Param("productId") Long productId);
    
    /**
     * 删除用户对某个商品的收藏
     * @param openId 用户openId
     * @param productId 商品ID
     * @return 删除的记录数
     */
    @Delete("DELETE FROM product_favorites WHERE open_id = #{openId} AND product_id = #{productId}")
    int deleteFavorite(@Param("openId") String openId, @Param("productId") Long productId);
    
    /**
     * 查询用户收藏的商品数量
     * @param openId 用户openId
     * @return 收藏数量
     */
    @Select("SELECT COUNT(*) FROM product_favorites WHERE open_id = #{openId}")
    int countFavoritesByOpenId(@Param("openId") String openId);
}
