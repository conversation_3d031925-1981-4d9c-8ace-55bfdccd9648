package com.zlz.mall_server.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zlz.mall_server.model.CustomerServiceMessage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@Mapper
public interface CustomerServiceMessageMapper extends BaseMapper<CustomerServiceMessage> {
    
    @Select("SELECT * FROM customer_service_messages WHERE session_id = #{sessionId} ORDER BY created_at ASC")
    List<CustomerServiceMessage> findMessagesBySessionId(@Param("sessionId") String sessionId);
    
    @Select("SELECT * FROM customer_service_messages WHERE session_id = #{sessionId} ORDER BY created_at DESC LIMIT #{limit}")
    List<CustomerServiceMessage> findRecentMessagesBySessionId(@Param("sessionId") String sessionId, @Param("limit") int limit);
    
    @Update("UPDATE customer_service_messages SET is_read = 1, read_at = NOW() WHERE session_id = #{sessionId} AND sender_type = #{senderType} AND is_read = 0")
    void markMessagesAsRead(@Param("sessionId") String sessionId, @Param("senderType") String senderType);
}
