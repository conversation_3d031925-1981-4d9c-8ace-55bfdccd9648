spring:
  application:
    name: mall_server
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *************************************************************************************************
    username: zlz
    password: zlzZLZ112233!
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 50MB
server:
  port: 8443
  ssl:
    enabled: true
#    key-store: classpath:keystore.p12
#    key-store-password: zlzmall123
#    key-store-type: PKCS12
#    key-alias: zlz-mall
#    key-password: zlzmall123
    key-store: /etc/letsencrypt/live/www.zhuanglz.cn/keystore.p12
    key-store-password: changeit
    key-store-type: PKCS12
    key-alias: tomcat
   # HTTP重定向端口
  http:
    port: 8080

# 抖音小程序配置
tt:
  appid: tt29ef5f52b6e798df01
  secret: 920d8a8d1ed2da34ba8056d094e4ea563cd32473

# MyBatis-Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
  global-config:
    db-config:
      id-type: auto
      table-underline: true

# 文件上传配置
file:
  upload:
    path: ./uploads/
