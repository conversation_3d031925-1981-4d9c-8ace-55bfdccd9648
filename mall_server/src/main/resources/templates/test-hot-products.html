<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>热门产品API测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }

        .test-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .product-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            background: #f8f9fa;
        }

        .product-image {
            width: 100px;
            height: 100px;
            object-fit: cover;
            border-radius: 8px;
        }

        .json-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1rem;
            font-family: monospace;
            font-size: 0.9rem;
            max-height: 400px;
            overflow-y: auto;
        }

        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
        }

        .status-success {
            background-color: #d4edda;
            color: #155724;
        }

        .status-error {
            background-color: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-white text-center mb-4">🔥 热门产品API测试</h1>
        
        <!-- 测试控制面板 -->
        <div class="test-card">
            <h5 class="mb-3">
                <i class="bi bi-gear me-2"></i>测试控制面板
            </h5>
            <div class="row">
                <div class="col-md-4">
                    <label for="limitCount" class="form-label">限制数量</label>
                    <input type="number" class="form-control" id="limitCount" value="2" min="1" max="10">
                </div>
                <div class="col-md-4">
                    <label for="sortType" class="form-label">排序方式</label>
                    <select class="form-select" id="sortType">
                        <option value="sortOrder">按排序权重</option>
                        <option value="sales">按销量</option>
                        <option value="price">按价格</option>
                        <option value="default">默认排序</option>
                    </select>
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <button class="btn btn-primary me-2" onclick="testHotProducts()">
                        <i class="bi bi-play me-1"></i>测试热门产品
                    </button>
                    <button class="btn btn-outline-secondary" onclick="clearResults()">
                        <i class="bi bi-trash me-1"></i>清空结果
                    </button>
                </div>
            </div>
        </div>

        <!-- API响应测试 -->
        <div class="test-card">
            <h5 class="mb-3">
                <i class="bi bi-cloud-download me-2"></i>API响应测试
            </h5>
            <div id="apiResults">
                <div class="text-muted">等待测试...</div>
            </div>
        </div>

        <!-- 产品展示 -->
        <div class="test-card">
            <h5 class="mb-3">
                <i class="bi bi-grid me-2"></i>热门产品展示
            </h5>
            <div id="productDisplay">
                <div class="text-muted">等待测试...</div>
            </div>
        </div>

        <!-- 数据库排序验证 -->
        <div class="test-card">
            <h5 class="mb-3">
                <i class="bi bi-sort-down me-2"></i>数据库排序验证
            </h5>
            <div id="sortValidation">
                <div class="text-muted">等待测试...</div>
            </div>
        </div>

        <!-- 返回按钮 -->
        <div class="text-center">
            <a href="/admin/products" class="btn btn-light">
                <i class="bi bi-arrow-left me-1"></i>返回产品管理
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 测试热门产品API
        async function testHotProducts() {
            const limitCount = document.getElementById('limitCount').value;
            const sortType = document.getElementById('sortType').value;
            
            // 清空之前的结果
            document.getElementById('apiResults').innerHTML = '<div class="text-info">正在测试...</div>';
            document.getElementById('productDisplay').innerHTML = '<div class="text-info">正在加载...</div>';
            document.getElementById('sortValidation').innerHTML = '<div class="text-info">正在验证...</div>';

            try {
                // 测试API响应
                const url = `/api/products/list?sortType=${sortType}&limit=${limitCount}`;
                console.log('请求URL:', url);
                
                const response = await fetch(url);
                const data = await response.json();

                displayApiResults(data, url);
                
                if (data.success && data.data) {
                    displayProducts(data.data);
                    validateSorting(data.data, sortType);
                } else {
                    document.getElementById('productDisplay').innerHTML = 
                        '<div class="alert alert-warning">API返回失败，无法显示产品</div>';
                    document.getElementById('sortValidation').innerHTML = 
                        '<div class="alert alert-warning">API返回失败，无法验证排序</div>';
                }
            } catch (error) {
                console.error('测试失败:', error);
                document.getElementById('apiResults').innerHTML = 
                    `<div class="alert alert-danger">测试失败: ${error.message}</div>`;
            }
        }

        // 显示API结果
        function displayApiResults(data, url) {
            const container = document.getElementById('apiResults');
            
            let html = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>API状态</h6>
                        <span class="status-badge ${data.success ? 'status-success' : 'status-error'}">
                            ${data.success ? '✅ 成功' : '❌ 失败'}
                        </span>
                        <p class="mt-2"><strong>请求URL:</strong> ${url}</p>
                        <p><strong>消息:</strong> ${data.message || '无'}</p>
                        <p><strong>数据数量:</strong> ${data.data ? data.data.length : 0}</p>
                    </div>
                    <div class="col-md-6">
                        <h6>响应数据</h6>
                        <div class="json-display">
                            ${JSON.stringify(data, null, 2)}
                        </div>
                    </div>
                </div>
            `;
            
            container.innerHTML = html;
        }

        // 显示产品
        function displayProducts(products) {
            const container = document.getElementById('productDisplay');
            
            if (!products || products.length === 0) {
                container.innerHTML = '<div class="alert alert-warning">没有找到产品数据</div>';
                return;
            }
            
            let html = '<div class="row">';
            products.forEach((product, index) => {
                const imageUrl = processImageUrl(product.mainImage);
                
                html += `
                    <div class="col-md-6 mb-3">
                        <div class="product-card">
                            <div class="row">
                                <div class="col-4">
                                    <img src="${imageUrl}" class="product-image" alt="${product.name}" 
                                         onerror="this.src='/images/products/充电线缆.png'">
                                </div>
                                <div class="col-8">
                                    <h6>${product.name}</h6>
                                    <p class="text-primary"><strong>¥${product.price}</strong></p>
                                    <p class="text-muted mb-1">销量: ${product.sales || 0}</p>
                                    <p class="text-muted mb-1">库存: ${product.stock || 0}</p>
                                    <p class="text-muted mb-1">排序权重: ${product.sortOrder || 0}</p>
                                    <p class="text-muted mb-0">ID: ${product.id}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });
            html += '</div>';
            
            container.innerHTML = html;
        }

        // 验证排序
        function validateSorting(products, sortType) {
            const container = document.getElementById('sortValidation');
            
            if (!products || products.length === 0) {
                container.innerHTML = '<div class="alert alert-warning">没有数据可验证</div>';
                return;
            }
            
            let isCorrectlySorted = true;
            let sortField = '';
            let sortOrder = 'DESC';
            
            switch (sortType) {
                case 'sortOrder':
                    sortField = 'sort_order';
                    break;
                case 'sales':
                    sortField = 'sales';
                    break;
                case 'price':
                    sortField = 'price';
                    break;
                default:
                    sortField = 'sort_order';
            }
            
            // 检查排序是否正确
            for (let i = 1; i < products.length; i++) {
                const prev = products[i - 1];
                const curr = products[i];
                
                let prevValue, currValue;
                
                if (sortField === 'sort_order') {
                    prevValue = prev.sortOrder || 0;
                    currValue = curr.sortOrder || 0;
                } else if (sortField === 'sales') {
                    prevValue = prev.sales || 0;
                    currValue = curr.sales || 0;
                } else if (sortField === 'price') {
                    prevValue = parseFloat(prev.price) || 0;
                    currValue = parseFloat(curr.price) || 0;
                }
                
                if (sortOrder === 'DESC' && prevValue < currValue) {
                    isCorrectlySorted = false;
                    break;
                }
            }
            
            let html = `
                <div class="alert ${isCorrectlySorted ? 'alert-success' : 'alert-danger'}">
                    <h6>${isCorrectlySorted ? '✅ 排序正确' : '❌ 排序错误'}</h6>
                    <p><strong>排序字段:</strong> ${sortField}</p>
                    <p><strong>排序方式:</strong> ${sortOrder}</p>
                </div>
                
                <h6>排序详情:</h6>
                <div class="row">
            `;
            
            products.forEach((product, index) => {
                let value;
                if (sortField === 'sort_order') {
                    value = product.sortOrder || 0;
                } else if (sortField === 'sales') {
                    value = product.sales || 0;
                } else if (sortField === 'price') {
                    value = parseFloat(product.price) || 0;
                }
                
                html += `
                    <div class="col-md-6 mb-2">
                        <div class="d-flex justify-content-between align-items-center p-2 border rounded">
                            <span>${product.name}</span>
                            <span class="badge bg-primary">${sortField}: ${value}</span>
                        </div>
                    </div>
                `;
            });
            
            html += '</div>';
            container.innerHTML = html;
        }

        // 处理图片URL
        function processImageUrl(imageUrl) {
            if (!imageUrl || imageUrl.trim() === '') {
                return '/images/products/充电线缆.png';
            }
            
            if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
                return imageUrl;
            }
            
            if (imageUrl.startsWith('/uploads/')) {
                return `https://localhost:8443${imageUrl}`;
            }
            
            return `https://localhost:8443/uploads/${imageUrl}`;
        }

        // 清空结果
        function clearResults() {
            document.getElementById('apiResults').innerHTML = '<div class="text-muted">等待测试...</div>';
            document.getElementById('productDisplay').innerHTML = '<div class="text-muted">等待测试...</div>';
            document.getElementById('sortValidation').innerHTML = '<div class="text-muted">等待测试...</div>';
        }

        // 页面加载时自动测试
        document.addEventListener('DOMContentLoaded', function() {
            testHotProducts();
        });
    </script>
</body>
</html>
