<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品图片显示测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .product-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        .product-image {
            width: 200px;
            height: 200px;
            object-fit: cover;
            border-radius: 8px;
        }
        .image-url {
            font-size: 0.8rem;
            color: #666;
            word-break: break-all;
        }
        .status {
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="mb-4">🖼️ 商品图片显示测试</h1>
        
        <div class="row mb-4">
            <div class="col-md-6">
                <button class="btn btn-primary" onclick="testProductAPI()">测试商品API</button>
            </div>
            <div class="col-md-6">
                <button class="btn btn-success" onclick="testImageAccess()">测试图片访问</button>
            </div>
        </div>

        <div id="results"></div>
    </div>

    <script>
        // 测试商品API
        async function testProductAPI() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="alert alert-info">正在测试商品API...</div>';

            try {
                const response = await fetch('/api/products/list');
                const data = await response.json();

                if (data.success && data.data.length > 0) {
                    let html = '<h3>📦 商品列表测试结果</h3>';
                    
                    data.data.slice(0, 5).forEach((product, index) => {
                        html += `
                            <div class="product-card">
                                <div class="row">
                                    <div class="col-md-3">
                                        <img src="${product.mainImage}" 
                                             class="product-image" 
                                             alt="${product.name}"
                                             onload="markImageStatus(this, 'success')"
                                             onerror="markImageStatus(this, 'error')">
                                    </div>
                                    <div class="col-md-9">
                                        <h5>${product.name}</h5>
                                        <p><strong>价格:</strong> ¥${product.price}</p>
                                        <p><strong>主图URL:</strong></p>
                                        <div class="image-url">${product.mainImage}</div>
                                        <div class="mt-2">
                                            <span class="status" id="status-${index}">加载中...</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;
                    });

                    resultsDiv.innerHTML = html;
                } else {
                    resultsDiv.innerHTML = '<div class="alert alert-warning">没有找到商品数据</div>';
                }
            } catch (error) {
                resultsDiv.innerHTML = `<div class="alert alert-danger">API测试失败: ${error.message}</div>`;
            }
        }

        // 测试图片访问
        async function testImageAccess() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="alert alert-info">正在测试图片访问...</div>';

            const testUrls = [
                '/uploads/test-image.jpg',
                'https://localhost:8443/uploads/test-image.jpg',
                '/uploads/product_main_20250615_143022_a1b2c3d4.jpg'
            ];

            let html = '<h3>🔗 图片访问测试结果</h3>';

            for (let i = 0; i < testUrls.length; i++) {
                const url = testUrls[i];
                html += `
                    <div class="product-card">
                        <div class="row">
                            <div class="col-md-3">
                                <img src="${url}" 
                                     class="product-image" 
                                     alt="测试图片 ${i + 1}"
                                     onload="markImageStatus(this, 'success')"
                                     onerror="markImageStatus(this, 'error')">
                            </div>
                            <div class="col-md-9">
                                <h5>测试图片 ${i + 1}</h5>
                                <p><strong>URL:</strong></p>
                                <div class="image-url">${url}</div>
                                <div class="mt-2">
                                    <span class="status" id="test-status-${i}">加载中...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }

            resultsDiv.innerHTML = html;
        }

        // 标记图片加载状态
        function markImageStatus(img, status) {
            const statusElements = document.querySelectorAll('.status');
            statusElements.forEach((element, index) => {
                if (element.textContent === '加载中...') {
                    element.textContent = status === 'success' ? '✅ 加载成功' : '❌ 加载失败';
                    element.className = `status ${status}`;
                    return false; // 只更新第一个找到的元素
                }
            });
        }

        // 页面加载时自动测试
        window.onload = function() {
            testProductAPI();
        };
    </script>
</body>
</html>
