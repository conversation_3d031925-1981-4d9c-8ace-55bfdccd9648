<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客服API测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #1e88e5;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
        }
        button {
            background: #1e88e5;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0d47a1;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 3px;
            box-sizing: border-box;
        }
        .result {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background: #d4edda;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎧 客服系统API测试</h1>
        <p>这个页面用于测试小程序客服API接口的功能</p>
    </div>

    <div class="container">
        <div class="test-section">
            <h2>1. 创建会话</h2>
            <input type="text" id="openId" placeholder="输入openId (例如: test_user_123)" value="test_user_123">
            <button onclick="createSession()">创建会话</button>
            <div id="sessionResult" class="result"></div>
        </div>

        <div class="test-section">
            <h2>2. 发送消息</h2>
            <input type="text" id="sessionId" placeholder="会话ID (从上面获取)">
            <input type="text" id="messageOpenId" placeholder="openId" value="test_user_123">
            <textarea id="messageContent" placeholder="消息内容" rows="3">你好，我的充电桩无法充电，请帮忙看看</textarea>
            <button onclick="sendMessage()">发送消息</button>
            <div id="messageResult" class="result"></div>
        </div>

        <div class="test-section">
            <h2>3. 获取消息历史</h2>
            <input type="text" id="getSessionId" placeholder="会话ID">
            <button onclick="getMessages()">获取消息</button>
            <div id="messagesResult" class="result"></div>
        </div>

        <div class="test-section">
            <h2>4. 标记已读</h2>
            <input type="text" id="readSessionId" placeholder="会话ID">
            <button onclick="markAsRead()">标记已读</button>
            <div id="readResult" class="result"></div>
        </div>

        <div class="test-section">
            <h2>5. 获取会话状态</h2>
            <input type="text" id="statusSessionId" placeholder="会话ID">
            <button onclick="getSessionStatus()">获取状态</button>
            <div id="statusResult" class="result"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8080/api/customer-service';

        function showResult(elementId, data, isSuccess = true) {
            const element = document.getElementById(elementId);
            element.textContent = JSON.stringify(data, null, 2);
            element.className = `result ${isSuccess ? 'success' : 'error'}`;
        }

        async function createSession() {
            const openId = document.getElementById('openId').value;
            try {
                const response = await fetch(`${API_BASE}/session`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ openId })
                });
                const data = await response.json();
                showResult('sessionResult', data, data.success);
                
                // 自动填充会话ID到其他输入框
                if (data.success && data.data.sessionId) {
                    document.getElementById('sessionId').value = data.data.sessionId;
                    document.getElementById('getSessionId').value = data.data.sessionId;
                    document.getElementById('readSessionId').value = data.data.sessionId;
                    document.getElementById('statusSessionId').value = data.data.sessionId;
                }
            } catch (error) {
                showResult('sessionResult', { error: error.message }, false);
            }
        }

        async function sendMessage() {
            const sessionId = document.getElementById('sessionId').value;
            const openId = document.getElementById('messageOpenId').value;
            const content = document.getElementById('messageContent').value;
            
            try {
                const response = await fetch(`${API_BASE}/message`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ sessionId, openId, content })
                });
                const data = await response.json();
                showResult('messageResult', data, data.success);
            } catch (error) {
                showResult('messageResult', { error: error.message }, false);
            }
        }

        async function getMessages() {
            const sessionId = document.getElementById('getSessionId').value;
            try {
                const response = await fetch(`${API_BASE}/messages/${sessionId}`);
                const data = await response.json();
                showResult('messagesResult', data, data.success);
            } catch (error) {
                showResult('messagesResult', { error: error.message }, false);
            }
        }

        async function markAsRead() {
            const sessionId = document.getElementById('readSessionId').value;
            try {
                const response = await fetch(`${API_BASE}/read`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ sessionId })
                });
                const data = await response.json();
                showResult('readResult', data, data.success);
            } catch (error) {
                showResult('readResult', { error: error.message }, false);
            }
        }

        async function getSessionStatus() {
            const sessionId = document.getElementById('statusSessionId').value;
            try {
                const response = await fetch(`${API_BASE}/status/${sessionId}`);
                const data = await response.json();
                showResult('statusResult', data, data.success);
            } catch (error) {
                showResult('statusResult', { error: error.message }, false);
            }
        }
    </script>
</body>
</html>
