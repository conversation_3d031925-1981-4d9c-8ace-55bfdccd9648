<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品添加功能测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }

        .test-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .field-test {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            background: #f8f9fa;
        }

        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
        }

        .status-success {
            background-color: #d4edda;
            color: #155724;
        }

        .status-error {
            background-color: #f8d7da;
            color: #721c24;
        }

        .status-warning {
            background-color: #fff3cd;
            color: #856404;
        }

        .json-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1rem;
            font-family: monospace;
            font-size: 0.9rem;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-white text-center mb-4">🧪 产品添加功能测试</h1>
        
        <!-- 测试表单 -->
        <div class="test-card">
            <h5 class="mb-3">
                <i class="bi bi-plus-circle me-2"></i>测试产品添加
            </h5>
            <form id="testProductForm" enctype="multipart/form-data">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">产品名称 *</label>
                            <input type="text" class="form-control" name="name" value="测试产品-智能充电桩" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">产品简称</label>
                            <input type="text" class="form-control" name="shortName" value="智能桩">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">品牌</label>
                            <input type="text" class="form-control" name="brand" value="测试品牌">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">型号</label>
                            <input type="text" class="form-control" name="model" value="TEST-001">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">分类 *</label>
                            <select class="form-select" name="categoryId" required>
                                <option value="1">充电枪</option>
                                <option value="2">线缆</option>
                                <option value="3">模块</option>
                                <option value="4">保护器</option>
                                <option value="5">配件</option>
                                <option value="6">工具</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">售价 *</label>
                            <input type="number" class="form-control" name="price" value="999.00" step="0.01" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">原价</label>
                            <input type="number" class="form-control" name="originalPrice" value="1299.00" step="0.01">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">库存 *</label>
                            <input type="number" class="form-control" name="stock" value="100" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">销量</label>
                            <input type="number" class="form-control" name="sales" value="50">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">重量 (kg)</label>
                            <input type="number" class="form-control" name="weight" value="2.5" step="0.01">
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">尺寸</label>
                            <input type="text" class="form-control" name="dimensions" value="200×150×100mm">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">保修期</label>
                            <input type="text" class="form-control" name="warrantyPeriod" value="2年">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">适用车型</label>
                            <textarea class="form-control" name="compatibleCars" rows="2">特斯拉、比亚迪、蔚来等主流电动汽车</textarea>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">产品描述</label>
                            <textarea class="form-control" name="description" rows="3">这是一个测试产品，用于验证产品添加功能是否正常工作。</textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">主图 *</label>
                            <input type="file" class="form-control" name="mainImageFile" accept="image/*" required>
                        </div>
                    </div>
                </div>

                <!-- JSON字段 -->
                <div class="row">
                    <div class="col-md-12">
                        <h6>JSON字段测试</h6>
                        <input type="hidden" name="features" value='["智能充电","快速充电","安全保护","远程监控"]'>
                        <input type="hidden" name="specifications" value='[{"name":"功率","value":"7kW"},{"name":"电压","value":"220V"},{"name":"电流","value":"32A"}]'>
                        <input type="hidden" name="specs" value='[{"id":1,"name":"标准版"},{"id":2,"name":"豪华版"}]'>
                        <input type="hidden" name="services" value='["包邮","7天无理由退换","质保2年"]'>
                        <input type="hidden" name="seoTitle" value="测试产品SEO标题">
                        <input type="hidden" name="seoKeywords" value="充电桩,智能,测试">
                        <input type="hidden" name="seoDescription" value="这是一个测试产品的SEO描述">
                        <input type="hidden" name="status" value="1">
                    </div>
                </div>

                <div class="text-center">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="bi bi-plus-circle me-1"></i>测试添加产品
                    </button>
                </div>
            </form>
        </div>

        <!-- 测试结果 -->
        <div class="test-card">
            <h5 class="mb-3">
                <i class="bi bi-clipboard-data me-2"></i>测试结果
            </h5>
            <div id="testResults">
                <div class="text-muted">等待测试...</div>
            </div>
        </div>

        <!-- 字段验证 -->
        <div class="test-card">
            <h5 class="mb-3">
                <i class="bi bi-check-square me-2"></i>字段验证
            </h5>
            <div id="fieldValidation">
                <div class="text-muted">提交后显示字段验证结果...</div>
            </div>
        </div>

        <!-- 返回按钮 -->
        <div class="text-center">
            <a href="/admin/products" class="btn btn-light">
                <i class="bi bi-arrow-left me-1"></i>返回产品管理
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.getElementById('testProductForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const resultsDiv = document.getElementById('testResults');
            const validationDiv = document.getElementById('fieldValidation');
            
            resultsDiv.innerHTML = '<div class="alert alert-info">正在提交测试产品...</div>';
            
            try {
                const formData = new FormData(this);
                
                // 显示提交的数据
                console.log('提交的表单数据:');
                for (let [key, value] of formData.entries()) {
                    console.log(key + ':', value);
                }
                
                const response = await fetch('/admin/api/products', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                // 显示结果
                if (result.success) {
                    resultsDiv.innerHTML = `
                        <div class="alert alert-success">
                            <h6>✅ 产品添加成功！</h6>
                            <p><strong>产品ID:</strong> ${result.data.productId}</p>
                            <p><strong>产品名称:</strong> ${result.data.name}</p>
                            <p><strong>主图:</strong> ${result.data.mainImage}</p>
                        </div>
                        <div class="json-display">
                            ${JSON.stringify(result, null, 2)}
                        </div>
                    `;
                    
                    // 验证产品详情
                    setTimeout(() => {
                        verifyProductDetail(result.data.productId);
                    }, 1000);
                } else {
                    resultsDiv.innerHTML = `
                        <div class="alert alert-danger">
                            <h6>❌ 产品添加失败</h6>
                            <p>${result.message}</p>
                        </div>
                        <div class="json-display">
                            ${JSON.stringify(result, null, 2)}
                        </div>
                    `;
                }
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <h6>🚨 请求失败</h6>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        });

        // 验证产品详情
        async function verifyProductDetail(productId) {
            const validationDiv = document.getElementById('fieldValidation');
            
            try {
                const response = await fetch(`/api/products/detail/${productId}`);
                const result = await response.json();
                
                if (result.success) {
                    const product = result.data.product;
                    
                    // 检查字段完整性
                    const fields = [
                        { name: 'name', label: '产品名称', value: product.name },
                        { name: 'shortName', label: '产品简称', value: product.shortName },
                        { name: 'brand', label: '品牌', value: product.brand },
                        { name: 'model', label: '型号', value: product.model },
                        { name: 'price', label: '价格', value: product.price },
                        { name: 'originalPrice', label: '原价', value: product.originalPrice },
                        { name: 'stock', label: '库存', value: product.stock },
                        { name: 'sales', label: '销量', value: product.sales },
                        { name: 'weight', label: '重量', value: product.weight },
                        { name: 'dimensions', label: '尺寸', value: product.dimensions },
                        { name: 'warrantyPeriod', label: '保修期', value: product.warrantyPeriod },
                        { name: 'compatibleCars', label: '适用车型', value: product.compatibleCars },
                        { name: 'description', label: '描述', value: product.description },
                        { name: 'features', label: '特点', value: product.features },
                        { name: 'specifications', label: '规格参数', value: product.specifications },
                        { name: 'specs', label: '规格选项', value: product.specs },
                        { name: 'services', label: '服务', value: product.services },
                        { name: 'mainImage', label: '主图', value: product.mainImage },
                        { name: 'images', label: '轮播图', value: product.images },
                        { name: 'detailImage', label: '详情图', value: product.detailImage }
                    ];
                    
                    let html = '<div class="row">';
                    fields.forEach((field, index) => {
                        if (index % 4 === 0 && index > 0) {
                            html += '</div><div class="row">';
                        }
                        
                        const hasValue = field.value !== null && field.value !== undefined && field.value !== '';
                        const status = hasValue ? 'success' : 'warning';
                        
                        html += `
                            <div class="col-md-3 mb-2">
                                <div class="field-test">
                                    <span class="status-badge status-${status}">
                                        ${hasValue ? '✅' : '⚠️'} ${field.label}
                                    </span>
                                    <div class="mt-1" style="font-size: 0.8rem; color: #666;">
                                        ${hasValue ? (field.value.toString().length > 50 ? field.value.toString().substring(0, 50) + '...' : field.value) : '无数据'}
                                    </div>
                                </div>
                            </div>
                        `;
                    });
                    html += '</div>';
                    
                    validationDiv.innerHTML = html;
                } else {
                    validationDiv.innerHTML = `
                        <div class="alert alert-warning">
                            无法获取产品详情进行验证: ${result.message}
                        </div>
                    `;
                }
            } catch (error) {
                validationDiv.innerHTML = `
                    <div class="alert alert-danger">
                        验证失败: ${error.message}
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
