<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快速审核服务网点</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .service-center {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            background: #f9f9f9;
        }
        .status {
            padding: 5px 10px;
            border-radius: 4px;
            font-weight: bold;
            color: white;
        }
        .status.pending { background: #ffc107; color: #000; }
        .status.approved { background: #28a745; }
        .status.rejected { background: #dc3545; }
        .status.suspended { background: #6c757d; }
        .approve-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .approve-btn:hover {
            background: #218838;
        }
        .result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <h1>🔧 快速审核服务网点</h1>
    
    <div>
        <button onclick="loadServiceCenters()" style="background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">
            刷新服务网点列表
        </button>
        <button onclick="approveAllPending()" style="background: #28a745; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin-left: 10px;">
            批量审核通过所有待审核
        </button>
    </div>
    
    <div id="result" class="result" style="display: none;"></div>
    
    <div id="serviceCentersList">
        <!-- 服务网点列表将在这里显示 -->
    </div>

    <script>
        // 页面加载时获取服务网点列表
        document.addEventListener('DOMContentLoaded', function() {
            loadServiceCenters();
        });

        // 加载服务网点列表
        async function loadServiceCenters() {
            try {
                // 获取统计信息
                const statsResponse = await fetch('/api/service-centers/stats');
                const statsResult = await statsResponse.json();
                
                if (statsResult.success) {
                    displayStats(statsResult.data);
                }
                
                // 获取已审核通过的服务网点
                const approvedResponse = await fetch('/api/service-centers/approved');
                const approvedResult = await approvedResponse.json();
                
                if (approvedResult.success) {
                    displayServiceCenters(approvedResult.data, 'approved');
                }
                
            } catch (error) {
                showResult('加载失败: ' + error.message, 'error');
            }
        }

        // 显示统计信息
        function displayStats(stats) {
            const statsHtml = `
                <div style="background: #e9ecef; padding: 15px; border-radius: 8px; margin: 20px 0;">
                    <h3>📊 服务网点统计</h3>
                    <p><strong>总数:</strong> ${stats.total}</p>
                    <p><strong>已审核通过:</strong> ${stats.approved}</p>
                    <p><strong>待审核:</strong> ${stats.pending || 0}</p>
                    <p><strong>推荐网点:</strong> ${stats.featured}</p>
                </div>
            `;
            
            document.getElementById('serviceCentersList').innerHTML = statsHtml + document.getElementById('serviceCentersList').innerHTML;
        }

        // 显示服务网点列表
        function displayServiceCenters(serviceCenters, status) {
            const listContainer = document.getElementById('serviceCentersList');
            
            const html = `
                <h3>📋 ${status === 'approved' ? '已审核通过' : '待审核'}的服务网点 (${serviceCenters.length}个)</h3>
                ${serviceCenters.map(center => `
                    <div class="service-center">
                        <h4>${center.name}</h4>
                        <p><strong>ID:</strong> ${center.id}</p>
                        <p><strong>地址:</strong> ${center.address}</p>
                        <p><strong>电话:</strong> ${center.phone}</p>
                        <p><strong>联系人:</strong> ${center.contactPerson}</p>
                        <p><strong>状态:</strong> <span class="status ${center.status}">${getStatusText(center.status)}</span></p>
                        <p><strong>是否激活:</strong> ${center.isActive ? '是' : '否'}</p>
                        <p><strong>申请时间:</strong> ${center.applicationTime || center.createdAt}</p>
                        ${center.reviewTime ? `<p><strong>审核时间:</strong> ${center.reviewTime}</p>` : ''}
                        ${center.reviewNotes ? `<p><strong>审核备注:</strong> ${center.reviewNotes}</p>` : ''}
                    </div>
                `).join('')}
            `;
            
            listContainer.innerHTML += html;
        }

        // 获取状态文本
        function getStatusText(status) {
            const texts = {
                'pending': '待审核',
                'approved': '已通过',
                'rejected': '已拒绝',
                'suspended': '已暂停',
                'closed': '已关闭'
            };
            return texts[status] || status;
        }

        // 批量审核通过所有待审核的服务网点
        async function approveAllPending() {
            if (!confirm('确定要批量审核通过所有待审核的服务网点吗？')) {
                return;
            }

            try {
                showResult('正在批量审核...', 'success');
                
                // 这里需要调用后端API来批量审核
                // 由于我们的快速测试控制器可能还没生效，我们提供手动指导
                showResult(`
                    批量审核功能需要后端支持。请按以下步骤手动操作：
                    
                    1. 登录管理后台: https://localhost:8443/admin/login
                    2. 进入服务网点管理: https://localhost:8443/admin/service-centers
                    3. 查看所有状态的服务网点
                    4. 对每个待审核的服务网点点击"通过申请"
                    
                    或者直接在数据库中执行SQL：
                    UPDATE service_centers 
                    SET status = 'approved', isActive = true, reviewTime = NOW()
                    WHERE id > 1 AND status != 'approved';
                `, 'success');
                
            } catch (error) {
                showResult('批量审核失败: ' + error.message, 'error');
            }
        }

        // 显示结果消息
        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = message.replace(/\n/g, '<br>');
            resultDiv.style.display = 'block';
        }
    </script>
</body>
</html>
