<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>服务网点申请图片测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .image-gallery {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 20px 0;
        }
        .test-image {
            width: 120px;
            height: 120px;
            border-radius: 8px;
            border: 2px solid #e0e0e0;
            object-fit: cover;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .test-image:hover {
            border-color: #1e88e5;
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        .business-license {
            max-width: 300px;
            max-height: 200px;
            border-radius: 8px;
            border: 2px solid #e0e0e0;
            object-fit: contain;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .business-license:hover {
            border-color: #1e88e5;
            transform: scale(1.02);
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
        }
        .test-url {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
        }
        .status {
            padding: 5px 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>🧪 服务网点申请图片测试页面</h1>
    
    <!-- 测试1: 营业执照显示 -->
    <div class="test-section">
        <h2>📋 测试1: 营业执照显示</h2>
        <div class="test-url">营业执照URL: /uploads/20250610_143914_aea5df47.png</div>
        <img src="/uploads/20250610_143914_aea5df47.png" class="business-license" 
             alt="营业执照"
             onload="updateStatus('test1', 'success', '营业执照加载成功')"
             onerror="updateStatus('test1', 'error', '营业执照加载失败')">
        <div id="test1" class="status info">加载中...</div>
    </div>

    <!-- 测试2: 资质证书显示 -->
    <div class="test-section">
        <h2>📋 测试2: 资质证书显示</h2>
        <p>模拟资质证书JSON数据:</p>
        <div class="test-url">["\/uploads\/20250610_143914_aea5df47.png", "\/uploads\/20250610_143914_4a433ed2.png"]</div>
        <div class="certificates-gallery" data-certificates='["/uploads/20250610_143914_aea5df47.png", "/uploads/20250610_143914_4a433ed2.png"]'>
            <!-- 资质证书将通过JavaScript动态加载 -->
        </div>
        <div id="test2" class="status info">等待JavaScript处理...</div>
    </div>

    <!-- 测试3: 网点照片显示 -->
    <div class="test-section">
        <h2>📋 测试3: 网点照片显示</h2>
        <p>模拟网点照片JSON数据:</p>
        <div class="test-url">["\/uploads\/20250610_143914_aea5df47.png", "\/uploads\/20250610_143914_4a433ed2.png"]</div>
        <div class="station-images-gallery" data-images='["/uploads/20250610_143914_aea5df47.png", "/uploads/20250610_143914_4a433ed2.png"]'>
            <!-- 网点照片将通过JavaScript动态加载 -->
        </div>
        <div id="test3" class="status info">等待JavaScript处理...</div>
    </div>

    <!-- 测试4: 服务网点数据模拟 -->
    <div class="test-section">
        <h2>📋 测试4: 完整服务网点数据模拟</h2>
        <button onclick="testServiceCenterData()">模拟服务网点详情</button>
        <div id="serviceCenterDetailContent"></div>
        <div id="test4" class="status info">点击按钮开始测试</div>
    </div>

    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 页面加载完成，开始测试');
            initTestImages();
        });

        // 更新状态显示
        function updateStatus(testId, type, message) {
            const element = document.getElementById(testId);
            element.className = `status ${type}`;
            element.textContent = message;
            console.log(`📊 ${testId}: ${message}`);
        }

        // 初始化测试图片
        function initTestImages() {
            // 初始化资质证书图片
            const certificatesGalleries = document.querySelectorAll('.certificates-gallery');
            certificatesGalleries.forEach(gallery => {
                const certificatesData = gallery.getAttribute('data-certificates');
                if (certificatesData) {
                    try {
                        const certificates = JSON.parse(certificatesData);
                        renderCertificateImages(gallery, certificates);
                        updateStatus('test2', 'success', `成功渲染${certificates.length}张资质证书`);
                    } catch (e) {
                        console.error('解析资质证书数据失败:', e);
                        updateStatus('test2', 'error', '解析JSON失败: ' + e.message);
                    }
                }
            });

            // 初始化网点照片
            const stationImagesGalleries = document.querySelectorAll('.station-images-gallery');
            stationImagesGalleries.forEach(gallery => {
                const imagesData = gallery.getAttribute('data-images');
                if (imagesData) {
                    try {
                        const images = JSON.parse(imagesData);
                        renderStationImages(gallery, images);
                        updateStatus('test3', 'success', `成功渲染${images.length}张网点照片`);
                    } catch (e) {
                        console.error('解析网点照片数据失败:', e);
                        updateStatus('test3', 'error', '解析JSON失败: ' + e.message);
                    }
                }
            });
        }

        // 渲染资质证书图片
        function renderCertificateImages(gallery, certificates) {
            gallery.innerHTML = '';
            
            certificates.forEach((url, index) => {
                const img = document.createElement('img');
                img.src = processImageUrl(url);
                img.className = 'test-image';
                img.alt = `资质证书 ${index + 1}`;
                img.onclick = () => showImagePreview(img.src);
                
                img.onload = () => console.log(`✅ 资质证书${index}加载成功:`, img.src);
                img.onerror = () => console.error(`❌ 资质证书${index}加载失败:`, img.src);
                
                gallery.appendChild(img);
            });
        }

        // 渲染网点照片
        function renderStationImages(gallery, images) {
            gallery.innerHTML = '';
            
            images.forEach((url, index) => {
                const img = document.createElement('img');
                img.src = processImageUrl(url);
                img.className = 'test-image';
                img.alt = `网点照片 ${index + 1}`;
                img.onclick = () => showImagePreview(img.src);
                
                img.onload = () => console.log(`✅ 网点照片${index}加载成功:`, img.src);
                img.onerror = () => console.error(`❌ 网点照片${index}加载失败:`, img.src);
                
                gallery.appendChild(img);
            });
        }

        // 处理图片URL，避免重复路径
        function processImageUrl(url) {
            if (url.startsWith('http')) {
                return url;
            } else if (url.startsWith('/uploads/')) {
                return url;
            } else {
                return '/uploads/' + url;
            }
        }

        // 显示图片预览
        function showImagePreview(imageSrc) {
            window.open(imageSrc, '_blank');
        }

        // 测试服务网点数据
        function testServiceCenterData() {
            const mockServiceCenter = {
                name: "测试服务网点",
                businessLicense: "/uploads/20250610_143914_aea5df47.png",
                qualificationCertificates: '["\/uploads\/20250610_143914_aea5df47.png", "\/uploads\/20250610_143914_4a433ed2.png"]',
                images: '["\/uploads\/20250610_143914_aea5df47.png", "\/uploads\/20250610_143914_4a433ed2.png"]'
            };

            const content = document.getElementById('serviceCenterDetailContent');
            content.innerHTML = `
                <div style="border: 1px solid #ddd; padding: 20px; border-radius: 8px; margin: 20px 0;">
                    <h3>${mockServiceCenter.name}</h3>
                    
                    <!-- 营业执照 -->
                    <div style="margin: 20px 0;">
                        <h4 style="color: #1e88e5;">📄 营业执照</h4>
                        <img src="${mockServiceCenter.businessLicense}" class="business-license" alt="营业执照">
                    </div>
                    
                    <!-- 资质证书 -->
                    <div style="margin: 20px 0;">
                        <h4 style="color: #4caf50;">🏆 资质证书</h4>
                        <div class="certificates-gallery-test" data-certificates="${mockServiceCenter.qualificationCertificates}">
                            <!-- 将通过JavaScript加载 -->
                        </div>
                    </div>
                    
                    <!-- 网点照片 -->
                    <div style="margin: 20px 0;">
                        <h4 style="color: #ff9800;">📷 网点照片</h4>
                        <div class="station-images-gallery-test" data-images="${mockServiceCenter.images}">
                            <!-- 将通过JavaScript加载 -->
                        </div>
                    </div>
                </div>
            `;

            // 初始化新添加的图片
            setTimeout(() => {
                const certificatesGallery = content.querySelector('.certificates-gallery-test');
                if (certificatesGallery) {
                    const certificatesData = certificatesGallery.getAttribute('data-certificates');
                    const certificates = JSON.parse(certificatesData);
                    renderCertificateImages(certificatesGallery, certificates);
                }

                const stationImagesGallery = content.querySelector('.station-images-gallery-test');
                if (stationImagesGallery) {
                    const imagesData = stationImagesGallery.getAttribute('data-images');
                    const images = JSON.parse(imagesData);
                    renderStationImages(stationImagesGallery, images);
                }

                updateStatus('test4', 'success', '服务网点数据模拟完成');
            }, 100);
        }
    </script>
</body>
</html>
