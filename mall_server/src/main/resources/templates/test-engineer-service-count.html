<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工程师服务次数自增功能测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }

        .test-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .engineer-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            background: #f8f9fa;
        }

        .order-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            background: #fff;
        }

        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
        }

        .status-pending { background-color: #fff3cd; color: #856404; }
        .status-accepted { background-color: #d1ecf1; color: #0c5460; }
        .status-processing { background-color: #d4edda; color: #155724; }
        .status-completed { background-color: #d1ecf1; color: #0c5460; }

        .count-display {
            font-size: 1.2rem;
            font-weight: bold;
            color: #007bff;
        }

        .log-area {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1rem;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-white text-center mb-4">🔧 工程师服务次数自增功能测试</h1>
        
        <!-- 测试控制面板 -->
        <div class="test-card">
            <h5 class="mb-3">
                <i class="bi bi-gear me-2"></i>测试控制面板
            </h5>
            <div class="row">
                <div class="col-md-3">
                    <label for="engineerId" class="form-label">工程师ID</label>
                    <input type="number" class="form-control" id="engineerId" value="1" min="1">
                </div>
                <div class="col-md-3">
                    <label for="orderId" class="form-label">订单ID</label>
                    <input type="number" class="form-control" id="orderId" value="1" min="1">
                </div>
                <div class="col-md-6 d-flex align-items-end">
                    <button class="btn btn-primary me-2" onclick="loadEngineerInfo()">
                        <i class="bi bi-person me-1"></i>加载工程师信息
                    </button>
                    <button class="btn btn-info me-2" onclick="loadOrderInfo()">
                        <i class="bi bi-list-task me-1"></i>加载订单信息
                    </button>
                    <button class="btn btn-outline-secondary" onclick="clearLogs()">
                        <i class="bi bi-trash me-1"></i>清空日志
                    </button>
                </div>
            </div>
        </div>

        <!-- 工程师信息展示 -->
        <div class="test-card">
            <h5 class="mb-3">
                <i class="bi bi-person-badge me-2"></i>工程师信息
            </h5>
            <div id="engineerInfo">
                <div class="text-muted">请先加载工程师信息...</div>
            </div>
        </div>

        <!-- 订单信息展示 -->
        <div class="test-card">
            <h5 class="mb-3">
                <i class="bi bi-list-task me-2"></i>订单信息
            </h5>
            <div id="orderInfo">
                <div class="text-muted">请先加载订单信息...</div>
            </div>
        </div>

        <!-- 测试操作 -->
        <div class="test-card">
            <h5 class="mb-3">
                <i class="bi bi-play-circle me-2"></i>测试操作
            </h5>
            <div class="row">
                <div class="col-md-6">
                    <h6>分配工程师测试</h6>
                    <p class="text-muted">将工程师分配给订单，会增加总接单数</p>
                    <button class="btn btn-success" onclick="testAssignEngineer()">
                        <i class="bi bi-person-plus me-1"></i>分配工程师
                    </button>
                </div>
                <div class="col-md-6">
                    <h6>完成订单测试</h6>
                    <p class="text-muted">将订单状态改为已完成，会增加完成订单数</p>
                    <button class="btn btn-warning" onclick="testCompleteOrder()">
                        <i class="bi bi-check-circle me-1"></i>完成订单
                    </button>
                </div>
            </div>
        </div>

        <!-- 操作日志 -->
        <div class="test-card">
            <h5 class="mb-3">
                <i class="bi bi-journal-text me-2"></i>操作日志
            </h5>
            <div id="logArea" class="log-area">
                <div class="text-muted">等待操作...</div>
            </div>
        </div>

        <!-- 返回按钮 -->
        <div class="text-center">
            <a href="/admin/engineers" class="btn btn-light me-2">
                <i class="bi bi-arrow-left me-1"></i>返回工程师管理
            </a>
            <a href="/admin/orders" class="btn btn-light">
                <i class="bi bi-arrow-left me-1"></i>返回订单管理
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 加载工程师信息
        async function loadEngineerInfo() {
            const engineerId = document.getElementById('engineerId').value;
            const container = document.getElementById('engineerInfo');
            
            container.innerHTML = '<div class="text-info">正在加载工程师信息...</div>';
            addLog(`🔍 加载工程师信息，ID: ${engineerId}`);

            try {
                const response = await fetch(`/admin/api/engineers/${engineerId}`);
                const data = await response.json();

                if (data.success && data.data) {
                    displayEngineerInfo(data.data);
                    addLog(`✅ 工程师信息加载成功`);
                } else {
                    container.innerHTML = '<div class="alert alert-warning">工程师信息加载失败</div>';
                    addLog(`❌ 工程师信息加载失败: ${data.message}`);
                }
            } catch (error) {
                console.error('加载工程师信息失败:', error);
                container.innerHTML = '<div class="alert alert-danger">请求失败</div>';
                addLog(`❌ 请求失败: ${error.message}`);
            }
        }

        // 加载订单信息
        async function loadOrderInfo() {
            const orderId = document.getElementById('orderId').value;
            const container = document.getElementById('orderInfo');
            
            container.innerHTML = '<div class="text-info">正在加载订单信息...</div>';
            addLog(`🔍 加载订单信息，ID: ${orderId}`);

            try {
                const response = await fetch(`/admin/api/orders/${orderId}`);
                const data = await response.json();

                if (data.success && data.data) {
                    displayOrderInfo(data.data);
                    addLog(`✅ 订单信息加载成功`);
                } else {
                    container.innerHTML = '<div class="alert alert-warning">订单信息加载失败</div>';
                    addLog(`❌ 订单信息加载失败: ${data.message}`);
                }
            } catch (error) {
                console.error('加载订单信息失败:', error);
                container.innerHTML = '<div class="alert alert-danger">请求失败</div>';
                addLog(`❌ 请求失败: ${error.message}`);
            }
        }

        // 测试分配工程师
        async function testAssignEngineer() {
            const engineerId = document.getElementById('engineerId').value;
            const orderId = document.getElementById('orderId').value;
            
            addLog(`🔄 开始测试分配工程师，订单ID: ${orderId}, 工程师ID: ${engineerId}`);

            try {
                const response = await fetch(`/admin/api/orders/${orderId}/assign`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `engineerId=${engineerId}`
                });
                const data = await response.json();

                if (data.success) {
                    addLog(`✅ 工程师分配成功: ${data.message}`);
                    addLog(`📊 工程师总接单数应该已增加`);
                    
                    // 重新加载信息验证
                    setTimeout(() => {
                        loadEngineerInfo();
                        loadOrderInfo();
                    }, 1000);
                } else {
                    addLog(`❌ 工程师分配失败: ${data.message}`);
                }
            } catch (error) {
                console.error('分配工程师失败:', error);
                addLog(`❌ 分配工程师请求失败: ${error.message}`);
            }
        }

        // 测试完成订单
        async function testCompleteOrder() {
            const orderId = document.getElementById('orderId').value;
            
            addLog(`🔄 开始测试完成订单，订单ID: ${orderId}`);

            try {
                const response = await fetch(`/admin/api/orders/${orderId}/status`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `status=completed&remark=测试完成订单`
                });
                const data = await response.json();

                if (data.success) {
                    addLog(`✅ 订单完成成功: ${data.message}`);
                    addLog(`📊 工程师完成订单数应该已增加`);
                    
                    // 重新加载信息验证
                    setTimeout(() => {
                        loadEngineerInfo();
                        loadOrderInfo();
                    }, 1000);
                } else {
                    addLog(`❌ 订单完成失败: ${data.message}`);
                }
            } catch (error) {
                console.error('完成订单失败:', error);
                addLog(`❌ 完成订单请求失败: ${error.message}`);
            }
        }

        // 显示工程师信息
        function displayEngineerInfo(engineer) {
            const container = document.getElementById('engineerInfo');
            
            const html = `
                <div class="engineer-card">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>${engineer.name}</h6>
                            <p class="mb-1"><strong>ID:</strong> ${engineer.id}</p>
                            <p class="mb-1"><strong>电话:</strong> ${engineer.phone}</p>
                            <p class="mb-1"><strong>状态:</strong> 
                                <span class="badge ${engineer.status === 'approved' ? 'bg-success' : 'bg-secondary'}">
                                    ${getStatusText(engineer.status)}
                                </span>
                            </p>
                        </div>
                        <div class="col-md-6">
                            <h6>服务统计</h6>
                            <p class="mb-1"><strong>总接单数:</strong> 
                                <span class="count-display">${engineer.totalOrders || 0}</span>
                            </p>
                            <p class="mb-1"><strong>完成订单数:</strong> 
                                <span class="count-display">${engineer.completedOrders || 0}</span>
                            </p>
                            <p class="mb-1"><strong>成功率:</strong> 
                                <span class="count-display">${engineer.successRate || 0}%</span>
                            </p>
                            <p class="mb-1"><strong>评分:</strong> 
                                <span class="count-display">${engineer.rating || 0}</span>
                            </p>
                        </div>
                    </div>
                </div>
            `;
            
            container.innerHTML = html;
        }

        // 显示订单信息
        function displayOrderInfo(order) {
            const container = document.getElementById('orderInfo');
            
            const html = `
                <div class="order-card">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>订单 ${order.orderNo}</h6>
                            <p class="mb-1"><strong>ID:</strong> ${order.id}</p>
                            <p class="mb-1"><strong>故障类型:</strong> ${order.faultType}</p>
                            <p class="mb-1"><strong>状态:</strong> 
                                <span class="status-badge status-${order.status}">
                                    ${getOrderStatusText(order.status)}
                                </span>
                            </p>
                        </div>
                        <div class="col-md-6">
                            <h6>工程师信息</h6>
                            <p class="mb-1"><strong>工程师ID:</strong> ${order.engineerId || '未分配'}</p>
                            <p class="mb-1"><strong>工程师姓名:</strong> ${order.engineerName || '未分配'}</p>
                            <p class="mb-1"><strong>工程师电话:</strong> ${order.engineerPhone || '未分配'}</p>
                            <p class="mb-1"><strong>完成时间:</strong> ${formatDateTime(order.completedAt) || '未完成'}</p>
                        </div>
                    </div>
                </div>
            `;
            
            container.innerHTML = html;
        }

        // 添加日志
        function addLog(message) {
            const logArea = document.getElementById('logArea');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            
            if (logArea.textContent.includes('等待操作...')) {
                logArea.innerHTML = '';
            }
            
            logArea.innerHTML += logEntry + '\n';
            logArea.scrollTop = logArea.scrollHeight;
        }

        // 清空日志
        function clearLogs() {
            document.getElementById('logArea').innerHTML = '<div class="text-muted">等待操作...</div>';
        }

        // 获取状态文本
        function getStatusText(status) {
            const statusMap = {
                'pending': '待审核',
                'approved': '已通过',
                'rejected': '已拒绝',
                'suspended': '已暂停',
                'active': '活跃中'
            };
            return statusMap[status] || status;
        }

        // 获取订单状态文本
        function getOrderStatusText(status) {
            const statusMap = {
                'pending': '待接单',
                'accepted': '待上门',
                'processing': '维修中',
                'completed': '已完成',
                'cancelled': '已取消'
            };
            return statusMap[status] || status;
        }

        // 格式化日期时间
        function formatDateTime(dateTimeStr) {
            if (!dateTimeStr) return null;
            const date = new Date(dateTimeStr);
            return date.toLocaleString('zh-CN');
        }

        // 页面加载时自动加载信息
        document.addEventListener('DOMContentLoaded', function() {
            loadEngineerInfo();
            loadOrderInfo();
        });
    </script>
</body>
</html>
