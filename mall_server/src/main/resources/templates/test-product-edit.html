<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品编辑功能测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }

        .test-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .api-test {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            background: #f8f9fa;
        }

        .json-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1rem;
            font-family: monospace;
            font-size: 0.9rem;
            max-height: 400px;
            overflow-y: auto;
        }

        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
        }

        .status-success {
            background-color: #d4edda;
            color: #155724;
        }

        .status-error {
            background-color: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-white text-center mb-4">🔧 产品编辑功能测试</h1>
        
        <!-- 测试控制面板 -->
        <div class="test-card">
            <h5 class="mb-3">
                <i class="bi bi-gear me-2"></i>测试控制面板
            </h5>
            <div class="row">
                <div class="col-md-4">
                    <label for="productId" class="form-label">产品ID</label>
                    <input type="number" class="form-control" id="productId" value="1" min="1">
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <button class="btn btn-primary me-2" onclick="testGetProductDetail()">
                        <i class="bi bi-eye me-1"></i>测试获取详情
                    </button>
                    <button class="btn btn-success me-2" onclick="testEditProduct()">
                        <i class="bi bi-pencil me-1"></i>测试编辑
                    </button>
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <button class="btn btn-outline-secondary" onclick="clearResults()">
                        <i class="bi bi-trash me-1"></i>清空结果
                    </button>
                </div>
            </div>
        </div>

        <!-- API测试结果 -->
        <div class="test-card">
            <h5 class="mb-3">
                <i class="bi bi-cloud-download me-2"></i>API测试结果
            </h5>
            <div id="apiResults">
                <div class="text-muted">等待测试...</div>
            </div>
        </div>

        <!-- 产品详情展示 -->
        <div class="test-card">
            <h5 class="mb-3">
                <i class="bi bi-info-circle me-2"></i>产品详情展示
            </h5>
            <div id="productDetails">
                <div class="text-muted">等待测试...</div>
            </div>
        </div>

        <!-- 编辑测试 -->
        <div class="test-card">
            <h5 class="mb-3">
                <i class="bi bi-pencil me-2"></i>编辑功能测试
            </h5>
            <div id="editTest">
                <div class="text-muted">点击"测试编辑"开始测试...</div>
            </div>
        </div>

        <!-- 返回按钮 -->
        <div class="text-center">
            <a href="/admin/products" class="btn btn-light">
                <i class="bi bi-arrow-left me-1"></i>返回产品管理
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 测试获取产品详情
        async function testGetProductDetail() {
            const productId = document.getElementById('productId').value;
            const apiResults = document.getElementById('apiResults');
            const productDetails = document.getElementById('productDetails');
            
            apiResults.innerHTML = '<div class="text-info">正在获取产品详情...</div>';
            productDetails.innerHTML = '<div class="text-info">正在加载...</div>';

            try {
                const response = await fetch(`/admin/api/products/${productId}`);
                const data = await response.json();

                displayApiResults('GET详情', `/admin/api/products/${productId}`, data);
                
                if (data.success && data.data) {
                    displayProductDetails(data.data);
                } else {
                    productDetails.innerHTML = '<div class="alert alert-warning">获取产品详情失败</div>';
                }
            } catch (error) {
                console.error('获取产品详情失败:', error);
                apiResults.innerHTML = `<div class="alert alert-danger">请求失败: ${error.message}</div>`;
            }
        }

        // 测试编辑产品
        async function testEditProduct() {
            const productId = document.getElementById('productId').value;
            const editTest = document.getElementById('editTest');
            
            editTest.innerHTML = '<div class="text-info">正在测试编辑功能...</div>';

            // 模拟编辑数据
            const formData = new FormData();
            formData.append('name', '测试编辑产品名称');
            formData.append('shortName', '测试简称');
            formData.append('brand', '测试品牌');
            formData.append('model', 'TEST-EDIT-001');
            formData.append('categoryId', '1');
            formData.append('description', '这是一个编辑测试的产品描述');
            formData.append('price', '888.88');
            formData.append('originalPrice', '999.99');
            formData.append('stock', '50');
            formData.append('sales', '25');
            formData.append('sortOrder', '10');
            formData.append('weight', '1.5');
            formData.append('dimensions', '150×100×80mm');
            formData.append('warrantyPeriod', '18个月');
            formData.append('compatibleCars', '测试车型A、测试车型B');
            formData.append('features', '["编辑测试特点1","编辑测试特点2","编辑测试特点3"]');
            formData.append('specifications', '[{"name":"测试参数1","value":"测试值1"},{"name":"测试参数2","value":"测试值2"}]');
            formData.append('specs', '[{"id":1,"name":"测试规格A"},{"id":2,"name":"测试规格B"}]');
            formData.append('services', '["编辑测试服务1","编辑测试服务2"]');
            formData.append('seoTitle', '编辑测试SEO标题');
            formData.append('seoKeywords', '编辑,测试,关键词');
            formData.append('seoDescription', '编辑测试SEO描述');
            formData.append('status', '1');

            try {
                const response = await fetch(`/admin/api/products/${productId}`, {
                    method: 'PUT',
                    body: formData
                });
                const data = await response.json();

                displayApiResults('PUT编辑', `/admin/api/products/${productId}`, data);
                
                if (data.success) {
                    editTest.innerHTML = `
                        <div class="alert alert-success">
                            <h6>✅ 编辑测试成功！</h6>
                            <p><strong>产品ID:</strong> ${productId}</p>
                            <p><strong>响应消息:</strong> ${data.message}</p>
                        </div>
                        <div class="mt-3">
                            <button class="btn btn-primary" onclick="testGetProductDetail()">
                                <i class="bi bi-refresh me-1"></i>重新获取详情验证
                            </button>
                        </div>
                    `;
                } else {
                    editTest.innerHTML = `
                        <div class="alert alert-danger">
                            <h6>❌ 编辑测试失败</h6>
                            <p>${data.message}</p>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('编辑产品失败:', error);
                editTest.innerHTML = `<div class="alert alert-danger">编辑请求失败: ${error.message}</div>`;
            }
        }

        // 显示API结果
        function displayApiResults(method, url, data) {
            const container = document.getElementById('apiResults');
            
            const html = `
                <div class="api-test">
                    <h6>${method} API测试结果</h6>
                    <p><strong>URL:</strong> ${url}</p>
                    <p><strong>状态:</strong> 
                        <span class="status-badge ${data.success ? 'status-success' : 'status-error'}">
                            ${data.success ? '✅ 成功' : '❌ 失败'}
                        </span>
                    </p>
                    <p><strong>消息:</strong> ${data.message || '无'}</p>
                    <div class="mt-3">
                        <h6>完整响应:</h6>
                        <div class="json-display">
                            ${JSON.stringify(data, null, 2)}
                        </div>
                    </div>
                </div>
            `;
            
            container.innerHTML = html;
        }

        // 显示产品详情
        function displayProductDetails(product) {
            const container = document.getElementById('productDetails');
            
            const html = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>基本信息</h6>
                        <table class="table table-sm table-borderless">
                            <tr><td class="text-muted">产品名称:</td><td>${product.name || '-'}</td></tr>
                            <tr><td class="text-muted">产品简称:</td><td>${product.shortName || '-'}</td></tr>
                            <tr><td class="text-muted">品牌:</td><td>${product.brand || '-'}</td></tr>
                            <tr><td class="text-muted">型号:</td><td>${product.model || '-'}</td></tr>
                            <tr><td class="text-muted">分类ID:</td><td>${product.categoryId || '-'}</td></tr>
                            <tr><td class="text-muted">状态:</td><td>${product.status === 1 ? '已上架' : '已下架'}</td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>价格库存</h6>
                        <table class="table table-sm table-borderless">
                            <tr><td class="text-muted">售价:</td><td>¥${product.price || '-'}</td></tr>
                            <tr><td class="text-muted">原价:</td><td>¥${product.originalPrice || '-'}</td></tr>
                            <tr><td class="text-muted">库存:</td><td>${product.stock || '-'}件</td></tr>
                            <tr><td class="text-muted">销量:</td><td>${product.sales || '-'}件</td></tr>
                            <tr><td class="text-muted">排序权重:</td><td>${product.sortOrder || '-'}</td></tr>
                            <tr><td class="text-muted">重量:</td><td>${product.weight || '-'}kg</td></tr>
                        </table>
                    </div>
                </div>
                
                <div class="row mt-3">
                    <div class="col-12">
                        <h6>产品描述</h6>
                        <p class="text-muted">${product.description || '-'}</p>
                    </div>
                </div>
                
                <div class="row mt-3">
                    <div class="col-md-6">
                        <h6>物理属性</h6>
                        <table class="table table-sm table-borderless">
                            <tr><td class="text-muted">尺寸:</td><td>${product.dimensions || '-'}</td></tr>
                            <tr><td class="text-muted">保修期:</td><td>${product.warrantyPeriod || '-'}</td></tr>
                            <tr><td class="text-muted">适用车型:</td><td>${product.compatibleCars || '-'}</td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>SEO信息</h6>
                        <table class="table table-sm table-borderless">
                            <tr><td class="text-muted">SEO标题:</td><td>${product.seoTitle || '-'}</td></tr>
                            <tr><td class="text-muted">SEO关键词:</td><td>${product.seoKeywords || '-'}</td></tr>
                            <tr><td class="text-muted">SEO描述:</td><td>${product.seoDescription || '-'}</td></tr>
                        </table>
                    </div>
                </div>
                
                <div class="row mt-3">
                    <div class="col-12">
                        <h6>JSON字段</h6>
                        <div class="json-display">
                            <strong>特点:</strong> ${product.features || 'null'}<br>
                            <strong>规格参数:</strong> ${product.specifications || 'null'}<br>
                            <strong>规格选项:</strong> ${product.specs || 'null'}<br>
                            <strong>服务说明:</strong> ${product.services || 'null'}
                        </div>
                    </div>
                </div>
                
                <div class="row mt-3">
                    <div class="col-12">
                        <h6>系统信息</h6>
                        <table class="table table-sm table-borderless">
                            <tr><td class="text-muted">产品ID:</td><td>${product.id}</td></tr>
                            <tr><td class="text-muted">创建时间:</td><td>${formatDateTime(product.createdAt)}</td></tr>
                            <tr><td class="text-muted">更新时间:</td><td>${formatDateTime(product.updatedAt)}</td></tr>
                        </table>
                    </div>
                </div>
            `;
            
            container.innerHTML = html;
        }

        // 格式化日期时间
        function formatDateTime(dateTimeStr) {
            if (!dateTimeStr) return '-';
            const date = new Date(dateTimeStr);
            return date.toLocaleString('zh-CN');
        }

        // 清空结果
        function clearResults() {
            document.getElementById('apiResults').innerHTML = '<div class="text-muted">等待测试...</div>';
            document.getElementById('productDetails').innerHTML = '<div class="text-muted">等待测试...</div>';
            document.getElementById('editTest').innerHTML = '<div class="text-muted">点击"测试编辑"开始测试...</div>';
        }

        // 页面加载时自动测试
        document.addEventListener('DOMContentLoaded', function() {
            testGetProductDetail();
        });
    </script>
</body>
</html>
