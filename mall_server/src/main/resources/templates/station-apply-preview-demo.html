<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>服务网点申请页面图片预览优化演示</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 400px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .demo-section {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
        }
        
        .section-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        
        /* 模拟小程序样式 */
        .upload-area {
            position: relative;
            width: 100%;
            height: 120px;
            border: 2px dashed #ddd;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #fafafa;
            overflow: hidden;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .upload-area:hover {
            background-color: #f0f0f0;
            border-color: #1e88e5;
        }
        
        .uploaded-image {
            width: 100%;
            height: 100%;
            border-radius: 8px;
            object-fit: cover;
            transition: all 0.3s ease;
        }
        
        .uploaded-image:hover {
            transform: scale(1.02);
        }
        
        .reupload-btn {
            position: absolute;
            top: 8px;
            right: 8px;
            width: 32px;
            height: 32px;
            background-color: rgba(0, 0, 0, 0.6);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            font-size: 16px;
            z-index: 2;
            cursor: pointer;
        }
        
        .upload-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            margin-top: 10px;
        }
        
        .upload-item {
            position: relative;
            width: 100%;
            height: 80px;
            border-radius: 8px;
            overflow: hidden;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            cursor: pointer;
        }
        
        .upload-item:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
        }
        
        .upload-placeholder {
            border: 2px dashed #ddd;
            background-color: #fafafa;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 12px;
        }
        
        .delete-btn {
            position: absolute;
            top: -8px;
            right: -8px;
            width: 24px;
            height: 24px;
            background-color: #ff4757;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            font-size: 12px;
            z-index: 4;
            box-shadow: 0 2px 8px rgba(255, 71, 87, 0.3);
            cursor: pointer;
        }
        
        .image-mask {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;
            border-radius: 8px;
        }
        
        .upload-item:hover .image-mask {
            opacity: 1;
        }
        
        .upload-tip {
            font-size: 12px;
            color: #999;
            margin-top: 8px;
            line-height: 1.4;
        }
        
        .demo-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .feature-list {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .feature-list h3 {
            margin: 0 0 10px 0;
            color: #1e88e5;
        }
        
        .feature-list ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .feature-list li {
            margin: 5px 0;
            color: #333;
        }
    </style>
</head>
<body>
    <h1>📸 图片预览优化演示</h1>
    
    <div class="feature-list">
        <h3>✨ 优化功能</h3>
        <ul>
            <li>🖼️ 点击图片可预览放大</li>
            <li>📷 重新上传按钮</li>
            <li>👁️ 悬停显示预览图标</li>
            <li>🗑️ 优化的删除按钮</li>
            <li>💡 友好的操作提示</li>
            <li>🎨 流畅的动画效果</li>
        </ul>
    </div>
    
    <!-- 营业执照上传演示 -->
    <div class="demo-section">
        <div class="section-title">营业执照 *</div>
        <div class="upload-area" onclick="showPreview('https://via.placeholder.com/400x300/1e88e5/ffffff?text=营业执照')">
            <img src="https://via.placeholder.com/400x300/1e88e5/ffffff?text=营业执照" class="uploaded-image" alt="营业执照">
            <div class="reupload-btn" onclick="event.stopPropagation(); alert('重新上传营业执照')">
                📷
            </div>
        </div>
        <div class="upload-tip">
            📄 请上传清晰的营业执照照片
            <div style="margin-top: 3px; color: #666;">💡 点击图片可预览，点击相机图标可重新上传</div>
        </div>
    </div>
    
    <!-- 资质证书上传演示 -->
    <div class="demo-section">
        <div class="section-title">资质证书</div>
        <div class="upload-grid">
            <div class="upload-item" onclick="showPreview('https://via.placeholder.com/300x300/4caf50/ffffff?text=证书1')">
                <img src="https://via.placeholder.com/300x300/4caf50/ffffff?text=证书1" class="demo-image" alt="资质证书1">
                <div class="delete-btn" onclick="event.stopPropagation(); alert('删除证书1')">×</div>
                <div class="image-mask">
                    <span style="color: white; font-size: 24px;">👁️</span>
                </div>
            </div>
            <div class="upload-item" onclick="showPreview('https://via.placeholder.com/300x300/ff9800/ffffff?text=证书2')">
                <img src="https://via.placeholder.com/300x300/ff9800/ffffff?text=证书2" class="demo-image" alt="资质证书2">
                <div class="delete-btn" onclick="event.stopPropagation(); alert('删除证书2')">×</div>
                <div class="image-mask">
                    <span style="color: white; font-size: 24px;">👁️</span>
                </div>
            </div>
            <div class="upload-item upload-placeholder" onclick="alert('添加新证书')">
                <div>➕</div>
                <div>添加证书</div>
            </div>
        </div>
        <div class="upload-tip">
            🏆 上传相关资质证书（如安装资质、维修许可等）
            <div style="margin-top: 3px; color: #666;">💡 点击图片可预览，最多9张</div>
        </div>
    </div>
    
    <!-- 网点照片上传演示 -->
    <div class="demo-section">
        <div class="section-title">网点照片</div>
        <div class="upload-grid">
            <div class="upload-item" onclick="showPreview('https://via.placeholder.com/300x300/e91e63/ffffff?text=外观')">
                <img src="https://via.placeholder.com/300x300/e91e63/ffffff?text=外观" class="demo-image" alt="网点外观">
                <div class="delete-btn" onclick="event.stopPropagation(); alert('删除外观照片')">×</div>
                <div class="image-mask">
                    <span style="color: white; font-size: 24px;">👁️</span>
                </div>
            </div>
            <div class="upload-item" onclick="showPreview('https://via.placeholder.com/300x300/9c27b0/ffffff?text=内部')">
                <img src="https://via.placeholder.com/300x300/9c27b0/ffffff?text=内部" class="demo-image" alt="内部环境">
                <div class="delete-btn" onclick="event.stopPropagation(); alert('删除内部照片')">×</div>
                <div class="image-mask">
                    <span style="color: white; font-size: 24px;">👁️</span>
                </div>
            </div>
            <div class="upload-item" onclick="showPreview('https://via.placeholder.com/300x300/607d8b/ffffff?text=设备')">
                <img src="https://via.placeholder.com/300x300/607d8b/ffffff?text=设备" class="demo-image" alt="设备设施">
                <div class="delete-btn" onclick="event.stopPropagation(); alert('删除设备照片')">×</div>
                <div class="image-mask">
                    <span style="color: white; font-size: 24px;">👁️</span>
                </div>
            </div>
            <div class="upload-item upload-placeholder" onclick="alert('添加网点照片')">
                <div>➕</div>
                <div>添加图片</div>
            </div>
        </div>
        <div class="upload-tip">
            📸 建议上传网点外观、内部环境、设备设施等照片
            <div style="margin-top: 3px; color: #666;">💡 点击图片可预览，支持JPG、PNG格式，最多9张</div>
        </div>
    </div>
    
    <div class="feature-list">
        <h3>🎯 用户体验提升</h3>
        <ul>
            <li>📱 更直观的图片预览体验</li>
            <li>🎨 优雅的悬停动画效果</li>
            <li>💡 清晰的操作指引</li>
            <li>🔄 便捷的重新上传功能</li>
            <li>👀 视觉反馈更加友好</li>
        </ul>
    </div>

    <script>
        function showPreview(src) {
            // 创建预览模态框
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.8);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 9999;
                cursor: pointer;
            `;
            
            const img = document.createElement('img');
            img.src = src;
            img.style.cssText = `
                max-width: 90%;
                max-height: 90%;
                border-radius: 8px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            `;
            
            modal.appendChild(img);
            document.body.appendChild(modal);
            
            // 点击关闭
            modal.onclick = () => {
                document.body.removeChild(modal);
            };
            
            // 添加关闭按钮
            const closeBtn = document.createElement('div');
            closeBtn.innerHTML = '×';
            closeBtn.style.cssText = `
                position: absolute;
                top: 20px;
                right: 30px;
                color: white;
                font-size: 40px;
                cursor: pointer;
            `;
            modal.appendChild(closeBtn);
        }
    </script>
</body>
</html>
