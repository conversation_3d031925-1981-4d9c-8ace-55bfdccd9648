<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模态框修复测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }

        .test-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .test-button {
            margin: 0.5rem;
            padding: 0.75rem 1.5rem;
            border-radius: 25px;
            border: none;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .log-item {
            padding: 0.5rem;
            margin: 0.25rem 0;
            border-radius: 5px;
            font-family: monospace;
            font-size: 0.9rem;
        }

        .log-success {
            background: #e8f5e8;
            color: #2e7d32;
        }

        .log-error {
            background: #ffebee;
            color: #c62828;
        }

        .log-info {
            background: #e3f2fd;
            color: #1976d2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-white text-center mb-4">🔧 模态框和按钮修复测试</h1>
        
        <!-- 测试按钮区域 -->
        <div class="test-card">
            <h5 class="mb-3">
                <i class="bi bi-play-circle me-2"></i>功能测试
            </h5>
            <div class="d-flex flex-wrap">
                <button class="test-button" onclick="testFilterFunction('')">
                    <i class="bi bi-list-ul me-1"></i>测试全部产品筛选
                </button>
                <button class="test-button" onclick="testFilterFunction('1')">
                    <i class="bi bi-check-circle me-1"></i>测试已上架筛选
                </button>
                <button class="test-button" onclick="testFilterFunction('0')">
                    <i class="bi bi-x-circle me-1"></i>测试已下架筛选
                </button>
                <button class="test-button" onclick="testModalFunction()">
                    <i class="bi bi-plus-circle me-1"></i>测试添加产品模态框
                </button>
            </div>
        </div>

        <!-- 测试结果显示 -->
        <div class="test-card">
            <h5 class="mb-3">
                <i class="bi bi-clipboard-data me-2"></i>测试结果
            </h5>
            <div id="testResults" style="max-height: 400px; overflow-y: auto;">
                <div class="log-item log-info">等待测试...</div>
            </div>
            <div class="mt-3">
                <button class="btn btn-outline-secondary btn-sm" onclick="clearResults()">
                    <i class="bi bi-trash me-1"></i>清空日志
                </button>
                <button class="btn btn-outline-primary btn-sm" onclick="runAllTests()">
                    <i class="bi bi-play me-1"></i>运行所有测试
                </button>
            </div>
        </div>

        <!-- 元素检查 -->
        <div class="test-card">
            <h5 class="mb-3">
                <i class="bi bi-search me-2"></i>元素检查
            </h5>
            <div id="elementCheck">
                <button class="btn btn-outline-info btn-sm" onclick="checkElements()">
                    <i class="bi bi-search me-1"></i>检查页面元素
                </button>
            </div>
        </div>

        <!-- 返回按钮 -->
        <div class="text-center">
            <a href="/admin/products" class="btn btn-light">
                <i class="bi bi-arrow-left me-1"></i>返回产品管理
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 测试筛选功能
        function testFilterFunction(status) {
            addLog(`🧪 测试筛选功能: "${status}"`, 'info');
            
            try {
                // 模拟filterProducts函数
                const url = new URL(window.location);
                if (status) {
                    url.searchParams.set('status', status);
                } else {
                    url.searchParams.delete('status');
                }
                url.searchParams.delete('page');
                
                addLog(`✅ 筛选功能正常: ${url.toString()}`, 'success');
                return true;
            } catch (error) {
                addLog(`❌ 筛选功能错误: ${error.message}`, 'error');
                return false;
            }
        }

        // 测试模态框功能
        function testModalFunction() {
            addLog('🧪 测试添加产品模态框功能', 'info');
            
            try {
                // 检查关键元素是否存在
                const elements = [
                    'addProductForm',
                    'mainImagePreview', 
                    'imagesPreview',
                    'detailImagePreview',
                    'addProductModal'
                ];
                
                let allExists = true;
                elements.forEach(id => {
                    const element = document.getElementById(id);
                    if (!element) {
                        addLog(`❌ 元素不存在: ${id}`, 'error');
                        allExists = false;
                    } else {
                        addLog(`✅ 元素存在: ${id}`, 'success');
                    }
                });
                
                if (allExists) {
                    addLog('✅ 所有必需元素都存在，模态框功能应该正常', 'success');
                } else {
                    addLog('❌ 部分元素缺失，可能导致模态框错误', 'error');
                }
                
                return allExists;
            } catch (error) {
                addLog(`❌ 模态框测试错误: ${error.message}`, 'error');
                return false;
            }
        }

        // 检查页面元素
        function checkElements() {
            const elementCheck = document.getElementById('elementCheck');
            
            const elementsToCheck = [
                'addProductForm',
                'addProductModal', 
                'mainImagePreview',
                'mainImagePreviewImg',
                'imagesPreview',
                'detailImagePreview',
                'detailImagePreviewImg',
                'productMainImage',
                'productImages',
                'productDetailImage',
                'confirmAddProductBtn'
            ];
            
            let html = '<div class="row">';
            elementsToCheck.forEach((id, index) => {
                const element = document.getElementById(id);
                const exists = element !== null;
                const status = exists ? 'success' : 'danger';
                const icon = exists ? 'check-circle' : 'x-circle';
                
                if (index % 3 === 0 && index > 0) {
                    html += '</div><div class="row">';
                }
                
                html += `
                    <div class="col-md-4 mb-2">
                        <div class="alert alert-${status} py-2">
                            <i class="bi bi-${icon} me-2"></i>
                            <small>${id}</small>
                        </div>
                    </div>
                `;
            });
            html += '</div>';
            
            elementCheck.innerHTML = html;
        }

        // 运行所有测试
        function runAllTests() {
            clearResults();
            addLog('🚀 开始运行所有测试...', 'info');
            
            setTimeout(() => testFilterFunction(''), 500);
            setTimeout(() => testFilterFunction('1'), 1000);
            setTimeout(() => testFilterFunction('0'), 1500);
            setTimeout(() => testModalFunction(), 2000);
            
            setTimeout(() => {
                addLog('🎉 所有测试完成!', 'success');
            }, 2500);
        }

        // 添加日志
        function addLog(message, type = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const logItem = document.createElement('div');
            logItem.className = `log-item log-${type}`;
            logItem.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            resultsDiv.appendChild(logItem);
            
            // 滚动到底部
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        // 清空结果
        function clearResults() {
            document.getElementById('testResults').innerHTML = '<div class="log-item log-info">日志已清空</div>';
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            addLog('📄 测试页面加载完成', 'success');
            
            // 自动检查元素
            setTimeout(() => {
                checkElements();
            }, 500);
        });
    </script>
</body>
</html>
