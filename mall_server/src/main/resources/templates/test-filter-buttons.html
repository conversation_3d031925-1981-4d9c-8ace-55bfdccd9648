<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>筛选按钮测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }

        .container {
            max-width: 1200px;
        }

        .filter-tabs {
            background: white;
            border-radius: 15px;
            padding: 1rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .filter-tab {
            padding: 0.75rem 1.5rem;
            border-radius: 25px;
            border: none;
            background: transparent;
            color: #666;
            margin-right: 1rem;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            z-index: 10;
            pointer-events: auto;
        }

        .filter-tab.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .filter-tab:hover {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            transform: translateY(-2px);
        }

        .test-result {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .log-item {
            padding: 0.5rem;
            margin: 0.25rem 0;
            border-radius: 5px;
            font-family: monospace;
            font-size: 0.9rem;
        }

        .log-info {
            background: #e3f2fd;
            color: #1976d2;
        }

        .log-success {
            background: #e8f5e8;
            color: #2e7d32;
        }

        .log-error {
            background: #ffebee;
            color: #c62828;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-white text-center mb-4">🔧 筛选按钮功能测试</h1>
        
        <!-- 筛选按钮测试区域 -->
        <div class="filter-tabs">
            <h5 class="mb-3">
                <i class="bi bi-funnel me-2"></i>产品筛选测试
            </h5>
            <div class="d-flex flex-wrap">
                <button class="filter-tab active" onclick="testFilterProducts('')" type="button">
                    <i class="bi bi-list-ul me-1"></i>全部产品
                </button>
                <button class="filter-tab" onclick="testFilterProducts('1')" type="button">
                    <i class="bi bi-check-circle me-1"></i>已上架
                </button>
                <button class="filter-tab" onclick="testFilterProducts('0')" type="button">
                    <i class="bi bi-x-circle me-1"></i>已下架
                </button>
            </div>
        </div>

        <!-- 测试结果显示 -->
        <div class="test-result">
            <h5 class="mb-3">
                <i class="bi bi-clipboard-data me-2"></i>测试结果
            </h5>
            <div id="testResults">
                <div class="log-item log-info">等待测试...</div>
            </div>
            <div class="mt-3">
                <button class="btn btn-outline-secondary btn-sm" onclick="clearResults()">
                    <i class="bi bi-trash me-1"></i>清空日志
                </button>
                <button class="btn btn-outline-primary btn-sm" onclick="runAllTests()">
                    <i class="bi bi-play me-1"></i>运行所有测试
                </button>
            </div>
        </div>

        <!-- 当前状态显示 -->
        <div class="test-result">
            <h5 class="mb-3">
                <i class="bi bi-info-circle me-2"></i>当前状态
            </h5>
            <div class="row">
                <div class="col-md-6">
                    <p><strong>当前URL:</strong> <span id="currentUrl"></span></p>
                    <p><strong>状态参数:</strong> <span id="statusParam"></span></p>
                </div>
                <div class="col-md-6">
                    <p><strong>页面加载时间:</strong> <span id="loadTime"></span></p>
                    <p><strong>测试次数:</strong> <span id="testCount">0</span></p>
                </div>
            </div>
        </div>

        <!-- 返回按钮 -->
        <div class="text-center">
            <a href="/admin/products" class="btn btn-light">
                <i class="bi bi-arrow-left me-1"></i>返回产品管理
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let testCount = 0;

        // 测试筛选功能
        function testFilterProducts(status) {
            testCount++;
            document.getElementById('testCount').textContent = testCount;

            addLog(`测试开始: 筛选状态 "${status}"`, 'info');
            
            try {
                // 模拟原始的filterProducts函数逻辑
                const url = new URL(window.location);
                const originalUrl = url.toString();
                
                if (status) {
                    url.searchParams.set('status', status);
                } else {
                    url.searchParams.delete('status');
                }
                url.searchParams.delete('page');
                
                const newUrl = url.toString();
                
                addLog(`原始URL: ${originalUrl}`, 'info');
                addLog(`新URL: ${newUrl}`, 'info');
                
                // 更新按钮状态
                updateButtonStates(status);
                
                // 更新当前状态显示
                updateCurrentStatus();
                
                addLog(`✅ 筛选功能测试成功`, 'success');
                
                // 模拟页面跳转（实际项目中会真正跳转）
                setTimeout(() => {
                    addLog(`🔄 模拟页面跳转到: ${newUrl}`, 'info');
                }, 500);
                
            } catch (error) {
                addLog(`❌ 测试失败: ${error.message}`, 'error');
                console.error('测试错误:', error);
            }
        }

        // 更新按钮状态
        function updateButtonStates(activeStatus) {
            const buttons = document.querySelectorAll('.filter-tab');
            buttons.forEach((button, index) => {
                button.classList.remove('active');
                
                // 根据按钮索引和状态值确定是否激活
                if ((index === 0 && activeStatus === '') ||
                    (index === 1 && activeStatus === '1') ||
                    (index === 2 && activeStatus === '0')) {
                    button.classList.add('active');
                }
            });
        }

        // 添加日志
        function addLog(message, type = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const logItem = document.createElement('div');
            logItem.className = `log-item log-${type}`;
            logItem.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            resultsDiv.appendChild(logItem);
            
            // 滚动到底部
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        // 清空结果
        function clearResults() {
            document.getElementById('testResults').innerHTML = '<div class="log-item log-info">日志已清空</div>';
            testCount = 0;
            document.getElementById('testCount').textContent = testCount;
        }

        // 运行所有测试
        function runAllTests() {
            clearResults();
            addLog('🚀 开始运行所有测试...', 'info');
            
            setTimeout(() => testFilterProducts(''), 500);
            setTimeout(() => testFilterProducts('1'), 1500);
            setTimeout(() => testFilterProducts('0'), 2500);
            setTimeout(() => testFilterProducts(''), 3500);
            
            setTimeout(() => {
                addLog('🎉 所有测试完成!', 'success');
            }, 4000);
        }

        // 更新当前状态
        function updateCurrentStatus() {
            const url = new URL(window.location);
            document.getElementById('currentUrl').textContent = url.toString();
            document.getElementById('statusParam').textContent = url.searchParams.get('status') || '无';
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('loadTime').textContent = new Date().toLocaleString();
            updateCurrentStatus();
            addLog('📄 页面加载完成，筛选按钮测试就绪', 'success');
            
            // 检查按钮数量
            const buttons = document.querySelectorAll('.filter-tab');
            addLog(`🔍 找到 ${buttons.length} 个筛选按钮`, 'info');
            
            // 测试按钮点击事件
            buttons.forEach((button, index) => {
                button.addEventListener('click', function() {
                    addLog(`👆 按钮 ${index + 1} 被点击: ${this.textContent.trim()}`, 'info');
                });
            });
        });
    </script>
</body>
</html>
