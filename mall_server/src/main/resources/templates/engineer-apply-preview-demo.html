<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工程师申请页面图片预览优化演示</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 400px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .demo-section {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
        }
        
        .section-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        
        /* 模拟小程序样式 */
        .upload-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            margin-top: 10px;
        }
        
        .upload-item {
            position: relative;
            width: 100%;
            height: 80px;
            border-radius: 8px;
            overflow: hidden;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            cursor: pointer;
        }
        
        .upload-item:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
        }
        
        .upload-placeholder {
            border: 2px dashed #ddd;
            background-color: #fafafa;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 12px;
        }
        
        .delete-btn {
            position: absolute;
            top: -8px;
            right: -8px;
            width: 24px;
            height: 24px;
            background-color: #ff4757;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            font-size: 12px;
            z-index: 4;
            box-shadow: 0 2px 8px rgba(255, 71, 87, 0.3);
            cursor: pointer;
        }
        
        .image-mask {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;
            border-radius: 8px;
        }
        
        .upload-item:hover .image-mask {
            opacity: 1;
        }
        
        .upload-tip {
            font-size: 12px;
            color: #999;
            margin-top: 8px;
            line-height: 1.4;
        }
        
        .demo-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .feature-list {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .feature-list h3 {
            margin: 0 0 10px 0;
            color: #1e88e5;
        }
        
        .feature-list ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .feature-list li {
            margin: 5px 0;
            color: #333;
        }

        .comparison-section {
            background: #fff3e0;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }

        .comparison-section h3 {
            margin: 0 0 10px 0;
            color: #f57c00;
        }

        .before-after {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 15px;
        }

        .before, .after {
            text-align: center;
            padding: 10px;
            border-radius: 8px;
        }

        .before {
            background: #ffebee;
            border: 1px solid #ffcdd2;
        }

        .after {
            background: #e8f5e8;
            border: 1px solid #c8e6c9;
        }

        .admin-demo {
            background: #f3e5f5;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }

        .admin-demo h3 {
            margin: 0 0 10px 0;
            color: #7b1fa2;
        }

        .admin-gallery {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 8px;
            margin-top: 10px;
        }

        .admin-image {
            width: 100%;
            height: 60px;
            object-fit: cover;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .admin-image:hover {
            transform: scale(1.1);
            border-color: #7b1fa2;
            box-shadow: 0 2px 8px rgba(123, 31, 162, 0.3);
        }
    </style>
</head>
<body>
    <h1>👨‍🔧 工程师申请图片预览优化</h1>
    
    <div class="feature-list">
        <h3>✨ 优化功能</h3>
        <ul>
            <li>🖼️ 点击图片可预览放大</li>
            <li>👁️ 悬停显示预览图标</li>
            <li>🗑️ 优化的删除按钮</li>
            <li>💡 友好的操作提示</li>
            <li>🎨 流畅的动画效果</li>
            <li>🖥️ 管理端图片展示</li>
        </ul>
    </div>

    <div class="comparison-section">
        <h3>📊 优化对比</h3>
        <div class="before-after">
            <div class="before">
                <h4>优化前 ❌</h4>
                <ul style="text-align: left; font-size: 11px;">
                    <li>图片无法预览</li>
                    <li>删除按钮不明显</li>
                    <li>无操作反馈</li>
                    <li>管理端无图片显示</li>
                </ul>
            </div>
            <div class="after">
                <h4>优化后 ✅</h4>
                <ul style="text-align: left; font-size: 11px;">
                    <li>点击预览大图</li>
                    <li>悬停显示操作</li>
                    <li>流畅动画效果</li>
                    <li>管理端完整展示</li>
                </ul>
            </div>
        </div>
    </div>
    
    <!-- 资质证书上传演示 -->
    <div class="demo-section">
        <div class="section-title">资质证书 *</div>
        <div class="upload-grid">
            <div class="upload-item" onclick="showPreview('https://via.placeholder.com/300x300/4caf50/ffffff?text=电工证')">
                <img src="https://via.placeholder.com/300x300/4caf50/ffffff?text=电工证" class="demo-image" alt="电工证">
                <div class="delete-btn" onclick="event.stopPropagation(); alert('删除电工证')">×</div>
                <div class="image-mask">
                    <span style="color: white; font-size: 20px;">👁️</span>
                </div>
            </div>
            <div class="upload-item" onclick="showPreview('https://via.placeholder.com/300x300/ff9800/ffffff?text=技能证书')">
                <img src="https://via.placeholder.com/300x300/ff9800/ffffff?text=技能证书" class="demo-image" alt="技能证书">
                <div class="delete-btn" onclick="event.stopPropagation(); alert('删除技能证书')">×</div>
                <div class="image-mask">
                    <span style="color: white; font-size: 20px;">👁️</span>
                </div>
            </div>
            <div class="upload-item" onclick="showPreview('https://via.placeholder.com/300x300/2196f3/ffffff?text=培训证书')">
                <img src="https://via.placeholder.com/300x300/2196f3/ffffff?text=培训证书" class="demo-image" alt="培训证书">
                <div class="delete-btn" onclick="event.stopPropagation(); alert('删除培训证书')">×</div>
                <div class="image-mask">
                    <span style="color: white; font-size: 20px;">👁️</span>
                </div>
            </div>
            <div class="upload-item upload-placeholder" onclick="alert('添加新证书')">
                <div>➕</div>
                <div>添加证书</div>
            </div>
        </div>
        <div class="upload-tip">
            🏆 请上传电工证、技能证书、培训证书等相关资质
            <div style="margin-top: 3px; color: #666;">💡 点击图片可预览，最多9张</div>
        </div>
    </div>
    
    <!-- 工作照片上传演示 -->
    <div class="demo-section">
        <div class="section-title">工作照片</div>
        <div class="upload-grid">
            <div class="upload-item" onclick="showPreview('https://via.placeholder.com/300x300/e91e63/ffffff?text=现场作业')">
                <img src="https://via.placeholder.com/300x300/e91e63/ffffff?text=现场作业" class="demo-image" alt="现场作业">
                <div class="delete-btn" onclick="event.stopPropagation(); alert('删除现场作业照片')">×</div>
                <div class="image-mask">
                    <span style="color: white; font-size: 20px;">👁️</span>
                </div>
            </div>
            <div class="upload-item" onclick="showPreview('https://via.placeholder.com/300x300/9c27b0/ffffff?text=设备维修')">
                <img src="https://via.placeholder.com/300x300/9c27b0/ffffff?text=设备维修" class="demo-image" alt="设备维修">
                <div class="delete-btn" onclick="event.stopPropagation(); alert('删除设备维修照片')">×</div>
                <div class="image-mask">
                    <span style="color: white; font-size: 20px;">👁️</span>
                </div>
            </div>
            <div class="upload-item" onclick="showPreview('https://via.placeholder.com/300x300/607d8b/ffffff?text=工具使用')">
                <img src="https://via.placeholder.com/300x300/607d8b/ffffff?text=工具使用" class="demo-image" alt="工具使用">
                <div class="delete-btn" onclick="event.stopPropagation(); alert('删除工具使用照片')">×</div>
                <div class="image-mask">
                    <span style="color: white; font-size: 20px;">👁️</span>
                </div>
            </div>
            <div class="upload-item upload-placeholder" onclick="alert('添加工作照片')">
                <div>➕</div>
                <div>添加照片</div>
            </div>
        </div>
        <div class="upload-tip">
            📸 建议上传工作现场照片，展示您的专业技能
            <div style="margin-top: 3px; color: #666;">💡 点击图片可预览，支持JPG、PNG格式，最多9张</div>
        </div>
    </div>

    <div class="admin-demo">
        <h3>🖥️ 管理端图片展示</h3>
        <p style="font-size: 12px; margin: 5px 0;">管理员可以在工程师详情中查看所有上传的图片：</p>
        
        <div style="margin: 10px 0;">
            <strong style="font-size: 13px;">📋 资质证书：</strong>
            <div class="admin-gallery">
                <img src="https://via.placeholder.com/120x120/4caf50/ffffff?text=电工证" class="admin-image" onclick="showPreview(this.src)" alt="电工证">
                <img src="https://via.placeholder.com/120x120/ff9800/ffffff?text=技能证书" class="admin-image" onclick="showPreview(this.src)" alt="技能证书">
                <img src="https://via.placeholder.com/120x120/2196f3/ffffff?text=培训证书" class="admin-image" onclick="showPreview(this.src)" alt="培训证书">
                <img src="https://via.placeholder.com/120x120/9e9e9e/ffffff?text=更多" class="admin-image" onclick="alert('查看更多证书')" alt="更多">
            </div>
        </div>

        <div style="margin: 10px 0;">
            <strong style="font-size: 13px;">📸 工作照片：</strong>
            <div class="admin-gallery">
                <img src="https://via.placeholder.com/120x120/e91e63/ffffff?text=现场" class="admin-image" onclick="showPreview(this.src)" alt="现场作业">
                <img src="https://via.placeholder.com/120x120/9c27b0/ffffff?text=维修" class="admin-image" onclick="showPreview(this.src)" alt="设备维修">
                <img src="https://via.placeholder.com/120x120/607d8b/ffffff?text=工具" class="admin-image" onclick="showPreview(this.src)" alt="工具使用">
                <img src="https://via.placeholder.com/120x120/795548/ffffff?text=完工" class="admin-image" onclick="showPreview(this.src)" alt="完工照片">
            </div>
        </div>
    </div>
    
    <div class="feature-list">
        <h3>🎯 用户体验提升</h3>
        <ul>
            <li>📱 更直观的图片预览体验</li>
            <li>🎨 优雅的悬停动画效果</li>
            <li>💡 清晰的操作指引</li>
            <li>👀 视觉反馈更加友好</li>
            <li>🖥️ 管理端完整的图片展示</li>
            <li>🔍 支持图片放大预览</li>
        </ul>
    </div>

    <script>
        function showPreview(src) {
            // 创建预览模态框
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.8);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 9999;
                cursor: pointer;
            `;
            
            const img = document.createElement('img');
            img.src = src;
            img.style.cssText = `
                max-width: 90%;
                max-height: 90%;
                border-radius: 8px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            `;
            
            modal.appendChild(img);
            document.body.appendChild(modal);
            
            // 点击关闭
            modal.onclick = () => {
                document.body.removeChild(modal);
            };
            
            // 添加关闭按钮
            const closeBtn = document.createElement('div');
            closeBtn.innerHTML = '×';
            closeBtn.style.cssText = `
                position: absolute;
                top: 20px;
                right: 30px;
                color: white;
                font-size: 40px;
                cursor: pointer;
            `;
            modal.appendChild(closeBtn);
        }
    </script>
</body>
</html>
