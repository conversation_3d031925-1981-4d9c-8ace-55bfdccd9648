<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片显示测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .image-gallery {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 20px 0;
        }
        .fault-image {
            width: 120px;
            height: 120px;
            border-radius: 8px;
            border: 2px solid #e0e0e0;
            object-fit: cover;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .fault-image:hover {
            border-color: #667eea;
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        .test-url {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
        }
        .status {
            padding: 5px 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>🧪 图片显示测试页面</h1>
    
    <!-- 测试1: 直接URL访问 -->
    <div class="test-section">
        <h2>📋 测试1: 直接URL访问</h2>
        <div class="test-url">https://localhost:8443/uploads/20250610_143914_aea5df47.png</div>
        <img src="/uploads/20250610_143914_aea5df47.png" class="fault-image" 
             onload="updateStatus('test1', 'success', '图片加载成功')"
             onerror="updateStatus('test1', 'error', '图片加载失败')">
        <div id="test1" class="status info">加载中...</div>
    </div>

    <!-- 测试2: 模拟JSON数据解析 -->
    <div class="test-section">
        <h2>📋 测试2: 模拟JSON数据解析</h2>
        <p>模拟从数据库获取的JSON数据:</p>
        <div class="test-url">["\/uploads\/20250610_143914_aea5df47.png", "\/uploads\/20250610_143914_4a433ed2.png"]</div>
        <div class="image-gallery" data-images='["/uploads/20250610_143914_aea5df47.png", "/uploads/20250610_143914_4a433ed2.png"]'>
            <!-- 图片将通过JavaScript动态加载 -->
        </div>
        <div id="test2" class="status info">等待JavaScript处理...</div>
    </div>

    <!-- 测试3: 错误路径测试 -->
    <div class="test-section">
        <h2>📋 测试3: 错误路径测试</h2>
        <p>测试重复路径问题:</p>
        <div class="test-url">https://localhost:8443/uploads//uploads/20250610_143914_aea5df47.png</div>
        <img src="/uploads//uploads/20250610_143914_aea5df47.png" class="fault-image"
             onload="updateStatus('test3', 'success', '意外成功')"
             onerror="updateStatus('test3', 'error', '路径错误，加载失败（预期结果）')">
        <div id="test3" class="status info">加载中...</div>
    </div>

    <!-- 测试4: 文件存在性检查 -->
    <div class="test-section">
        <h2>📋 测试4: 文件存在性检查</h2>
        <button onclick="checkFileExists()">检查文件是否存在</button>
        <div id="fileCheckResult"></div>
    </div>

    <!-- 测试5: 网络请求测试 -->
    <div class="test-section">
        <h2>📋 测试5: 网络请求测试</h2>
        <button onclick="testNetworkRequest()">测试网络请求</button>
        <div id="networkResult"></div>
    </div>

    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 页面加载完成，开始测试');
            initFaultImages();
        });

        // 更新状态显示
        function updateStatus(testId, type, message) {
            const element = document.getElementById(testId);
            element.className = `status ${type}`;
            element.textContent = message;
            console.log(`📊 ${testId}: ${message}`);
        }

        // 初始化故障图片（复制自orders.html）
        function initFaultImages() {
            const imageGalleries = document.querySelectorAll('.image-gallery');
            console.log('🖼️ 找到图片容器数量:', imageGalleries.length);
            
            imageGalleries.forEach((gallery, index) => {
                const imagesData = gallery.getAttribute('data-images');
                console.log(`📋 容器${index} 图片数据:`, imagesData);
                
                if (imagesData) {
                    try {
                        const imageUrls = JSON.parse(imagesData);
                        console.log(`🔗 解析后的图片URLs:`, imageUrls);
                        renderFaultImages(gallery, imageUrls);
                        updateStatus('test2', 'success', `成功渲染${imageUrls.length}张图片`);
                    } catch (e) {
                        console.error('❌ 解析图片数据失败:', e);
                        updateStatus('test2', 'error', '解析JSON失败: ' + e.message);
                    }
                }
            });
        }

        // 渲染故障图片（复制自orders.html）
        function renderFaultImages(gallery, imageUrls) {
            console.log('🎨 开始渲染图片，数量:', imageUrls.length);
            gallery.innerHTML = '';

            imageUrls.forEach((url, index) => {
                console.log(`📸 处理图片${index}:`, url);
                
                const img = document.createElement('img');
                // 处理图片URL，避免重复路径
                let imageSrc = url;
                if (url.startsWith('http')) {
                    // 完整URL，直接使用
                    imageSrc = url;
                    console.log(`🌐 完整URL:`, imageSrc);
                } else if (url.startsWith('/uploads/')) {
                    // 已经包含/uploads/前缀，直接使用
                    imageSrc = url;
                    console.log(`📁 已有前缀:`, imageSrc);
                } else {
                    // 只有文件名，添加/uploads/前缀
                    imageSrc = '/uploads/' + url;
                    console.log(`➕ 添加前缀:`, imageSrc);
                }

                img.src = imageSrc;
                img.className = 'fault-image';
                img.alt = `故障图片 ${index + 1}`;
                
                // 添加加载事件监听
                img.onload = () => {
                    console.log(`✅ 图片${index}加载成功:`, imageSrc);
                };
                img.onerror = () => {
                    console.error(`❌ 图片${index}加载失败:`, imageSrc);
                };

                gallery.appendChild(img);
            });
        }

        // 检查文件是否存在
        async function checkFileExists() {
            const resultDiv = document.getElementById('fileCheckResult');
            resultDiv.innerHTML = '<div class="status info">检查中...</div>';
            
            const testFiles = [
                '/uploads/20250610_143914_aea5df47.png',
                '/uploads/20250610_143914_4a433ed2.png',
                '/uploads/test.txt'
            ];
            
            let results = [];
            
            for (const file of testFiles) {
                try {
                    const response = await fetch(file, { method: 'HEAD' });
                    const status = response.ok ? 'success' : 'error';
                    const message = response.ok ? '文件存在' : `HTTP ${response.status}`;
                    results.push(`<div class="status ${status}">${file}: ${message}</div>`);
                } catch (error) {
                    results.push(`<div class="status error">${file}: 网络错误</div>`);
                }
            }
            
            resultDiv.innerHTML = results.join('');
        }

        // 测试网络请求
        async function testNetworkRequest() {
            const resultDiv = document.getElementById('networkResult');
            resultDiv.innerHTML = '<div class="status info">测试中...</div>';
            
            try {
                // 测试API端点
                const response = await fetch('/api/upload/image', {
                    method: 'OPTIONS'
                });
                
                const apiStatus = response.ok ? 'success' : 'error';
                const apiMessage = response.ok ? 'API端点可访问' : `HTTP ${response.status}`;
                
                resultDiv.innerHTML = `
                    <div class="status ${apiStatus}">上传API: ${apiMessage}</div>
                    <div class="status info">CORS Headers: ${response.headers.get('Access-Control-Allow-Origin') || '无'}</div>
                `;
                
            } catch (error) {
                resultDiv.innerHTML = `<div class="status error">网络请求失败: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
