<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品管理 - 充电桩维修管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        /* 采用小程序端配色方案 */
        :root {
            --primary-color: #1e88e5;
            --secondary-color: #64b5f6;
            --accent-color: #0d47a1;
            --text-color: #333333;
            --light-text: #757575;
            --background-color: #f5f5f5;
            --white: #ffffff;
            --border-color: #e0e0e0;
            --success-color: #4caf50;
            --warning-color: #ff9800;
            --error-color: #f44336;
            --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.05);
            --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.08);
        }

        body {
            background-color: var(--background-color);
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            color: var(--text-color);
            line-height: 1.5;
        }

        .sidebar {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
            min-height: 100vh;
            box-shadow: var(--shadow-medium);
        }

        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 1rem 1.5rem;
            border-radius: 8px;
            margin: 0.25rem 1rem;
            transition: all 0.3s ease;
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background-color: rgba(255,255,255,0.2);
            color: white;
            transform: translateX(5px);
        }

        .main-content {
            padding: 2rem;
        }

        .user-info {
            background: rgba(255,255,255,0.15);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 1.5rem 1rem;
            margin-bottom: 1.5rem;
            text-align: center;
            border: 1px solid rgba(255,255,255,0.2);
            box-shadow: var(--shadow-light);
        }

        .user-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: var(--white);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            color: var(--primary-color);
            font-size: 1.5rem;
            box-shadow: var(--shadow-light);
            position: relative;
        }

        .user-avatar::after {
            content: '';
            position: absolute;
            inset: -2px;
            border-radius: 50%;
            padding: 2px;
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            mask-composite: exclude;
        }

        .user-info h6 {
            color: var(--white) !important;
            font-weight: 600;
            margin-bottom: 0.5rem !important;
            font-size: 1rem;
        }

        .user-info small {
            color: rgba(255,255,255,0.8) !important;
            font-size: 0.85rem;
            font-weight: 500;
        }

        .filter-tabs {
            background: white;
            border-radius: 15px;
            padding: 1rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .filter-tab {
            padding: 0.75rem 1.5rem;
            border-radius: 25px;
            border: none;
            background: transparent;
            color: #666;
            margin-right: 1rem;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            z-index: 10;
            pointer-events: auto;
        }

        .filter-tab.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .filter-tab:hover {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            transform: translateY(-2px);
        }

        /* 筛选操作区域 */
        .filter-actions {
            display: flex;
            align-items: center;
        }

        /* 添加产品按钮样式 */
        .btn-add-product {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            text-decoration: none;
            padding: 0.75rem 1.5rem;
            border-radius: 25px;
            font-weight: 500;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            box-shadow: 0 2px 8px rgba(40, 167, 69, 0.2);
            border: none;
            cursor: pointer;
        }

        .btn-add-product:hover {
            color: white;
            text-decoration: none;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
            background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
        }

        .btn-add-product:focus {
            color: white;
            text-decoration: none;
            box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .filter-tabs .d-flex.justify-content-between {
                flex-direction: column;
                gap: 1rem;
            }

            .filter-actions {
                justify-content: center;
                width: 100%;
            }

            .btn-add-product {
                width: 100%;
                justify-content: center;
            }
        }

        @media (max-width: 576px) {
            .filter-tab {
                padding: 0.5rem 1rem;
                margin-right: 0.5rem;
                margin-bottom: 0.5rem;
                font-size: 0.9rem;
            }

            .btn-add-product {
                padding: 0.6rem 1.2rem;
                font-size: 0.9rem;
            }
        }

        .product-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .product-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }

        .product-image {
            width: 120px;
            height: 120px;
            border-radius: 8px;
            object-fit: cover;
            margin-right: 1.5rem;
        }

        .product-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .info-item {
            display: flex;
            flex-direction: column;
        }

        .info-label {
            font-size: 0.85rem;
            color: #666;
            margin-bottom: 0.25rem;
        }

        .info-value {
            font-weight: 600;
            color: #333;
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-上架 {
            background: #d4edda;
            color: #155724;
        }

        .status-下架 {
            background: #f8d7da;
            color: #721c24;
        }

        .action-btn {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            border: none;
            font-size: 0.85rem;
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .btn-edit {
            background: #e3f2fd;
            color: #1976d2;
        }

        .btn-edit:hover {
            background: #1976d2;
            color: white;
        }

        .btn-toggle-status {
            background: #fff3cd;
            color: #856404;
        }

        .btn-toggle-status:hover {
            background: #856404;
            color: white;
        }

        .btn-delete {
            background: #f8d7da;
            color: #721c24;
        }

        .btn-delete:hover {
            background: #721c24;
            color: white;
        }

        .btn-outline-secondary {
            background: #f8f9fa;
            color: #6c757d;
            border: 1px solid #dee2e6;
        }

        .btn-outline-secondary:hover {
            background: #6c757d;
            color: white;
        }

        .product-features {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-top: 0.5rem;
        }

        .feature-tag {
            background: #e9ecef;
            color: #495057;
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.75rem;
        }

        /* 图片预览模态框 */
        .image-preview-modal {
            display: none;
            position: fixed;
            z-index: 9999;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.8);
            cursor: pointer;
        }

        .image-preview-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            max-width: 90%;
            max-height: 90%;
        }

        .image-preview-img {
            width: 100%;
            height: auto;
            border-radius: 8px;
        }

        .image-preview-close {
            position: absolute;
            top: 15px;
            right: 25px;
            color: white;
            font-size: 35px;
            font-weight: bold;
            cursor: pointer;
        }

        .image-preview-close:hover {
            opacity: 0.7;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="user-info">
                        <div class="user-avatar">
                            <i class="fas fa-user-shield"></i>
                        </div>
                        <h6 th:text="${admin.realName ?: '超级管理员'}">超级管理员</h6>
                        <small th:text="${admin.role ?: 'super_admin'}">super_admin</small>
                    </div>

                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/index">
                                <i class="bi bi-house-door-fill me-2"></i>
                                仪表盘
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/engineers">
                                <i class="bi bi-people-fill me-2"></i>
                                工程师管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/service-centers">
                                <i class="bi bi-building me-2"></i>
                                服务网点管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="/admin/products">
                                <i class="bi bi-box-seam me-2"></i>
                                产品管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/orders">
                                <i class="fas fa-clipboard-list me-2"></i>
                                订单管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/customer-service">
                                <i class="fas fa-comments me-2"></i>
                                在线客服
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/logout">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                退出登录
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <!-- 顶部导航 -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="bi bi-box-seam text-primary me-2"></i>
                        产品管理
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="location.reload()">
                                <i class="bi bi-arrow-clockwise"></i> 刷新
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 状态筛选 -->
                <div class="filter-tabs">
                    <div class="d-flex flex-wrap justify-content-between align-items-center">
                        <div class="d-flex flex-wrap">
                            <button class="filter-tab" th:classappend="${currentStatus == '' ? 'active' : ''}"
                                    onclick="console.log('全部产品按钮被点击'); filterProducts('');"
                                    type="button">
                                <i class="bi bi-list-ul me-1"></i>全部产品
                            </button>
                            <button class="filter-tab" th:classappend="${currentStatus == '1' ? 'active' : ''}"
                                    onclick="console.log('已上架按钮被点击'); filterProducts('1');"
                                    type="button">
                                <i class="bi bi-check-circle me-1"></i>已上架
                            </button>
                            <button class="filter-tab" th:classappend="${currentStatus == '0' ? 'active' : ''}"
                                    onclick="console.log('已下架按钮被点击'); filterProducts('0');"
                                    type="button">
                                <i class="bi bi-x-circle me-1"></i>已下架
                            </button>
                        </div>
                        <div class="filter-actions">
                            <button class="btn-add-product" onclick="showAddProductModal()">
                                <i class="bi bi-plus-circle me-1"></i>添加产品
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 产品列表 -->
                <div class="row">
                    <div class="col-12" th:if="${productPage.records.size() == 0}">
                        <div class="text-center py-5">
                            <i class="bi bi-box-seam display-1 text-muted"></i>
                            <h4 class="text-muted mt-3">暂无产品数据</h4>
                            <p class="text-muted">当前筛选条件下没有找到产品</p>
                        </div>
                    </div>

                    <div class="col-12" th:each="product : ${productPage.records}">
                        <div class="product-card">
                            <div class="d-flex align-items-start mb-3">
                                <img th:src="${product.mainImage}" alt="产品图片" class="product-image"
                                     onclick="showImagePreview(this.src)" style="cursor: pointer;">
                                <div class="flex-grow-1">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <div>
                                            <h5 class="mb-1" th:text="${product.name}">产品名称</h5>
                                            <small class="text-muted" th:text="'ID: ' + ${product.id} + ' | 创建时间: ' + ${#temporals.format(product.createdAt, 'yyyy-MM-dd HH:mm')}">产品信息</small>
                                        </div>
                                        <span class="status-badge" th:classappend="'status-' + (${product.status == 1 ? '上架' : '下架'})"
                                              th:text="${product.status == 1 ? '已上架' : '已下架'}">
                                            状态
                                        </span>
                                    </div>

                                    <div class="product-info">
                                        <div class="info-item">
                                            <span class="info-label">价格</span>
                                            <span class="info-value">
                                                ¥<span th:text="${product.price}">0.00</span>
                                                <small th:if="${product.originalPrice != null and product.originalPrice > product.price}"
                                                       class="text-muted text-decoration-line-through ms-2">
                                                    ¥<span th:text="${product.originalPrice}">0.00</span>
                                                </small>
                                            </span>
                                        </div>
                                        <div class="info-item">
                                            <span class="info-label">库存</span>
                                            <span class="info-value" th:text="${product.stock} + '件'">库存</span>
                                        </div>
                                        <div class="info-item">
                                            <span class="info-label">销量</span>
                                            <span class="info-value" th:text="${product.sales} + '件'">销量</span>
                                        </div>
                                        <div class="info-item">
                                            <span class="info-label">分类</span>
                                            <span class="info-value" th:text="${product.categoryId}">分类ID</span>
                                        </div>
                                        <div class="info-item" th:if="${product.brand}">
                                            <span class="info-label">品牌</span>
                                            <span class="info-value" th:text="${product.brand}">品牌</span>
                                        </div>
                                        <div class="info-item" th:if="${product.model}">
                                            <span class="info-label">型号</span>
                                            <span class="info-value" th:text="${product.model}">型号</span>
                                        </div>
                                    </div>

                                    <!-- 产品特点 -->
                                    <div class="mb-3" th:if="${product.features}">
                                        <span class="info-label">产品特点：</span>
                                        <div class="product-features features-container" th:data-features="${product.features}">
                                            <!-- 特点标签将通过JavaScript动态生成 -->
                                        </div>
                                    </div>

                                    <!-- 产品描述 -->
                                    <div class="mb-3" th:if="${product.description}">
                                        <span class="info-label">产品描述：</span>
                                        <span class="info-value" th:text="${product.description}">产品描述</span>
                                    </div>

                                    <!-- 操作按钮 -->
                                    <div class="d-flex flex-wrap">
                                        <button class="action-btn btn-edit"
                                                onclick="showEditProductModal(this)"
                                                th:data-product-id="${product.id}">
                                            <i class="bi bi-pencil me-1"></i>编辑
                                        </button>

                                        <button class="action-btn btn-toggle-status"
                                                onclick="toggleProductStatus(this)"
                                                th:data-product-id="${product.id}"
                                                th:data-current-status="${product.status}">
                                            <i class="bi bi-arrow-repeat me-1"></i>
                                            <span th:text="${product.status == 1 ? '下架' : '上架'}">切换状态</span>
                                        </button>

                                        <button class="action-btn btn-delete"
                                                onclick="deleteProduct(this)"
                                                th:data-product-id="${product.id}">
                                            <i class="bi bi-trash me-1"></i>删除
                                        </button>

                                        <button class="action-btn btn-outline-secondary"
                                                onclick="viewProductDetail(this)"
                                                th:data-product-id="${product.id}">
                                            <i class="bi bi-eye me-1"></i>查看详情
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 分页 -->
                <nav th:if="${productPage.pages > 1}" aria-label="产品分页">
                    <ul class="pagination justify-content-center">
                        <li class="page-item" th:classappend="${productPage.current == 1 ? 'disabled' : ''}">
                            <a class="page-link" th:href="@{/admin/products(page=${productPage.current - 1}, status=${currentStatus})}">上一页</a>
                        </li>

                        <li th:each="i : ${#numbers.sequence(1, productPage.pages)}"
                            class="page-item"
                            th:classappend="${i == productPage.current ? 'active' : ''}">
                            <a class="page-link" th:href="@{/admin/products(page=${i}, status=${currentStatus})}" th:text="${i}">1</a>
                        </li>

                        <li class="page-item" th:classappend="${productPage.current == productPage.pages ? 'disabled' : ''}">
                            <a class="page-link" th:href="@{/admin/products(page=${productPage.current + 1}, status=${currentStatus})}">下一页</a>
                        </li>
                    </ul>
                </nav>
            </main>
        </div>
    </div>

    <!-- 添加产品模态框 -->
    <div class="modal fade" id="addProductModal" tabindex="-1" aria-labelledby="addProductModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addProductModalLabel">
                        <i class="bi bi-plus-circle me-2"></i>添加产品
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addProductForm" enctype="multipart/form-data">
                        <!-- 导航标签 -->
                        <ul class="nav nav-tabs mb-4" id="productFormTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="basic-tab" data-bs-toggle="tab" data-bs-target="#basic-info" type="button" role="tab">
                                    <i class="bi bi-info-circle me-1"></i>基本信息
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="details-tab" data-bs-toggle="tab" data-bs-target="#product-details" type="button" role="tab">
                                    <i class="bi bi-list-ul me-1"></i>产品详情
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="specs-tab" data-bs-toggle="tab" data-bs-target="#specifications" type="button" role="tab">
                                    <i class="bi bi-gear me-1"></i>规格参数
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="images-tab" data-bs-toggle="tab" data-bs-target="#product-images" type="button" role="tab">
                                    <i class="bi bi-image me-1"></i>产品图片
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="seo-tab" data-bs-toggle="tab" data-bs-target="#seo-settings" type="button" role="tab">
                                    <i class="bi bi-search me-1"></i>SEO设置
                                </button>
                            </li>
                        </ul>

                        <!-- 标签内容 -->
                        <div class="tab-content" id="productFormTabContent">
                            <!-- 基本信息标签 -->
                            <div class="tab-pane fade show active" id="basic-info" role="tabpanel">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6 class="border-bottom pb-2 mb-3">
                                            <i class="bi bi-info-circle me-2"></i>基本信息
                                        </h6>
                                        <div class="mb-3">
                                            <label for="productName" class="form-label">产品名称 <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="productName" name="name" required>
                                        </div>
                                        <div class="mb-3">
                                            <label for="productShortName" class="form-label">产品简称</label>
                                            <input type="text" class="form-control" id="productShortName" name="shortName"
                                                   placeholder="用于列表显示的简短名称">
                                            <div class="form-text">用于商品列表显示，建议不超过20个字符</div>
                                        </div>
                                        <div class="mb-3">
                                            <label for="productBrand" class="form-label">品牌</label>
                                            <input type="text" class="form-control" id="productBrand" name="brand">
                                        </div>
                                        <div class="mb-3">
                                            <label for="productModel" class="form-label">型号</label>
                                            <input type="text" class="form-control" id="productModel" name="model">
                                        </div>
                                        <div class="mb-3">
                                            <label for="productCategory" class="form-label">分类 <span class="text-danger">*</span></label>
                                            <select class="form-select" id="productCategory" name="categoryId" required>
                                                <option value="">请选择分类</option>
                                                <option value="1">充电枪</option>
                                                <option value="2">线缆</option>
                                                <option value="3">模块</option>
                                                <option value="4">保护器</option>
                                                <option value="5">配件</option>
                                                <option value="6">工具</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label for="productDescription" class="form-label">产品描述</label>
                                            <textarea class="form-control" id="productDescription" name="description" rows="4"
                                                      placeholder="详细描述产品的功能、特性和用途"></textarea>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <h6 class="border-bottom pb-2 mb-3">
                                            <i class="bi bi-currency-dollar me-2"></i>价格和库存
                                        </h6>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="productPrice" class="form-label">售价 (元) <span class="text-danger">*</span></label>
                                                    <input type="number" class="form-control" id="productPrice" name="price" min="0" step="0.01" required>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="productOriginalPrice" class="form-label">原价 (元)</label>
                                                    <input type="number" class="form-control" id="productOriginalPrice" name="originalPrice" min="0" step="0.01">
                                                    <div class="form-text">用于显示折扣信息</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="productStock" class="form-label">库存数量 <span class="text-danger">*</span></label>
                                                    <input type="number" class="form-control" id="productStock" name="stock" min="0" required>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="productSales" class="form-label">初始销量</label>
                                                    <input type="number" class="form-control" id="productSales" name="sales" min="0" value="0">
                                                    <div class="form-text">用于显示销量信息</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <label for="productSortOrder" class="form-label">排序权重</label>
                                            <input type="number" class="form-control" id="productSortOrder" name="sortOrder" value="0">
                                            <div class="form-text">数值越大排序越靠前</div>
                                        </div>
                                        <div class="mb-3">
                                            <label for="productWeight" class="form-label">重量 (kg)</label>
                                            <input type="number" class="form-control" id="productWeight" name="weight" min="0" step="0.01">
                                        </div>
                                        <div class="mb-3">
                                            <label for="productDimensions" class="form-label">尺寸</label>
                                            <input type="text" class="form-control" id="productDimensions" name="dimensions"
                                                   placeholder="如：长×宽×高 (cm)">
                                        </div>
                                        <div class="mb-3">
                                            <label for="productWarrantyPeriod" class="form-label">保修期</label>
                                            <input type="text" class="form-control" id="productWarrantyPeriod" name="warrantyPeriod"
                                                   placeholder="如：1年、2年、终身保修">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 产品详情标签 -->
                            <div class="tab-pane fade" id="product-details" role="tabpanel">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6 class="border-bottom pb-2 mb-3">
                                            <i class="bi bi-list-ul me-2"></i>产品特点
                                        </h6>
                                        <div class="mb-3">
                                            <label for="productFeatures" class="form-label">产品特点</label>
                                            <div id="featuresContainer">
                                                <div class="feature-input-group mb-2">
                                                    <div class="input-group">
                                                        <input type="text" class="form-control feature-input" placeholder="输入产品特点">
                                                        <button class="btn btn-outline-danger" type="button" onclick="removeFeature(this)">
                                                            <i class="bi bi-trash"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="addFeature()">
                                                <i class="bi bi-plus me-1"></i>添加特点
                                            </button>
                                            <input type="hidden" id="productFeatures" name="features">
                                        </div>

                                        <div class="mb-3">
                                            <label for="productCompatibleCars" class="form-label">适用车型</label>
                                            <textarea class="form-control" id="productCompatibleCars" name="compatibleCars" rows="4"
                                                      placeholder="请输入适用的车型，如：特斯拉Model 3/Y、比亚迪汉EV、小鹏P7等"></textarea>
                                            <div class="form-text">详细说明产品适用的电动车型号</div>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <h6 class="border-bottom pb-2 mb-3">
                                            <i class="bi bi-tools me-2"></i>服务说明
                                        </h6>
                                        <div class="mb-3">
                                            <label for="productServices" class="form-label">服务说明</label>
                                            <div id="servicesContainer">
                                                <div class="service-input-group mb-2">
                                                    <div class="input-group">
                                                        <input type="text" class="form-control service-input" placeholder="输入服务说明">
                                                        <button class="btn btn-outline-danger" type="button" onclick="removeService(this)">
                                                            <i class="bi bi-trash"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="addService()">
                                                <i class="bi bi-plus me-1"></i>添加服务
                                            </button>
                                            <input type="hidden" id="productServices" name="services">
                                            <div class="form-text mt-2">如：免费安装、7天无理由退货、全国联保等</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 规格参数标签 -->
                            <div class="tab-pane fade" id="specifications" role="tabpanel">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6 class="border-bottom pb-2 mb-3">
                                            <i class="bi bi-gear me-2"></i>规格参数
                                        </h6>
                                        <div class="mb-3">
                                            <label class="form-label">规格参数</label>
                                            <div id="specificationsContainer">
                                                <div class="specification-input-group mb-2">
                                                    <div class="row">
                                                        <div class="col-5">
                                                            <input type="text" class="form-control spec-name" placeholder="参数名称">
                                                        </div>
                                                        <div class="col-5">
                                                            <input type="text" class="form-control spec-value" placeholder="参数值">
                                                        </div>
                                                        <div class="col-2">
                                                            <button class="btn btn-outline-danger" type="button" onclick="removeSpecification(this)">
                                                                <i class="bi bi-trash"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="addSpecification()">
                                                <i class="bi bi-plus me-1"></i>添加参数
                                            </button>
                                            <input type="hidden" id="productSpecifications" name="specifications">
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <h6 class="border-bottom pb-2 mb-3">
                                            <i class="bi bi-list-check me-2"></i>规格选项
                                        </h6>
                                        <div class="mb-3">
                                            <label class="form-label">规格选项</label>
                                            <div id="specsContainer">
                                                <div class="spec-input-group mb-2">
                                                    <div class="input-group">
                                                        <input type="text" class="form-control spec-option" placeholder="规格选项名称">
                                                        <button class="btn btn-outline-danger" type="button" onclick="removeSpec(this)">
                                                            <i class="bi bi-trash"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="addSpec()">
                                                <i class="bi bi-plus me-1"></i>添加规格
                                            </button>
                                            <input type="hidden" id="productSpecs" name="specs">
                                            <div class="form-text mt-2">如：标准版、豪华版、快充版等</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 产品图片标签 -->
                            <div class="tab-pane fade" id="product-images" role="tabpanel">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6 class="border-bottom pb-2 mb-3">
                                            <i class="bi bi-image me-2"></i>主图和轮播图
                                        </h6>
                                        <div class="mb-3">
                                            <label for="productMainImage" class="form-label">主图 <span class="text-danger">*</span></label>
                                            <input type="file" class="form-control" id="productMainImage" name="mainImageFile" accept="image/*" required>
                                            <div class="form-text">建议尺寸：800x800像素，支持JPG、PNG格式</div>
                                            <div id="mainImagePreview" class="mt-2" style="display: none;">
                                                <img id="mainImagePreviewImg" src="" alt="主图预览" style="max-width: 200px; max-height: 200px; border-radius: 8px;">
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <label for="productImages" class="form-label">轮播图片</label>
                                            <input type="file" class="form-control" id="productImages" name="imageFiles" accept="image/*" multiple>
                                            <div class="form-text">可选择多张图片用于轮播展示，建议尺寸：800x800像素</div>
                                            <div id="imagesPreview" class="mt-2 d-flex flex-wrap gap-2"></div>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <h6 class="border-bottom pb-2 mb-3">
                                            <i class="bi bi-file-image me-2"></i>详情图片
                                        </h6>
                                        <div class="mb-3">
                                            <label for="productDetailImage" class="form-label">详情图片</label>
                                            <input type="file" class="form-control" id="productDetailImage" name="detailImageFile" accept="image/*">
                                            <div class="form-text">用于商品详情页面展示，建议尺寸：800x1200像素</div>
                                            <div id="detailImagePreview" class="mt-2" style="display: none;">
                                                <img id="detailImagePreviewImg" src="" alt="详情图预览" style="max-width: 200px; max-height: 300px; border-radius: 8px;">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- SEO设置标签 -->
                            <div class="tab-pane fade" id="seo-settings" role="tabpanel">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6 class="border-bottom pb-2 mb-3">
                                            <i class="bi bi-search me-2"></i>SEO优化
                                        </h6>
                                        <div class="mb-3">
                                            <label for="productSeoTitle" class="form-label">SEO标题</label>
                                            <input type="text" class="form-control" id="productSeoTitle" name="seoTitle"
                                                   placeholder="搜索引擎显示的标题">
                                            <div class="form-text">建议长度：50-60个字符</div>
                                        </div>
                                        <div class="mb-3">
                                            <label for="productSeoKeywords" class="form-label">SEO关键词</label>
                                            <input type="text" class="form-control" id="productSeoKeywords" name="seoKeywords"
                                                   placeholder="关键词1,关键词2,关键词3">
                                            <div class="form-text">多个关键词用逗号分隔</div>
                                        </div>
                                        <div class="mb-3">
                                            <label for="productSeoDescription" class="form-label">SEO描述</label>
                                            <textarea class="form-control" id="productSeoDescription" name="seoDescription" rows="4"
                                                      placeholder="搜索引擎显示的描述信息"></textarea>
                                            <div class="form-text">建议长度：150-160个字符</div>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <h6 class="border-bottom pb-2 mb-3">
                                            <i class="bi bi-gear me-2"></i>其他设置
                                        </h6>
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="productStatus" name="status" checked>
                                                <label class="form-check-label" for="productStatus">
                                                    立即上架
                                                </label>
                                            </div>
                                        </div>
                                        <div class="alert alert-info">
                                            <i class="bi bi-info-circle me-2"></i>
                                            <strong>提示：</strong>SEO设置有助于提高产品在搜索引擎中的排名和曝光度。
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-success" id="confirmAddProductBtn">
                        <i class="bi bi-plus-circle me-1"></i>添加产品
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑产品模态框 -->
    <div class="modal fade" id="editProductModal" tabindex="-1" aria-labelledby="editProductModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editProductModalLabel">
                        <i class="bi bi-pencil me-2"></i>编辑产品
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="editProductForm" enctype="multipart/form-data">
                        <input type="hidden" id="editProductId" name="id">

                        <!-- 导航标签 -->
                        <ul class="nav nav-tabs mb-4" id="editProductFormTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="edit-basic-tab" data-bs-toggle="tab" data-bs-target="#edit-basic-info" type="button" role="tab">
                                    <i class="bi bi-info-circle me-1"></i>基本信息
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="edit-details-tab" data-bs-toggle="tab" data-bs-target="#edit-product-details" type="button" role="tab">
                                    <i class="bi bi-list-ul me-1"></i>产品详情
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="edit-specs-tab" data-bs-toggle="tab" data-bs-target="#edit-specifications" type="button" role="tab">
                                    <i class="bi bi-gear me-1"></i>规格参数
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="edit-images-tab" data-bs-toggle="tab" data-bs-target="#edit-product-images" type="button" role="tab">
                                    <i class="bi bi-image me-1"></i>产品图片
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="edit-seo-tab" data-bs-toggle="tab" data-bs-target="#edit-seo-settings" type="button" role="tab">
                                    <i class="bi bi-search me-1"></i>SEO设置
                                </button>
                            </li>
                        </ul>

                        <!-- 标签内容 -->
                        <div class="tab-content" id="editProductFormTabContent">
                            <!-- 基本信息标签 -->
                            <div class="tab-pane fade show active" id="edit-basic-info" role="tabpanel">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6 class="border-bottom pb-2 mb-3">
                                            <i class="bi bi-info-circle me-2"></i>基本信息
                                        </h6>
                                        <div class="mb-3">
                                            <label for="editProductName" class="form-label">产品名称 <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="editProductName" name="name" required>
                                        </div>
                                        <div class="mb-3">
                                            <label for="editProductShortName" class="form-label">产品简称</label>
                                            <input type="text" class="form-control" id="editProductShortName" name="shortName">
                                        </div>
                                        <div class="mb-3">
                                            <label for="editProductBrand" class="form-label">品牌</label>
                                            <input type="text" class="form-control" id="editProductBrand" name="brand">
                                        </div>
                                        <div class="mb-3">
                                            <label for="editProductModel" class="form-label">型号</label>
                                            <input type="text" class="form-control" id="editProductModel" name="model">
                                        </div>
                                        <div class="mb-3">
                                            <label for="editProductCategory" class="form-label">分类 <span class="text-danger">*</span></label>
                                            <select class="form-select" id="editProductCategory" name="categoryId" required>
                                                <option value="">请选择分类</option>
                                                <option value="1">充电枪</option>
                                                <option value="2">线缆</option>
                                                <option value="3">模块</option>
                                                <option value="4">保护器</option>
                                                <option value="5">配件</option>
                                                <option value="6">工具</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label for="editProductDescription" class="form-label">产品描述</label>
                                            <textarea class="form-control" id="editProductDescription" name="description" rows="4"></textarea>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <h6 class="border-bottom pb-2 mb-3">
                                            <i class="bi bi-currency-dollar me-2"></i>价格和库存
                                        </h6>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="editProductPrice" class="form-label">售价 (元) <span class="text-danger">*</span></label>
                                                    <input type="number" class="form-control" id="editProductPrice" name="price" min="0" step="0.01" required>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="editProductOriginalPrice" class="form-label">原价 (元)</label>
                                                    <input type="number" class="form-control" id="editProductOriginalPrice" name="originalPrice" min="0" step="0.01">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="editProductStock" class="form-label">库存数量 <span class="text-danger">*</span></label>
                                                    <input type="number" class="form-control" id="editProductStock" name="stock" min="0" required>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="editProductSales" class="form-label">销量</label>
                                                    <input type="number" class="form-control" id="editProductSales" name="sales" min="0">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <label for="editProductSortOrder" class="form-label">排序权重</label>
                                            <input type="number" class="form-control" id="editProductSortOrder" name="sortOrder">
                                        </div>
                                        <div class="mb-3">
                                            <label for="editProductWeight" class="form-label">重量 (kg)</label>
                                            <input type="number" class="form-control" id="editProductWeight" name="weight" min="0" step="0.01">
                                        </div>
                                        <div class="mb-3">
                                            <label for="editProductDimensions" class="form-label">尺寸</label>
                                            <input type="text" class="form-control" id="editProductDimensions" name="dimensions">
                                        </div>
                                        <div class="mb-3">
                                            <label for="editProductWarrantyPeriod" class="form-label">保修期</label>
                                            <input type="text" class="form-control" id="editProductWarrantyPeriod" name="warrantyPeriod">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 产品详情标签 -->
                            <div class="tab-pane fade" id="edit-product-details" role="tabpanel">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6 class="border-bottom pb-2 mb-3">
                                            <i class="bi bi-list-ul me-2"></i>产品特点
                                        </h6>
                                        <div class="mb-3">
                                            <label class="form-label">产品特点</label>
                                            <div id="editFeaturesContainer">
                                                <!-- 动态生成的特点输入框 -->
                                            </div>
                                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="addEditFeature()">
                                                <i class="bi bi-plus me-1"></i>添加特点
                                            </button>
                                            <input type="hidden" id="editProductFeatures" name="features">
                                        </div>

                                        <div class="mb-3">
                                            <label for="editProductCompatibleCars" class="form-label">适用车型</label>
                                            <textarea class="form-control" id="editProductCompatibleCars" name="compatibleCars" rows="4"></textarea>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <h6 class="border-bottom pb-2 mb-3">
                                            <i class="bi bi-tools me-2"></i>服务说明
                                        </h6>
                                        <div class="mb-3">
                                            <label class="form-label">服务说明</label>
                                            <div id="editServicesContainer">
                                                <!-- 动态生成的服务输入框 -->
                                            </div>
                                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="addEditService()">
                                                <i class="bi bi-plus me-1"></i>添加服务
                                            </button>
                                            <input type="hidden" id="editProductServices" name="services">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 规格参数标签 -->
                            <div class="tab-pane fade" id="edit-specifications" role="tabpanel">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6 class="border-bottom pb-2 mb-3">
                                            <i class="bi bi-gear me-2"></i>规格参数
                                        </h6>
                                        <div class="mb-3">
                                            <label class="form-label">规格参数</label>
                                            <div id="editSpecificationsContainer">
                                                <!-- 动态生成的规格参数输入框 -->
                                            </div>
                                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="addEditSpecification()">
                                                <i class="bi bi-plus me-1"></i>添加参数
                                            </button>
                                            <input type="hidden" id="editProductSpecifications" name="specifications">
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <h6 class="border-bottom pb-2 mb-3">
                                            <i class="bi bi-list-check me-2"></i>规格选项
                                        </h6>
                                        <div class="mb-3">
                                            <label class="form-label">规格选项</label>
                                            <div id="editSpecsContainer">
                                                <!-- 动态生成的规格选项输入框 -->
                                            </div>
                                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="addEditSpec()">
                                                <i class="bi bi-plus me-1"></i>添加规格
                                            </button>
                                            <input type="hidden" id="editProductSpecs" name="specs">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 产品图片标签 -->
                            <div class="tab-pane fade" id="edit-product-images" role="tabpanel">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6 class="border-bottom pb-2 mb-3">
                                            <i class="bi bi-image me-2"></i>当前图片
                                        </h6>
                                        <div class="mb-3">
                                            <label class="form-label">当前主图</label>
                                            <div id="currentMainImagePreview" class="mb-2">
                                                <!-- 当前主图预览 -->
                                            </div>
                                            <label for="editProductMainImage" class="form-label">更换主图</label>
                                            <input type="file" class="form-control" id="editProductMainImage" name="mainImageFile" accept="image/*">
                                            <div class="form-text">选择文件将替换当前主图</div>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">当前轮播图</label>
                                            <div id="currentImagesPreview" class="mb-2 d-flex flex-wrap gap-2">
                                                <!-- 当前轮播图预览 -->
                                            </div>
                                            <label for="editProductImages" class="form-label">更换轮播图</label>
                                            <input type="file" class="form-control" id="editProductImages" name="imageFiles" accept="image/*" multiple>
                                            <div class="form-text">选择文件将替换当前轮播图</div>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <h6 class="border-bottom pb-2 mb-3">
                                            <i class="bi bi-file-image me-2"></i>详情图片
                                        </h6>
                                        <div class="mb-3">
                                            <label class="form-label">当前详情图</label>
                                            <div id="currentDetailImagePreview" class="mb-2">
                                                <!-- 当前详情图预览 -->
                                            </div>
                                            <label for="editProductDetailImage" class="form-label">更换详情图</label>
                                            <input type="file" class="form-control" id="editProductDetailImage" name="detailImageFile" accept="image/*">
                                            <div class="form-text">选择文件将替换当前详情图</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- SEO设置标签 -->
                            <div class="tab-pane fade" id="edit-seo-settings" role="tabpanel">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6 class="border-bottom pb-2 mb-3">
                                            <i class="bi bi-search me-2"></i>SEO优化
                                        </h6>
                                        <div class="mb-3">
                                            <label for="editProductSeoTitle" class="form-label">SEO标题</label>
                                            <input type="text" class="form-control" id="editProductSeoTitle" name="seoTitle">
                                        </div>
                                        <div class="mb-3">
                                            <label for="editProductSeoKeywords" class="form-label">SEO关键词</label>
                                            <input type="text" class="form-control" id="editProductSeoKeywords" name="seoKeywords">
                                        </div>
                                        <div class="mb-3">
                                            <label for="editProductSeoDescription" class="form-label">SEO描述</label>
                                            <textarea class="form-control" id="editProductSeoDescription" name="seoDescription" rows="4"></textarea>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <h6 class="border-bottom pb-2 mb-3">
                                            <i class="bi bi-gear me-2"></i>其他设置
                                        </h6>
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="editProductStatus" name="status">
                                                <label class="form-check-label" for="editProductStatus">
                                                    上架状态
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="confirmEditProductBtn">
                        <i class="bi bi-check me-1"></i>保存修改
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 产品详情模态框 -->
    <div class="modal fade" id="productDetailModal" tabindex="-1" aria-labelledby="productDetailModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="productDetailModalLabel">
                        <i class="bi bi-eye me-2"></i>产品详情
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="text-center mb-4">
                                <img id="detailMainImage" src="" alt="产品主图" class="img-fluid rounded" style="max-height: 300px;">
                            </div>
                            <div id="detailImageGallery" class="d-flex flex-wrap gap-2 justify-content-center">
                                <!-- 图片画廊 -->
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="border-bottom pb-2 mb-3">基本信息</h6>
                                    <table class="table table-borderless">
                                        <tr>
                                            <td class="text-muted" style="width: 100px;">产品名称:</td>
                                            <td id="detailName">-</td>
                                        </tr>
                                        <tr>
                                            <td class="text-muted">产品简称:</td>
                                            <td id="detailShortName">-</td>
                                        </tr>
                                        <tr>
                                            <td class="text-muted">品牌:</td>
                                            <td id="detailBrand">-</td>
                                        </tr>
                                        <tr>
                                            <td class="text-muted">型号:</td>
                                            <td id="detailModel">-</td>
                                        </tr>
                                        <tr>
                                            <td class="text-muted">分类:</td>
                                            <td id="detailCategory">-</td>
                                        </tr>
                                        <tr>
                                            <td class="text-muted">状态:</td>
                                            <td id="detailStatus">-</td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="border-bottom pb-2 mb-3">价格库存</h6>
                                    <table class="table table-borderless">
                                        <tr>
                                            <td class="text-muted" style="width: 100px;">售价:</td>
                                            <td id="detailPrice">-</td>
                                        </tr>
                                        <tr>
                                            <td class="text-muted">原价:</td>
                                            <td id="detailOriginalPrice">-</td>
                                        </tr>
                                        <tr>
                                            <td class="text-muted">库存:</td>
                                            <td id="detailStock">-</td>
                                        </tr>
                                        <tr>
                                            <td class="text-muted">销量:</td>
                                            <td id="detailSales">-</td>
                                        </tr>
                                        <tr>
                                            <td class="text-muted">排序权重:</td>
                                            <td id="detailSortOrder">-</td>
                                        </tr>
                                        <tr>
                                            <td class="text-muted">重量:</td>
                                            <td id="detailWeight">-</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>

                            <div class="row mt-3">
                                <div class="col-12">
                                    <h6 class="border-bottom pb-2 mb-3">产品描述</h6>
                                    <p id="detailDescription" class="text-muted">-</p>
                                </div>
                            </div>

                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <h6 class="border-bottom pb-2 mb-3">产品特点</h6>
                                    <div id="detailFeatures">
                                        <!-- 产品特点标签 -->
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="border-bottom pb-2 mb-3">服务说明</h6>
                                    <div id="detailServices">
                                        <!-- 服务说明标签 -->
                                    </div>
                                </div>
                            </div>

                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <h6 class="border-bottom pb-2 mb-3">规格参数</h6>
                                    <div id="detailSpecifications">
                                        <!-- 规格参数表格 -->
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="border-bottom pb-2 mb-3">规格选项</h6>
                                    <div id="detailSpecs">
                                        <!-- 规格选项标签 -->
                                    </div>
                                </div>
                            </div>

                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <h6 class="border-bottom pb-2 mb-3">物理属性</h6>
                                    <table class="table table-borderless">
                                        <tr>
                                            <td class="text-muted" style="width: 100px;">尺寸:</td>
                                            <td id="detailDimensions">-</td>
                                        </tr>
                                        <tr>
                                            <td class="text-muted">保修期:</td>
                                            <td id="detailWarrantyPeriod">-</td>
                                        </tr>
                                        <tr>
                                            <td class="text-muted">适用车型:</td>
                                            <td id="detailCompatibleCars">-</td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="border-bottom pb-2 mb-3">SEO信息</h6>
                                    <table class="table table-borderless">
                                        <tr>
                                            <td class="text-muted" style="width: 100px;">SEO标题:</td>
                                            <td id="detailSeoTitle">-</td>
                                        </tr>
                                        <tr>
                                            <td class="text-muted">SEO关键词:</td>
                                            <td id="detailSeoKeywords">-</td>
                                        </tr>
                                        <tr>
                                            <td class="text-muted">SEO描述:</td>
                                            <td id="detailSeoDescription">-</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>

                            <div class="row mt-3">
                                <div class="col-12">
                                    <h6 class="border-bottom pb-2 mb-3">系统信息</h6>
                                    <table class="table table-borderless">
                                        <tr>
                                            <td class="text-muted" style="width: 100px;">产品ID:</td>
                                            <td id="detailId">-</td>
                                        </tr>
                                        <tr>
                                            <td class="text-muted">创建时间:</td>
                                            <td id="detailCreatedAt">-</td>
                                        </tr>
                                        <tr>
                                            <td class="text-muted">更新时间:</td>
                                            <td id="detailUpdatedAt">-</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary" onclick="editProductFromDetail()">
                        <i class="bi bi-pencil me-1"></i>编辑产品
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 图片预览模态框 -->
    <div id="imagePreviewModal" class="image-preview-modal" onclick="closeImagePreview()">
        <span class="image-preview-close" onclick="closeImagePreview()">&times;</span>
        <div class="image-preview-content">
            <img id="previewImage" class="image-preview-img" src="" alt="预览图片">
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 筛选产品
        function filterProducts(status) {
            console.log('筛选产品被调用，状态:', status); // 调试日志

            try {
                const url = new URL(window.location);
                if (status) {
                    url.searchParams.set('status', status);
                } else {
                    url.searchParams.delete('status');
                }
                url.searchParams.delete('page'); // 重置页码

                console.log('跳转到URL:', url.toString()); // 调试日志
                window.location.href = url.toString();
            } catch (error) {
                console.error('筛选产品时发生错误:', error);
                alert('筛选功能出现错误，请刷新页面重试');
            }
        }

        // 显示添加产品模态框
        function showAddProductModal() {
            try {
                // 清空表单
                document.getElementById('addProductForm').reset();

                // 清空图片预览
                const mainImagePreview = document.getElementById('mainImagePreview');
                if (mainImagePreview) {
                    mainImagePreview.style.display = 'none';
                }

                const imagesPreview = document.getElementById('imagesPreview');
                if (imagesPreview) {
                    imagesPreview.innerHTML = '';
                }

                const detailImagePreview = document.getElementById('detailImagePreview');
                if (detailImagePreview) {
                    detailImagePreview.style.display = 'none';
                }

                // 显示模态框
                const modal = new bootstrap.Modal(document.getElementById('addProductModal'));
                modal.show();

                console.log('添加产品模态框已打开');
            } catch (error) {
                console.error('打开添加产品模态框时发生错误:', error);
                alert('打开添加产品窗口失败，请刷新页面重试');
            }
        }

        // 显示编辑产品模态框
        function showEditProductModal(button) {
            const productId = button.getAttribute('data-product-id');
            console.log('🔄 开始编辑产品，ID:', productId);

            // 获取产品详情
            fetch(`/admin/api/products/${productId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        populateEditForm(data.data);
                        const editModal = new bootstrap.Modal(document.getElementById('editProductModal'));
                        editModal.show();
                    } else {
                        alert('获取产品信息失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('获取产品详情失败:', error);
                    alert('获取产品信息失败，请重试');
                });
        }

        // 切换产品状态
        function toggleProductStatus(button) {
            const productId = button.getAttribute('data-product-id');
            const currentStatus = button.getAttribute('data-current-status');
            const newStatus = currentStatus === '1' ? '0' : '1';
            const action = newStatus === '1' ? '上架' : '下架';

            if (confirm(`确定要${action}这个产品吗？`)) {
                fetch(`/admin/api/products/${productId}/status`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ status: parseInt(newStatus) })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(`产品${action}成功！`);
                        location.reload();
                    } else {
                        alert(`${action}失败：` + (data.message || '未知错误'));
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('网络错误，请稍后重试');
                });
            }
        }

        // 删除产品
        function deleteProduct(button) {
            const productId = button.getAttribute('data-product-id');

            if (confirm('确定要删除这个产品吗？删除后无法恢复！')) {
                fetch(`/admin/api/products/${productId}`, {
                    method: 'DELETE'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('产品删除成功！');
                        location.reload();
                    } else {
                        alert('删除失败：' + (data.message || '未知错误'));
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('网络错误，请稍后重试');
                });
            }
        }

        // 查看产品详情
        function viewProductDetail(button) {
            const productId = button.getAttribute('data-product-id');
            console.log('👁️ 查看产品详情，ID:', productId);

            // 获取产品详情
            fetch(`/admin/api/products/${productId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        populateDetailModal(data.data);
                        const detailModal = new bootstrap.Modal(document.getElementById('productDetailModal'));
                        detailModal.show();
                    } else {
                        alert('获取产品详情失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('获取产品详情失败:', error);
                    alert('获取产品详情失败，请重试');
                });
        }

        // 图片预览功能
        function showImagePreview(imageSrc) {
            document.getElementById('previewImage').src = imageSrc;
            document.getElementById('imagePreviewModal').style.display = 'block';
        }

        function closeImagePreview() {
            document.getElementById('imagePreviewModal').style.display = 'none';
        }

        // 填充详情模态框
        function populateDetailModal(product) {
            console.log('📋 填充产品详情:', product);

            // 基本信息
            document.getElementById('detailName').textContent = product.name || '-';
            document.getElementById('detailShortName').textContent = product.shortName || '-';
            document.getElementById('detailBrand').textContent = product.brand || '-';
            document.getElementById('detailModel').textContent = product.model || '-';
            document.getElementById('detailCategory').textContent = getCategoryName(product.categoryId);
            document.getElementById('detailStatus').innerHTML = product.status === 1 ?
                '<span class="badge bg-success">已上架</span>' : '<span class="badge bg-secondary">已下架</span>';

            // 价格库存
            document.getElementById('detailPrice').textContent = product.price ? `¥${product.price}` : '-';
            document.getElementById('detailOriginalPrice').textContent = product.originalPrice ? `¥${product.originalPrice}` : '-';
            document.getElementById('detailStock').textContent = product.stock ? `${product.stock}件` : '-';
            document.getElementById('detailSales').textContent = product.sales ? `${product.sales}件` : '-';
            document.getElementById('detailSortOrder').textContent = product.sortOrder || '-';
            document.getElementById('detailWeight').textContent = product.weight ? `${product.weight}kg` : '-';

            // 描述
            document.getElementById('detailDescription').textContent = product.description || '-';

            // 物理属性
            document.getElementById('detailDimensions').textContent = product.dimensions || '-';
            document.getElementById('detailWarrantyPeriod').textContent = product.warrantyPeriod || '-';
            document.getElementById('detailCompatibleCars').textContent = product.compatibleCars || '-';

            // SEO信息
            document.getElementById('detailSeoTitle').textContent = product.seoTitle || '-';
            document.getElementById('detailSeoKeywords').textContent = product.seoKeywords || '-';
            document.getElementById('detailSeoDescription').textContent = product.seoDescription || '-';

            // 系统信息
            document.getElementById('detailId').textContent = product.id;
            document.getElementById('detailCreatedAt').textContent = formatDateTime(product.createdAt);
            document.getElementById('detailUpdatedAt').textContent = formatDateTime(product.updatedAt);

            // 主图
            const mainImageEl = document.getElementById('detailMainImage');
            if (product.mainImage) {
                mainImageEl.src = product.mainImage;
                mainImageEl.style.display = 'block';
            } else {
                mainImageEl.style.display = 'none';
            }

            // 图片画廊
            const gallery = document.getElementById('detailImageGallery');
            gallery.innerHTML = '';
            if (product.images) {
                const images = product.images.split(',');
                images.forEach(img => {
                    if (img.trim()) {
                        const imgEl = document.createElement('img');
                        imgEl.src = img.trim();
                        imgEl.alt = '产品图片';
                        imgEl.className = 'img-thumbnail';
                        imgEl.style.width = '80px';
                        imgEl.style.height = '80px';
                        imgEl.style.objectFit = 'cover';
                        imgEl.style.cursor = 'pointer';
                        imgEl.onclick = () => showImagePreview(img.trim());
                        gallery.appendChild(imgEl);
                    }
                });
            }

            // JSON字段
            populateDetailJsonFields(product);

            // 存储当前产品ID用于编辑
            window.currentDetailProductId = product.id;
        }

        // 填充详情JSON字段
        function populateDetailJsonFields(product) {
            // 产品特点
            const featuresEl = document.getElementById('detailFeatures');
            if (product.features) {
                try {
                    const features = typeof product.features === 'string' ? JSON.parse(product.features) : product.features;
                    featuresEl.innerHTML = features.map(feature =>
                        `<span class="badge bg-light text-dark me-1 mb-1">${feature}</span>`
                    ).join('');
                } catch (e) {
                    featuresEl.innerHTML = '<span class="text-muted">解析失败</span>';
                }
            } else {
                featuresEl.innerHTML = '<span class="text-muted">-</span>';
            }

            // 服务说明
            const servicesEl = document.getElementById('detailServices');
            if (product.services) {
                try {
                    const services = typeof product.services === 'string' ? JSON.parse(product.services) : product.services;
                    servicesEl.innerHTML = services.map(service =>
                        `<span class="badge bg-info text-white me-1 mb-1">${service}</span>`
                    ).join('');
                } catch (e) {
                    servicesEl.innerHTML = '<span class="text-muted">解析失败</span>';
                }
            } else {
                servicesEl.innerHTML = '<span class="text-muted">-</span>';
            }

            // 规格参数
            const specificationsEl = document.getElementById('detailSpecifications');
            if (product.specifications) {
                try {
                    const specifications = typeof product.specifications === 'string' ? JSON.parse(product.specifications) : product.specifications;
                    specificationsEl.innerHTML = `
                        <table class="table table-sm table-borderless">
                            ${specifications.map(spec =>
                                `<tr><td class="text-muted" style="width: 80px;">${spec.name}:</td><td>${spec.value}</td></tr>`
                            ).join('')}
                        </table>
                    `;
                } catch (e) {
                    specificationsEl.innerHTML = '<span class="text-muted">解析失败</span>';
                }
            } else {
                specificationsEl.innerHTML = '<span class="text-muted">-</span>';
            }

            // 规格选项
            const specsEl = document.getElementById('detailSpecs');
            if (product.specs) {
                try {
                    const specs = typeof product.specs === 'string' ? JSON.parse(product.specs) : product.specs;
                    specsEl.innerHTML = specs.map(spec =>
                        `<span class="badge bg-primary me-1 mb-1">${spec.name || spec}</span>`
                    ).join('');
                } catch (e) {
                    specsEl.innerHTML = '<span class="text-muted">解析失败</span>';
                }
            } else {
                specsEl.innerHTML = '<span class="text-muted">-</span>';
            }
        }

        // 从详情页面编辑产品
        function editProductFromDetail() {
            if (window.currentDetailProductId) {
                // 关闭详情模态框
                const detailModal = bootstrap.Modal.getInstance(document.getElementById('productDetailModal'));
                detailModal.hide();

                // 打开编辑模态框
                setTimeout(() => {
                    const button = document.createElement('button');
                    button.setAttribute('data-product-id', window.currentDetailProductId);
                    showEditProductModal(button);
                }, 300);
            }
        }

        // 获取分类名称
        function getCategoryName(categoryId) {
            const categories = {
                1: '充电枪',
                2: '线缆',
                3: '模块',
                4: '保护器',
                5: '配件',
                6: '工具'
            };
            return categories[categoryId] || '未知分类';
        }

        // 格式化日期时间
        function formatDateTime(dateTimeStr) {
            if (!dateTimeStr) return '-';
            const date = new Date(dateTimeStr);
            return date.toLocaleString('zh-CN');
        }

        // 填充编辑表单
        function populateEditForm(product) {
            console.log('📋 填充编辑表单:', product);

            // 设置产品ID
            document.getElementById('editProductId').value = product.id;

            // 基本信息
            document.getElementById('editProductName').value = product.name || '';
            document.getElementById('editProductShortName').value = product.shortName || '';
            document.getElementById('editProductBrand').value = product.brand || '';
            document.getElementById('editProductModel').value = product.model || '';
            document.getElementById('editProductCategory').value = product.categoryId || '';
            document.getElementById('editProductDescription').value = product.description || '';

            // 价格和库存
            document.getElementById('editProductPrice').value = product.price || '';
            document.getElementById('editProductOriginalPrice').value = product.originalPrice || '';
            document.getElementById('editProductStock').value = product.stock || '';
            document.getElementById('editProductSales').value = product.sales || '';
            document.getElementById('editProductSortOrder').value = product.sortOrder || '';
            document.getElementById('editProductWeight').value = product.weight || '';
            document.getElementById('editProductDimensions').value = product.dimensions || '';
            document.getElementById('editProductWarrantyPeriod').value = product.warrantyPeriod || '';

            // 其他信息
            document.getElementById('editProductCompatibleCars').value = product.compatibleCars || '';

            // SEO信息
            document.getElementById('editProductSeoTitle').value = product.seoTitle || '';
            document.getElementById('editProductSeoKeywords').value = product.seoKeywords || '';
            document.getElementById('editProductSeoDescription').value = product.seoDescription || '';

            // 状态
            document.getElementById('editProductStatus').checked = product.status === 1;

            // 处理JSON字段
            populateEditJsonFields(product);

            // 显示当前图片
            displayCurrentImages(product);
        }

        // 填充编辑表单的JSON字段
        function populateEditJsonFields(product) {
            // 清空容器
            document.getElementById('editFeaturesContainer').innerHTML = '';
            document.getElementById('editServicesContainer').innerHTML = '';
            document.getElementById('editSpecificationsContainer').innerHTML = '';
            document.getElementById('editSpecsContainer').innerHTML = '';

            // 产品特点
            if (product.features) {
                try {
                    const features = typeof product.features === 'string' ? JSON.parse(product.features) : product.features;
                    features.forEach(feature => {
                        addEditFeature(feature);
                    });
                } catch (e) {
                    console.error('解析产品特点失败:', e);
                }
            }
            if (document.getElementById('editFeaturesContainer').children.length === 0) {
                addEditFeature();
            }

            // 服务说明
            if (product.services) {
                try {
                    const services = typeof product.services === 'string' ? JSON.parse(product.services) : product.services;
                    services.forEach(service => {
                        addEditService(service);
                    });
                } catch (e) {
                    console.error('解析服务说明失败:', e);
                }
            }
            if (document.getElementById('editServicesContainer').children.length === 0) {
                addEditService();
            }

            // 规格参数
            if (product.specifications) {
                try {
                    const specifications = typeof product.specifications === 'string' ? JSON.parse(product.specifications) : product.specifications;
                    specifications.forEach(spec => {
                        addEditSpecification(spec.name, spec.value);
                    });
                } catch (e) {
                    console.error('解析规格参数失败:', e);
                }
            }
            if (document.getElementById('editSpecificationsContainer').children.length === 0) {
                addEditSpecification();
            }

            // 规格选项
            if (product.specs) {
                try {
                    const specs = typeof product.specs === 'string' ? JSON.parse(product.specs) : product.specs;
                    specs.forEach(spec => {
                        addEditSpec(spec.name || spec);
                    });
                } catch (e) {
                    console.error('解析规格选项失败:', e);
                }
            }
            if (document.getElementById('editSpecsContainer').children.length === 0) {
                addEditSpec();
            }
        }

        // 显示当前图片
        function displayCurrentImages(product) {
            // 主图
            const currentMainImagePreview = document.getElementById('currentMainImagePreview');
            if (product.mainImage) {
                currentMainImagePreview.innerHTML = `
                    <img src="${product.mainImage}" alt="当前主图" style="max-width: 200px; max-height: 200px; border-radius: 8px;" class="img-thumbnail">
                `;
            } else {
                currentMainImagePreview.innerHTML = '<p class="text-muted">暂无主图</p>';
            }

            // 轮播图
            const currentImagesPreview = document.getElementById('currentImagesPreview');
            if (product.images) {
                const images = product.images.split(',');
                currentImagesPreview.innerHTML = images.map(img => `
                    <img src="${img.trim()}" alt="轮播图" style="width: 100px; height: 100px; object-fit: cover; border-radius: 8px;" class="img-thumbnail">
                `).join('');
            } else {
                currentImagesPreview.innerHTML = '<p class="text-muted">暂无轮播图</p>';
            }

            // 详情图
            const currentDetailImagePreview = document.getElementById('currentDetailImagePreview');
            if (product.detailImage) {
                currentDetailImagePreview.innerHTML = `
                    <img src="${product.detailImage}" alt="当前详情图" style="max-width: 200px; max-height: 300px; border-radius: 8px;" class="img-thumbnail">
                `;
            } else {
                currentDetailImagePreview.innerHTML = '<p class="text-muted">暂无详情图</p>';
            }
        }

        // 编辑表单的动态字段管理函数
        function addEditFeature(value = '') {
            const container = document.getElementById('editFeaturesContainer');
            const div = document.createElement('div');
            div.className = 'input-group mb-2';
            div.innerHTML = `
                <input type="text" class="form-control" placeholder="输入产品特点" value="${value}" onchange="updateEditFeatures()">
                <button class="btn btn-outline-danger" type="button" onclick="removeEditFeature(this)">
                    <i class="bi bi-trash"></i>
                </button>
            `;
            container.appendChild(div);
            updateEditFeatures();
        }

        function removeEditFeature(button) {
            button.parentElement.remove();
            updateEditFeatures();
        }

        function updateEditFeatures() {
            const container = document.getElementById('editFeaturesContainer');
            if (!container) return;

            const inputs = container.querySelectorAll('input');
            const features = Array.from(inputs)
                .map(input => input.value)
                .filter(value => value && value.trim() !== '');

            const hiddenInput = document.getElementById('editProductFeatures');
            if (hiddenInput) {
                hiddenInput.value = JSON.stringify(features);
            }
        }

        function addEditService(value = '') {
            const container = document.getElementById('editServicesContainer');
            const div = document.createElement('div');
            div.className = 'input-group mb-2';
            div.innerHTML = `
                <input type="text" class="form-control" placeholder="输入服务说明" value="${value}" onchange="updateEditServices()">
                <button class="btn btn-outline-danger" type="button" onclick="removeEditService(this)">
                    <i class="bi bi-trash"></i>
                </button>
            `;
            container.appendChild(div);
            updateEditServices();
        }

        function removeEditService(button) {
            button.parentElement.remove();
            updateEditServices();
        }

        function updateEditServices() {
            const container = document.getElementById('editServicesContainer');
            if (!container) return;

            const inputs = container.querySelectorAll('input');
            const services = Array.from(inputs)
                .map(input => input.value)
                .filter(value => value && value.trim() !== '');

            const hiddenInput = document.getElementById('editProductServices');
            if (hiddenInput) {
                hiddenInput.value = JSON.stringify(services);
            }
        }

        function addEditSpecification(name = '', value = '') {
            const container = document.getElementById('editSpecificationsContainer');
            if (!container) return;

            const div = document.createElement('div');
            div.className = 'row mb-2';
            div.innerHTML = `
                <div class="col-md-4">
                    <input type="text" class="form-control spec-name-input" placeholder="参数名" value="${name}" onchange="updateEditSpecifications()">
                </div>
                <div class="col-md-6">
                    <input type="text" class="form-control spec-value-input" placeholder="参数值" value="${value}" onchange="updateEditSpecifications()">
                </div>
                <div class="col-md-2">
                    <button class="btn btn-outline-danger w-100" type="button" onclick="removeEditSpecification(this)">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            `;
            container.appendChild(div);
            updateEditSpecifications();
        }

        function removeEditSpecification(button) {
            button.closest('.row').remove();
            updateEditSpecifications();
        }

        function updateEditSpecifications() {
            const container = document.getElementById('editSpecificationsContainer');
            if (!container) return;

            const rows = container.querySelectorAll('.row');
            const specifications = [];
            rows.forEach(row => {
                const nameInput = row.querySelector('.spec-name-input');
                const valueInput = row.querySelector('.spec-value-input');
                if (nameInput && valueInput && nameInput.value && valueInput.value) {
                    if (nameInput.value.trim() && valueInput.value.trim()) {
                        specifications.push({
                            name: nameInput.value.trim(),
                            value: valueInput.value.trim()
                        });
                    }
                }
            });

            const hiddenInput = document.getElementById('editProductSpecifications');
            if (hiddenInput) {
                hiddenInput.value = JSON.stringify(specifications);
            }
        }

        function addEditSpec(value = '') {
            const container = document.getElementById('editSpecsContainer');
            const div = document.createElement('div');
            div.className = 'input-group mb-2';
            div.innerHTML = `
                <input type="text" class="form-control" placeholder="输入规格选项" value="${value}" onchange="updateEditSpecs()">
                <button class="btn btn-outline-danger" type="button" onclick="removeEditSpec(this)">
                    <i class="bi bi-trash"></i>
                </button>
            `;
            container.appendChild(div);
            updateEditSpecs();
        }

        function removeEditSpec(button) {
            button.parentElement.remove();
            updateEditSpecs();
        }

        function updateEditSpecs() {
            const container = document.getElementById('editSpecsContainer');
            if (!container) return;

            const inputs = container.querySelectorAll('input');
            const specs = Array.from(inputs)
                .map((input, index) => ({
                    id: index + 1,
                    name: input.value ? input.value.trim() : ''
                }))
                .filter(spec => spec.name !== '');

            const hiddenInput = document.getElementById('editProductSpecs');
            if (hiddenInput) {
                hiddenInput.value = JSON.stringify(specs);
            }
        }

        // 原有的动态表单功能
        function addFeature() {
            const container = document.getElementById('featuresContainer');
            const div = document.createElement('div');
            div.className = 'feature-input-group mb-2';
            div.innerHTML = `
                <div class="input-group">
                    <input type="text" class="form-control feature-input" placeholder="输入产品特点">
                    <button class="btn btn-outline-danger" type="button" onclick="removeFeature(this)">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            `;
            container.appendChild(div);
        }

        function removeFeature(button) {
            button.closest('.feature-input-group').remove();
        }

        function addService() {
            const container = document.getElementById('servicesContainer');
            const div = document.createElement('div');
            div.className = 'service-input-group mb-2';
            div.innerHTML = `
                <div class="input-group">
                    <input type="text" class="form-control service-input" placeholder="输入服务说明">
                    <button class="btn btn-outline-danger" type="button" onclick="removeService(this)">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            `;
            container.appendChild(div);
        }

        function removeService(button) {
            button.closest('.service-input-group').remove();
        }

        function addSpecification() {
            const container = document.getElementById('specificationsContainer');
            const div = document.createElement('div');
            div.className = 'specification-input-group mb-2';
            div.innerHTML = `
                <div class="row">
                    <div class="col-5">
                        <input type="text" class="form-control spec-name" placeholder="参数名称">
                    </div>
                    <div class="col-5">
                        <input type="text" class="form-control spec-value" placeholder="参数值">
                    </div>
                    <div class="col-2">
                        <button class="btn btn-outline-danger" type="button" onclick="removeSpecification(this)">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </div>
            `;
            container.appendChild(div);
        }

        function removeSpecification(button) {
            button.closest('.specification-input-group').remove();
        }

        function addSpec() {
            const container = document.getElementById('specsContainer');
            const div = document.createElement('div');
            div.className = 'spec-input-group mb-2';
            div.innerHTML = `
                <div class="input-group">
                    <input type="text" class="form-control spec-option" placeholder="规格选项名称">
                    <button class="btn btn-outline-danger" type="button" onclick="removeSpec(this)">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            `;
            container.appendChild(div);
        }

        function removeSpec(button) {
            button.closest('.spec-input-group').remove();
        }

        // 收集动态表单数据
        function collectFormData() {
            // 收集产品特点
            const features = [];
            document.querySelectorAll('.feature-input').forEach(input => {
                if (input.value.trim()) {
                    features.push(input.value.trim());
                }
            });
            document.getElementById('productFeatures').value = JSON.stringify(features);

            // 收集服务说明
            const services = [];
            document.querySelectorAll('.service-input').forEach(input => {
                if (input.value.trim()) {
                    services.push(input.value.trim());
                }
            });
            document.getElementById('productServices').value = JSON.stringify(services);

            // 收集规格参数
            const specifications = [];
            document.querySelectorAll('.specification-input-group').forEach(group => {
                const name = group.querySelector('.spec-name').value.trim();
                const value = group.querySelector('.spec-value').value.trim();
                if (name && value) {
                    specifications.push({ name, value });
                }
            });
            document.getElementById('productSpecifications').value = JSON.stringify(specifications);

            // 收集规格选项
            const specs = [];
            document.querySelectorAll('.spec-option').forEach(input => {
                if (input.value.trim()) {
                    specs.push(input.value.trim());
                }
            });
            document.getElementById('productSpecs').value = JSON.stringify(specs);
        }

        // 图片预览功能 - 安全版本
        function setupImagePreview(inputId, previewId, previewImgId) {
            const input = document.getElementById(inputId);
            const preview = document.getElementById(previewId);
            const previewImg = document.getElementById(previewImgId);

            if (!input || !preview || !previewImg) {
                console.warn(`图片预览设置失败: ${inputId}, ${previewId}, ${previewImgId} 中有元素不存在`);
                return;
            }

            input.addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        previewImg.src = e.target.result;
                        preview.style.display = 'block';
                    };
                    reader.readAsDataURL(file);
                } else {
                    preview.style.display = 'none';
                }
            });
        }

        // 多图片预览功能 - 安全版本
        function setupMultiImagePreview(inputId, previewId) {
            const input = document.getElementById(inputId);
            const preview = document.getElementById(previewId);

            if (!input || !preview) {
                console.warn(`多图片预览设置失败: ${inputId}, ${previewId} 中有元素不存在`);
                return;
            }

            input.addEventListener('change', function(e) {
                const files = e.target.files;
                preview.innerHTML = '';

                for (let i = 0; i < files.length; i++) {
                    const file = files[i];
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const img = document.createElement('img');
                        img.src = e.target.result;
                        img.style.maxWidth = '150px';
                        img.style.maxHeight = '150px';
                        img.style.borderRadius = '8px';
                        img.style.objectFit = 'cover';
                        img.style.cursor = 'pointer';
                        img.onclick = function() { showImagePreview(this.src); };
                        preview.appendChild(img);
                    };
                    reader.readAsDataURL(file);
                }
            });
        }

        // 初始化图片预览 - 安全版本
        function initImagePreviews() {
            try {
                // 只有当元素存在时才设置预览
                if (document.getElementById('productMainImage') &&
                    document.getElementById('mainImagePreview') &&
                    document.getElementById('mainImagePreviewImg')) {
                    setupImagePreview('productMainImage', 'mainImagePreview', 'mainImagePreviewImg');
                }

                if (document.getElementById('productDetailImage') &&
                    document.getElementById('detailImagePreview') &&
                    document.getElementById('detailImagePreviewImg')) {
                    setupImagePreview('productDetailImage', 'detailImagePreview', 'detailImagePreviewImg');
                }

                if (document.getElementById('productImages') &&
                    document.getElementById('imagesPreview')) {
                    setupMultiImagePreview('productImages', 'imagesPreview');
                }

                console.log('图片预览功能初始化完成');
            } catch (error) {
                console.error('初始化图片预览时发生错误:', error);
            }
        }

        // 主图预览 - 修复元素引用
        const productMainImageInput = document.getElementById('productMainImage');
        if (productMainImageInput) {
            productMainImageInput.addEventListener('change', function(e) {
                const file = e.target.files[0];
                const previewImg = document.getElementById('mainImagePreviewImg');
                const previewContainer = document.getElementById('mainImagePreview');

                if (file && previewImg && previewContainer) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        previewImg.src = e.target.result;
                        previewContainer.style.display = 'block';
                    };
                    reader.readAsDataURL(file);
                } else if (previewContainer) {
                    previewContainer.style.display = 'none';
                }
            });
        }

        // 详情图片预览 - 修复元素引用
        const productDetailImageInput = document.getElementById('productDetailImage');
        if (productDetailImageInput) {
            productDetailImageInput.addEventListener('change', function(e) {
                const file = e.target.files[0];
                const previewContainer = document.getElementById('detailImagePreview');

                if (previewContainer && file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const img = previewContainer.querySelector('img');
                        if (img) {
                            img.src = e.target.result;
                            previewContainer.style.display = 'block';
                        }
                    };
                    reader.readAsDataURL(file);
                } else if (previewContainer) {
                    previewContainer.style.display = 'none';
                }
            });
        }

        // 确认编辑产品
        document.getElementById('confirmEditProductBtn').addEventListener('click', function() {
            console.log('🔄 开始提交编辑表单...');

            try {
                // 更新JSON字段
                updateEditFeatures();
                updateEditServices();
                updateEditSpecifications();
                updateEditSpecs();

                const form = document.getElementById('editProductForm');
                if (!form) {
                    alert('编辑表单未找到');
                    return;
                }

                const formData = new FormData(form);
                const productId = document.getElementById('editProductId').value;

                if (!productId) {
                    alert('产品ID未找到');
                    return;
                }

            // 验证必填字段
            const requiredFields = ['name', 'categoryId', 'price', 'stock'];
            let isValid = true;

            for (const field of requiredFields) {
                const input = form.querySelector(`[name="${field}"]`);
                if (!input || !input.value.trim()) {
                    if (input) input.classList.add('is-invalid');
                    isValid = false;
                } else {
                    input.classList.remove('is-invalid');
                }
            }

            if (!isValid) {
                alert('请填写所有必填字段');
                return;
            }

            // 处理复选框值
            formData.set('status', document.getElementById('editProductStatus').checked ? '1' : '0');

            console.log('📤 提交编辑数据，产品ID:', productId);

            // 提交数据
            fetch(`/admin/api/products/${productId}`, {
                method: 'PUT',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                console.log('📥 编辑响应:', data);
                if (data.success) {
                    alert('产品编辑成功！');
                    bootstrap.Modal.getInstance(document.getElementById('editProductModal')).hide();
                    location.reload();
                } else {
                    alert('编辑失败：' + (data.message || '未知错误'));
                }
            })
            .catch(error => {
                console.error('编辑产品时发生错误:', error);
                alert('网络错误，请稍后重试');
            });

            } catch (error) {
                console.error('编辑表单处理错误:', error);
                alert('表单处理错误：' + error.message);
            }
        });

        // 确认添加产品
        document.getElementById('confirmAddProductBtn').addEventListener('click', function() {
            // 收集动态表单数据
            collectFormData();

            const form = document.getElementById('addProductForm');
            const formData = new FormData(form);

            // 验证必填字段
            const requiredFields = ['name', 'categoryId', 'price', 'stock'];
            let isValid = true;

            for (const field of requiredFields) {
                const input = form.querySelector(`[name="${field}"]`);
                if (!input.value.trim()) {
                    input.classList.add('is-invalid');
                    isValid = false;
                } else {
                    input.classList.remove('is-invalid');
                }
            }

            // 验证主图
            const mainImageFile = form.querySelector('[name="mainImageFile"]');
            if (!mainImageFile.files.length) {
                mainImageFile.classList.add('is-invalid');
                isValid = false;
            } else {
                mainImageFile.classList.remove('is-invalid');
            }

            if (!isValid) {
                alert('请填写所有必填字段并上传主图');
                return;
            }

            // 处理复选框值
            formData.set('status', document.getElementById('productStatus').checked ? '1' : '0');

            // 提交数据
            fetch('/admin/api/products', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('产品添加成功！');
                    bootstrap.Modal.getInstance(document.getElementById('addProductModal')).hide();
                    location.reload();
                } else {
                    alert('添加失败：' + (data.message || '未知错误'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('网络错误，请稍后重试');
            });
        });

        // 处理产品特点显示
        function initProductFeatures() {
            document.querySelectorAll('.features-container').forEach(container => {
                const features = container.getAttribute('data-features');
                if (features) {
                    try {
                        // 尝试解析JSON格式
                        const featuresArray = JSON.parse(features);
                        container.innerHTML = featuresArray.map(feature =>
                            `<span class="feature-tag">${feature}</span>`
                        ).join('');
                    } catch (e) {
                        // 如果不是JSON格式，按逗号分割
                        const featuresArray = features.split(',').map(f => f.trim()).filter(f => f);
                        container.innerHTML = featuresArray.map(feature =>
                            `<span class="feature-tag">${feature}</span>`
                        ).join('');
                    }
                }
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 页面加载完成，开始初始化...');

            // 初始化产品特点显示
            initProductFeatures();

            // 初始化图片预览功能
            initImagePreviews();

            // 调试：检查筛选按钮是否正确绑定
            console.log('📋 检查筛选按钮...');
            const filterButtons = document.querySelectorAll('.filter-tab');
            console.log('找到筛选按钮数量:', filterButtons.length);

            // 为每个筛选按钮添加额外的事件监听器作为备用
            filterButtons.forEach((button, index) => {
                console.log(`按钮 ${index}:`, button.textContent.trim());

                // 添加备用点击事件
                button.addEventListener('click', function(e) {
                    console.log('按钮被点击:', this.textContent.trim());

                    // 获取onclick属性中的状态值
                    const onclickAttr = this.getAttribute('onclick');
                    if (onclickAttr) {
                        const match = onclickAttr.match(/filterProducts\('([^']*)'\)/);
                        if (match) {
                            const status = match[1];
                            console.log('提取的状态值:', status);
                            filterProducts(status);
                        }
                    }
                });
            });

            console.log('✅ 页面初始化完成');
        });
    </script>
</body>
</html>