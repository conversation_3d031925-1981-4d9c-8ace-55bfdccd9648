<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订单管理 - 成都桩郎中新能源技术有限公司</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        /* 采用小程序端配色方案 */
        :root {
            --primary-color: #1e88e5;
            --secondary-color: #64b5f6;
            --accent-color: #0d47a1;
            --text-color: #333333;
            --light-text: #757575;
            --background-color: #f5f5f5;
            --white: #ffffff;
            --border-color: #e0e0e0;
            --success-color: #4caf50;
            --warning-color: #ff9800;
            --error-color: #f44336;
            --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.05);
            --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.08);
            --shadow-heavy: 0 8px 24px rgba(0, 0, 0, 0.12);
        }

        body {
            background-color: var(--background-color);
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            color: var(--text-color);
            line-height: 1.5;
        }

        .sidebar {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
            min-height: 100vh;
            box-shadow: var(--shadow-medium);
        }

        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 1rem 1.5rem;
            border-radius: 8px;
            margin: 0.25rem 1rem;
            transition: all 0.3s ease;
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background-color: rgba(255,255,255,0.2);
            color: white;
            transform: translateX(5px);
        }

        .main-content {
            padding: 2rem;
        }

        .user-info {
            background: rgba(255,255,255,0.15);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 1.5rem 1rem;
            margin-bottom: 1.5rem;
            text-align: center;
            border: 1px solid rgba(255,255,255,0.2);
            box-shadow: var(--shadow-light);
        }

        .user-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: var(--white);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            color: var(--primary-color);
            font-size: 1.5rem;
            box-shadow: var(--shadow-light);
            position: relative;
        }

        .user-avatar::after {
            content: '';
            position: absolute;
            inset: -2px;
            border-radius: 50%;
            padding: 2px;
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            mask-composite: exclude;
        }

        .user-info h6 {
            color: var(--white) !important;
            font-weight: 600;
            margin-bottom: 0.5rem !important;
            font-size: 1rem;
        }

        .user-info small {
            color: rgba(255,255,255,0.8) !important;
            font-size: 0.85rem;
            font-weight: 500;
        }

        .order-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .order-card:hover {
            transform: translateY(-2px);
        }

        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
        }

        .status-pending {
            background-color: #fff3cd;
            color: #856404;
        }

        .status-accepted {
            background-color: #d1ecf1;
            color: #0c5460;
        }

        .status-processing {
            background-color: #f8d7da;
            color: #721c24;
        }

        .status-completed {
            background-color: #d4edda;
            color: #155724;
        }

        .action-btn {
            padding: 0.5rem 1rem;
            border-radius: 8px;
            border: none;
            font-size: 0.85rem;
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
            transition: all 0.3s ease;
        }

        .btn-assign {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-next {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            color: #333;
        }

        .btn-complete {
            background: linear-gradient(135deg, #a8e6cf 0%, #dcedc1 100%);
            color: #333;
        }

        .filter-tabs {
            background: white;
            border-radius: 15px;
            padding: 1rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .filter-tab {
            padding: 0.75rem 1.5rem;
            border-radius: 25px;
            border: none;
            background: transparent;
            color: #666;
            margin-right: 1rem;
            transition: all 0.3s ease;
        }

        .filter-tab.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .order-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .info-item {
            display: flex;
            flex-direction: column;
        }

        .info-label {
            font-size: 0.85rem;
            color: #666;
            margin-bottom: 0.25rem;
        }

        .info-value {
            font-weight: 600;
            color: #333;
        }

        /* 工程师卡片样式 */
        .engineer-card {
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .engineer-card:hover {
            border-color: #667eea;
            transform: translateY(-2px);
        }

        .engineer-card.selected {
            border-color: #667eea;
            background-color: #f8f9ff;
        }

        .engineer-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
        }

        /* 耗材表格样式 */
        #materialsTable input {
            border: none;
            background: transparent;
            padding: 0.5rem;
        }

        #materialsTable input:focus {
            background: #f8f9fa;
            border: 1px solid #667eea;
            border-radius: 4px;
        }

        #materialsTable .subtotal-input {
            background: #f8f9fa !important;
            font-weight: 600;
        }

        /* 故障图片样式 */
        .fault-images {
            margin-top: 10px;
        }

        .image-gallery {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .fault-image {
            width: 120px;
            height: 120px;
            border-radius: 8px;
            border: 2px solid #e0e0e0;
            object-fit: cover;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .fault-image:hover {
            border-color: #667eea;
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        /* 图片预览模态框 */
        .image-preview-modal {
            display: none;
            position: fixed;
            z-index: 9999;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.8);
            cursor: pointer;
        }

        .image-preview-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            max-width: 90%;
            max-height: 90%;
        }

        .image-preview-img {
            width: 100%;
            height: auto;
            border-radius: 8px;
        }

        .image-preview-close {
            position: absolute;
            top: 15px;
            right: 25px;
            color: white;
            font-size: 35px;
            font-weight: bold;
            cursor: pointer;
        }

        .image-preview-close:hover {
            opacity: 0.7;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="user-info">
                        <div class="user-avatar">
                            <i class="fas fa-user-shield"></i>
                        </div>
                        <h6 th:text="${admin.realName ?: '超级管理员'}">超级管理员</h6>
                        <small th:text="${admin.role ?: 'super_admin'}">super_admin</small>
                    </div>

                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/index">
                                <i class="bi bi-house-door-fill me-2"></i>
                                仪表盘
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/engineers">
                                <i class="bi bi-people-fill me-2"></i>
                                工程师管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/service-centers">
                                <i class="bi bi-building me-2"></i>
                                服务网点管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/products">
                                <i class="bi bi-box-seam me-2"></i>
                                产品管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="/admin/orders">
                                <i class="fas fa-clipboard-list me-2"></i>
                                订单管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/customer-service">
                                <i class="fas fa-comments me-2"></i>
                                在线客服
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/logout">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                退出登录
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <!-- 顶部导航 -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-4 border-bottom">
                    <h1 class="h2" >
                        <i class="fas fa-clipboard-list me-2" style="color: var(--primary-color);"></i>
                        订单管理
                    </h1>
<!--                    <div class="btn-toolbar mb-2 mb-md-0">-->
<!--                        <div class="btn-group me-2">-->
<!--                            <button type="button" class="btn btn-sm" onclick="location.reload()">-->
<!--                                <i class="fas fa-sync-alt me-1"></i> 刷新-->
<!--                            </button>-->
<!--                        </div>-->
<!--                    </div>-->
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="location.reload()">
                                <i class="bi bi-arrow-clockwise"></i> 刷新
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 状态筛选 -->
                <div class="filter-tabs">
                    <div class="d-flex flex-wrap">
                        <button class="filter-tab" th:classappend="${currentStatus == '' ? 'active' : ''}"
                                onclick="filterOrders('')">
                            <i class="bi bi-list-ul me-1"></i>全部订单
                        </button>
                        <button class="filter-tab" th:classappend="${currentStatus == 'pending' ? 'active' : ''}"
                                onclick="filterOrders('pending')">
                            <i class="bi bi-hourglass-split me-1"></i>待接单
                        </button>
                        <button class="filter-tab" th:classappend="${currentStatus == 'accepted' ? 'active' : ''}"
                                onclick="filterOrders('accepted')">
                            <i class="bi bi-person-check me-1"></i>待上门
                        </button>
                        <button class="filter-tab" th:classappend="${currentStatus == 'processing' ? 'active' : ''}"
                                onclick="filterOrders('processing')">
                            <i class="bi bi-tools me-1"></i>维修中
                        </button>
                        <button class="filter-tab" th:classappend="${currentStatus == 'completed' ? 'active' : ''}"
                                onclick="filterOrders('completed')">
                            <i class="bi bi-check-circle me-1"></i>已完成
                        </button>
                    </div>
                </div>

                <!-- 订单列表 -->
                <div class="row">
                    <div class="col-12" th:if="${orderPage.records.size() == 0}">
                        <div class="text-center py-5">
                            <i class="bi bi-inbox display-1 text-muted"></i>
                            <h4 class="text-muted mt-3">暂无订单数据</h4>
                            <p class="text-muted">当前筛选条件下没有找到订单</p>
                        </div>
                    </div>

                    <div class="col-12" th:each="order : ${orderPage.records}">
                        <div class="order-card">
                            <div class="d-flex justify-content-between align-items-start mb-3">
                                <div>
                                    <h5 class="mb-1" th:text="${order.orderNo}">订单编号</h5>
                                    <small class="text-muted" th:text="${#temporals.format(order.createdAt, 'yyyy-MM-dd HH:mm')}">创建时间</small>
                                </div>
                                <span class="status-badge" th:classappend="'status-' + ${order.status}"
                                      th:text="${order.status == 'pending' ? '待接单' :
                                               (order.status == 'accepted' ? '待上门' :
                                               (order.status == 'processing' ? '维修中' : '已完成'))}">
                                    状态
                                </span>
                            </div>

                            <div class="order-info">
                                <div class="info-item">
                                    <span class="info-label">故障类型</span>
                                    <span class="info-value fault-type" th:data-fault-type="${order.faultType}" th:text="${order.faultType}">故障类型</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">设备型号</span>
                                    <span class="info-value" th:text="${order.model}">设备型号</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">联系人</span>
                                    <span class="info-value" th:text="${order.name}">联系人</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">联系电话</span>
                                    <span class="info-value" th:text="${order.phone}">联系电话</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">预约时间</span>
                                    <span class="info-value" th:text="${order.appointmentDate + ' ' + order.appointmentTime}">预约时间</span>
                                </div>
                                <div class="info-item" th:if="${order.engineerName}">
                                    <span class="info-label">分配工程师</span>
                                    <span class="info-value" th:text="${order.engineerName}">工程师</span>
                                </div>
                            </div>

                            <div class="mb-3">
                                <span class="info-label">服务地址</span>
                                <p class="info-value mb-0" th:text="${order.fullAddress}">服务地址</p>
                            </div>

                            <div class="mb-3">
                                <span class="info-label">故障描述</span>
                                <p class="info-value mb-0" th:text="${order.description}">故障描述</p>
                            </div>

                            <!-- 故障图片 -->
                            <div class="mb-3" th:if="${order.images != null and !#strings.isEmpty(order.images)}">
                                <span class="info-label">故障图片</span>
                                <div class="fault-images mt-2">
                                    <div class="image-gallery" th:attr="data-images=${order.images}">
                                        <!-- 图片将通过JavaScript动态加载 -->
                                    </div>
                                </div>
                            </div>

                            <!-- 操作按钮 -->
                            <div class="d-flex flex-wrap">
                                <!-- 待接单状态：可以分配工程师 -->
                                <button th:if="${order.status == 'pending'}"
                                        class="action-btn btn-assign"
                                        onclick="showAssignModal(this)"
                                        th:data-order-id="${order.id}">
                                    <i class="bi bi-person-plus me-1"></i>分配工程师
                                </button>

                                <!-- 待上门状态：可以开始维修 -->
                                <button th:if="${order.status == 'accepted'}"
                                        class="action-btn btn-next"
                                        onclick="updateStatus(this, 'processing')"
                                        th:data-order-id="${order.id}">
                                    <i class="bi bi-tools me-1"></i>开始维修
                                </button>

                                <!-- 维修中状态：可以完成订单 -->
                                <button th:if="${order.status == 'processing'}"
                                        class="action-btn btn-complete"
                                        onclick="showCompleteModal(this)"
                                        th:data-order-id="${order.id}">
                                    <i class="bi bi-check-circle me-1"></i>完成订单
                                </button>

                                <button class="action-btn btn-outline-secondary"
                                        onclick="viewOrderDetail(this)"
                                        th:data-order-id="${order.id}">
                                    <i class="bi bi-eye me-1"></i>查看详情
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 分页 -->
                <nav th:if="${orderPage.pages > 1}" aria-label="订单分页">
                    <ul class="pagination justify-content-center">
                        <li class="page-item" th:classappend="${orderPage.current == 1 ? 'disabled' : ''}">
                            <a class="page-link" th:href="@{/admin/orders(page=${orderPage.current - 1}, status=${currentStatus})}">上一页</a>
                        </li>

                        <li th:each="i : ${#numbers.sequence(1, orderPage.pages)}"
                            class="page-item"
                            th:classappend="${i == orderPage.current ? 'active' : ''}">
                            <a class="page-link" th:href="@{/admin/orders(page=${i}, status=${currentStatus})}" th:text="${i}">1</a>
                        </li>

                        <li class="page-item" th:classappend="${orderPage.current == orderPage.pages ? 'disabled' : ''}">
                            <a class="page-link" th:href="@{/admin/orders(page=${orderPage.current + 1}, status=${currentStatus})}">下一页</a>
                        </li>
                    </ul>
                </nav>
            </main>
        </div>
    </div>

    <!-- 分配工程师模态框 -->
    <div class="modal fade" id="assignEngineerModal" tabindex="-1" aria-labelledby="assignEngineerModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="assignEngineerModalLabel">
                        <i class="bi bi-person-plus me-2"></i>分配工程师
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">选择工程师：</label>
                        <div id="engineerList" class="row">
                            <!-- 工程师列表将通过JavaScript动态加载 -->
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="confirmAssignBtn" disabled>确认分配</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 完成订单模态框 -->
    <div class="modal fade" id="completeOrderModal" tabindex="-1" aria-labelledby="completeOrderModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="completeOrderModalLabel">
                        <i class="bi bi-check-circle me-2"></i>完成订单
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="completeOrderForm">
                        <!-- 维修情况 -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="border-bottom pb-2 mb-3">
                                    <i class="bi bi-tools me-2"></i>维修情况
                                </h6>
                            </div>
                            <div class="col-md-6">
                                <label for="repairResult" class="form-label">维修结果</label>
                                <select class="form-select" id="repairResult" required>
                                    <option value="">请选择维修结果</option>
                                    <option value="success">维修成功</option>
                                    <option value="partial">部分修复</option>
                                    <option value="failed">维修失败</option>
                                    <option value="replacement">需要更换设备</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="repairTime" class="form-label">维修耗时（小时）</label>
                                <input type="number" class="form-control" id="repairTime" step="0.5" min="0" required>
                            </div>
                            <div class="col-12 mt-3">
                                <label for="repairDescription" class="form-label">维修详情</label>
                                <textarea class="form-control" id="repairDescription" rows="3" placeholder="请详细描述维修过程和解决方案" required></textarea>
                            </div>
                        </div>

                        <!-- 耗材明细 -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="border-bottom pb-2 mb-3">
                                    <i class="bi bi-box me-2"></i>耗材明细
                                </h6>
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <span>耗材列表</span>
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="addMaterialRow()">
                                        <i class="bi bi-plus"></i> 添加耗材
                                    </button>
                                </div>
                                <div class="table-responsive">
                                    <table class="table table-bordered" id="materialsTable">
                                        <thead class="table-light">
                                            <tr>
                                                <th width="30%">耗材名称</th>
                                                <th width="15%">规格型号</th>
                                                <th width="10%">数量</th>
                                                <th width="15%">单价（元）</th>
                                                <th width="15%">小计（元）</th>
                                                <th width="15%">操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="materialsTableBody">
                                            <!-- 耗材行将通过JavaScript动态添加 -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- 费用明细 -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="border-bottom pb-2 mb-3">
                                    <i class="bi bi-calculator me-2"></i>费用明细
                                </h6>
                            </div>
                            <div class="col-md-4">
                                <label for="laborFee" class="form-label">人工费（元）</label>
                                <input type="number" class="form-control" id="laborFee" step="0.01" min="0" required>
                            </div>
                            <div class="col-md-4">
                                <label for="serviceFee" class="form-label">服务费（元）</label>
                                <input type="number" class="form-control" id="serviceFee" step="0.01" min="0" value="0">
                            </div>
                            <div class="col-md-4">
                                <label for="totalMaterialsFee" class="form-label">耗材总费用（元）</label>
                                <input type="number" class="form-control" id="totalMaterialsFee" step="0.01" readonly>
                            </div>
                            <div class="col-12 mt-3">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-8">
                                                <h6 class="mb-1">费用汇总</h6>
                                                <small class="text-muted">人工费 + 服务费 + 耗材费用</small>
                                            </div>
                                            <div class="col-md-4 text-end">
                                                <h4 class="text-primary mb-0">
                                                    ¥ <span id="totalAmount">0.00</span>
                                                </h4>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 备注 -->
                        <div class="row">
                            <div class="col-12">
                                <label for="completeRemark" class="form-label">完成备注</label>
                                <textarea class="form-control" id="completeRemark" rows="2" placeholder="其他说明或注意事项"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-success" id="confirmCompleteBtn">
                        <i class="bi bi-check-circle me-1"></i>完成订单
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 订单详情模态框 -->
    <div class="modal fade" id="orderDetailModal" tabindex="-1" aria-labelledby="orderDetailModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="orderDetailModalLabel">
                        <i class="bi bi-eye me-2"></i>订单详情
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="orderDetailContent">
                        <!-- 订单详情内容将通过JavaScript动态加载 -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary" id="printOrderBtn">
                        <i class="bi bi-printer me-1"></i>打印订单
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 图片预览模态框 -->
    <div id="imagePreviewModal" class="image-preview-modal" onclick="closeImagePreview()">
        <span class="image-preview-close" onclick="closeImagePreview()">&times;</span>
        <div class="image-preview-content">
            <img id="previewImage" class="image-preview-img" src="" alt="故障图片">
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 页面加载完成后初始化图片和故障类型转换
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 页面加载完成，开始初始化');
            initFaultImages();
            initFaultTypeTranslation();

            // 延迟再次执行，确保所有元素都已渲染
            setTimeout(function() {
                console.log('🔄 延迟执行故障类型转换');
                initFaultTypeTranslation();
            }, 500);
        });

        // 页面完全加载后再次执行
        window.addEventListener('load', function() {
            console.log('🎯 页面完全加载，再次执行故障类型转换');
            initFaultTypeTranslation();
        });

        // 故障类型映射表
        const faultTypeMap = {
            'no_charging': '无法充电',
            'slow_charging': '充电慢',
            'error_code': '报错代码',
            'port_damage': '接口损坏',
            'not_starting': '无法启动',
            'overheating': '过热',
            'display_issue': '显示故障',
            'other': '其他故障'
        };

        // 初始化故障类型转换
        function initFaultTypeTranslation() {
            console.log('🔧 开始初始化故障类型转换');

            // 转换列表中的故障类型
            const faultTypeElements = document.querySelectorAll('.fault-type');
            console.log('📋 找到故障类型元素数量:', faultTypeElements.length);

            faultTypeElements.forEach((element, index) => {
                const faultType = element.getAttribute('data-fault-type');
                const chineseName = faultTypeMap[faultType] || faultType;
                console.log(`🔄 转换${index}: ${faultType} -> ${chineseName}`);
                element.textContent = chineseName;
            });

            // 转换详情弹窗中的故障类型
            const faultTypeTextElements = document.querySelectorAll('.fault-type-text');
            console.log('📋 找到详情故障类型元素数量:', faultTypeTextElements.length);

            faultTypeTextElements.forEach((element, index) => {
                const faultType = element.getAttribute('data-fault-type');
                const chineseName = faultTypeMap[faultType] || faultType;
                console.log(`🔄 详情转换${index}: ${faultType} -> ${chineseName}`);
                element.textContent = chineseName;
            });
        }

        // 获取故障类型中文名称
        function getFaultTypeName(faultType) {
            return faultTypeMap[faultType] || faultType;
        }

        // 初始化故障图片
        function initFaultImages() {
            const imageGalleries = document.querySelectorAll('.image-gallery');
            console.log('🖼️ 找到图片容器数量:', imageGalleries.length);

            imageGalleries.forEach((gallery, index) => {
                const imagesData = gallery.getAttribute('data-images');
                console.log(`📋 容器${index} 图片数据:`, imagesData);

                if (imagesData) {
                    try {
                        const imageUrls = JSON.parse(imagesData);
                        console.log(`🔗 解析后的图片URLs:`, imageUrls);
                        renderFaultImages(gallery, imageUrls);
                    } catch (e) {
                        console.error('❌ 解析图片数据失败:', e);
                        console.error('原始数据:', imagesData);
                    }
                }
            });
        }

        // 渲染故障图片
        function renderFaultImages(gallery, imageUrls) {
            console.log('🎨 开始渲染图片，数量:', imageUrls.length);
            gallery.innerHTML = '';

            imageUrls.forEach((url, index) => {
                console.log(`📸 处理图片${index}:`, url);

                const img = document.createElement('img');
                // 处理图片URL，避免重复路径
                let imageSrc = url;
                if (url.startsWith('http')) {
                    // 完整URL，直接使用
                    imageSrc = url;
                    console.log(`🌐 完整URL:`, imageSrc);
                } else if (url.startsWith('/uploads/')) {
                    // 已经包含/uploads/前缀，直接使用
                    imageSrc = url;
                    console.log(`📁 已有前缀:`, imageSrc);
                } else {
                    // 只有文件名，添加/uploads/前缀
                    imageSrc = '/uploads/' + url;
                    console.log(`➕ 添加前缀:`, imageSrc);
                }

                img.src = imageSrc;
                img.className = 'fault-image';
                img.alt = `故障图片 ${index + 1}`;
                img.onclick = () => showImagePreview(img.src);

                // 添加加载事件监听
                img.onload = () => console.log(`✅ 图片${index}加载成功:`, imageSrc);
                img.onerror = () => console.error(`❌ 图片${index}加载失败:`, imageSrc);

                gallery.appendChild(img);
            });
        }

        // 显示图片预览
        function showImagePreview(imageSrc) {
            const modal = document.getElementById('imagePreviewModal');
            const previewImg = document.getElementById('previewImage');

            previewImg.src = imageSrc;
            modal.style.display = 'block';

            // 阻止事件冒泡
            event.stopPropagation();
        }

        // 关闭图片预览
        function closeImagePreview() {
            const modal = document.getElementById('imagePreviewModal');
            modal.style.display = 'none';
        }

        // 筛选订单
        function filterOrders(status) {
            const url = new URL(window.location);
            if (status) {
                url.searchParams.set('status', status);
            } else {
                url.searchParams.delete('status');
            }
            url.searchParams.set('page', '1');
            window.location.href = url.toString();
        }

        // 更新订单状态
        function updateStatus(button, newStatus) {
            const orderId = button.dataset.orderId;
            const statusText = {
                'accepted': '接单',
                'processing': '开始维修',
                'completed': '完成订单'
            };

            if (confirm(`确定要${statusText[newStatus]}吗？`)) {
                fetch(`/admin/api/orders/${orderId}/status`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `status=${newStatus}`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(data.message);
                        location.reload();
                    } else {
                        alert(data.message || '操作失败');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('网络错误，请稍后重试');
                });
            }
        }

        // 显示分配工程师模态框
        function showAssignModal(button) {
            const orderId = button.dataset.orderId;
            currentOrderId = orderId;

            // 加载可用工程师列表
            loadAvailableEngineers();

            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('assignEngineerModal'));
            modal.show();
        }

        // 全局变量
        let currentOrderId = null;
        let selectedEngineerId = null;
        let materialRowIndex = 0;

        // 加载可用工程师列表
        function loadAvailableEngineers() {
            fetch('/admin/api/engineers/available')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        renderEngineerList(data.data);
                    } else {
                        alert('加载工程师列表失败：' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('网络错误，请稍后重试');
                });
        }

        // 渲染工程师列表
        function renderEngineerList(engineers) {
            const engineerList = document.getElementById('engineerList');
            engineerList.innerHTML = '';

            if (engineers.length === 0) {
                engineerList.innerHTML = '<div class="col-12 text-center text-muted">暂无可用工程师</div>';
                return;
            }

            engineers.forEach(engineer => {
                const engineerCard = document.createElement('div');
                engineerCard.className = 'col-md-6 mb-3';
                engineerCard.innerHTML = `
                    <div class="card engineer-card" data-engineer-id="${engineer.id}" onclick="selectEngineer(this)">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <div class="engineer-avatar">
                                        <i class="bi bi-person-fill"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="card-title mb-1">${engineer.name}</h6>
                                    <p class="card-text mb-1">
                                        <small class="text-muted">
                                            <i class="bi bi-telephone me-1"></i>${engineer.phone}
                                        </small>
                                    </p>
                                    <p class="card-text mb-1">
                                        <small class="text-muted">
                                            <i class="bi bi-tools me-1"></i>${engineer.specialties || '通用维修'}
                                        </small>
                                    </p>
                                    <div class="d-flex align-items-center">
                                        <span class="badge bg-success me-2">
                                            <i class="bi bi-star-fill me-1"></i>${engineer.rating || '5.0'}
                                        </span>
                                        <small class="text-muted">${engineer.completedOrders || 0}单完成</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                engineerList.appendChild(engineerCard);
            });
        }

        // 选择工程师
        function selectEngineer(card) {
            // 移除其他选中状态
            document.querySelectorAll('.engineer-card').forEach(c => c.classList.remove('selected'));

            // 添加选中状态
            card.classList.add('selected');
            selectedEngineerId = card.dataset.engineerId;

            // 启用确认按钮
            document.getElementById('confirmAssignBtn').disabled = false;
        }

        // 确认分配工程师
        document.getElementById('confirmAssignBtn').addEventListener('click', function() {
            if (selectedEngineerId && currentOrderId) {
                assignEngineer(currentOrderId, selectedEngineerId);
            }
        });

        // 分配工程师
        function assignEngineer(orderId, engineerId) {
            fetch(`/admin/api/orders/${orderId}/assign`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `engineerId=${engineerId}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.message);
                    location.reload();
                } else {
                    alert(data.message || '分配失败');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('网络错误，请稍后重试');
            });
        }

        // 显示完成订单模态框
        function showCompleteModal(button) {
            const orderId = button.dataset.orderId;
            currentOrderId = orderId;

            // 重置表单
            document.getElementById('completeOrderForm').reset();
            document.getElementById('materialsTableBody').innerHTML = '';
            materialRowIndex = 0;

            // 添加一行默认耗材
            addMaterialRow();

            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('completeOrderModal'));
            modal.show();
        }

        // 添加耗材行
        function addMaterialRow() {
            const tbody = document.getElementById('materialsTableBody');
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>
                    <input type="text" class="form-control" name="materialName_${materialRowIndex}" placeholder="请输入耗材名称">
                </td>
                <td>
                    <input type="text" class="form-control" name="materialSpec_${materialRowIndex}" placeholder="规格型号">
                </td>
                <td>
                    <input type="number" class="form-control quantity-input" name="materialQuantity_${materialRowIndex}"
                           min="1" value="1" onchange="calculateRowTotal(this)">
                </td>
                <td>
                    <input type="number" class="form-control price-input" name="materialPrice_${materialRowIndex}"
                           step="0.01" min="0" onchange="calculateRowTotal(this)">
                </td>
                <td>
                    <input type="number" class="form-control subtotal-input" name="materialSubtotal_${materialRowIndex}"
                           step="0.01" readonly>
                </td>
                <td>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeMaterialRow(this)">
                        <i class="bi bi-trash"></i>
                    </button>
                </td>
            `;
            tbody.appendChild(row);
            materialRowIndex++;
        }

        // 删除耗材行
        function removeMaterialRow(button) {
            const row = button.closest('tr');
            row.remove();
            calculateTotalMaterials();
        }

        // 计算单行小计
        function calculateRowTotal(input) {
            const row = input.closest('tr');
            const quantity = parseFloat(row.querySelector('.quantity-input').value) || 0;
            const price = parseFloat(row.querySelector('.price-input').value) || 0;
            const subtotal = quantity * price;

            row.querySelector('.subtotal-input').value = subtotal.toFixed(2);
            calculateTotalMaterials();
        }

        // 计算耗材总费用
        function calculateTotalMaterials() {
            const subtotalInputs = document.querySelectorAll('.subtotal-input');
            let total = 0;

            subtotalInputs.forEach(input => {
                total += parseFloat(input.value) || 0;
            });

            document.getElementById('totalMaterialsFee').value = total.toFixed(2);
            calculateTotalAmount();
        }

        // 计算总费用
        function calculateTotalAmount() {
            const laborFee = parseFloat(document.getElementById('laborFee').value) || 0;
            const serviceFee = parseFloat(document.getElementById('serviceFee').value) || 0;
            const materialsFee = parseFloat(document.getElementById('totalMaterialsFee').value) || 0;

            const total = laborFee + serviceFee + materialsFee;
            document.getElementById('totalAmount').textContent = total.toFixed(2);
        }

        // 监听费用输入变化
        document.getElementById('laborFee').addEventListener('input', calculateTotalAmount);
        document.getElementById('serviceFee').addEventListener('input', calculateTotalAmount);

        // 确认完成订单
        document.getElementById('confirmCompleteBtn').addEventListener('click', function() {
            if (validateCompleteForm()) {
                submitCompleteOrder();
            }
        });

        // 验证完成订单表单
        function validateCompleteForm() {
            const form = document.getElementById('completeOrderForm');
            const requiredFields = form.querySelectorAll('[required]');

            for (let field of requiredFields) {
                if (!field.value.trim()) {
                    alert('请填写所有必填项');
                    field.focus();
                    return false;
                }
            }

            return true;
        }

        // 提交完成订单
        function submitCompleteOrder() {
            const formData = collectCompleteOrderData();

            fetch(`/admin/api/orders/${currentOrderId}/complete`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('订单完成成功！');
                    bootstrap.Modal.getInstance(document.getElementById('completeOrderModal')).hide();
                    location.reload();
                } else {
                    alert(data.message || '操作失败');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('网络错误，请稍后重试');
            });
        }

        // 收集完成订单数据
        function collectCompleteOrderData() {
            const materials = [];
            const rows = document.querySelectorAll('#materialsTableBody tr');

            rows.forEach((row, index) => {
                const name = row.querySelector(`[name^="materialName_"]`).value;
                const spec = row.querySelector(`[name^="materialSpec_"]`).value;
                const quantity = row.querySelector(`[name^="materialQuantity_"]`).value;
                const price = row.querySelector(`[name^="materialPrice_"]`).value;
                const subtotal = row.querySelector(`[name^="materialSubtotal_"]`).value;

                if (name.trim()) {
                    materials.push({
                        name: name,
                        specification: spec,
                        quantity: parseInt(quantity),
                        unitPrice: parseFloat(price),
                        subtotal: parseFloat(subtotal)
                    });
                }
            });

            return {
                repairResult: document.getElementById('repairResult').value,
                repairTime: parseFloat(document.getElementById('repairTime').value),
                repairDescription: document.getElementById('repairDescription').value,
                materials: materials,
                laborFee: parseFloat(document.getElementById('laborFee').value),
                serviceFee: parseFloat(document.getElementById('serviceFee').value),
                materialsFee: parseFloat(document.getElementById('totalMaterialsFee').value),
                totalAmount: parseFloat(document.getElementById('totalAmount').textContent),
                remark: document.getElementById('completeRemark').value
            };
        }

        // 带备注更新状态
        function updateStatusWithRemark(orderId, status, remark) {
            let body = `status=${status}`;
            if (remark) {
                body += `&remark=${encodeURIComponent(remark)}`;
            }

            fetch(`/admin/api/orders/${orderId}/status`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: body
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.message);
                    location.reload();
                } else {
                    alert(data.message || '操作失败');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('网络错误，请稍后重试');
            });
        }

        // 查看订单详情
        function viewOrderDetail(button) {
            const orderId = button.dataset.orderId;

            // 加载订单详情
            fetch(`/admin/api/orders/${orderId}/detail`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        renderOrderDetail(data.data);
                        const modal = new bootstrap.Modal(document.getElementById('orderDetailModal'));
                        modal.show();
                    } else {
                        alert('加载订单详情失败：' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('网络错误，请稍后重试');
                });
        }

        // 渲染订单详情
        function renderOrderDetail(order) {
            const content = document.getElementById('orderDetailContent');

            // 解析耗材明细
            let materialsHtml = '';
            if (order.materialsDetail) {
                try {
                    const materials = typeof order.materialsDetail === 'string'
                        ? JSON.parse(order.materialsDetail)
                        : order.materialsDetail;

                    if (materials.length > 0) {
                        materialsHtml = `
                            <div class="row mb-4">
                                <div class="col-12">
                                    <h6 class="border-bottom pb-2 mb-3">
                                        <i class="bi bi-box me-2"></i>耗材明细
                                    </h6>
                                    <div class="table-responsive">
                                        <table class="table table-bordered">
                                            <thead class="table-light">
                                                <tr>
                                                    <th>耗材名称</th>
                                                    <th>规格型号</th>
                                                    <th>数量</th>
                                                    <th>单价</th>
                                                    <th>小计</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                ${materials.map(material => `
                                                    <tr>
                                                        <td>${material.name || '-'}</td>
                                                        <td>${material.specification || '-'}</td>
                                                        <td>${material.quantity || 0}</td>
                                                        <td>¥${material.unitPrice || 0}</td>
                                                        <td>¥${material.subtotal || 0}</td>
                                                    </tr>
                                                `).join('')}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        `;
                    }
                } catch (e) {
                    console.error('解析耗材明细失败:', e);
                }
            }

            content.innerHTML = `
                <div class="row">
                    <!-- 基本信息 -->
                    <div class="col-md-6 mb-4">
                        <h6 class="border-bottom pb-2 mb-3">
                            <i class="bi bi-info-circle me-2"></i>基本信息
                        </h6>
                        <table class="table table-borderless">
                            <tr><td class="fw-bold">订单编号:</td><td>${order.orderNo || '-'}</td></tr>
                            <tr><td class="fw-bold">故障类型:</td><td><span class="fault-type-text" data-fault-type="${order.faultType}">${order.faultType || '-'}</span></td></tr>
                            <tr><td class="fw-bold">设备型号:</td><td>${order.model || '-'}</td></tr>
                            <tr><td class="fw-bold">服务类型:</td><td>${order.serviceType === 'home' ? '上门维修' : '远程指导'}</td></tr>
                            <tr><td class="fw-bold">订单状态:</td><td><span class="badge bg-${getStatusColor(order.status)}">${getStatusText(order.status)}</span></td></tr>
                            <tr><td class="fw-bold">创建时间:</td><td>${formatDateTime(order.createdAt)}</td></tr>
                            ${order.completedAt ? `<tr><td class="fw-bold">完成时间:</td><td>${formatDateTime(order.completedAt)}</td></tr>` : ''}
                        </table>
                    </div>

                    <!-- 联系信息 -->
                    <div class="col-md-6 mb-4">
                        <h6 class="border-bottom pb-2 mb-3">
                            <i class="bi bi-person me-2"></i>联系信息
                        </h6>
                        <table class="table table-borderless">
                            <tr><td class="fw-bold">联系人:</td><td>${order.name || '-'}</td></tr>
                            <tr><td class="fw-bold">联系电话:</td><td>${order.phone || '-'}</td></tr>
                            <tr><td class="fw-bold">服务地址:</td><td>${order.fullAddress || '-'}</td></tr>
                            <tr><td class="fw-bold">预约时间:</td><td>${order.appointmentDate || '-'} ${order.appointmentTime || ''}</td></tr>
                            ${order.engineerName ? `
                                <tr><td class="fw-bold">分配工程师:</td><td>${order.engineerName}</td></tr>
                                <tr><td class="fw-bold">工程师电话:</td><td>${order.engineerPhone || '-'}</td></tr>
                            ` : ''}
                        </table>
                    </div>
                </div>

                <!-- 故障描述 -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h6 class="border-bottom pb-2 mb-3">
                            <i class="bi bi-chat-text me-2"></i>故障描述
                        </h6>
                        <p class="bg-light p-3 rounded">${order.description || '无描述'}</p>
                    </div>
                </div>

                ${order.repairDescription ? `
                    <!-- 维修详情 -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="border-bottom pb-2 mb-3">
                                <i class="bi bi-tools me-2"></i>维修详情
                            </h6>
                            <table class="table table-borderless">
                                <tr><td class="fw-bold">维修结果:</td><td>${getRepairResultText(order.repairResult)}</td></tr>
                                <tr><td class="fw-bold">维修耗时:</td><td>${order.repairTime || 0}小时</td></tr>
                                <tr><td class="fw-bold">维修说明:</td><td>${order.repairDescription}</td></tr>
                            </table>
                        </div>
                    </div>
                ` : ''}

                ${materialsHtml}

                ${order.status === 'completed' ? `
                    <!-- 费用明细 -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="border-bottom pb-2 mb-3">
                                <i class="bi bi-calculator me-2"></i>费用明细
                            </h6>
                            <div class="card">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-8">
                                            <table class="table table-borderless mb-0">
                                                ${order.laborFee > 0 ? `<tr><td>人工费:</td><td class="text-end">¥${order.laborFee}</td></tr>` : ''}
                                                ${order.serviceFee > 0 ? `<tr><td>服务费:</td><td class="text-end">¥${order.serviceFee}</td></tr>` : ''}
                                                ${order.materialsFee > 0 ? `<tr><td>耗材费:</td><td class="text-end">¥${order.materialsFee}</td></tr>` : ''}
                                            </table>
                                        </div>
                                        <div class="col-md-4 text-end">
                                            <h4 class="text-primary">总费用: ¥${order.totalFee || '0.00'}</h4>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                ` : ''}

                ${order.remark ? `
                    <!-- 备注信息 -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="border-bottom pb-2 mb-3">
                                <i class="bi bi-sticky me-2"></i>备注信息
                            </h6>
                            <p class="bg-light p-3 rounded">${order.remark}</p>
                        </div>
                    </div>
                ` : ''}
            `;

            // 转换详情弹窗中的故障类型
            setTimeout(() => {
                const faultTypeTextElements = document.querySelectorAll('.fault-type-text');
                faultTypeTextElements.forEach((element) => {
                    const faultType = element.getAttribute('data-fault-type');
                    const chineseName = getFaultTypeName(faultType);
                    element.textContent = chineseName;
                });
            }, 100);
        }

        // 获取状态颜色
        function getStatusColor(status) {
            const colors = {
                'pending': 'warning',
                'accepted': 'info',
                'processing': 'primary',
                'completed': 'success'
            };
            return colors[status] || 'secondary';
        }

        // 获取状态文本
        function getStatusText(status) {
            const texts = {
                'pending': '待接单',
                'accepted': '待上门',
                'processing': '维修中',
                'completed': '已完成'
            };
            return texts[status] || status;
        }

        // 获取维修结果文本
        function getRepairResultText(result) {
            const texts = {
                'success': '维修成功',
                'partial': '部分修复',
                'failed': '维修失败',
                'replacement': '需要更换设备'
            };
            return texts[result] || '未知';
        }

        // 格式化日期时间
        function formatDateTime(dateTimeStr) {
            if (!dateTimeStr) return '-';
            const date = new Date(dateTimeStr);
            return date.toLocaleString('zh-CN');
        }

        // 打印订单
        document.getElementById('printOrderBtn').addEventListener('click', function() {
            window.print();
        });
    </script>
</body>
</html>
