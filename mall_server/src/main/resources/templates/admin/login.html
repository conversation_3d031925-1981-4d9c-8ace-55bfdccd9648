<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理后台登录 - 成都桩郎中新能源技术有限公司</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        /* 采用小程序端配色方案 */
        :root {
            --primary-color: #1e88e5;
            --secondary-color: #64b5f6;
            --accent-color: #0d47a1;
            --text-color: #333333;
            --light-text: #757575;
            --background-color: #f5f5f5;
            --white: #ffffff;
            --border-color: #e0e0e0;
            --success-color: #4caf50;
            --warning-color: #ff9800;
            --error-color: #f44336;
            --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.05);
            --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.08);
            --shadow-heavy: 0 8px 24px rgba(0, 0, 0, 0.12);
        }

        body {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            position: relative;
            overflow: hidden;
        }

        /* 背景装饰 */
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image:
                radial-gradient(circle at 20% 80%, rgba(255,255,255,0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255,255,255,0.1) 0%, transparent 50%);
            pointer-events: none;
        }
        
        .login-container {
            background: var(--white);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            box-shadow: var(--shadow-heavy);
            padding: 3rem;
            width: 100%;
            max-width: 480px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            z-index: 1;
        }

        .login-header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .company-logo {
            width: 100px;
            height: 100px;
            background: var(--white);
            border-radius: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            box-shadow: var(--shadow-heavy);
            padding: 0;
            position: relative;
            transition: all 0.3s ease;
        }


        .company-logo:hover {
            transform: scale(1.05);
            box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
        }

        .company-logo::before {
            content: '';
            position: absolute;
            inset: -3px;
            border-radius: 27px;
            padding: 3px;
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color), var(--primary-color));
            mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            mask-composite: exclude;
            opacity: 0.6;
        }

        .company-logo img {
            width: 100%;
            height: 100%;
            object-fit: contain;
            position: relative;
            z-index: 1;
        }

        .login-header h1 {
            color: var(--text-color);
            font-weight: 600;
            margin-bottom: 0.5rem;
            font-size: 1.5rem;
        }

        .login-header p {
            color: var(--light-text);
            margin: 0;
            font-size: 0.9rem;
        }

        .company-name {
            color: var(--primary-color);
            font-weight: 600;
            font-size: 0.85rem;
            margin-top: 0.5rem;
        }
        
        .input-group {
            margin-bottom: 1.5rem;
        }

        .form-control {
            border: 2px solid var(--border-color);
            border-radius: 16px;
            padding: 1rem 1.25rem;
            transition: all 0.3s ease;
            font-size: 0.95rem;
            background: var(--white);
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(30, 136, 229, 0.15);
            background: var(--white);
        }

        .input-group-text {
            background: var(--white);
            border: 2px solid var(--border-color);
            border-right: none;
            border-radius: 16px 0 0 16px;
            padding: 1rem 1.25rem;
            color: var(--light-text);
        }

        .input-group .form-control {
            border-left: none;
            border-radius: 0 16px 16px 0;
        }

        .input-group:focus-within .input-group-text {
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        .btn-login {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
            border: none;
            border-radius: 16px;
            padding: 1rem 2rem;
            font-weight: 600;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            width: 100%;
            position: relative;
            overflow: hidden;
        }

        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }

        .btn-login:active {
            transform: translateY(0);
        }
        
        .alert {
            border-radius: 16px;
            border: none;
            font-size: 0.9rem;
        }

        .loading {
            display: none;
        }

        .login-footer {
            text-align: center;
            margin-top: 2rem;
            padding-top: 1.5rem;
            border-top: 1px solid var(--border-color);
        }

        .login-footer small {
            color: var(--light-text);
            font-size: 0.8rem;
        }

        .login-footer .badge {
            background: var(--background-color);
            color: var(--text-color);
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 12px;
            margin-top: 0.5rem;
            display: inline-block;
        }

        /* 响应式设计 */
        @media (max-width: 576px) {
            .login-container {
                margin: 1rem;
                padding: 2rem;
                max-width: none;
            }

            .company-logo {
                width: 100px;
                height: 100px;
                padding: 4px;
            }

            .login-header h1 {
                font-size: 1.25rem;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <div class="company-logo">
                <img src="/image/logo/zlz.svg" alt="成都桩郎中新能源技术有限公司" />
            </div>
            <h1>管理后台</h1>
            <p>充电桩维修服务管理平台</p>
            <div class="company-name">成都桩郎中新能源技术有限公司</div>
        </div>
        
        <div id="alertContainer"></div>
        
        <form id="loginForm">
            <div class="input-group">
                <span class="input-group-text">
                    <i class="fas fa-user"></i>
                </span>
                <input type="text" class="form-control" id="username" name="username" placeholder="请输入用户名" required>
            </div>

            <div class="input-group">
                <span class="input-group-text">
                    <i class="fas fa-lock"></i>
                </span>
                <input type="password" class="form-control" id="password" name="password" placeholder="请输入密码" required>
            </div>

            <button type="submit" class="btn btn-primary btn-login">
                <span class="login-text">
                    <i class="fas fa-sign-in-alt me-2"></i>立即登录
                </span>
                <span class="loading">
                    <i class="fas fa-spinner fa-spin me-2"></i>登录中...
                </span>
            </button>
        </form>
        
        <div class="login-footer">
            <small>© 2024 成都桩郎中新能源技术有限公司</small>
            <div class="badge">
                <i class="fas fa-shield-alt me-1"></i>安全登录
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const alertContainer = document.getElementById('alertContainer');
            const loginText = document.querySelector('.login-text');
            const loading = document.querySelector('.loading');
            
            if (!username || !password) {
                showAlert('请输入用户名和密码', 'danger');
                return;
            }
            
            // 显示加载状态
            loginText.style.display = 'none';
            loading.style.display = 'inline';
            
            // 发送登录请求
            fetch('/admin/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `username=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('登录成功，正在跳转...', 'success');
                    setTimeout(() => {
                        window.location.href = '/admin/index';
                    }, 1000);
                } else {
                    showAlert(data.message || '登录失败', 'danger');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('网络错误，请稍后重试', 'danger');
            })
            .finally(() => {
                // 恢复按钮状态
                loginText.style.display = 'inline';
                loading.style.display = 'none';
            });
        });
        
        function showAlert(message, type) {
            const alertContainer = document.getElementById('alertContainer');
            alertContainer.innerHTML = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
        }
        
        // 添加旋转动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
            .spin {
                animation: spin 1s linear infinite;
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
