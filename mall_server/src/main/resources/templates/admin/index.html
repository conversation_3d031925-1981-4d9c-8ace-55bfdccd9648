<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理后台 - 成都桩郎中新能源技术有限公司</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        /* 采用小程序端配色方案 */
        :root {
            --primary-color: #1e88e5;
            --secondary-color: #64b5f6;
            --accent-color: #0d47a1;
            --text-color: #333333;
            --light-text: #757575;
            --background-color: #f5f5f5;
            --white: #ffffff;
            --border-color: #e0e0e0;
            --success-color: #4caf50;
            --warning-color: #ff9800;
            --error-color: #f44336;
            --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.05);
            --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.08);
            --shadow-heavy: 0 8px 24px rgba(0, 0, 0, 0.12);
        }

        body {
            background-color: var(--background-color);
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            color: var(--text-color);
            line-height: 1.5;
        }

        .sidebar {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
            min-height: 100vh;
            box-shadow: var(--shadow-medium);
        }

        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 1rem 1.5rem;
            border-radius: 8px;
            margin: 0.25rem 1rem;
            transition: all 0.3s ease;
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background-color: rgba(255,255,255,0.2);
            color: white;
            transform: translateX(5px);
        }

        .main-content {
            padding: 2rem;
        }

        .stats-card {
            background: var(--white);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: var(--shadow-light);
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
            height: 100%;
            position: relative;
            overflow: hidden;
        }

        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
        }

        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-heavy);
        }

        .stats-icon {
            width: 70px;
            height: 70px;
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.8rem;
            color: var(--white);
            margin-bottom: 1.5rem;
            position: relative;
            box-shadow: var(--shadow-light);
        }

        .stats-icon::after {
            content: '';
            position: absolute;
            inset: -2px;
            border-radius: 20px;
            padding: 2px;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.3), transparent);
            mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            mask-composite: exclude;
        }

        .stats-number {
            font-size: 2.5rem;
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: 0.5rem;
        }

        .stats-label {
            color: var(--light-text);
            font-size: 0.9rem;
            margin: 0;
            font-weight: 500;
        }

        .navbar-brand {
            font-weight: 600;
            color: var(--text-color) !important;
        }

        .user-info {
            background: rgba(255,255,255,0.15);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 1.5rem 1rem;
            margin-bottom: 1.5rem;
            text-align: center;
            border: 1px solid rgba(255,255,255,0.2);
            box-shadow: var(--shadow-light);
        }

        .user-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: var(--white);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            color: var(--primary-color);
            font-size: 1.5rem;
            box-shadow: var(--shadow-light);
            position: relative;
        }

        .user-avatar::after {
            content: '';
            position: absolute;
            inset: -2px;
            border-radius: 50%;
            padding: 2px;
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            mask-composite: exclude;
        }

        .user-info h6 {
            color: var(--white) !important;
            font-weight: 600;
            margin-bottom: 0.5rem !important;
            font-size: 1rem;
        }

        .user-info small {
            color: rgba(255,255,255,0.8) !important;
            font-size: 0.85rem;
            font-weight: 500;
        }

        .quick-actions {
            background: var(--white);
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: var(--shadow-light);
            border: 1px solid var(--border-color);
            margin-top: 2rem;
        }

        .action-btn {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
            border: none;
            border-radius: 8px;
            padding: 1rem;
            color: var(--white);
            text-decoration: none;
            display: block;
            text-align: center;
            transition: all 0.3s ease;
            margin-bottom: 1rem;
            font-weight: 500;
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
            color: var(--white);
        }

        .company-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
            color: var(--white);
            padding: 2.5rem 2rem;
            border-radius: 24px;
            margin-bottom: 3rem;
            text-align: center;
            box-shadow: var(--shadow-medium);
            position: relative;
            overflow: hidden;
        }

        .company-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 20%, rgba(255,255,255,0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(255,255,255,0.1) 0%, transparent 50%);
            pointer-events: none;
        }

        .company-header .content {
            position: relative;
            z-index: 1;
        }

        .company-logo-header {
            width: 120px;
            height: 120px;
            background: var(--white);
            border-radius: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.3);
            padding: 4px;
            position: relative;
            transition: all 0.3s ease;
            box-shadow: var(--shadow-medium);
        }

        .company-logo-header:hover {
            transform: scale(1.05);
            box-shadow: var(--shadow-heavy);
        }

        .company-logo-header::before {
            content: '';
            position: absolute;
            inset: -3px;
            border-radius: 27px;
            padding: 3px;
            background: linear-gradient(45deg, rgba(255,255,255,0.8), rgba(255,255,255,0.4), rgba(255,255,255,0.8));
            mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            mask-composite: exclude;
            opacity: 0.8;
        }

        .company-logo-header img {
            width: 100%;
            height: 100%;
            object-fit: contain;
            position: relative;
            z-index: 1;
        }

        .company-name {
            font-size: 1.5rem;
            font-weight: 600;
            margin: 0 0 0.5rem 0;
            letter-spacing: 0.5px;
        }

        .company-subtitle {
            font-size: 1rem;
            opacity: 0.9;
            margin: 0;
            font-weight: 400;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="user-info">
                        <div class="user-avatar">
                            <i class="fas fa-user-shield"></i>
                        </div>
                        <h6 th:text="${admin.realName ?: '超级管理员'}">超级管理员</h6>
                        <small th:text="${admin.role ?: 'super_admin'}">super_admin</small>
                    </div>

                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="/admin/index">
                                <i class="bi bi-house-door-fill me-2"></i>
                                仪表盘
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/engineers">
                                <i class="bi bi-people-fill me-2"></i>
                                工程师管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/service-centers">
                                <i class="bi bi-building me-2"></i>
                                服务网点管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/products">
                                <i class="bi bi-box-seam me-2"></i>
                                产品管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/orders">
                                <i class="fas fa-clipboard-list me-2"></i>
                                订单管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/customer-service">
                                <i class="fas fa-comments me-2"></i>
                                在线客服
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/logout">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                退出登录
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <!-- 公司信息头部 -->
                <div class="company-header">
                    <div class="content">
                        <div class="company-logo-header">
                            <img src="/image/logo/zlz.svg" alt="成都桩郎中新能源技术有限公司" />
                        </div>
                        <h1 class="company-name">成都桩郎中新能源技术有限公司</h1>
                        <p class="company-subtitle">充电桩维修服务管理平台</p>
                    </div>
                </div>

                <!-- 顶部导航 -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-4 border-bottom">
                    <h1 class="h2" style="color: var(--primary-color);">
                        <i class="fas fa-tachometer-alt me-2"></i>
                        管理仪表盘
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm" style="background-color: var(--primary-color); color: var(--white); border: none; border-radius: 12px; padding: 0.5rem 1rem;" onclick="location.reload()">
                                <i class="fas fa-sync-alt me-1"></i> 刷新数据
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 统计卡片 -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="stats-card">
                            <div class="stats-icon" style="background: var(--primary-color);">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stats-number" th:text="${stats.totalEngineers}">0</div>
                            <p class="stats-label">总工程师数</p>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="stats-card">
                            <div class="stats-icon" style="background: var(--warning-color);">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="stats-number" th:text="${stats.pendingEngineers}">0</div>
                            <p class="stats-label">待审核工程师</p>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="stats-card">
                            <div class="stats-icon" style="background: var(--secondary-color);">
                                <i class="fas fa-building"></i>
                            </div>
                            <div class="stats-number" th:text="${stats.totalServiceCenters}">0</div>
                            <p class="stats-label">总服务网点</p>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="stats-card">
                            <div class="stats-icon" style="background: var(--error-color);">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="stats-number" th:text="${stats.pendingServiceCenters}">0</div>
                            <p class="stats-label">待审核网点</p>
                        </div>
                    </div>
                </div>

                <!-- 订单统计卡片 -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="stats-card">
                            <div class="stats-icon" style="background: var(--accent-color);">
                                <i class="fas fa-clipboard-list"></i>
                            </div>
                            <div class="stats-number" th:text="${stats.totalOrders}">0</div>
                            <p class="stats-label">总订单数</p>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="stats-card">
                            <div class="stats-icon" style="background: var(--warning-color);">
                                <i class="fas fa-hourglass-half"></i>
                            </div>
                            <div class="stats-number" th:text="${stats.pendingOrders}">0</div>
                            <p class="stats-label">待接单</p>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="stats-card">
                            <div class="stats-icon" style="background: var(--primary-color);">
                                <i class="fas fa-tools"></i>
                            </div>
                            <div class="stats-number" th:text="${stats.processingOrders}">0</div>
                            <p class="stats-label">维修中</p>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="stats-card">
                            <div class="stats-icon" style="background: var(--success-color);">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="stats-number" th:text="${stats.completedOrders}">0</div>
                            <p class="stats-label">已完成</p>
                        </div>
                    </div>
                </div>

                <!-- 快速操作 -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="quick-actions">
                            <h5 class="mb-3" style="color: var(--primary-color);">
                                <i class="fas fa-bolt me-2"></i>
                                快速操作
                            </h5>
                            <a href="/admin/engineers?status=pending" class="action-btn">
                                <i class="fas fa-user-check me-2"></i>
                                审核工程师申请
                            </a>
                            <a href="/admin/service-centers?status=pending" class="action-btn">
                                <i class="fas fa-building me-2"></i>
                                审核服务网点申请
                            </a>
                            <a href="/admin/orders?status=pending" class="action-btn">
                                <i class="fas fa-clipboard-check me-2"></i>
                                处理待接单订单
                            </a>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="quick-actions">
                            <h5 class="mb-3" style="color: var(--success-color);">
                                <i class="fas fa-chart-line me-2"></i>
                                系统概览
                            </h5>
                            <div class="row text-center">
                                <div class="col-6">
                                    <h4 style="color: var(--success-color);" th:text="${stats.approvedEngineers}">0</h4>
                                    <small style="color: var(--light-text);">已认证工程师</small>
                                </div>
                                <div class="col-6">
                                    <h4 style="color: var(--primary-color);" th:text="${stats.approvedServiceCenters}">0</h4>
                                    <small style="color: var(--light-text);">已认证网点</small>
                                </div>
                            </div>
                            <div class="row text-center mt-3">
                                <div class="col-12">
                                    <small style="color: var(--light-text);">
                                        <i class="fas fa-clock me-1"></i>
                                        最后更新：<span id="lastUpdateTime"></span>
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 更新时间显示
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            document.getElementById('lastUpdateTime').textContent = timeString;
        }

        // 页面加载时更新时间
        document.addEventListener('DOMContentLoaded', function() {
            updateTime();
            // 每秒更新时间
            setInterval(updateTime, 1000);
        });

        // 自动刷新统计数据
        setInterval(function() {
            location.reload();
        }, 300000); // 5分钟刷新一次

        // 添加页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.stats-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
