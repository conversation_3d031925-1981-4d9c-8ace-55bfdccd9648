<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>服务网点管理 - 成都桩郎中新能源技术有限公司</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        /* 采用小程序端配色方案 */
        :root {
            --primary-color: #1e88e5;
            --secondary-color: #64b5f6;
            --accent-color: #0d47a1;
            --text-color: #333333;
            --light-text: #757575;
            --background-color: #f5f5f5;
            --white: #ffffff;
            --border-color: #e0e0e0;
            --success-color: #4caf50;
            --warning-color: #ff9800;
            --error-color: #f44336;
            --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.05);
            --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.08);
        }

        body {
            background-color: var(--background-color);
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            color: var(--text-color);
            line-height: 1.5;
        }

        .sidebar {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
            min-height: 100vh;
            box-shadow: var(--shadow-medium);
        }

        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 1rem 1.5rem;
            border-radius: 8px;
            margin: 0.25rem 1rem;
            transition: all 0.3s ease;
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background-color: rgba(255,255,255,0.2);
            color: white;
            transform: translateX(5px);
        }

        .main-content {
            padding: 2rem;
        }

        .user-info {
            background: rgba(255,255,255,0.15);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 1.5rem 1rem;
            margin-bottom: 1.5rem;
            text-align: center;
            border: 1px solid rgba(255,255,255,0.2);
            box-shadow: var(--shadow-light);
        }

        .user-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: var(--white);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            color: var(--primary-color);
            font-size: 1.5rem;
            box-shadow: var(--shadow-light);
            position: relative;
        }

        .user-avatar::after {
            content: '';
            position: absolute;
            inset: -2px;
            border-radius: 50%;
            padding: 2px;
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            mask-composite: exclude;
        }

        .user-info h6 {
            color: var(--white) !important;
            font-weight: 600;
            margin-bottom: 0.5rem !important;
            font-size: 1rem;
        }

        .user-info small {
            color: rgba(255,255,255,0.8) !important;
            font-size: 0.85rem;
            font-weight: 500;
        }

        .service-center-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .service-center-card:hover {
            transform: translateY(-2px);
        }

        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
        }

        .status-pending {
            background-color: #fff3cd;
            color: #856404;
        }

        .status-approved {
            background-color: #d4edda;
            color: #155724;
        }

        .status-rejected {
            background-color: #f8d7da;
            color: #721c24;
        }

        .status-suspended {
            background-color: #e2e3e5;
            color: #383d41;
        }

        .status-closed {
            background-color: #f8d7da;
            color: #721c24;
        }

        .status-active {
            background-color: #d1ecf1;
            color: #0c5460;
        }

        .status-online {
            background-color: #cff4fc;
            color: #055160;
        }

        .action-btn {
            padding: 0.5rem 1rem;
            border-radius: 8px;
            border: none;
            font-size: 0.85rem;
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
            transition: all 0.3s ease;
        }

        .btn-approve {
            background: linear-gradient(135deg, #a8e6cf 0%, #dcedc1 100%);
            color: #333;
        }

        .btn-reject {
            background: linear-gradient(135deg, #ffb3ba 0%, #ffdfba 100%);
            color: #333;
        }

        .btn-suspend {
            background: linear-gradient(135deg, #e2e3e5 0%, #d6d8db 100%);
            color: #333;
        }

        .btn-activate {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .filter-tabs {
            background: white;
            border-radius: 15px;
            padding: 1rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .filter-tab {
            padding: 0.75rem 1.5rem;
            border-radius: 25px;
            border: none;
            background: transparent;
            color: #666;
            margin-right: 1rem;
            transition: all 0.3s ease;
        }

        .filter-tab.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .filter-actions {
            display: flex;
            align-items: center;
        }

        /* 添加服务网点按钮样式 */
        .btn-add-service-center {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            text-decoration: none;
            padding: 0.75rem 1.5rem;
            border-radius: 25px;
            border: none;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
            cursor: pointer;
        }

        .btn-add-service-center:hover {
            background: linear-gradient(135deg, #218838 0%, #1ea085 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
            color: white;
        }

        .btn-add-service-center:active {
            transform: translateY(0);
        }

        .service-center-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .info-item {
            display: flex;
            flex-direction: column;
        }

        .info-label {
            font-size: 0.85rem;
            color: #666;
            margin-bottom: 0.25rem;
        }

        .info-value {
            font-weight: 600;
            color: #333;
        }

        .service-center-icon {
            width: 60px;
            height: 60px;
            border-radius: 10px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            margin-right: 1rem;
        }

        .rating-stars {
            color: #ffc107;
        }

        .service-tag {
            display: inline-block;
            background: #e9ecef;
            color: #495057;
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.75rem;
            margin-right: 0.5rem;
            margin-bottom: 0.25rem;
        }

        .location-info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        /* 申请材料图片样式 */
        .business-license-container {
            display: flex;
            justify-content: flex-start;
            margin-bottom: 20px;
        }

        .business-license-image {
            max-width: 300px;
            max-height: 200px;
            border-radius: 8px;
            border: 2px solid #e0e0e0;
            object-fit: contain;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .business-license-image:hover {
            border-color: var(--primary-color);
            transform: scale(1.02);
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
        }

        .certificates-gallery,
        .station-images-gallery {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .certificate-image,
        .station-image {
            width: 120px;
            height: 120px;
            border-radius: 8px;
            border: 2px solid #e0e0e0;
            object-fit: cover;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .certificate-image:hover,
        .station-image:hover {
            border-color: var(--primary-color);
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        /* 图片预览模态框 */
        .image-preview-modal {
            display: none;
            position: fixed;
            z-index: 9999;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.8);
            cursor: pointer;
        }

        .image-preview-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            max-width: 90%;
            max-height: 90%;
        }

        .image-preview-img {
            width: 100%;
            height: auto;
            border-radius: 8px;
        }

        .image-preview-close {
            position: absolute;
            top: 15px;
            right: 25px;
            color: white;
            font-size: 35px;
            font-weight: bold;
            cursor: pointer;
        }

        .image-preview-close:hover {
            opacity: 0.7;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="user-info">
                        <div class="user-avatar">
                            <i class="fas fa-user-shield"></i>
                        </div>
                        <h6 th:text="${admin.realName ?: '超级管理员'}">超级管理员</h6>
                        <small th:text="${admin.role ?: 'super_admin'}">super_admin</small>
                    </div>

                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/index">
                                <i class="bi bi-house-door-fill me-2"></i>
                                仪表盘
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/engineers">
                                <i class="bi bi-people-fill me-2"></i>
                                工程师管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="/admin/service-centers">
                                <i class="bi bi-building me-2"></i>
                                服务网点管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/products">
                                <i class="bi bi-box-seam me-2"></i>
                                产品管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/orders">
                                <i class="fas fa-clipboard-list me-2"></i>
                                订单管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/customer-service">
                                <i class="fas fa-comments me-2"></i>
                                在线客服
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/logout">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                退出登录
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <!-- 顶部导航 -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="bi bi-building text-primary me-2"></i>
                        服务网点管理
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="location.reload()">
                                <i class="bi bi-arrow-clockwise"></i> 刷新
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 状态筛选 -->
                <div class="filter-tabs">
                    <div class="d-flex flex-wrap justify-content-between align-items-center">
                        <div class="d-flex flex-wrap">
                            <button class="filter-tab" th:classappend="${currentStatus == '' ? 'active' : ''}"
                                    onclick="filterServiceCenters('')">
                                <i class="bi bi-list-ul me-1"></i>全部网点
                            </button>
                            <button class="filter-tab" th:classappend="${currentStatus == 'pending' ? 'active' : ''}"
                                    onclick="filterServiceCenters('pending')">
                                <i class="bi bi-hourglass-split me-1"></i>待审核
                            </button>
                            <button class="filter-tab" th:classappend="${currentStatus == 'approved' ? 'active' : ''}"
                                    onclick="filterServiceCenters('approved')">
                                <i class="bi bi-check-circle me-1"></i>已通过
                            </button>
                            <button class="filter-tab" th:classappend="${currentStatus == 'rejected' ? 'active' : ''}"
                                    onclick="filterServiceCenters('rejected')">
                                <i class="bi bi-x-circle me-1"></i>已拒绝
                            </button>
                            <button class="filter-tab" th:classappend="${currentStatus == 'suspended' ? 'active' : ''}"
                                    onclick="filterServiceCenters('suspended')">
                                <i class="bi bi-pause-circle me-1"></i>已暂停
                            </button>
                            <button class="filter-tab" th:classappend="${currentStatus == 'closed' ? 'active' : ''}"
                                    onclick="filterServiceCenters('closed')">
                                <i class="bi bi-stop-circle me-1"></i>已关闭
                            </button>
                        </div>
                        <div class="filter-actions">
                            <button class="btn-add-service-center" onclick="showAddServiceCenterModal()">
                                <i class="bi bi-building-add me-1"></i>添加服务网点
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 服务网点列表 -->
                <div class="row">
                    <div class="col-12" th:if="${serviceCenterPage.records.size() == 0}">
                        <div class="text-center py-5">
                            <i class="bi bi-building-x display-1 text-muted"></i>
                            <h4 class="text-muted mt-3">暂无服务网点数据</h4>
                            <p class="text-muted">当前筛选条件下没有找到服务网点</p>
                        </div>
                    </div>

                    <div class="col-12" th:each="serviceCenter : ${serviceCenterPage.records}">
                        <div class="service-center-card">
                            <div class="d-flex justify-content-between align-items-start mb-3">
                                <div class="d-flex align-items-center">
                                    <div class="service-center-icon">
                                        <i class="bi bi-building"></i>
                                    </div>
                                    <div>
                                        <h5 class="mb-1" th:text="${serviceCenter.name}">服务网点名称</h5>
                                        <div class="d-flex align-items-center mb-1">
                                            <span class="rating-stars me-2">
                                                <i class="bi bi-star-fill" th:each="i : ${#numbers.sequence(1, 5)}"
                                                   th:classappend="${i <= (serviceCenter.rating ?: 5) ? '' : ' text-muted'}"></i>
                                            </span>
                                            <span class="text-muted" th:text="${serviceCenter.rating ?: '5.0'} + ' (' + ${serviceCenter.reviewCount ?: 0} + '条评价)'">5.0 (0条评价)</span>
                                        </div>
                                        <small class="text-muted" th:text="${#temporals.format(serviceCenter.createdAt, 'yyyy-MM-dd HH:mm')} + ' 申请'">申请时间</small>
                                    </div>
                                </div>
                                <span class="status-badge" th:classappend="'status-' + ${serviceCenter.status}"
                                      th:text="${serviceCenter.status == 'pending' ? '待审核' :
                                               (serviceCenter.status == 'approved' ? '已通过' :
                                               (serviceCenter.status == 'rejected' ? '已拒绝' :
                                               (serviceCenter.status == 'suspended' ? '已暂停' : '已关闭')))}">
                                    状态
                                </span>
                            </div>

                            <!-- 位置信息 -->
                            <div class="location-info">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="bi bi-geo-alt-fill text-primary me-2"></i>
                                    <span class="fw-bold">位置信息</span>
                                </div>
                                <div class="row">
                                    <div class="col-md-8">
                                        <p class="mb-1" th:text="${serviceCenter.address}">详细地址</p>
                                        <small class="text-muted" th:text="${serviceCenter.province} + ' ' + ${serviceCenter.city} + ' ' + ${serviceCenter.district}">省市区</small>
                                    </div>
                                    <div class="col-md-4 text-end">
                                        <small class="text-muted">
                                            经纬度: <span th:text="${serviceCenter.longitude} + ',' + ${serviceCenter.latitude}">经纬度</span>
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <div class="service-center-info">
                                <div class="info-item">
                                    <span class="info-label">联系电话</span>
                                    <span class="info-value" th:text="${serviceCenter.phone}">联系电话</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">联系人</span>
                                    <span class="info-value" th:text="${serviceCenter.contactPerson ?: '未填写'}">联系人</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">营业时间</span>
                                    <span class="info-value" th:text="${serviceCenter.businessHours ?: '未填写'}">营业时间</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">服务费用</span>
                                    <span class="info-value" th:text="'¥' + ${serviceCenter.serviceFee ?: 0} + '/次'">服务费用</span>
                                </div>
                                <div class="info-item" th:if="${serviceCenter.reviewTime}">
                                    <span class="info-label">审核时间</span>
                                    <span class="info-value" th:text="${#temporals.format(serviceCenter.reviewTime, 'yyyy-MM-dd HH:mm')}">审核时间</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">总服务次数</span>
                                    <span class="info-value" th:text="${serviceCenter.totalServices ?: 0} + '次'">总服务次数</span>
                                </div>
                            </div>

                            <!-- 服务类型 -->
                            <div class="mb-3" th:if="${serviceCenter.serviceTypes}">
                                <span class="info-label">服务类型：</span>
                                <div class="mt-1 service-types-container" th:data-service-types="${serviceCenter.serviceTypes}">
                                    <!-- 服务类型标签将通过JavaScript动态生成 -->
                                </div>
                            </div>

                            <!-- 服务描述 -->
                            <div class="mb-3" th:if="${serviceCenter.serviceDescription}">
                                <span class="info-label">服务描述</span>
                                <p class="info-value mb-0" th:text="${serviceCenter.serviceDescription}">服务描述</p>
                            </div>

                            <!-- 操作按钮 -->
                            <div class="d-flex flex-wrap">
                                <!-- 待审核状态：可以审核 -->
                                <button th:if="${serviceCenter.status == 'pending'}"
                                        class="action-btn btn-approve"
                                        onclick="showReviewModal(this, 'approve')"
                                        th:data-service-center-id="${serviceCenter.id}">
                                    <i class="bi bi-check-circle me-1"></i>通过申请
                                </button>

                                <button th:if="${serviceCenter.status == 'pending'}"
                                        class="action-btn btn-reject"
                                        onclick="showReviewModal(this, 'reject')"
                                        th:data-service-center-id="${serviceCenter.id}">
                                    <i class="bi bi-x-circle me-1"></i>拒绝申请
                                </button>

                                <!-- 已通过状态：可以暂停 -->
                                <button th:if="${serviceCenter.status == 'approved'}"
                                        class="action-btn btn-suspend"
                                        onclick="updateStatus(this, 'suspended')"
                                        th:data-service-center-id="${serviceCenter.id}">
                                    <i class="bi bi-pause-circle me-1"></i>暂停服务
                                </button>

                                <!-- 已暂停状态：可以恢复 -->
                                <button th:if="${serviceCenter.status == 'suspended'}"
                                        class="action-btn btn-activate"
                                        onclick="updateStatus(this, 'approved')"
                                        th:data-service-center-id="${serviceCenter.id}">
                                    <i class="bi bi-play-circle me-1"></i>恢复服务
                                </button>

                                <button class="action-btn btn-outline-secondary"
                                        onclick="viewServiceCenterDetail(this)"
                                        th:data-service-center-id="${serviceCenter.id}">
                                    <i class="bi bi-eye me-1"></i>查看详情
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 分页 -->
                <nav th:if="${serviceCenterPage.pages > 1}" aria-label="服务网点分页">
                    <ul class="pagination justify-content-center">
                        <li class="page-item" th:classappend="${serviceCenterPage.current == 1 ? 'disabled' : ''}">
                            <a class="page-link" th:href="@{/admin/service-centers(page=${serviceCenterPage.current - 1}, status=${currentStatus})}">上一页</a>
                        </li>

                        <li th:each="i : ${#numbers.sequence(1, serviceCenterPage.pages)}"
                            class="page-item"
                            th:classappend="${i == serviceCenterPage.current ? 'active' : ''}">
                            <a class="page-link" th:href="@{/admin/service-centers(page=${i}, status=${currentStatus})}" th:text="${i}">1</a>
                        </li>

                        <li class="page-item" th:classappend="${serviceCenterPage.current == serviceCenterPage.pages ? 'disabled' : ''}">
                            <a class="page-link" th:href="@{/admin/service-centers(page=${serviceCenterPage.current + 1}, status=${currentStatus})}">下一页</a>
                        </li>
                    </ul>
                </nav>
            </main>
        </div>
    </div>

    <!-- 审核服务网点模态框 -->
    <div class="modal fade" id="reviewServiceCenterModal" tabindex="-1" aria-labelledby="reviewServiceCenterModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="reviewServiceCenterModalLabel">
                        <i class="bi bi-building-check me-2"></i>审核服务网点申请
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="reviewServiceCenterForm">
                        <div class="mb-3">
                            <label for="reviewAction" class="form-label">审核结果</label>
                            <select class="form-select" id="reviewAction" required>
                                <option value="">请选择审核结果</option>
                                <option value="approve">通过申请</option>
                                <option value="reject">拒绝申请</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="reviewNotes" class="form-label">审核备注</label>
                            <textarea class="form-control" id="reviewNotes" rows="3" placeholder="请填写审核意见或拒绝原因"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="confirmReviewBtn">确认审核</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 服务网点详情模态框 -->
    <div class="modal fade" id="serviceCenterDetailModal" tabindex="-1" aria-labelledby="serviceCenterDetailModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="serviceCenterDetailModalLabel">
                        <i class="bi bi-building me-2"></i>服务网点详情
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="serviceCenterDetailContent">
                        <!-- 服务网点详情内容将通过JavaScript动态加载 -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加服务网点模态框 -->
    <div class="modal fade" id="addServiceCenterModal" tabindex="-1" aria-labelledby="addServiceCenterModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addServiceCenterModalLabel">
                        <i class="bi bi-building-add me-2"></i>添加服务网点
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addServiceCenterForm" enctype="multipart/form-data">
                        <div class="row">
                            <!-- 基本信息 -->
                            <div class="col-md-6">
                                <h6 class="border-bottom pb-2 mb-3">
                                    <i class="bi bi-building me-2"></i>基本信息
                                </h6>
                                <div class="mb-3">
                                    <label for="serviceCenterName" class="form-label">网点名称 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="serviceCenterName" name="name" required>
                                </div>
                                <div class="mb-3">
                                    <label for="contactPerson" class="form-label">联系人 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="contactPerson" name="contactPerson" required>
                                </div>
                                <div class="mb-3">
                                    <label for="phone" class="form-label">联系电话 <span class="text-danger">*</span></label>
                                    <input type="tel" class="form-control" id="phone" name="phone" required>
                                </div>
                                <div class="mb-3">
                                    <label for="email" class="form-label">邮箱</label>
                                    <input type="email" class="form-control" id="email" name="email">
                                </div>
                                <div class="mb-3">
                                    <label for="serviceFee" class="form-label">服务费用 (元/次)</label>
                                    <input type="number" class="form-control" id="serviceFee" name="serviceFee" min="0" step="0.01">
                                </div>
                            </div>

                            <!-- 位置信息 -->
                            <div class="col-md-6">
                                <h6 class="border-bottom pb-2 mb-3">
                                    <i class="bi bi-geo-alt me-2"></i>位置信息
                                </h6>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="province" class="form-label">省份 <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="province" name="province" required>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="city" class="form-label">城市 <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="city" name="city" required>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="district" class="form-label">区县 <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="district" name="district" required>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="address" class="form-label">详细地址 <span class="text-danger">*</span></label>
                                    <textarea class="form-control" id="address" name="address" rows="2" required></textarea>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="longitude" class="form-label">经度</label>
                                            <input type="number" class="form-control" id="longitude" name="longitude" step="0.000001">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="latitude" class="form-label">纬度</label>
                                            <input type="number" class="form-control" id="latitude" name="latitude" step="0.000001">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- 营业信息 -->
                            <div class="col-md-6">
                                <h6 class="border-bottom pb-2 mb-3">
                                    <i class="bi bi-clock me-2"></i>营业信息
                                </h6>
                                <div class="mb-3">
                                    <label for="businessHours" class="form-label">营业时间</label>
                                    <input type="text" class="form-control" id="businessHours" name="businessHours" placeholder="例如：09:00-18:00">
                                </div>
                                <div class="mb-3">
                                    <label for="businessDays" class="form-label">营业日期</label>
                                    <input type="text" class="form-control" id="businessDays" name="businessDays" placeholder="例如：周一至周日">
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="is24Hours" name="is24Hours">
                                        <label class="form-check-label" for="is24Hours">
                                            24小时营业
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- 服务信息 -->
                            <div class="col-md-6">
                                <h6 class="border-bottom pb-2 mb-3">
                                    <i class="bi bi-tools me-2"></i>服务信息
                                </h6>
                                <div class="mb-3">
                                    <label for="serviceTypes" class="form-label">服务类型</label>
                                    <div class="service-types-checkboxes">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" value="维修服务" id="serviceType1">
                                            <label class="form-check-label" for="serviceType1">维修服务</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" value="安装服务" id="serviceType2">
                                            <label class="form-check-label" for="serviceType2">安装服务</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" value="保养服务" id="serviceType3">
                                            <label class="form-check-label" for="serviceType3">保养服务</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" value="技术支持" id="serviceType4">
                                            <label class="form-check-label" for="serviceType4">技术支持</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="serviceDescription" class="form-label">服务描述</label>
                                    <textarea class="form-control" id="serviceDescription" name="serviceDescription" rows="3"></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- 其他设置 -->
                        <div class="row">
                            <div class="col-12">
                                <h6 class="border-bottom pb-2 mb-3">
                                    <i class="bi bi-gear me-2"></i>其他设置
                                </h6>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="isActive" name="isActive" checked>
                                            <label class="form-check-label" for="isActive">
                                                启用网点
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="isFeatured" name="isFeatured">
                                            <label class="form-check-label" for="isFeatured">
                                                推荐网点
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="status" class="form-label">初始状态</label>
                                            <select class="form-select" id="status" name="status">
                                                <option value="approved">已通过</option>
                                                <option value="pending">待审核</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-success" id="confirmAddServiceCenterBtn">
                        <i class="bi bi-building-add me-1"></i>添加网点
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 图片预览模态框 -->
    <div id="imagePreviewModal" class="image-preview-modal" onclick="closeImagePreview()">
        <span class="image-preview-close" onclick="closeImagePreview()">&times;</span>
        <div class="image-preview-content">
            <img id="previewImage" class="image-preview-img" src="" alt="预览图片">
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 全局变量
        let currentServiceCenterId = null;
        let currentAction = null;

        // 筛选服务网点
        function filterServiceCenters(status) {
            const url = new URL(window.location);
            if (status) {
                url.searchParams.set('status', status);
            } else {
                url.searchParams.delete('status');
            }
            url.searchParams.set('page', '1');
            window.location.href = url.toString();
        }

        // 显示审核模态框
        function showReviewModal(button, action) {
            const serviceCenterId = button.dataset.serviceCenterId;
            currentServiceCenterId = serviceCenterId;
            currentAction = action;

            // 设置模态框标题和默认值
            const modalTitle = document.getElementById('reviewServiceCenterModalLabel');
            const actionSelect = document.getElementById('reviewAction');

            if (action === 'approve') {
                modalTitle.innerHTML = '<i class="bi bi-check-circle me-2"></i>通过服务网点申请';
                actionSelect.value = 'approve';
            } else {
                modalTitle.innerHTML = '<i class="bi bi-x-circle me-2"></i>拒绝服务网点申请';
                actionSelect.value = 'reject';
            }

            // 清空备注
            document.getElementById('reviewNotes').value = '';

            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('reviewServiceCenterModal'));
            modal.show();
        }

        // 更新服务网点状态
        function updateStatus(button, newStatus) {
            const serviceCenterId = button.dataset.serviceCenterId;
            const statusText = {
                'suspended': '暂停服务',
                'approved': '恢复服务',
                'closed': '关闭网点'
            };

            if (confirm(`确定要${statusText[newStatus]}吗？`)) {
                fetch(`/admin/api/service-centers/${serviceCenterId}/status`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `status=${newStatus}`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(data.message);
                        location.reload();
                    } else {
                        alert(data.message || '操作失败');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('网络错误，请稍后重试');
                });
            }
        }

        // 确认审核
        document.getElementById('confirmReviewBtn').addEventListener('click', function() {
            const action = document.getElementById('reviewAction').value;
            const notes = document.getElementById('reviewNotes').value;

            if (!action) {
                alert('请选择审核结果');
                return;
            }

            if (action === 'reject' && !notes.trim()) {
                alert('拒绝申请时必须填写拒绝原因');
                return;
            }

            reviewServiceCenter(currentServiceCenterId, action, notes);
        });

        // 审核服务网点
        function reviewServiceCenter(serviceCenterId, action, notes) {
            let body = `action=${action}`;
            if (notes) {
                body += `&notes=${encodeURIComponent(notes)}`;
            }

            fetch(`/admin/api/service-centers/${serviceCenterId}/review`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: body
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.message);
                    bootstrap.Modal.getInstance(document.getElementById('reviewServiceCenterModal')).hide();
                    location.reload();
                } else {
                    alert(data.message || '审核失败');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('网络错误，请稍后重试');
            });
        }

        // 查看服务网点详情
        function viewServiceCenterDetail(button) {
            const serviceCenterId = button.dataset.serviceCenterId;

            // 加载服务网点详情
            fetch(`/admin/api/service-centers/${serviceCenterId}/detail`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        renderServiceCenterDetail(data.data);
                        const modal = new bootstrap.Modal(document.getElementById('serviceCenterDetailModal'));
                        modal.show();
                    } else {
                        alert('加载服务网点详情失败：' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('网络错误，请稍后重试');
                });
        }

        // 渲染服务网点详情
        function renderServiceCenterDetail(serviceCenter) {
            const content = document.getElementById('serviceCenterDetailContent');

            // 解析服务类型
            const serviceTypes = parseServiceTypesData(serviceCenter.serviceTypes);
            const serviceTypesHtml = renderServiceTags(serviceTypes);

            // 解析设备类型
            const equipmentTypes = parseServiceTypesData(serviceCenter.equipmentTypes);
            const equipmentTypesHtml = renderServiceTags(equipmentTypes);

            // 解析设施设备
            const facilities = parseServiceTypesData(serviceCenter.facilities);
            const facilitiesHtml = renderServiceTags(facilities);

            content.innerHTML = `
                <div class="row">
                    <!-- 基本信息 -->
                    <div class="col-md-6 mb-4">
                        <h6 class="border-bottom pb-2 mb-3">
                            <i class="bi bi-building me-2"></i>基本信息
                        </h6>
                        <table class="table table-borderless">
                            <tr><td class="fw-bold">网点名称:</td><td>${serviceCenter.name || '-'}</td></tr>
                            <tr><td class="fw-bold">联系电话:</td><td>${serviceCenter.phone || '-'}</td></tr>
                            <tr><td class="fw-bold">联系人:</td><td>${serviceCenter.contactPerson || '-'}</td></tr>
                            <tr><td class="fw-bold">邮箱:</td><td>${serviceCenter.email || '-'}</td></tr>
                            <tr><td class="fw-bold">营业时间:</td><td>${serviceCenter.businessHours || '-'}</td></tr>
                            <tr><td class="fw-bold">营业日期:</td><td>${serviceCenter.businessDays || '-'}</td></tr>
                            <tr><td class="fw-bold">24小时营业:</td><td>${serviceCenter.is24Hours ? '是' : '否'}</td></tr>
                        </table>
                    </div>

                    <!-- 状态信息 -->
                    <div class="col-md-6 mb-4">
                        <h6 class="border-bottom pb-2 mb-3">
                            <i class="bi bi-info-circle me-2"></i>状态信息
                        </h6>
                        <table class="table table-borderless">
                            <tr><td class="fw-bold">当前状态:</td><td><span class="badge bg-${getStatusColor(serviceCenter.status)}">${getStatusText(serviceCenter.status)}</span></td></tr>
                            <tr><td class="fw-bold">是否启用:</td><td>${serviceCenter.isActive ? '启用' : '禁用'}</td></tr>
                            <tr><td class="fw-bold">是否推荐:</td><td>${serviceCenter.isFeatured ? '推荐' : '普通'}</td></tr>
                            <tr><td class="fw-bold">申请时间:</td><td>${formatDateTime(serviceCenter.applicationTime || serviceCenter.createdAt)}</td></tr>
                            ${serviceCenter.reviewTime ? `<tr><td class="fw-bold">审核时间:</td><td>${formatDateTime(serviceCenter.reviewTime)}</td></tr>` : ''}
                            <tr><td class="fw-bold">最后活跃:</td><td>${formatDateTime(serviceCenter.lastActiveAt)}</td></tr>
                        </table>
                    </div>
                </div>

                <!-- 位置信息 -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h6 class="border-bottom pb-2 mb-3">
                            <i class="bi bi-geo-alt me-2"></i>位置信息
                        </h6>
                        <div class="row">
                            <div class="col-md-8">
                                <table class="table table-borderless">
                                    <tr><td class="fw-bold">详细地址:</td><td>${serviceCenter.address || '-'}</td></tr>
                                    <tr><td class="fw-bold">省市区:</td><td>${serviceCenter.province || ''} ${serviceCenter.city || ''} ${serviceCenter.district || ''}</td></tr>
                                    <tr><td class="fw-bold">经纬度:</td><td>${serviceCenter.longitude || '-'}, ${serviceCenter.latitude || '-'}</td></tr>
                                    <tr><td class="fw-bold">位置精度:</td><td>${serviceCenter.locationAccuracy || '-'}</td></tr>
                                    <tr><td class="fw-bold">停车信息:</td><td>${serviceCenter.parkingInfo || '-'}</td></tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 服务信息 -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h6 class="border-bottom pb-2 mb-3">
                            <i class="bi bi-tools me-2"></i>服务信息
                        </h6>
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>服务类型:</strong></p>
                                <div class="mb-3">${serviceTypesHtml}</div>
                            </div>
                            <div class="col-md-6">
                                <p><strong>支持设备:</strong></p>
                                <div class="mb-3">${equipmentTypesHtml}</div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>设施设备:</strong></p>
                                <div class="mb-3">${facilitiesHtml}</div>
                            </div>
                        </div>
                        ${serviceCenter.serviceDescription ? `
                            <div class="row">
                                <div class="col-12">
                                    <p><strong>服务描述:</strong></p>
                                    <p class="bg-light p-3 rounded">${serviceCenter.serviceDescription}</p>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>

                <!-- 费用信息 -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h6 class="border-bottom pb-2 mb-3">
                            <i class="bi bi-currency-dollar me-2"></i>费用信息
                        </h6>
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr><td class="fw-bold">基础服务费:</td><td>¥${serviceCenter.serviceFee || 0}/次</td></tr>
                                    <tr><td class="fw-bold">检测费用:</td><td>¥${serviceCenter.inspectionFee || 0}/次</td></tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 评价信息 -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h6 class="border-bottom pb-2 mb-3">
                            <i class="bi bi-star me-2"></i>评价信息
                        </h6>
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr><td class="fw-bold">平均评分:</td><td>${serviceCenter.rating || '5.0'} 分</td></tr>
                                    <tr><td class="fw-bold">评价数量:</td><td>${serviceCenter.reviewCount || 0} 条</td></tr>
                                    <tr><td class="fw-bold">总服务次数:</td><td>${serviceCenter.totalServices || 0} 次</td></tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                ${serviceCenter.reviewNotes ? `
                    <!-- 审核备注 -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="border-bottom pb-2 mb-3">
                                <i class="bi bi-clipboard-check me-2"></i>审核备注
                            </h6>
                            <p class="bg-light p-3 rounded">${serviceCenter.reviewNotes}</p>
                        </div>
                    </div>
                ` : ''}

                ${serviceCenter.rejectionReason ? `
                    <!-- 拒绝原因 -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="border-bottom pb-2 mb-3">
                                <i class="bi bi-x-circle me-2"></i>拒绝原因
                            </h6>
                            <p class="bg-danger bg-opacity-10 p-3 rounded text-danger">${serviceCenter.rejectionReason}</p>
                        </div>
                    </div>
                ` : ''}

                <!-- 申请材料 -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h6 class="border-bottom pb-2 mb-3">
                            <i class="bi bi-file-earmark-image me-2"></i>申请材料
                        </h6>

                        <!-- 营业执照 -->
                        ${serviceCenter.businessLicense ? `
                            <div class="mb-4">
                                <h6 class="text-primary mb-2">
                                    <i class="bi bi-file-earmark-text me-1"></i>营业执照
                                </h6>
                                <div class="business-license-container">
                                    <img src="${serviceCenter.businessLicense}"
                                         class="business-license-image"
                                         alt="营业执照"
                                         onclick="showImagePreview('${serviceCenter.businessLicense}')"
                                         onerror="this.style.display='none'; this.nextSibling.style.display='block';">
                                    <div style="display: none; color: red;">❌ 营业执照加载失败</div>
                                </div>
                            </div>
                        ` : ''}

                        <!-- 资质证书 -->
                        ${serviceCenter.qualificationCertificates ? `
                            <div class="mb-4">
                                <h6 class="text-success mb-2">
                                    <i class="bi bi-award me-1"></i>资质证书
                                </h6>
                                <div class="certificates-gallery" data-certificates="${serviceCenter.qualificationCertificates.replace(/"/g, '&quot;')}">
                                    <!-- 资质证书将通过JavaScript动态加载 -->
                                </div>
                            </div>
                        ` : ''}

                        <!-- 网点照片 -->
                        ${serviceCenter.images ? `
                            <div class="mb-4">
                                <h6 class="text-info mb-2">
                                    <i class="bi bi-camera me-1"></i>网点照片
                                </h6>
                                <div class="station-images-gallery" data-images="${serviceCenter.images.replace(/"/g, '&quot;')}">
                                    <!-- 网点照片将通过JavaScript动态加载 -->
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;

            // 初始化图片显示
            initServiceCenterImages();
        }

        // 获取状态颜色
        function getStatusColor(status) {
            const colors = {
                'pending': 'warning',
                'approved': 'success',
                'rejected': 'danger',
                'suspended': 'secondary',
                'closed': 'dark'
            };
            return colors[status] || 'secondary';
        }

        // 获取状态文本
        function getStatusText(status) {
            const texts = {
                'pending': '待审核',
                'approved': '已通过',
                'rejected': '已拒绝',
                'suspended': '已暂停',
                'closed': '已关闭'
            };
            return texts[status] || status;
        }

        // 格式化日期时间
        function formatDateTime(dateTime) {
            if (!dateTime) return '-';
            const date = new Date(dateTime);
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        // 显示图片预览
        function showImagePreview(imageSrc) {
            const modal = document.getElementById('imagePreviewModal');
            const previewImg = document.getElementById('previewImage');

            previewImg.src = imageSrc;
            modal.style.display = 'block';

            // 阻止事件冒泡
            if (event) {
                event.stopPropagation();
            }
        }

        // 关闭图片预览
        function closeImagePreview() {
            const modal = document.getElementById('imagePreviewModal');
            modal.style.display = 'none';
        }

        // 初始化服务网点图片
        function initServiceCenterImages() {
            // 初始化资质证书图片
            const certificatesGalleries = document.querySelectorAll('.certificates-gallery');
            certificatesGalleries.forEach(gallery => {
                const certificatesData = gallery.getAttribute('data-certificates');
                if (certificatesData) {
                    try {
                        // 解码HTML实体
                        const decodedData = certificatesData.replace(/&quot;/g, '"');
                        const certificates = JSON.parse(decodedData);
                        renderCertificateImages(gallery, certificates);
                    } catch (e) {
                        console.error('解析资质证书数据失败:', e, certificatesData);
                    }
                }
            });

            // 初始化网点照片
            const stationImagesGalleries = document.querySelectorAll('.station-images-gallery');
            stationImagesGalleries.forEach(gallery => {
                const imagesData = gallery.getAttribute('data-images');
                if (imagesData) {
                    try {
                        // 解码HTML实体
                        const decodedData = imagesData.replace(/&quot;/g, '"');
                        const images = JSON.parse(decodedData);
                        renderStationImages(gallery, images);
                    } catch (e) {
                        console.error('解析网点照片数据失败:', e, imagesData);
                    }
                }
            });
        }

        // 渲染资质证书图片
        function renderCertificateImages(gallery, certificates) {
            gallery.innerHTML = '';

            certificates.forEach((url, index) => {
                const img = document.createElement('img');
                img.src = processImageUrl(url);
                img.className = 'certificate-image';
                img.alt = `资质证书 ${index + 1}`;
                img.onclick = () => showImagePreview(img.src);

                img.onload = () => console.log(`✅ 资质证书${index}加载成功:`, img.src);
                img.onerror = () => {
                    console.error(`❌ 资质证书${index}加载失败:`, img.src);
                    img.style.display = 'none';
                };

                gallery.appendChild(img);
            });
        }

        // 渲染网点照片
        function renderStationImages(gallery, images) {
            gallery.innerHTML = '';

            images.forEach((url, index) => {
                const img = document.createElement('img');
                img.src = processImageUrl(url);
                img.className = 'station-image';
                img.alt = `网点照片 ${index + 1}`;
                img.onclick = () => showImagePreview(img.src);

                img.onload = () => console.log(`✅ 网点照片${index}加载成功:`, img.src);
                img.onerror = () => {
                    console.error(`❌ 网点照片${index}加载失败:`, img.src);
                    img.style.display = 'none';
                };

                gallery.appendChild(img);
            });
        }

        // 处理图片URL，避免重复路径
        function processImageUrl(url) {
            if (url.startsWith('http')) {
                return url;
            } else if (url.startsWith('/uploads/')) {
                return url;
            } else {
                return '/uploads/' + url;
            }
        }

        // 解析服务类型数据的通用函数
        function parseServiceTypesData(serviceTypesData) {
            if (!serviceTypesData) return [];

            console.log('🔍 解析服务类型数据:', serviceTypesData);

            // 如果是字符串，尝试多种解析方式
            if (typeof serviceTypesData === 'string') {
                // 1. 尝试JSON解析
                try {
                    const parsed = JSON.parse(serviceTypesData);
                    if (Array.isArray(parsed)) {
                        console.log('✅ JSON解析成功:', parsed);
                        return parsed.map(item => String(item).trim()).filter(item => item);
                    }
                } catch (e) {
                    console.log('⚠️ JSON解析失败，尝试其他方式');
                }

                // 2. 移除JSON格式字符，然后按逗号分割
                let cleanData = serviceTypesData
                    .replace(/^\[|\]$/g, '')  // 移除首尾的方括号
                    .replace(/"/g, '')        // 移除所有引号
                    .replace(/'/g, '')        // 移除所有单引号
                    .trim();

                if (cleanData) {
                    const result = cleanData.split(',').map(item => item.trim()).filter(item => item);
                    console.log('✅ 清理后解析成功:', result);
                    return result;
                }
            }

            // 3. 如果已经是数组
            if (Array.isArray(serviceTypesData)) {
                console.log('✅ 已是数组:', serviceTypesData);
                return serviceTypesData.map(item => String(item).trim()).filter(item => item);
            }

            console.log('⚠️ 无法解析，返回空数组');
            return [];
        }

        // 渲染服务类型标签
        function renderServiceTags(serviceTypes) {
            if (!serviceTypes || serviceTypes.length === 0) {
                return '<span class="text-muted">未填写</span>';
            }

            return serviceTypes.map(type =>
                `<span class="service-tag">${type}</span>`
            ).join('');
        }

        // 显示添加服务网点模态框
        function showAddServiceCenterModal() {
            // 清空表单
            document.getElementById('addServiceCenterForm').reset();

            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('addServiceCenterModal'));
            modal.show();
        }

        // 确认添加服务网点
        document.getElementById('confirmAddServiceCenterBtn').addEventListener('click', function() {
            const form = document.getElementById('addServiceCenterForm');
            const formData = new FormData(form);

            // 收集服务类型
            const serviceTypes = [];
            document.querySelectorAll('.service-types-checkboxes input[type="checkbox"]:checked').forEach(checkbox => {
                serviceTypes.push(checkbox.value);
            });
            formData.set('serviceTypes', JSON.stringify(serviceTypes));

            // 验证必填字段
            const requiredFields = ['name', 'contactPerson', 'phone', 'province', 'city', 'district', 'address'];
            let isValid = true;

            for (const field of requiredFields) {
                const input = form.querySelector(`[name="${field}"]`);
                if (!input.value.trim()) {
                    input.classList.add('is-invalid');
                    isValid = false;
                } else {
                    input.classList.remove('is-invalid');
                }
            }

            if (!isValid) {
                alert('请填写所有必填字段');
                return;
            }

            // 提交数据
            fetch('/admin/api/service-centers', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('服务网点添加成功！');
                    bootstrap.Modal.getInstance(document.getElementById('addServiceCenterModal')).hide();
                    location.reload();
                } else {
                    alert('添加失败：' + (data.message || '未知错误'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('网络错误，请稍后重试');
            });
        });

        // 页面加载完成后处理服务类型显示
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 开始处理服务类型显示');

            // 处理所有服务类型容器
            const serviceTypesContainers = document.querySelectorAll('.service-types-container');
            console.log(`📋 找到 ${serviceTypesContainers.length} 个服务类型容器`);

            serviceTypesContainers.forEach((container, index) => {
                const serviceTypesData = container.getAttribute('data-service-types');
                console.log(`🔧 处理容器 ${index + 1}:`, serviceTypesData);

                if (serviceTypesData) {
                    const serviceTypes = parseServiceTypesData(serviceTypesData);
                    container.innerHTML = renderServiceTags(serviceTypes);
                    console.log(`✅ 容器 ${index + 1} 处理完成，显示 ${serviceTypes.length} 个标签`);
                } else {
                    container.innerHTML = '<span class="text-muted">未填写</span>';
                    console.log(`⚠️ 容器 ${index + 1} 无数据`);
                }
            });

            console.log('🎉 服务类型显示处理完成');
        });
    </script>
</body>
</html>
