<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工程师管理 - 成都桩郎中新能源技术有限公司</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        /* 采用小程序端配色方案 */
        :root {
            --primary-color: #1e88e5;
            --secondary-color: #64b5f6;
            --accent-color: #0d47a1;
            --text-color: #333333;
            --light-text: #757575;
            --background-color: #f5f5f5;
            --white: #ffffff;
            --border-color: #e0e0e0;
            --success-color: #4caf50;
            --warning-color: #ff9800;
            --error-color: #f44336;
            --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.05);
            --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.08);
        }

        body {
            background-color: var(--background-color);
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            color: var(--text-color);
            line-height: 1.5;
        }

        .sidebar {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
            min-height: 100vh;
            box-shadow: var(--shadow-medium);
        }

        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 1rem 1.5rem;
            border-radius: 8px;
            margin: 0.25rem 1rem;
            transition: all 0.3s ease;
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background-color: rgba(255,255,255,0.2);
            color: white;
            transform: translateX(5px);
        }

        .main-content {
            padding: 2rem;
        }

        .user-info {
            background: rgba(255,255,255,0.15);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 1.5rem 1rem;
            margin-bottom: 1.5rem;
            text-align: center;
            border: 1px solid rgba(255,255,255,0.2);
            box-shadow: var(--shadow-light);
        }

        .user-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: var(--white);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            color: var(--primary-color);
            font-size: 1.5rem;
            box-shadow: var(--shadow-light);
            position: relative;
        }

        .user-avatar::after {
            content: '';
            position: absolute;
            inset: -2px;
            border-radius: 50%;
            padding: 2px;
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            mask-composite: exclude;
        }

        .user-info h6 {
            color: var(--white) !important;
            font-weight: 600;
            margin-bottom: 0.5rem !important;
            font-size: 1rem;
        }

        .user-info small {
            color: rgba(255,255,255,0.8) !important;
            font-size: 0.85rem;
            font-weight: 500;
        }

        .engineer-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .engineer-card:hover {
            transform: translateY(-2px);
        }

        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
        }

        .status-pending {
            background-color: #fff3cd;
            color: #856404;
        }

        .status-approved {
            background-color: #d4edda;
            color: #155724;
        }

        .status-rejected {
            background-color: #f8d7da;
            color: #721c24;
        }

        .status-suspended {
            background-color: #e2e3e5;
            color: #383d41;
        }

        .status-active {
            background-color: #d1ecf1;
            color: #0c5460;
        }

        .status-online {
            background-color: #cff4fc;
            color: #055160;
        }

        .status-offline {
            background-color: #f8d7da;
            color: #721c24;
        }

        .action-btn {
            padding: 0.5rem 1rem;
            border-radius: 8px;
            border: none;
            font-size: 0.85rem;
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
            transition: all 0.3s ease;
        }

        .btn-approve {
            background: linear-gradient(135deg, #a8e6cf 0%, #dcedc1 100%);
            color: #333;
        }

        .btn-reject {
            background: linear-gradient(135deg, #ffb3ba 0%, #ffdfba 100%);
            color: #333;
        }

        .btn-suspend {
            background: linear-gradient(135deg, #e2e3e5 0%, #d6d8db 100%);
            color: #333;
        }

        .btn-activate {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .filter-tabs {
            background: white;
            border-radius: 15px;
            padding: 1rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .filter-tab {
            padding: 0.75rem 1.5rem;
            border-radius: 25px;
            border: none;
            background: transparent;
            color: #666;
            margin-right: 1rem;
            transition: all 0.3s ease;
        }

        .filter-tab.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .filter-tab:hover {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            transform: translateY(-2px);
        }

        /* 筛选操作区域 */
        .filter-actions {
            display: flex;
            align-items: center;
        }

        /* 添加工程师按钮样式 */
        .btn-add-engineer {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            text-decoration: none;
            padding: 0.75rem 1.5rem;
            border-radius: 25px;
            font-weight: 500;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            box-shadow: 0 2px 8px rgba(40, 167, 69, 0.2);
            border: none;
        }

        .btn-add-engineer:hover {
            color: white;
            text-decoration: none;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
            background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
        }

        .btn-add-engineer:focus {
            color: white;
            text-decoration: none;
            box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .filter-tabs .d-flex.justify-content-between {
                flex-direction: column;
                gap: 1rem;
            }

            .filter-actions {
                justify-content: center;
                width: 100%;
            }

            .btn-add-engineer {
                width: 100%;
                justify-content: center;
            }
        }

        @media (max-width: 576px) {
            .filter-tab {
                padding: 0.5rem 1rem;
                margin-right: 0.5rem;
                margin-bottom: 0.5rem;
                font-size: 0.9rem;
            }

            .btn-add-engineer {
                padding: 0.6rem 1.2rem;
                font-size: 0.9rem;
            }
        }

        .engineer-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .info-item {
            display: flex;
            flex-direction: column;
        }

        .info-label {
            font-size: 0.85rem;
            color: #666;
            margin-bottom: 0.25rem;
        }

        .info-value {
            font-weight: 600;
            color: #333;
        }

        .engineer-avatar-large {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            margin-right: 1rem;
            overflow: hidden;
            position: relative;
            flex-shrink: 0;
        }

        .engineer-avatar-large img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 50%;
        }

        .engineer-avatar-large .avatar-placeholder {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
        }

        .rating-stars {
            color: #ffc107;
        }

        .specialty-tag {
            display: inline-block;
            background: #e9ecef;
            color: #495057;
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.75rem;
            margin-right: 0.5rem;
            margin-bottom: 0.25rem;
        }

        /* 图片画廊样式 */
        .certifications-gallery,
        .work-photos-gallery {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 15px;
        }

        .certificate-image,
        .work-photo-image {
            width: 120px;
            height: 120px;
            border-radius: 8px;
            border: 2px solid #e0e0e0;
            object-fit: cover;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .certificate-image:hover,
        .work-photo-image:hover {
            border-color: var(--primary-color);
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        /* 图片预览模态框 */
        .image-preview-modal {
            display: none;
            position: fixed;
            z-index: 9999;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.8);
            cursor: pointer;
        }

        .image-preview-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            max-width: 90%;
            max-height: 90%;
        }

        .image-preview-img {
            width: 100%;
            height: auto;
            border-radius: 8px;
        }

        .image-preview-close {
            position: absolute;
            top: 15px;
            right: 25px;
            color: white;
            font-size: 35px;
            font-weight: bold;
            cursor: pointer;
        }

        .image-preview-close:hover {
            opacity: 0.7;
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .image-item {
                margin-bottom: 15px;
            }

            #imagePreviewModal .modal-dialog {
                margin: 10px;
            }

            #imagePreviewModal img {
                max-height: 60vh;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="user-info">
                        <div class="user-avatar">
                            <i class="fas fa-user-shield"></i>
                        </div>
                        <h6 th:text="${admin.realName ?: '超级管理员'}">超级管理员</h6>
                        <small th:text="${admin.role ?: 'super_admin'}">super_admin</small>
                    </div>

                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/index">
                                <i class="bi bi-house-door-fill me-2"></i>
                                仪表盘
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="/admin/engineers">
                                <i class="bi bi-people-fill me-2"></i>
                                工程师管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/service-centers">
                                <i class="bi bi-building me-2"></i>
                                服务网点管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/products">
                                <i class="bi bi-box-seam me-2"></i>
                                产品管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/orders">
                                <i class="fas fa-clipboard-list me-2"></i>
                                订单管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/customer-service">
                                <i class="fas fa-comments me-2"></i>
                                在线客服
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/logout">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                退出登录
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <!-- 顶部导航 -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="bi bi-people-fill text-primary me-2"></i>
                        工程师管理
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="location.reload()">
                                <i class="bi bi-arrow-clockwise"></i> 刷新
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 状态筛选 -->
                <div class="filter-tabs">
                    <div class="d-flex flex-wrap justify-content-between align-items-center">
                        <div class="d-flex flex-wrap">
                            <button class="filter-tab" th:classappend="${currentStatus == '' ? 'active' : ''}"
                                    onclick="filterEngineers('')">
                                <i class="bi bi-list-ul me-1"></i>全部工程师
                            </button>
                            <button class="filter-tab" th:classappend="${currentStatus == 'pending' ? 'active' : ''}"
                                    onclick="filterEngineers('pending')">
                                <i class="bi bi-hourglass-split me-1"></i>待审核
                            </button>
                            <button class="filter-tab" th:classappend="${currentStatus == 'approved' ? 'active' : ''}"
                                    onclick="filterEngineers('approved')">
                                <i class="bi bi-check-circle me-1"></i>已通过
                            </button>
                            <button class="filter-tab" th:classappend="${currentStatus == 'rejected' ? 'active' : ''}"
                                    onclick="filterEngineers('rejected')">
                                <i class="bi bi-x-circle me-1"></i>已拒绝
                            </button>
                            <button class="filter-tab" th:classappend="${currentStatus == 'suspended' ? 'active' : ''}"
                                    onclick="filterEngineers('suspended')">
                                <i class="bi bi-pause-circle me-1"></i>已暂停
                            </button>
                        </div>
                        <div class="filter-actions">
                            <a href="/admin/add-engineer" class="btn-add-engineer">
                                <i class="bi bi-person-plus me-1"></i>添加工程师
                            </a>
                        </div>
                    </div>
                </div>

                <!-- 工程师列表 -->
                <div class="row">
                    <div class="col-12" th:if="${engineerPage.records.size() == 0}">
                        <div class="text-center py-5">
                            <i class="bi bi-person-x display-1 text-muted"></i>
                            <h4 class="text-muted mt-3">暂无工程师数据</h4>
                            <p class="text-muted">当前筛选条件下没有找到工程师</p>
                        </div>
                    </div>

                    <div class="col-12" th:each="engineer : ${engineerPage.records}">
                        <div class="engineer-card">
                            <div class="d-flex justify-content-between align-items-start mb-3">
                                <div class="d-flex align-items-center">
                                    <div class="engineer-avatar-large" th:data-engineer-id="${engineer.id}" th:data-avatar="${engineer.avatar}">
                                        <i class="bi bi-person-fill"></i>
                                    </div>
                                    <div>
                                        <h5 class="mb-1" th:text="${engineer.name}">工程师姓名</h5>
                                        <div class="d-flex align-items-center mb-1">
                                            <span class="rating-stars me-2">
                                                <i class="bi bi-star-fill" th:each="i : ${#numbers.sequence(1, 5)}"
                                                   th:classappend="${i <= (engineer.rating ?: 5) ? '' : ' text-muted'}"></i>
                                            </span>
                                            <span class="text-muted" th:text="${engineer.rating ?: '5.0'} + ' (' + ${engineer.completedOrders ?: 0} + '单完成)'">5.0 (0单完成)</span>
                                        </div>
                                        <small class="text-muted" th:text="${#temporals.format(engineer.createdAt, 'yyyy-MM-dd HH:mm')} + ' 申请'">申请时间</small>
                                    </div>
                                </div>
                                <span class="status-badge" th:classappend="'status-' + ${engineer.status}"
                                      th:text="${engineer.status == 'pending' ? '待审核' :
                                               (engineer.status == 'approved' ? '已通过' :
                                               (engineer.status == 'rejected' ? '已拒绝' :
                                               (engineer.status == 'suspended' ? '已暂停' : '活跃中')))}">
                                    状态
                                </span>
                            </div>

                            <div class="engineer-info">
                                <div class="info-item">
                                    <span class="info-label">联系电话</span>
                                    <span class="info-value" th:text="${engineer.phone}">联系电话</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">工作经验</span>
                                    <span class="info-value" th:text="${engineer.experienceYears ?: 0} + '年'">工作经验</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">学历</span>
                                    <span class="info-value" th:text="${engineer.education ?: '未填写'}">学历</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">服务费用</span>
                                    <span class="info-value" th:text="'¥' + ${engineer.serviceFee ?: 0} + '/次'">服务费用</span>
                                </div>
                                <div class="info-item" th:if="${engineer.reviewTime}">
                                    <span class="info-label">审核时间</span>
                                    <span class="info-value" th:text="${#temporals.format(engineer.reviewTime, 'yyyy-MM-dd HH:mm')}">审核时间</span>
                                </div>
                            </div>

                            <!-- 专业领域 -->
                            <div class="mb-3" th:if="${engineer.specialties}">
                                <span class="info-label">专业领域：</span>
                                <div class="mt-1 specialties-container" th:data-specialties="${engineer.specialties}">
                                    <!-- 专业领域标签将通过JavaScript动态生成 -->
                                </div>
                            </div>

                            <!-- 个人介绍 -->
                            <div class="mb-3" th:if="${engineer.introduction}">
                                <span class="info-label">个人介绍</span>
                                <p class="info-value mb-0" th:text="${engineer.introduction}">个人介绍</p>
                            </div>

                            <!-- 操作按钮 -->
                            <div class="d-flex flex-wrap">
                                <!-- 待审核状态：可以审核 -->
                                <button th:if="${engineer.status == 'pending'}"
                                        class="action-btn btn-approve"
                                        onclick="showReviewModal(this, 'approve')"
                                        th:data-engineer-id="${engineer.id}">
                                    <i class="bi bi-check-circle me-1"></i>通过申请
                                </button>

                                <button th:if="${engineer.status == 'pending'}"
                                        class="action-btn btn-reject"
                                        onclick="showReviewModal(this, 'reject')"
                                        th:data-engineer-id="${engineer.id}">
                                    <i class="bi bi-x-circle me-1"></i>拒绝申请
                                </button>

                                <!-- 已通过状态：可以暂停 -->
                                <button th:if="${engineer.status == 'approved'}"
                                        class="action-btn btn-suspend"
                                        onclick="updateStatus(this, 'suspended')"
                                        th:data-engineer-id="${engineer.id}">
                                    <i class="bi bi-pause-circle me-1"></i>暂停服务
                                </button>

                                <!-- 已暂停状态：可以恢复 -->
                                <button th:if="${engineer.status == 'suspended'}"
                                        class="action-btn btn-activate"
                                        onclick="updateStatus(this, 'approved')"
                                        th:data-engineer-id="${engineer.id}">
                                    <i class="bi bi-play-circle me-1"></i>恢复服务
                                </button>

                                <button class="action-btn btn-outline-primary"
                                        onclick="showEditEngineerModal(this)"
                                        th:data-engineer-id="${engineer.id}">
                                    <i class="bi bi-pencil me-1"></i>编辑
                                </button>

                                <button class="action-btn btn-outline-secondary"
                                        onclick="viewEngineerDetail(this)"
                                        th:data-engineer-id="${engineer.id}">
                                    <i class="bi bi-eye me-1"></i>查看详情
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 分页 -->
                <nav th:if="${engineerPage.pages > 1}" aria-label="工程师分页">
                    <ul class="pagination justify-content-center">
                        <li class="page-item" th:classappend="${engineerPage.current == 1 ? 'disabled' : ''}">
                            <a class="page-link" th:href="@{/admin/engineers(page=${engineerPage.current - 1}, status=${currentStatus})}">上一页</a>
                        </li>

                        <li th:each="i : ${#numbers.sequence(1, engineerPage.pages)}"
                            class="page-item"
                            th:classappend="${i == engineerPage.current ? 'active' : ''}">
                            <a class="page-link" th:href="@{/admin/engineers(page=${i}, status=${currentStatus})}" th:text="${i}">1</a>
                        </li>

                        <li class="page-item" th:classappend="${engineerPage.current == engineerPage.pages ? 'disabled' : ''}">
                            <a class="page-link" th:href="@{/admin/engineers(page=${engineerPage.current + 1}, status=${currentStatus})}">下一页</a>
                        </li>
                    </ul>
                </nav>
            </main>
        </div>
    </div>

    <!-- 审核工程师模态框 -->
    <div class="modal fade" id="reviewEngineerModal" tabindex="-1" aria-labelledby="reviewEngineerModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="reviewEngineerModalLabel">
                        <i class="bi bi-person-check me-2"></i>审核工程师申请
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="reviewEngineerForm">
                        <div class="mb-3">
                            <label for="reviewAction" class="form-label">审核结果</label>
                            <select class="form-select" id="reviewAction" required>
                                <option value="">请选择审核结果</option>
                                <option value="approve">通过申请</option>
                                <option value="reject">拒绝申请</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="reviewNotes" class="form-label">审核备注</label>
                            <textarea class="form-control" id="reviewNotes" rows="3" placeholder="请填写审核意见或拒绝原因"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="confirmReviewBtn">确认审核</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 工程师详情模态框 -->
    <div class="modal fade" id="engineerDetailModal" tabindex="-1" aria-labelledby="engineerDetailModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="engineerDetailModalLabel">
                        <i class="bi bi-person me-2"></i>工程师详情
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="engineerDetailContent">
                        <!-- 工程师详情内容将通过JavaScript动态加载 -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑工程师模态框 -->
    <div class="modal fade" id="editEngineerModal" tabindex="-1" aria-labelledby="editEngineerModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editEngineerModalLabel">
                        <i class="bi bi-pencil me-2"></i>编辑工程师信息
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="editEngineerForm">
                        <input type="hidden" id="editEngineerId" name="id">

                        <!-- 标签导航 -->
                        <ul class="nav nav-tabs mb-4" id="editEngineerTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="edit-basic-info-tab" data-bs-toggle="tab" data-bs-target="#edit-basic-info" type="button" role="tab">
                                    <i class="bi bi-person me-1"></i>基本信息
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="edit-professional-info-tab" data-bs-toggle="tab" data-bs-target="#edit-professional-info" type="button" role="tab">
                                    <i class="bi bi-tools me-1"></i>专业信息
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="edit-service-info-tab" data-bs-toggle="tab" data-bs-target="#edit-service-info" type="button" role="tab">
                                    <i class="bi bi-currency-dollar me-1"></i>服务信息
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="edit-status-info-tab" data-bs-toggle="tab" data-bs-target="#edit-status-info" type="button" role="tab">
                                    <i class="bi bi-gear me-1"></i>状态设置
                                </button>
                            </li>
                        </ul>

                        <!-- 标签内容 -->
                        <div class="tab-content" id="editEngineerTabContent">
                            <!-- 基本信息标签 -->
                            <div class="tab-pane fade show active" id="edit-basic-info" role="tabpanel">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="editEngineerName" class="form-label">姓名 <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="editEngineerName" name="name" required>
                                        </div>
                                        <div class="mb-3">
                                            <label for="editEngineerPhone" class="form-label">联系电话 <span class="text-danger">*</span></label>
                                            <input type="tel" class="form-control" id="editEngineerPhone" name="phone" required>
                                        </div>
                                        <div class="mb-3">
                                            <label for="editEngineerEmail" class="form-label">邮箱</label>
                                            <input type="email" class="form-control" id="editEngineerEmail" name="email">
                                        </div>
                                        <div class="mb-3">
                                            <label for="editEngineerGender" class="form-label">性别</label>
                                            <select class="form-select" id="editEngineerGender" name="gender">
                                                <option value="">请选择</option>
                                                <option value="男">男</option>
                                                <option value="女">女</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="editEngineerAge" class="form-label">年龄</label>
                                            <input type="number" class="form-control" id="editEngineerAge" name="age" min="18" max="65">
                                        </div>
                                        <div class="mb-3">
                                            <label for="editEngineerEducation" class="form-label">学历</label>
                                            <select class="form-select" id="editEngineerEducation" name="education">
                                                <option value="">请选择</option>
                                                <option value="初中">初中</option>
                                                <option value="高中">高中</option>
                                                <option value="中专">中专</option>
                                                <option value="大专">大专</option>
                                                <option value="本科">本科</option>
                                                <option value="硕士">硕士</option>
                                                <option value="博士">博士</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label for="editEngineerExperience" class="form-label">工作经验（年）</label>
                                            <input type="number" class="form-control" id="editEngineerExperience" name="experienceYears" min="0" max="50">
                                        </div>
                                        <div class="mb-3">
                                            <label for="editEngineerWorkTime" class="form-label">工作时间</label>
                                            <input type="text" class="form-control" id="editEngineerWorkTime" name="workTime" placeholder="例如：周一至周五 9:00-18:00">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-12">
                                        <div class="mb-3">
                                            <label for="editEngineerIntroduction" class="form-label">个人介绍</label>
                                            <textarea class="form-control" id="editEngineerIntroduction" name="introduction" rows="3" placeholder="请简要介绍个人工作经历和专业技能"></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 专业信息标签 -->
                            <div class="tab-pane fade" id="edit-professional-info" role="tabpanel">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">专业领域</label>
                                            <div id="editSpecialtiesContainer">
                                                <!-- 动态生成的专业领域输入框 -->
                                            </div>
                                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="addEditSpecialty()">
                                                <i class="bi bi-plus me-1"></i>添加专业领域
                                            </button>
                                            <input type="hidden" id="editEngineerSpecialties" name="specialties">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">技能标签</label>
                                            <div id="editSkillsContainer">
                                                <!-- 动态生成的技能标签输入框 -->
                                            </div>
                                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="addEditSkill()">
                                                <i class="bi bi-plus me-1"></i>添加技能标签
                                            </button>
                                            <input type="hidden" id="editEngineerSkills" name="skills">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-12">
                                        <div class="mb-3">
                                            <label class="form-label">服务区域</label>
                                            <div id="editWorkAreasContainer">
                                                <!-- 动态生成的服务区域输入框 -->
                                            </div>
                                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="addEditWorkArea()">
                                                <i class="bi bi-plus me-1"></i>添加服务区域
                                            </button>
                                            <input type="hidden" id="editEngineerWorkAreas" name="workAreas">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 服务信息标签 -->
                            <div class="tab-pane fade" id="edit-service-info" role="tabpanel">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="editEngineerHourlyRate" class="form-label">时薪（元/小时）</label>
                                            <input type="number" class="form-control" id="editEngineerHourlyRate" name="hourlyRate" min="0" step="0.01">
                                        </div>
                                        <div class="mb-3">
                                            <label for="editEngineerServiceFee" class="form-label">上门服务费（元/次）</label>
                                            <input type="number" class="form-control" id="editEngineerServiceFee" name="serviceFee" min="0" step="0.01">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="editEngineerRating" class="form-label">评分</label>
                                            <input type="number" class="form-control" id="editEngineerRating" name="rating" min="0" max="5" step="0.1">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">服务统计（只读）</label>
                                            <div class="row">
                                                <div class="col-4">
                                                    <input type="text" class="form-control" id="editEngineerTotalOrders" readonly placeholder="总接单数">
                                                </div>
                                                <div class="col-4">
                                                    <input type="text" class="form-control" id="editEngineerCompletedOrders" readonly placeholder="完成订单数">
                                                </div>
                                                <div class="col-4">
                                                    <input type="text" class="form-control" id="editEngineerSuccessRate" readonly placeholder="成功率">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 状态设置标签 -->
                            <div class="tab-pane fade" id="edit-status-info" role="tabpanel">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="editEngineerStatus" class="form-label">工程师状态</label>
                                            <select class="form-select" id="editEngineerStatus" name="status">
                                                <option value="pending">待审核</option>
                                                <option value="approved">已通过</option>
                                                <option value="rejected">已拒绝</option>
                                                <option value="suspended">已暂停</option>
                                                <option value="active">活跃中</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="editEngineerIsOnline" name="isOnline">
                                                <label class="form-check-label" for="editEngineerIsOnline">
                                                    当前在线
                                                </label>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="editEngineerIsAvailable" name="isAvailable">
                                                <label class="form-check-label" for="editEngineerIsAvailable">
                                                    可接单
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="editEngineerReviewNotes" class="form-label">审核备注</label>
                                            <textarea class="form-control" id="editEngineerReviewNotes" name="reviewNotes" rows="4" placeholder="审核意见或备注信息"></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="confirmEditEngineerBtn">
                        <i class="bi bi-check me-1"></i>保存修改
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 图片预览模态框 -->
    <div id="imagePreviewModal" class="image-preview-modal" onclick="closeImagePreview()">
        <span class="image-preview-close" onclick="closeImagePreview()">&times;</span>
        <div class="image-preview-content">
            <img id="previewImage" class="image-preview-img" src="" alt="预览图片">
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 全局变量
        let currentEngineerId = null;
        let currentAction = null;

        // 筛选工程师
        function filterEngineers(status) {
            const url = new URL(window.location);
            if (status) {
                url.searchParams.set('status', status);
            } else {
                url.searchParams.delete('status');
            }
            url.searchParams.set('page', '1');
            window.location.href = url.toString();
        }

        // 显示审核模态框
        function showReviewModal(button, action) {
            const engineerId = button.dataset.engineerId;
            currentEngineerId = engineerId;
            currentAction = action;

            // 设置模态框标题和默认值
            const modalTitle = document.getElementById('reviewEngineerModalLabel');
            const actionSelect = document.getElementById('reviewAction');

            if (action === 'approve') {
                modalTitle.innerHTML = '<i class="bi bi-check-circle me-2"></i>通过工程师申请';
                actionSelect.value = 'approve';
            } else {
                modalTitle.innerHTML = '<i class="bi bi-x-circle me-2"></i>拒绝工程师申请';
                actionSelect.value = 'reject';
            }

            // 清空备注
            document.getElementById('reviewNotes').value = '';

            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('reviewEngineerModal'));
            modal.show();
        }

        // 更新工程师状态
        function updateStatus(button, newStatus) {
            const engineerId = button.dataset.engineerId;
            const statusText = {
                'suspended': '暂停服务',
                'approved': '恢复服务',
                'active': '激活'
            };

            if (confirm(`确定要${statusText[newStatus]}吗？`)) {
                fetch(`/admin/api/engineers/${engineerId}/status`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `status=${newStatus}`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(data.message);
                        location.reload();
                    } else {
                        alert(data.message || '操作失败');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('网络错误，请稍后重试');
                });
            }
        }

        // 确认审核
        document.getElementById('confirmReviewBtn').addEventListener('click', function() {
            const action = document.getElementById('reviewAction').value;
            const notes = document.getElementById('reviewNotes').value;

            if (!action) {
                alert('请选择审核结果');
                return;
            }

            if (action === 'reject' && !notes.trim()) {
                alert('拒绝申请时必须填写拒绝原因');
                return;
            }

            reviewEngineer(currentEngineerId, action, notes);
        });

        // 审核工程师
        function reviewEngineer(engineerId, action, notes) {
            let body = `action=${action}`;
            if (notes) {
                body += `&notes=${encodeURIComponent(notes)}`;
            }

            fetch(`/admin/api/engineers/${engineerId}/review`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: body
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.message);
                    bootstrap.Modal.getInstance(document.getElementById('reviewEngineerModal')).hide();
                    location.reload();
                } else {
                    alert(data.message || '审核失败');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('网络错误，请稍后重试');
            });
        }

        // 显示编辑工程师模态框
        function showEditEngineerModal(button) {
            const engineerId = button.dataset.engineerId;
            console.log('🔄 开始编辑工程师，ID:', engineerId);

            // 获取工程师详情
            fetch(`/admin/api/engineers/${engineerId}/detail`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        populateEditEngineerForm(data.data);
                        const editModal = new bootstrap.Modal(document.getElementById('editEngineerModal'));
                        editModal.show();
                    } else {
                        alert('获取工程师信息失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('获取工程师详情失败:', error);
                    alert('获取工程师信息失败，请重试');
                });
        }

        // 填充编辑工程师表单
        function populateEditEngineerForm(engineer) {
            console.log('📋 填充编辑表单:', engineer);

            // 设置工程师ID
            document.getElementById('editEngineerId').value = engineer.id;

            // 基本信息
            document.getElementById('editEngineerName').value = engineer.name || '';
            document.getElementById('editEngineerPhone').value = engineer.phone || '';
            document.getElementById('editEngineerEmail').value = engineer.email || '';
            document.getElementById('editEngineerGender').value = engineer.gender || '';
            document.getElementById('editEngineerAge').value = engineer.age || '';
            document.getElementById('editEngineerEducation').value = engineer.education || '';
            document.getElementById('editEngineerExperience').value = engineer.experienceYears || '';
            document.getElementById('editEngineerWorkTime').value = engineer.workTime || '';
            document.getElementById('editEngineerIntroduction').value = engineer.introduction || '';

            // 服务信息
            document.getElementById('editEngineerHourlyRate').value = engineer.hourlyRate || '';
            document.getElementById('editEngineerServiceFee').value = engineer.serviceFee || '';
            document.getElementById('editEngineerRating').value = engineer.rating || '';

            // 服务统计（只读）
            document.getElementById('editEngineerTotalOrders').value = (engineer.totalOrders || 0) + ' 单';
            document.getElementById('editEngineerCompletedOrders').value = (engineer.completedOrders || 0) + ' 单';
            document.getElementById('editEngineerSuccessRate').value = (engineer.successRate || 100) + '%';

            // 状态设置
            document.getElementById('editEngineerStatus').value = engineer.status || 'pending';
            document.getElementById('editEngineerIsOnline').checked = engineer.isOnline || false;
            document.getElementById('editEngineerIsAvailable').checked = engineer.isAvailable || false;
            document.getElementById('editEngineerReviewNotes').value = engineer.reviewNotes || '';

            // 处理JSON字段
            populateEditJsonFields(engineer);
        }

        // 填充编辑表单的JSON字段
        function populateEditJsonFields(engineer) {
            // 清空容器
            document.getElementById('editSpecialtiesContainer').innerHTML = '';
            document.getElementById('editSkillsContainer').innerHTML = '';
            document.getElementById('editWorkAreasContainer').innerHTML = '';

            // 专业领域
            if (engineer.specialties) {
                try {
                    const specialties = parseSpecialtiesData(engineer.specialties);
                    specialties.forEach(specialty => {
                        addEditSpecialty(specialty);
                    });
                } catch (e) {
                    console.error('解析专业领域失败:', e);
                }
            }
            if (document.getElementById('editSpecialtiesContainer').children.length === 0) {
                addEditSpecialty();
            }

            // 技能标签
            if (engineer.skills) {
                try {
                    const skills = parseSpecialtiesData(engineer.skills);
                    skills.forEach(skill => {
                        addEditSkill(skill);
                    });
                } catch (e) {
                    console.error('解析技能标签失败:', e);
                }
            }
            if (document.getElementById('editSkillsContainer').children.length === 0) {
                addEditSkill();
            }

            // 服务区域
            if (engineer.workAreas) {
                try {
                    const workAreas = parseSpecialtiesData(engineer.workAreas);
                    workAreas.forEach(area => {
                        addEditWorkArea(area);
                    });
                } catch (e) {
                    console.error('解析服务区域失败:', e);
                }
            }
            if (document.getElementById('editWorkAreasContainer').children.length === 0) {
                addEditWorkArea();
            }
        }

        // 查看工程师详情
        function viewEngineerDetail(button) {
            const engineerId = button.dataset.engineerId;

            // 加载工程师详情
            fetch(`/admin/api/engineers/${engineerId}/detail`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        renderEngineerDetail(data.data);
                        const modal = new bootstrap.Modal(document.getElementById('engineerDetailModal'));
                        modal.show();
                    } else {
                        alert('加载工程师详情失败：' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('网络错误，请稍后重试');
                });
        }

        // 渲染工程师详情
        function renderEngineerDetail(engineer) {
            const content = document.getElementById('engineerDetailContent');

            // 解析专业领域
            const specialties = parseSpecialtiesData(engineer.specialties);
            const specialtiesHtml = renderSpecialtyTags(specialties);

            // 解析技能标签
            const skills = parseSpecialtiesData(engineer.skills);
            const skillsHtml = renderSpecialtyTags(skills);

            // 解析工作区域
            const workAreas = parseSpecialtiesData(engineer.workAreas);
            const workAreasHtml = renderSpecialtyTags(workAreas);

            content.innerHTML = `
                <div class="row">
                    <!-- 基本信息 -->
                    <div class="col-md-6 mb-4">
                        <h6 class="border-bottom pb-2 mb-3">
                            <i class="bi bi-person me-2"></i>基本信息
                        </h6>
                        <table class="table table-borderless">
                            <tr><td class="fw-bold">姓名:</td><td>${engineer.name || '-'}</td></tr>
                            <tr><td class="fw-bold">联系电话:</td><td>${engineer.phone || '-'}</td></tr>
                            <tr><td class="fw-bold">邮箱:</td><td>${engineer.email || '-'}</td></tr>
                            <tr><td class="fw-bold">性别:</td><td>${engineer.gender || '-'}</td></tr>
                            <tr><td class="fw-bold">年龄:</td><td>${engineer.age || '-'}岁</td></tr>
                            <tr><td class="fw-bold">学历:</td><td>${engineer.education || '-'}</td></tr>
                            <tr><td class="fw-bold">工作经验:</td><td>${engineer.experienceYears || 0}年</td></tr>
                        </table>
                    </div>

                    <!-- 状态信息 -->
                    <div class="col-md-6 mb-4">
                        <h6 class="border-bottom pb-2 mb-3">
                            <i class="bi bi-info-circle me-2"></i>状态信息
                        </h6>
                        <table class="table table-borderless">
                            <tr><td class="fw-bold">当前状态:</td><td><span class="badge bg-${getStatusColor(engineer.status)}">${getStatusText(engineer.status)}</span></td></tr>
                            <tr><td class="fw-bold">是否在线:</td><td>${engineer.isOnline ? '在线' : '离线'}</td></tr>
                            <tr><td class="fw-bold">是否可接单:</td><td>${engineer.isAvailable ? '可接单' : '不可接单'}</td></tr>
                            <tr><td class="fw-bold">申请时间:</td><td>${formatDateTime(engineer.applicationTime || engineer.createdAt)}</td></tr>
                            ${engineer.reviewTime ? `<tr><td class="fw-bold">审核时间:</td><td>${formatDateTime(engineer.reviewTime)}</td></tr>` : ''}
                            <tr><td class="fw-bold">最后活跃:</td><td>${formatDateTime(engineer.lastActiveAt)}</td></tr>
                        </table>
                    </div>
                </div>

                <!-- 专业信息 -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h6 class="border-bottom pb-2 mb-3">
                            <i class="bi bi-tools me-2"></i>专业信息
                        </h6>
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>专业领域:</strong></p>
                                <div class="mb-3">${specialtiesHtml}</div>
                            </div>
                            <div class="col-md-6">
                                <p><strong>技能标签:</strong></p>
                                <div class="mb-3">${skillsHtml}</div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>服务区域:</strong></p>
                                <div class="mb-3">${workAreasHtml}</div>
                            </div>
                            <div class="col-md-6">
                                <p><strong>工作时间:</strong></p>
                                <p>${engineer.workTime || '未填写'}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 费用信息 -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h6 class="border-bottom pb-2 mb-3">
                            <i class="bi bi-currency-dollar me-2"></i>费用信息
                        </h6>
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr><td class="fw-bold">时薪:</td><td>¥${engineer.hourlyRate || 0}/小时</td></tr>
                                    <tr><td class="fw-bold">上门服务费:</td><td>¥${engineer.serviceFee || 0}/次</td></tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 评价信息 -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h6 class="border-bottom pb-2 mb-3">
                            <i class="bi bi-star me-2"></i>评价信息
                        </h6>
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr><td class="fw-bold">平均评分:</td><td>${engineer.rating || '5.0'} 分</td></tr>
                                    <tr><td class="fw-bold">总接单数:</td><td>${engineer.totalOrders || 0} 单</td></tr>
                                    <tr><td class="fw-bold">完成订单数:</td><td>${engineer.completedOrders || 0} 单</td></tr>
                                    <tr><td class="fw-bold">成功率:</td><td>${engineer.successRate || '100.00'}%</td></tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                ${engineer.introduction ? `
                    <!-- 个人介绍 -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="border-bottom pb-2 mb-3">
                                <i class="bi bi-chat-text me-2"></i>个人介绍
                            </h6>
                            <p class="bg-light p-3 rounded">${engineer.introduction}</p>
                        </div>
                    </div>
                ` : ''}

                <!-- 资质证书 -->
                ${engineer.certifications ? `
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="border-bottom pb-2 mb-3">
                                <i class="bi bi-award me-2"></i>资质证书
                            </h6>
                            <div class="certifications-gallery" data-certifications="${engineer.certifications.replace(/"/g, '&quot;')}">
                                <!-- 资质证书将通过JavaScript动态加载 -->
                            </div>
                        </div>
                    </div>
                ` : ''}

                <!-- 工作照片 -->
                ${engineer.workPhotos ? `
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="border-bottom pb-2 mb-3">
                                <i class="bi bi-camera me-2"></i>工作照片
                            </h6>
                            <div class="work-photos-gallery" data-work-photos="${engineer.workPhotos.replace(/"/g, '&quot;')}">
                                <!-- 工作照片将通过JavaScript动态加载 -->
                            </div>
                        </div>
                    </div>
                ` : ''}

                ${engineer.reviewNotes ? `
                    <!-- 审核备注 -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="border-bottom pb-2 mb-3">
                                <i class="bi bi-clipboard-check me-2"></i>审核备注
                            </h6>
                            <p class="bg-light p-3 rounded">${engineer.reviewNotes}</p>
                        </div>
                    </div>
                ` : ''}
            `;

            // 初始化图片显示
            initEngineerImages();
        }

        // 获取状态颜色
        function getStatusColor(status) {
            const colors = {
                'pending': 'warning',
                'approved': 'success',
                'rejected': 'danger',
                'suspended': 'secondary',
                'active': 'info'
            };
            return colors[status] || 'secondary';
        }

        // 获取状态文本
        function getStatusText(status) {
            const texts = {
                'pending': '待审核',
                'approved': '已通过',
                'rejected': '已拒绝',
                'suspended': '已暂停',
                'active': '活跃中'
            };
            return texts[status] || status;
        }

        // 显示图片预览
        function showImagePreview(imageSrc) {
            const modal = document.getElementById('imagePreviewModal');
            const previewImg = document.getElementById('previewImage');

            previewImg.src = imageSrc;
            modal.style.display = 'block';

            // 阻止事件冒泡
            if (event) {
                event.stopPropagation();
            }
        }

        // 关闭图片预览
        function closeImagePreview() {
            const modal = document.getElementById('imagePreviewModal');
            modal.style.display = 'none';
        }

        // 初始化工程师图片
        function initEngineerImages() {
            // 初始化资质证书图片
            const certificationsGalleries = document.querySelectorAll('.certifications-gallery');
            certificationsGalleries.forEach(gallery => {
                const certificationsData = gallery.getAttribute('data-certifications');
                if (certificationsData) {
                    try {
                        // 解码HTML实体
                        const decodedData = certificationsData.replace(/&quot;/g, '"');
                        const certifications = JSON.parse(decodedData);
                        renderCertificationImages(gallery, certifications);
                    } catch (e) {
                        console.error('解析资质证书数据失败:', e, certificationsData);
                    }
                }
            });

            // 初始化工作照片
            const workPhotosGalleries = document.querySelectorAll('.work-photos-gallery');
            workPhotosGalleries.forEach(gallery => {
                const workPhotosData = gallery.getAttribute('data-work-photos');
                if (workPhotosData) {
                    try {
                        // 解码HTML实体
                        const decodedData = workPhotosData.replace(/&quot;/g, '"');
                        const workPhotos = JSON.parse(decodedData);
                        renderWorkPhotoImages(gallery, workPhotos);
                    } catch (e) {
                        console.error('解析工作照片数据失败:', e, workPhotosData);
                    }
                }
            });
        }

        // 渲染资质证书图片
        function renderCertificationImages(gallery, certifications) {
            gallery.innerHTML = '';

            certifications.forEach((url, index) => {
                const img = document.createElement('img');
                img.src = processImageUrl(url);
                img.className = 'certificate-image';
                img.alt = `资质证书 ${index + 1}`;
                img.onclick = () => showImagePreview(img.src);

                img.onload = () => console.log(`✅ 资质证书${index}加载成功:`, img.src);
                img.onerror = () => {
                    console.error(`❌ 资质证书${index}加载失败:`, img.src);
                    img.style.display = 'none';
                };

                gallery.appendChild(img);
            });
        }

        // 渲染工作照片
        function renderWorkPhotoImages(gallery, workPhotos) {
            gallery.innerHTML = '';

            workPhotos.forEach((url, index) => {
                const img = document.createElement('img');
                img.src = processImageUrl(url);
                img.className = 'work-photo-image';
                img.alt = `工作照片 ${index + 1}`;
                img.onclick = () => showImagePreview(img.src);

                img.onload = () => console.log(`✅ 工作照片${index}加载成功:`, img.src);
                img.onerror = () => {
                    console.error(`❌ 工作照片${index}加载失败:`, img.src);
                    img.style.display = 'none';
                };

                gallery.appendChild(img);
            });
        }

        // 处理图片URL，避免重复路径
        function processImageUrl(url) {
            if (url.startsWith('http')) {
                return url;
            } else if (url.startsWith('/uploads/')) {
                return url;
            } else {
                return '/uploads/' + url;
            }
        }

        // 格式化日期时间
        function formatDateTime(dateTime) {
            if (!dateTime) return '-';
            const date = new Date(dateTime);
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        // 解析专业领域数据的通用函数
        function parseSpecialtiesData(specialtiesData) {
            if (!specialtiesData) return [];

            console.log('🔍 解析专业领域数据:', specialtiesData);

            // 如果是字符串，尝试多种解析方式
            if (typeof specialtiesData === 'string') {
                // 1. 尝试JSON解析
                try {
                    const parsed = JSON.parse(specialtiesData);
                    if (Array.isArray(parsed)) {
                        console.log('✅ JSON解析成功:', parsed);
                        return parsed.map(item => String(item).trim()).filter(item => item);
                    }
                } catch (e) {
                    console.log('⚠️ JSON解析失败，尝试其他方式');
                }

                // 2. 移除JSON格式字符，然后按逗号分割
                let cleanData = specialtiesData
                    .replace(/^\[|\]$/g, '')  // 移除首尾的方括号
                    .replace(/"/g, '')        // 移除所有引号
                    .replace(/'/g, '')        // 移除所有单引号
                    .trim();

                if (cleanData) {
                    const result = cleanData.split(',').map(item => item.trim()).filter(item => item);
                    console.log('✅ 清理后解析成功:', result);
                    return result;
                }
            }

            // 3. 如果已经是数组
            if (Array.isArray(specialtiesData)) {
                console.log('✅ 已是数组:', specialtiesData);
                return specialtiesData.map(item => String(item).trim()).filter(item => item);
            }

            console.log('⚠️ 无法解析，返回空数组');
            return [];
        }

        // 编辑表单的动态字段管理函数
        function addEditSpecialty(value = '') {
            const container = document.getElementById('editSpecialtiesContainer');
            const div = document.createElement('div');
            div.className = 'input-group mb-2';
            div.innerHTML = `
                <input type="text" class="form-control" placeholder="输入专业领域" value="${value}" onchange="updateEditSpecialties()">
                <button class="btn btn-outline-danger" type="button" onclick="removeEditSpecialty(this)">
                    <i class="bi bi-trash"></i>
                </button>
            `;
            container.appendChild(div);
            updateEditSpecialties();
        }

        function removeEditSpecialty(button) {
            button.parentElement.remove();
            updateEditSpecialties();
        }

        function updateEditSpecialties() {
            const container = document.getElementById('editSpecialtiesContainer');
            if (!container) return;

            const inputs = container.querySelectorAll('input');
            const specialties = Array.from(inputs)
                .map(input => input.value)
                .filter(value => value && value.trim() !== '');

            const hiddenInput = document.getElementById('editEngineerSpecialties');
            if (hiddenInput) {
                hiddenInput.value = JSON.stringify(specialties);
            }
        }

        function addEditSkill(value = '') {
            const container = document.getElementById('editSkillsContainer');
            const div = document.createElement('div');
            div.className = 'input-group mb-2';
            div.innerHTML = `
                <input type="text" class="form-control" placeholder="输入技能标签" value="${value}" onchange="updateEditSkills()">
                <button class="btn btn-outline-danger" type="button" onclick="removeEditSkill(this)">
                    <i class="bi bi-trash"></i>
                </button>
            `;
            container.appendChild(div);
            updateEditSkills();
        }

        function removeEditSkill(button) {
            button.parentElement.remove();
            updateEditSkills();
        }

        function updateEditSkills() {
            const container = document.getElementById('editSkillsContainer');
            if (!container) return;

            const inputs = container.querySelectorAll('input');
            const skills = Array.from(inputs)
                .map(input => input.value)
                .filter(value => value && value.trim() !== '');

            const hiddenInput = document.getElementById('editEngineerSkills');
            if (hiddenInput) {
                hiddenInput.value = JSON.stringify(skills);
            }
        }

        function addEditWorkArea(value = '') {
            const container = document.getElementById('editWorkAreasContainer');
            const div = document.createElement('div');
            div.className = 'input-group mb-2';
            div.innerHTML = `
                <input type="text" class="form-control" placeholder="输入服务区域" value="${value}" onchange="updateEditWorkAreas()">
                <button class="btn btn-outline-danger" type="button" onclick="removeEditWorkArea(this)">
                    <i class="bi bi-trash"></i>
                </button>
            `;
            container.appendChild(div);
            updateEditWorkAreas();
        }

        function removeEditWorkArea(button) {
            button.parentElement.remove();
            updateEditWorkAreas();
        }

        function updateEditWorkAreas() {
            const container = document.getElementById('editWorkAreasContainer');
            if (!container) return;

            const inputs = container.querySelectorAll('input');
            const workAreas = Array.from(inputs)
                .map(input => input.value)
                .filter(value => value && value.trim() !== '');

            const hiddenInput = document.getElementById('editEngineerWorkAreas');
            if (hiddenInput) {
                hiddenInput.value = JSON.stringify(workAreas);
            }
        }

        // 渲染专业领域标签
        function renderSpecialtyTags(specialties) {
            if (!specialties || specialties.length === 0) {
                return '<span class="text-muted">未填写</span>';
            }

            return specialties.map(specialty =>
                `<span class="specialty-tag">${specialty}</span>`
            ).join('');
        }

        // 加载工程师头像
        function loadEngineerAvatars() {
            console.log('🚀 开始加载工程师头像');

            const avatarContainers = document.querySelectorAll('.engineer-avatar-large');
            console.log(`📋 找到 ${avatarContainers.length} 个头像容器`);

            avatarContainers.forEach((container, index) => {
                const engineerId = container.getAttribute('data-engineer-id');
                const avatarUrl = container.getAttribute('data-avatar');

                console.log(`🔧 处理头像 ${index + 1}: engineerId=${engineerId}, avatarUrl=${avatarUrl}`);

                if (avatarUrl && avatarUrl.trim() !== '') {
                    // 有头像URL，显示头像图片
                    const img = document.createElement('img');
                    img.src = processImageUrl(avatarUrl);
                    img.alt = '工程师头像';
                    img.onerror = function() {
                        console.error(`❌ 头像加载失败: ${img.src}`);
                        // 头像加载失败，显示默认图标
                        container.innerHTML = '<div class="avatar-placeholder"><i class="bi bi-person-fill"></i></div>';
                    };
                    img.onload = function() {
                        console.log(`✅ 头像加载成功: ${img.src}`);
                    };

                    // 清空容器并添加图片
                    container.innerHTML = '';
                    container.appendChild(img);
                } else {
                    // 没有头像URL，显示默认图标
                    container.innerHTML = '<div class="avatar-placeholder"><i class="bi bi-person-fill"></i></div>';
                    console.log(`⚠️ 工程师 ${engineerId} 没有头像，显示默认图标`);
                }
            });

            console.log('🎉 工程师头像加载完成');
        }

        // 处理图片URL，避免重复路径
        function processImageUrl(url) {
            if (!url) return '';

            if (url.startsWith('http')) {
                return url;
            } else if (url.startsWith('/uploads/')) {
                return url;
            } else {
                return '/uploads/' + url;
            }
        }

        // 确认编辑工程师
        document.getElementById('confirmEditEngineerBtn').addEventListener('click', function() {
            console.log('🔄 开始提交编辑表单...');

            try {
                // 更新JSON字段
                updateEditSpecialties();
                updateEditSkills();
                updateEditWorkAreas();

                const form = document.getElementById('editEngineerForm');
                if (!form) {
                    alert('编辑表单未找到');
                    return;
                }

                const formData = new FormData(form);
                const engineerId = document.getElementById('editEngineerId').value;

                if (!engineerId) {
                    alert('工程师ID未找到');
                    return;
                }

                // 验证必填字段
                const requiredFields = ['name', 'phone'];
                let isValid = true;

                for (const field of requiredFields) {
                    const input = form.querySelector(`[name="${field}"]`);
                    if (!input || !input.value.trim()) {
                        if (input) input.classList.add('is-invalid');
                        isValid = false;
                    } else {
                        input.classList.remove('is-invalid');
                    }
                }

                if (!isValid) {
                    alert('请填写所有必填字段');
                    return;
                }

                // 处理复选框值
                formData.set('isOnline', document.getElementById('editEngineerIsOnline').checked ? 'true' : 'false');
                formData.set('isAvailable', document.getElementById('editEngineerIsAvailable').checked ? 'true' : 'false');

                console.log('📤 提交编辑数据，工程师ID:', engineerId);

                // 提交数据
                fetch(`/admin/api/engineers/${engineerId}`, {
                    method: 'PUT',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    console.log('📥 编辑响应:', data);
                    if (data.success) {
                        alert('工程师信息编辑成功！');
                        bootstrap.Modal.getInstance(document.getElementById('editEngineerModal')).hide();
                        location.reload();
                    } else {
                        alert('编辑失败：' + (data.message || '未知错误'));
                    }
                })
                .catch(error => {
                    console.error('编辑工程师时发生错误:', error);
                    alert('网络错误，请稍后重试');
                });

            } catch (error) {
                console.error('编辑表单处理错误:', error);
                alert('表单处理错误：' + error.message);
            }
        });

        // 页面加载完成后处理专业领域显示和头像加载
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 开始处理页面初始化');

            // 加载工程师头像
            loadEngineerAvatars();

            // 处理所有专业领域容器
            const specialtiesContainers = document.querySelectorAll('.specialties-container');
            console.log(`📋 找到 ${specialtiesContainers.length} 个专业领域容器`);

            specialtiesContainers.forEach((container, index) => {
                const specialtiesData = container.getAttribute('data-specialties');
                console.log(`🔧 处理容器 ${index + 1}:`, specialtiesData);

                if (specialtiesData) {
                    const specialties = parseSpecialtiesData(specialtiesData);
                    container.innerHTML = renderSpecialtyTags(specialties);
                    console.log(`✅ 容器 ${index + 1} 处理完成，显示 ${specialties.length} 个标签`);
                } else {
                    container.innerHTML = '<span class="text-muted">未填写</span>';
                    console.log(`⚠️ 容器 ${index + 1} 无数据`);
                }
            });

            console.log('🎉 页面初始化完成');
        });
    </script>
</body>
</html>
