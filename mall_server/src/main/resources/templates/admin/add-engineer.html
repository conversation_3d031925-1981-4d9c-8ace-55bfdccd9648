<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>添加工程师 - 桩郎中管理后台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        /* 采用小程序端配色方案 */
        :root {
            --primary-color: #1e88e5;
            --secondary-color: #64b5f6;
            --accent-color: #0d47a1;
            --text-color: #333333;
            --light-text: #757575;
            --background-color: #f5f5f5;
            --white: #ffffff;
            --border-color: #e0e0e0;
            --success-color: #4caf50;
            --warning-color: #ff9800;
            --error-color: #f44336;
            --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.05);
            --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.08);
        }

        body {
            background-color: var(--background-color);
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            color: var(--text-color);
            line-height: 1.5;
        }

        .sidebar {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
            min-height: 100vh;
            box-shadow: var(--shadow-medium);
        }

        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 1rem 1.5rem;
            border-radius: 8px;
            margin: 0.25rem 1rem;
            transition: all 0.3s ease;
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background-color: rgba(255,255,255,0.2);
            color: white;
            transform: translateX(5px);
        }

        .main-content {
            padding: 2rem;
        }

        .user-info {
            background: rgba(255,255,255,0.15);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 1.5rem 1rem;
            margin-bottom: 1.5rem;
            text-align: center;
            border: 1px solid rgba(255,255,255,0.2);
            box-shadow: var(--shadow-light);
        }

        .user-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: var(--white);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            color: var(--primary-color);
            font-size: 1.5rem;
            box-shadow: var(--shadow-light);
            position: relative;
        }

        .user-avatar::after {
            content: '';
            position: absolute;
            inset: -2px;
            border-radius: 50%;
            padding: 2px;
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            mask-composite: exclude;
        }

        .user-info h6 {
            color: var(--white) !important;
            font-weight: 600;
            margin-bottom: 0.5rem !important;
            font-size: 1rem;
        }

        .user-info small {
            color: rgba(255,255,255,0.8) !important;
            font-size: 0.85rem;
            font-weight: 500;
        }

        .form-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .form-section {
            margin-bottom: 2rem;
            padding-bottom: 1.5rem;
            border-bottom: 1px solid #e9ecef;
        }

        .form-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .section-title {
            color: var(--primary-color);
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
        }

        .section-title i {
            margin-right: 0.5rem;
        }

        .form-label {
            color: var(--text-color);
            font-weight: 500;
            margin-bottom: 0.5rem;
        }

        .form-control, .form-select {
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 0.75rem;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(30, 136, 229, 0.25);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
            border: none;
            border-radius: 8px;
            padding: 0.75rem 2rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(30, 136, 229, 0.3);
        }

        .btn-secondary {
            background-color: #6c757d;
            border: none;
            border-radius: 8px;
            padding: 0.75rem 2rem;
            font-weight: 500;
        }

        .avatar-upload {
            text-align: center;
            margin-bottom: 1rem;
        }

        .avatar-preview {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            border: 3px solid var(--border-color);
            object-fit: cover;
            margin-bottom: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .avatar-preview:hover {
            border-color: var(--secondary-color);
            transform: scale(1.05);
        }

        .upload-btn {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
            color: white;
            border: none;
            border-radius: 20px;
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .upload-btn:hover {
            background: linear-gradient(135deg, var(--accent-color) 0%, var(--primary-color) 100%);
            transform: translateY(-1px);
        }

        .tag-input {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-top: 0.5rem;
        }

        .tag {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .tag .remove-tag {
            cursor: pointer;
            font-weight: bold;
        }

        .required {
            color: var(--error-color);
        }

        .loading {
            display: none;
        }

        .loading.show {
            display: inline-block;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 左侧导航 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="user-info">
                        <div class="user-avatar">
                            <i class="fas fa-user-shield"></i>
                        </div>
                        <h6 th:text="${admin?.realName ?: '超级管理员'}">超级管理员</h6>
                        <small th:text="${admin?.role ?: 'super_admin'}">super_admin</small>
                    </div>

                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/index">
                                <i class="bi bi-house-door-fill me-2"></i>
                                仪表盘
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="/admin/engineers">
                                <i class="bi bi-people-fill me-2"></i>
                                工程师管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/service-centers">
                                <i class="bi bi-building me-2"></i>
                                服务网点管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/products">
                                <i class="bi bi-box-seam me-2"></i>
                                产品管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/orders">
                                <i class="fas fa-clipboard-list me-2"></i>
                                订单管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/customer-service">
                                <i class="fas fa-comments me-2"></i>
                                在线客服
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/logout">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                退出登录
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <!-- 顶部导航 -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="bi bi-person-plus text-primary me-2"></i>
                        添加工程师
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <a href="/admin/engineers" class="btn btn-sm btn-outline-secondary">
                                <i class="bi bi-arrow-left"></i> 返回列表
                            </a>
                        </div>
                    </div>
                </div>

                <!-- 表单内容 -->
                <div class="form-card">
                    <form id="engineerForm">
                <!-- 基本信息 -->
                <div class="form-section">
                    <h5 class="section-title">
                        <i class="bi bi-person"></i>基本信息
                    </h5>
                    
                    <!-- 头像上传 -->
                    <div class="avatar-upload">
                        <img id="avatarPreview" src="/images/default-avatar.png" alt="头像预览" class="avatar-preview" onclick="document.getElementById('avatarInput').click()">
                        <br>
                        <input type="file" id="avatarInput" accept="image/*" style="display: none;" onchange="handleAvatarUpload(this)">
                        <button type="button" class="upload-btn" onclick="document.getElementById('avatarInput').click()">
                            <i class="bi bi-camera me-1"></i>上传头像
                        </button>
                        <input type="hidden" id="avatar" name="avatar">
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">姓名 <span class="required">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">联系电话 <span class="required">*</span></label>
                            <input type="tel" class="form-control" id="phone" name="phone" required>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">邮箱地址</label>
                            <input type="email" class="form-control" id="email" name="email">
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="gender" class="form-label">性别</label>
                            <select class="form-select" id="gender" name="gender">
                                <option value="">请选择</option>
                                <option value="男">男</option>
                                <option value="女">女</option>
                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="age" class="form-label">年龄</label>
                            <input type="number" class="form-control" id="age" name="age" min="18" max="65">
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="idCard" class="form-label">身份证号</label>
                        <input type="text" class="form-control" id="idCard" name="idCard" maxlength="18">
                    </div>
                </div>

                <!-- 专业信息 -->
                <div class="form-section">
                    <h5 class="section-title">
                        <i class="bi bi-tools"></i>专业信息
                    </h5>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="specialties" class="form-label">专业领域</label>
                            <input type="text" class="form-control" id="specialtyInput" placeholder="输入专业领域后按回车添加">
                            <div class="tag-input" id="specialtiesTags"></div>
                            <input type="hidden" id="specialties" name="specialties">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="experienceYears" class="form-label">工作经验（年）</label>
                            <input type="number" class="form-control" id="experienceYears" name="experienceYears" min="0" max="50">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="education" class="form-label">学历</label>
                            <select class="form-select" id="education" name="education">
                                <option value="">请选择</option>
                                <option value="初中">初中</option>
                                <option value="高中">高中</option>
                                <option value="中专">中专</option>
                                <option value="大专">大专</option>
                                <option value="本科">本科</option>
                                <option value="硕士">硕士</option>
                                <option value="博士">博士</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="certifications" class="form-label">资质证书</label>
                            <input type="text" class="form-control" id="certificationInput" placeholder="输入证书名称后按回车添加">
                            <div class="tag-input" id="certificationsTags"></div>
                            <input type="hidden" id="certifications" name="certifications">
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="skills" class="form-label">技能标签</label>
                        <input type="text" class="form-control" id="skillInput" placeholder="输入技能标签后按回车添加">
                        <div class="tag-input" id="skillsTags"></div>
                        <input type="hidden" id="skills" name="skills">
                    </div>
                </div>

                <!-- 工作信息 -->
                <div class="form-section">
                    <h5 class="section-title">
                        <i class="bi bi-briefcase"></i>工作信息
                    </h5>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="workAreas" class="form-label">服务区域</label>
                            <input type="text" class="form-control" id="workAreaInput" placeholder="输入服务区域后按回车添加">
                            <div class="tag-input" id="workAreasTags"></div>
                            <input type="hidden" id="workAreas" name="workAreas">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="workTime" class="form-label">工作时间</label>
                            <input type="text" class="form-control" id="workTime" name="workTime" placeholder="例如：周一至周日 8:00-18:00">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="hourlyRate" class="form-label">时薪（元/小时）</label>
                            <input type="number" class="form-control" id="hourlyRate" name="hourlyRate" min="0" step="0.01">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="serviceFee" class="form-label">上门服务费（元/次）</label>
                            <input type="number" class="form-control" id="serviceFee" name="serviceFee" min="0" step="0.01">
                        </div>
                    </div>
                </div>

                <!-- 个人介绍 -->
                <div class="form-section">
                    <h5 class="section-title">
                        <i class="bi bi-chat-text"></i>个人介绍
                    </h5>

                    <div class="mb-3">
                        <label for="bio" class="form-label">个人简介</label>
                        <textarea class="form-control" id="bio" name="bio" rows="3" placeholder="简短的个人介绍"></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="introduction" class="form-label">详细介绍</label>
                        <textarea class="form-control" id="introduction" name="introduction" rows="5" placeholder="详细的工作经历和专业能力介绍"></textarea>
                    </div>
                </div>

                <!-- 状态设置 -->
                <div class="form-section">
                    <h5 class="section-title">
                        <i class="bi bi-gear"></i>状态设置
                    </h5>

                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="status" class="form-label">审核状态</label>
                            <select class="form-select" id="status" name="status">
                                <option value="approved">已通过</option>
                                <option value="pending">待审核</option>
                                <option value="rejected">已拒绝</option>
                                <option value="suspended">已暂停</option>
                            </select>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="isOnline" class="form-label">在线状态</label>
                            <select class="form-select" id="isOnline" name="isOnline">
                                <option value="false">离线</option>
                                <option value="true">在线</option>
                            </select>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="isAvailable" class="form-label">接单状态</label>
                            <select class="form-select" id="isAvailable" name="isAvailable">
                                <option value="true">可接单</option>
                                <option value="false">不可接单</option>
                            </select>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="sortOrder" class="form-label">排序权重</label>
                        <input type="number" class="form-control" id="sortOrder" name="sortOrder" value="0" min="0">
                        <small class="form-text text-muted">数值越大排序越靠前</small>
                    </div>
                </div>

                <!-- 提交按钮 -->
                <div class="text-center">
                    <button type="button" class="btn btn-secondary me-3" onclick="history.back()">
                        <i class="bi bi-arrow-left me-1"></i>取消
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <span class="loading spinner-border spinner-border-sm me-2" role="status"></span>
                        <i class="bi bi-check-lg me-1"></i>添加工程师
                    </button>
                </div>
                    </form>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 标签管理功能
        const tagManagers = {
            specialties: { input: 'specialtyInput', container: 'specialtiesTags', hidden: 'specialties', tags: [] },
            certifications: { input: 'certificationInput', container: 'certificationsTags', hidden: 'certifications', tags: [] },
            skills: { input: 'skillInput', container: 'skillsTags', hidden: 'skills', tags: [] },
            workAreas: { input: 'workAreaInput', container: 'workAreasTags', hidden: 'workAreas', tags: [] }
        };

        // 初始化标签输入
        Object.keys(tagManagers).forEach(key => {
            const manager = tagManagers[key];
            const input = document.getElementById(manager.input);
            
            input.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    addTag(key, this.value.trim());
                    this.value = '';
                }
            });
        });

        function addTag(type, value) {
            if (!value || tagManagers[type].tags.includes(value)) return;
            
            tagManagers[type].tags.push(value);
            renderTags(type);
            updateHiddenInput(type);
        }

        function removeTag(type, value) {
            const index = tagManagers[type].tags.indexOf(value);
            if (index > -1) {
                tagManagers[type].tags.splice(index, 1);
                renderTags(type);
                updateHiddenInput(type);
            }
        }

        function renderTags(type) {
            const container = document.getElementById(tagManagers[type].container);
            container.innerHTML = tagManagers[type].tags.map(tag => 
                `<span class="tag">
                    ${tag}
                    <span class="remove-tag" onclick="removeTag('${type}', '${tag}')">&times;</span>
                </span>`
            ).join('');
        }

        function updateHiddenInput(type) {
            const hidden = document.getElementById(tagManagers[type].hidden);
            hidden.value = JSON.stringify(tagManagers[type].tags);
        }

        // 头像上传处理
        function handleAvatarUpload(input) {
            if (input.files && input.files[0]) {
                const file = input.files[0];
                
                // 验证文件类型
                if (!file.type.startsWith('image/')) {
                    alert('请选择图片文件');
                    return;
                }
                
                // 验证文件大小（5MB）
                if (file.size > 5 * 1024 * 1024) {
                    alert('图片大小不能超过5MB');
                    return;
                }
                
                // 预览图片
                const reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById('avatarPreview').src = e.target.result;
                };
                reader.readAsDataURL(file);
                
                // 上传图片
                uploadAvatar(file);
            }
        }

        function uploadAvatar(file) {
            const formData = new FormData();
            formData.append('file', file);
            
            fetch('/api/upload/image', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('avatar').value = data.data.url;
                    console.log('头像上传成功:', data.data.url);
                } else {
                    alert('头像上传失败：' + data.message);
                }
            })
            .catch(error => {
                console.error('头像上传错误:', error);
                alert('头像上传失败，请重试');
            });
        }

        // 表单提交
        document.getElementById('engineerForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const submitBtn = this.querySelector('button[type="submit"]');
            const loading = submitBtn.querySelector('.loading');
            
            // 显示加载状态
            loading.classList.add('show');
            submitBtn.disabled = true;
            
            // 收集表单数据
            const formData = new FormData(this);
            const engineerData = {};
            
            for (let [key, value] of formData.entries()) {
                engineerData[key] = value;
            }
            
            // 转换布尔值
            engineerData.isOnline = engineerData.isOnline === 'true';
            engineerData.isAvailable = engineerData.isAvailable === 'true';
            
            console.log('提交工程师数据:', engineerData);
            
            // 提交到后端
            fetch('/api/admin/engineers/add', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(engineerData)
            })
            .then(response => response.json())
            .then(data => {
                loading.classList.remove('show');
                submitBtn.disabled = false;
                
                if (data.success) {
                    alert('工程师添加成功！');
                    window.location.href = '/admin/engineers';
                } else {
                    alert('添加失败：' + data.message);
                }
            })
            .catch(error => {
                loading.classList.remove('show');
                submitBtn.disabled = false;
                console.error('提交错误:', error);
                alert('提交失败，请重试');
            });
        });
    </script>
</body>
</html>
