<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>在线客服工作台 - 成都桩郎中新能源技术有限公司</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #1e88e5;
            --primary-dark: #0d47a1;
            --success-color: #4caf50;
            --warning-color: #ff9800;
            --danger-color: #f44336;
            --light-bg: #f8f9fa;
            --border-color: #e0e0e0;
            --text-color: #333;
            --light-text: #666;
            --shadow: 0 2px 10px rgba(0,0,0,0.1);
            --chat-bg: #f5f7fa;
            --user-msg-bg: #f0f0f0;
            --admin-msg-bg: #1e88e5;
        }

        body {
            background-color: var(--light-bg);
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            color: var(--text-color);
            line-height: 1.5;
        }

        /* 管理后台通用侧边栏样式 */
        .admin-sidebar {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            min-height: 100vh;
            box-shadow: var(--shadow);
        }

        .admin-sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 1rem 1.5rem;
            border-radius: 8px;
            margin: 0.25rem 1rem;
            transition: all 0.3s ease;
        }

        .admin-sidebar .nav-link:hover,
        .admin-sidebar .nav-link.active {
            background-color: rgba(255,255,255,0.2);
            color: white;
            transform: translateX(5px);
        }

        .user-info {
            background: rgba(255,255,255,0.15);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 1.5rem 1rem;
            margin-bottom: 1.5rem;
            text-align: center;
            border: 1px solid rgba(255,255,255,0.2);
            box-shadow: var(--shadow);
        }

        .user-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            color: var(--primary-color);
            font-size: 1.5rem;
            box-shadow: var(--shadow);
        }

        .user-info h6 {
            color: white !important;
            font-weight: 600;
            margin-bottom: 0.5rem !important;
            font-size: 1rem;
        }

        .user-info small {
            color: rgba(255,255,255,0.8) !important;
            font-size: 0.85rem;
            font-weight: 500;
        }

        /* 客服工作区域 */
        .customer-service-container {
            display: flex;
            height: 100vh;
            background: white;
        }

        /* 客服会话侧边栏 */
        .cs-sidebar {
            width: 350px;
            background: white;
            border-right: 1px solid var(--border-color);
            display: flex;
            flex-direction: column;
        }

        .sidebar-header {
            padding: 20px;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            text-align: center;
        }

        .sidebar-header h4 {
            margin: 0;
            font-weight: 600;
        }

        .admin-info {
            font-size: 0.9rem;
            opacity: 0.9;
            margin-top: 5px;
        }

        .session-list {
            flex: 1;
            overflow-y: auto;
            padding: 10px;
        }

        .session-section {
            margin-bottom: 20px;
        }

        .section-title {
            font-size: 0.85rem;
            font-weight: 600;
            color: var(--light-text);
            margin-bottom: 10px;
            padding: 0 10px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .session-item {
            padding: 15px;
            margin-bottom: 8px;
            background: white;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
        }

        .session-item:hover {
            background: #f8f9fa;
            border-color: var(--primary-color);
            transform: translateY(-1px);
            box-shadow: var(--shadow);
        }

        .session-item.active {
            background: var(--user-msg-bg);
            border-color: var(--primary-color);
            box-shadow: var(--shadow);
        }

        .session-item.waiting {
            border-left: 4px solid var(--warning-color);
        }

        .session-user {
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: 5px;
        }

        .session-message {
            font-size: 0.9rem;
            color: var(--light-text);
            margin-bottom: 5px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .session-time {
            font-size: 0.8rem;
            color: var(--light-text);
        }

        .unread-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            background: var(--danger-color);
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.7rem;
            font-weight: 600;
        }

        /* 聊天区域 */
        .chat-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: var(--chat-bg);
        }

        .chat-header {
            padding: 20px;
            background: white;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .chat-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--text-color);
        }

        .chat-actions {
            display: flex;
            gap: 10px;
        }

        .btn-action {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: var(--primary-dark);
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn-warning {
            background: var(--warning-color);
            color: white;
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .message {
            display: flex;
            align-items: flex-end;
            gap: 10px;
            max-width: 70%;
        }

        .message.user {
            align-self: flex-start;
            flex-direction: row;
        }

        .message.admin {
            align-self: flex-end;
            flex-direction: row-reverse;
        }

        .message-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.9rem;
            font-weight: 600;
            flex-shrink: 0;
        }

        .message.user .message-avatar {
            background: #9e9e9e;
        }

        .message.admin .message-avatar {
            background: var(--primary-color);
        }

        .message-content {
            background: white;
            padding: 12px 16px;
            border-radius: 18px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            position: relative;
        }

        .message.user .message-content {
            background: var(--user-msg-bg);
            border-bottom-left-radius: 6px;
        }

        .message.admin .message-content {
            background: var(--admin-msg-bg);
            border-bottom-right-radius: 6px;
            color: white;
        }

        .message.admin .message-time {
            color: rgba(255, 255, 255, 0.8);
        }

        .message-text {
            line-height: 1.4;
            word-wrap: break-word;
        }

        .message.user .message-text {
            color: var(--text-color);
        }

        .message.admin .message-text {
            color: white;
        }

        .message-time {
            font-size: 0.75rem;
            color: var(--light-text);
            margin-top: 5px;
        }

        .empty-chat {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: var(--light-text);
        }

        .empty-chat i {
            font-size: 3rem;
            margin-bottom: 15px;
            opacity: 0.5;
        }

        /* 快捷回复 */
        .quick-replies {
            padding: 15px 20px;
            background: white;
            border-top: 1px solid var(--border-color);
        }

        .quick-replies-title {
            font-size: 0.85rem;
            color: var(--light-text);
            margin-bottom: 10px;
            font-weight: 600;
        }

        .quick-reply-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .quick-reply-btn {
            padding: 6px 12px;
            background: var(--light-bg);
            border: 1px solid var(--border-color);
            border-radius: 15px;
            font-size: 0.8rem;
            color: var(--text-color);
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .quick-reply-btn:hover {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        /* 输入区域 */
        .chat-input {
            padding: 20px;
            background: white;
            border-top: 1px solid var(--border-color);
        }

        .input-group {
            display: flex;
            align-items: flex-end;
            gap: 10px;
        }

        .message-input {
            flex: 1;
            border: 1px solid var(--border-color);
            border-radius: 20px;
            padding: 12px 16px;
            font-size: 0.9rem;
            resize: none;
            outline: none;
            transition: border-color 0.2s ease;
            max-height: 100px;
        }

        .message-input:focus {
            border-color: var(--primary-color);
        }

        .send-btn {
            width: 44px;
            height: 44px;
            border: none;
            border-radius: 50%;
            background: var(--primary-color);
            color: white;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .send-btn:hover {
            background: var(--primary-dark);
            transform: scale(1.05);
        }

        .send-btn:disabled {
            background: var(--border-color);
            cursor: not-allowed;
            transform: none;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .cs-sidebar {
                width: 100%;
                position: absolute;
                z-index: 1000;
                height: 100%;
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .cs-sidebar.show {
                transform: translateX(0);
            }

            .chat-area {
                width: 100%;
            }

            .customer-service-container {
                height: calc(100vh - 60px);
            }
        }

        /* 滚动条样式 */
        .session-list::-webkit-scrollbar,
        .chat-messages::-webkit-scrollbar {
            width: 6px;
        }

        .session-list::-webkit-scrollbar-track,
        .chat-messages::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        .session-list::-webkit-scrollbar-thumb,
        .chat-messages::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        .session-list::-webkit-scrollbar-thumb:hover,
        .chat-messages::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 管理后台侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block admin-sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="user-info">
                        <div class="user-avatar">
                            <i class="fas fa-user-shield"></i>
                        </div>
                        <h6 th:text="${admin?.realName ?: '超级管理员'}">超级管理员</h6>
                        <small th:text="${admin?.role ?: 'super_admin'}">super_admin</small>
                    </div>

                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/index">
                                <i class="bi bi-house-door-fill me-2"></i>
                                仪表盘
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/engineers">
                                <i class="bi bi-people-fill me-2"></i>
                                工程师管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/service-centers">
                                <i class="bi bi-building me-2"></i>
                                服务网点管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/products">
                                <i class="bi bi-box-seam me-2"></i>
                                产品管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/orders">
                                <i class="fas fa-clipboard-list me-2"></i>
                                订单管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="/admin/customer-service">
                                <i class="fas fa-comments me-2"></i>
                                在线客服
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/logout">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                退出登录
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-0">
                <div class="customer-service-container">
                    <!-- 客服会话侧边栏 -->
                    <div class="cs-sidebar">
                        <div class="sidebar-header">
                            <h4><i class="fas fa-headset me-2"></i>客服工作台</h4>
                            <div class="admin-info">
                                <i class="fas fa-user me-1"></i>
                                <span th:text="${admin?.realName ?: '管理员'}">管理员</span>
                            </div>
                        </div>

            <div class="session-list">
                <!-- 等待接入的会话 -->
                <div class="session-section" th:if="${waitingSessions != null and waitingSessions.size() > 0}">
                    <div class="section-title">
                        <i class="fas fa-clock me-1"></i>
                        等待接入 (<span th:text="${waitingSessions.size()}">0</span>)
                    </div>
                    <div th:each="csSession : ${waitingSessions}"
                         class="session-item waiting"
                         th:data-session-id="${csSession.sessionId}"
                         onclick="selectSession(this, 'waiting')">
                        <div class="session-user" th:text="${csSession.userNickname ?: '用户'}">用户昵称</div>
                        <div class="session-message" th:text="${csSession.lastMessageContent ?: '用户发起了咨询'}">最后消息</div>
                        <div class="session-time" th:text="${csSession.createdAt != null ? #temporals.format(csSession.createdAt, 'HH:mm') : '刚刚'}">时间</div>
                        <span class="unread-badge" th:if="${csSession.unreadCountAdmin > 0}" th:text="${csSession.unreadCountAdmin}">1</span>
                    </div>
                </div>

                <!-- 进行中的会话 -->
                <div class="session-section" th:if="${activeSessions != null and activeSessions.size() > 0}">
                    <div class="section-title">
                        <i class="fas fa-comments me-1"></i>
                        进行中 (<span th:text="${activeSessions.size()}">0</span>)
                    </div>
                    <div th:each="csSession : ${activeSessions}"
                         class="session-item"
                         th:data-session-id="${csSession.sessionId}"
                         onclick="selectSession(this, 'active')">
                        <div class="session-user" th:text="${csSession.userNickname ?: '用户'}">用户昵称</div>
                        <div class="session-message" th:text="${csSession.lastMessageContent ?: '暂无消息'}">最后消息</div>
                        <div class="session-time" th:text="${csSession.lastMessageTime != null ? #temporals.format(csSession.lastMessageTime, 'HH:mm') : '刚刚'}">时间</div>
                        <span class="unread-badge" th:if="${csSession.unreadCountAdmin > 0}" th:text="${csSession.unreadCountAdmin}">1</span>
                    </div>
                </div>

                <!-- 无会话提示 -->
                <div th:if="${(waitingSessions == null or waitingSessions.size() == 0) and (activeSessions == null or activeSessions.size() == 0)}"
                     class="text-center py-4">
                    <i class="fas fa-inbox fa-2x text-muted mb-3"></i>
                    <p class="text-muted">暂无客服会话</p>
                    <small class="text-muted">用户发起咨询后会显示在这里</small>
                </div>
            </div>
        </div>

        <!-- 聊天区域 -->
        <div class="chat-area">
            <!-- 聊天头部 -->
            <div class="chat-header">
                <div class="chat-title" id="chatTitle">
                    <i class="fas fa-comment-dots me-2"></i>
                    选择一个会话开始聊天
                </div>
                <div class="chat-actions" id="chatActions" style="display: none;">
                    <button class="btn-action btn-success" id="assignBtn" onclick="assignCurrentSession()" style="display: none;">
                        <i class="fas fa-user-plus me-1"></i>接入会话
                    </button>
                    <button class="btn-action btn-primary" onclick="loadChatMessages()">
                        <i class="fas fa-sync me-1"></i>刷新
                    </button>
                    <button class="btn-action btn-warning" onclick="closeSession()" style="display: none;">
                        <i class="fas fa-times me-1"></i>关闭会话
                    </button>
                </div>
            </div>

            <!-- 聊天消息区域 -->
            <div class="chat-messages" id="chatMessages">
                <div class="empty-chat">
                    <i class="fas fa-comment-dots"></i>
                    <p>请从左侧选择一个会话开始聊天</p>
                    <small>选择等待接入的会话可以立即为用户提供服务</small>
                </div>
            </div>

            <!-- 快捷回复 -->
            <div class="quick-replies" id="quickReplies" style="display: none;">
                <div class="quick-replies-title">
                    <i class="fas fa-bolt me-1"></i>快捷回复
                </div>
                <div class="quick-reply-buttons">
                    <div th:each="reply : ${quickReplies}">
                        <button class="quick-reply-btn" th:text="${reply.title}" th:data-content="${reply.content}" onclick="useQuickReply(this)">快捷回复</button>
                    </div>
                    <!-- 默认快捷回复 -->
                    <button class="quick-reply-btn" data-content="您好！我是客服，很高兴为您服务，请问有什么可以帮助您的吗？" onclick="useQuickReply(this)">欢迎语</button>
                    <button class="quick-reply-btn" data-content="好的，请稍等，我马上为您查询处理。" onclick="useQuickReply(this)">稍等回复</button>
                    <button class="quick-reply-btn" data-content="感谢您的咨询，如果还有其他问题，随时联系我们。祝您生活愉快！" onclick="useQuickReply(this)">感谢语</button>
                </div>
            </div>

            <!-- 输入区域 -->
            <div class="chat-input" id="chatInput" style="display: none;">
                <div class="input-group">
                    <textarea class="message-input" id="messageInput" placeholder="输入消息... (按 Enter 发送，Shift+Enter 换行)" rows="1"></textarea>
                    <button class="send-btn" id="sendBtn" onclick="sendMessage()">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
            </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentSessionId = null;
        let currentSessionStatus = null;
        let currentUserNickname = null;

        // 选择会话
        function selectSession(element, status) {
            // 移除其他会话的active状态
            document.querySelectorAll('.session-item').forEach(item => {
                item.classList.remove('active');
            });

            // 添加当前会话的active状态
            element.classList.add('active');

            currentSessionId = element.dataset.sessionId;
            currentSessionStatus = status;
            currentUserNickname = element.querySelector('.session-user').textContent;

            // 更新聊天标题
            document.getElementById('chatTitle').innerHTML = `
                <i class="fas fa-user me-2"></i>
                与 ${currentUserNickname} 的对话
            `;

            // 显示操作按钮
            document.getElementById('chatActions').style.display = 'flex';

            // 根据状态显示不同的按钮
            const assignBtn = document.getElementById('assignBtn');
            if (status === 'waiting') {
                assignBtn.style.display = 'block';
                assignBtn.innerHTML = '<i class="fas fa-user-plus me-1"></i>接入会话';
            } else {
                assignBtn.style.display = 'none';
                loadChatMessages();
            }
        }

        // 接入会话
        function assignCurrentSession() {
            if (!currentSessionId) return;

            const assignBtn = document.getElementById('assignBtn');
            assignBtn.disabled = true;
            assignBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>接入中...';

            fetch('/admin/customer-service/api/assign', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ sessionId: currentSessionId })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    currentSessionStatus = 'active';
                    assignBtn.style.display = 'none';
                    loadChatMessages();
                    showNotification('会话接入成功！', 'success');
                    // 刷新页面以更新会话列表
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showNotification('接入失败：' + data.message, 'error');
                    assignBtn.disabled = false;
                    assignBtn.innerHTML = '<i class="fas fa-user-plus me-1"></i>接入会话';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('接入失败，请稍后重试', 'error');
                assignBtn.disabled = false;
                assignBtn.innerHTML = '<i class="fas fa-user-plus me-1"></i>接入会话';
            });
        }

        // 加载聊天消息
        function loadChatMessages() {
            if (!currentSessionId) return;

            // 显示聊天输入区域
            document.getElementById('chatInput').style.display = 'block';
            document.getElementById('quickReplies').style.display = 'block';

            fetch(`/admin/customer-service/api/messages/${currentSessionId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayMessages(data.data);
                    // 标记消息为已读
                    markAsRead();
                } else {
                    console.error('加载消息失败：', data.message);
                    showNotification('加载消息失败', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('加载消息失败', 'error');
            });
        }

        // 显示消息
        function displayMessages(messages) {
            const chatMessages = document.getElementById('chatMessages');
            chatMessages.innerHTML = '';

            if (messages.length === 0) {
                chatMessages.innerHTML = `
                    <div class="empty-chat">
                        <i class="fas fa-comment-dots"></i>
                        <p>还没有消息</p>
                        <small>开始与 ${currentUserNickname} 的对话吧</small>
                    </div>
                `;
                return;
            }

            messages.forEach(message => {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${message.senderType}`;

                const time = new Date(message.createdAt).toLocaleTimeString('zh-CN', {
                    hour: '2-digit',
                    minute: '2-digit'
                });

                const avatar = message.senderType === 'user' ?
                    (message.senderName ? message.senderName.charAt(0) : '用') :
                    '客';

                messageDiv.innerHTML = `
                    <div class="message-avatar">${avatar}</div>
                    <div class="message-content">
                        <div class="message-text">${message.content}</div>
                        <div class="message-time">${time}</div>
                    </div>
                `;

                chatMessages.appendChild(messageDiv);
            });

            // 滚动到底部
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // 发送消息
        function sendMessage() {
            if (!currentSessionId) return;

            const messageInput = document.getElementById('messageInput');
            const sendBtn = document.getElementById('sendBtn');
            const content = messageInput.value.trim();

            if (!content) return;

            // 禁用发送按钮
            sendBtn.disabled = true;
            sendBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

            fetch('/admin/customer-service/api/message', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    sessionId: currentSessionId,
                    content: content
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    messageInput.value = '';
                    messageInput.style.height = 'auto';
                    loadChatMessages(); // 重新加载消息
                    showNotification('消息发送成功', 'success');
                } else {
                    showNotification('发送失败：' + data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('发送失败，请稍后重试', 'error');
            })
            .finally(() => {
                // 恢复发送按钮
                sendBtn.disabled = false;
                sendBtn.innerHTML = '<i class="fas fa-paper-plane"></i>';
                messageInput.focus();
            });
        }

        // 使用快捷回复
        function useQuickReply(button) {
            const content = button.dataset.content;
            const messageInput = document.getElementById('messageInput');
            messageInput.value = content;
            messageInput.focus();
            // 自动调整高度
            autoResizeTextarea(messageInput);
        }

        // 标记消息为已读
        function markAsRead() {
            if (!currentSessionId) return;

            fetch('/admin/customer-service/api/read', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ sessionId: currentSessionId })
            })
            .catch(error => {
                console.error('Error:', error);
            });
        }

        // 关闭会话
        function closeSession() {
            if (!currentSessionId) return;

            if (!confirm('确定要关闭这个会话吗？')) return;

            fetch('/admin/customer-service/api/close', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ sessionId: currentSessionId })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('会话已关闭', 'success');
                    location.reload();
                } else {
                    showNotification('关闭失败：' + data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('关闭失败，请稍后重试', 'error');
            });
        }

        // 显示通知
        function showNotification(message, type = 'info') {
            // 创建通知元素
            const notification = document.createElement('div');
            notification.className = `alert alert-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'} position-fixed`;
            notification.style.cssText = `
                top: 20px;
                right: 20px;
                z-index: 9999;
                min-width: 300px;
                animation: slideInRight 0.3s ease;
            `;
            notification.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'} me-2"></i>
                ${message}
            `;

            document.body.appendChild(notification);

            // 3秒后自动移除
            setTimeout(() => {
                notification.style.animation = 'slideOutRight 0.3s ease';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // 自动调整文本框高度
        function autoResizeTextarea(textarea) {
            textarea.style.height = 'auto';
            textarea.style.height = Math.min(textarea.scrollHeight, 100) + 'px';
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            const messageInput = document.getElementById('messageInput');

            // 回车发送消息
            messageInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });

            // 自动调整文本框高度
            messageInput.addEventListener('input', function() {
                autoResizeTextarea(this);
            });
        });

        // 定时刷新消息
        setInterval(function() {
            if (currentSessionId && currentSessionStatus === 'active') {
                loadChatMessages();
            }
        }, 5000); // 每5秒刷新一次

        // 添加动画样式
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOutRight {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
