<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片上传测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
        }
        .upload-area:hover {
            border-color: #1e88e5;
            background-color: #f5f5f5;
        }
        .preview-area {
            margin: 20px 0;
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        .preview-image {
            width: 150px;
            height: 150px;
            object-fit: cover;
            border-radius: 8px;
            border: 2px solid #e0e0e0;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        button {
            background: #1e88e5;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background: #1565c0;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <h1>🧪 图片上传测试页面</h1>
    
    <div class="upload-area" onclick="document.getElementById('fileInput').click()">
        <p>📸 点击选择图片或拖拽图片到此处</p>
        <p>支持格式: JPG, PNG, GIF, BMP, WEBP</p>
        <p>最大大小: 5MB</p>
    </div>
    
    <input type="file" id="fileInput" accept="image/*" multiple style="display: none;">
    
    <button onclick="uploadImages()" id="uploadBtn" disabled>上传图片</button>
    <button onclick="clearResults()">清除结果</button>
    
    <div id="previewArea" class="preview-area"></div>
    <div id="results"></div>
    
    <h2>🔗 测试链接</h2>
    <div id="testLinks"></div>

    <script>
        let selectedFiles = [];
        
        document.getElementById('fileInput').addEventListener('change', function(e) {
            selectedFiles = Array.from(e.target.files);
            previewImages();
            document.getElementById('uploadBtn').disabled = selectedFiles.length === 0;
        });
        
        function previewImages() {
            const previewArea = document.getElementById('previewArea');
            previewArea.innerHTML = '';
            
            selectedFiles.forEach((file, index) => {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const img = document.createElement('img');
                    img.src = e.target.result;
                    img.className = 'preview-image';
                    img.title = file.name;
                    previewArea.appendChild(img);
                };
                reader.readAsDataURL(file);
            });
        }
        
        async function uploadImages() {
            const resultsDiv = document.getElementById('results');
            const testLinksDiv = document.getElementById('testLinks');
            const uploadBtn = document.getElementById('uploadBtn');
            
            uploadBtn.disabled = true;
            uploadBtn.textContent = '上传中...';
            
            for (let i = 0; i < selectedFiles.length; i++) {
                const file = selectedFiles[i];
                const formData = new FormData();
                formData.append('file', file);
                
                try {
                    const response = await fetch('/api/upload/image', {
                        method: 'POST',
                        body: formData
                    });
                    
                    const result = await response.json();
                    
                    const resultDiv = document.createElement('div');
                    resultDiv.className = result.success ? 'result success' : 'result error';
                    
                    if (result.success) {
                        const imageUrl = result.data.url;
                        resultDiv.innerHTML = `
                            <h4>✅ ${file.name} 上传成功</h4>
                            <p><strong>文件名:</strong> ${result.data.fileName}</p>
                            <p><strong>大小:</strong> ${(result.data.size / 1024).toFixed(2)} KB</p>
                            <p><strong>URL:</strong> <a href="${imageUrl}" target="_blank">${imageUrl}</a></p>
                            <img src="${imageUrl}" style="max-width: 200px; margin-top: 10px;" onerror="this.style.display='none'; this.nextSibling.style.display='block';">
                            <div style="display: none; color: red;">❌ 图片加载失败</div>
                        `;
                        
                        // 添加测试链接
                        const testLink = document.createElement('div');
                        testLink.innerHTML = `
                            <p><a href="${imageUrl}" target="_blank">🔗 测试访问: ${imageUrl}</a></p>
                        `;
                        testLinksDiv.appendChild(testLink);
                        
                    } else {
                        resultDiv.innerHTML = `
                            <h4>❌ ${file.name} 上传失败</h4>
                            <p><strong>错误:</strong> ${result.message}</p>
                        `;
                    }
                    
                    resultsDiv.appendChild(resultDiv);
                    
                } catch (error) {
                    const resultDiv = document.createElement('div');
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `
                        <h4>❌ ${file.name} 上传失败</h4>
                        <p><strong>网络错误:</strong> ${error.message}</p>
                    `;
                    resultsDiv.appendChild(resultDiv);
                }
            }
            
            uploadBtn.disabled = false;
            uploadBtn.textContent = '上传图片';
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
            document.getElementById('testLinks').innerHTML = '';
            document.getElementById('previewArea').innerHTML = '';
            document.getElementById('fileInput').value = '';
            selectedFiles = [];
            document.getElementById('uploadBtn').disabled = true;
        }
        
        // 拖拽上传
        const uploadArea = document.querySelector('.upload-area');
        
        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            this.style.borderColor = '#1e88e5';
            this.style.backgroundColor = '#f5f5f5';
        });
        
        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            this.style.borderColor = '#ccc';
            this.style.backgroundColor = 'transparent';
        });
        
        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            this.style.borderColor = '#ccc';
            this.style.backgroundColor = 'transparent';
            
            const files = Array.from(e.dataTransfer.files).filter(file => file.type.startsWith('image/'));
            if (files.length > 0) {
                selectedFiles = files;
                previewImages();
                document.getElementById('uploadBtn').disabled = false;
            }
        });
    </script>
</body>
</html>
