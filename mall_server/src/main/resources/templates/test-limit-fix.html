<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>热门产品限制测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }

        .test-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .product-item {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 0.5rem;
            background: #f8f9fa;
        }

        .json-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1rem;
            font-family: monospace;
            font-size: 0.9rem;
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-white text-center mb-4">🔥 热门产品限制测试</h1>
        
        <!-- 测试控制 -->
        <div class="test-card">
            <h5 class="mb-3">测试控制</h5>
            <div class="row">
                <div class="col-md-3">
                    <label for="limitInput" class="form-label">限制数量</label>
                    <input type="number" class="form-control" id="limitInput" value="2" min="0" max="10">
                </div>
                <div class="col-md-3">
                    <label for="sortInput" class="form-label">排序方式</label>
                    <select class="form-select" id="sortInput">
                        <option value="sortOrder">按排序权重</option>
                        <option value="sales">按销量</option>
                        <option value="price">按价格</option>
                        <option value="default">默认排序</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="categoryInput" class="form-label">分类ID</label>
                    <input type="number" class="form-control" id="categoryInput" value="0" min="0">
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button class="btn btn-primary" onclick="testAPI()">测试API</button>
                </div>
            </div>
        </div>

        <!-- API测试结果 -->
        <div class="test-card">
            <h5 class="mb-3">API测试结果</h5>
            <div id="apiResults">
                <div class="text-muted">等待测试...</div>
            </div>
        </div>

        <!-- 产品列表 -->
        <div class="test-card">
            <h5 class="mb-3">产品列表</h5>
            <div id="productList">
                <div class="text-muted">等待测试...</div>
            </div>
        </div>

        <!-- 验证结果 -->
        <div class="test-card">
            <h5 class="mb-3">验证结果</h5>
            <div id="validationResults">
                <div class="text-muted">等待测试...</div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        async function testAPI() {
            const limit = document.getElementById('limitInput').value;
            const sortType = document.getElementById('sortInput').value;
            const categoryId = document.getElementById('categoryInput').value;
            
            const apiUrl = `/api/products/list?categoryId=${categoryId}&sortType=${sortType}&limit=${limit}`;
            
            console.log('🔍 测试API:', apiUrl);
            
            try {
                const response = await fetch(apiUrl);
                const data = await response.json();
                
                displayAPIResults(data, apiUrl);
                displayProductList(data.data || []);
                validateResults(data.data || [], parseInt(limit));
                
            } catch (error) {
                console.error('API测试失败:', error);
                document.getElementById('apiResults').innerHTML = 
                    `<div class="alert alert-danger">API测试失败: ${error.message}</div>`;
            }
        }

        function displayAPIResults(data, url) {
            const container = document.getElementById('apiResults');
            
            const html = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>请求信息</h6>
                        <p><strong>URL:</strong> ${url}</p>
                        <p><strong>状态:</strong> <span class="badge ${data.success ? 'bg-success' : 'bg-danger'}">${data.success ? '成功' : '失败'}</span></p>
                        <p><strong>消息:</strong> ${data.message || '无'}</p>
                        <p><strong>数据数量:</strong> ${data.data ? data.data.length : 0}</p>
                    </div>
                    <div class="col-md-6">
                        <h6>完整响应</h6>
                        <div class="json-display">
                            ${JSON.stringify(data, null, 2)}
                        </div>
                    </div>
                </div>
            `;
            
            container.innerHTML = html;
        }

        function displayProductList(products) {
            const container = document.getElementById('productList');
            
            if (!products || products.length === 0) {
                container.innerHTML = '<div class="alert alert-warning">没有产品数据</div>';
                return;
            }
            
            let html = '';
            products.forEach((product, index) => {
                html += `
                    <div class="product-item">
                        <div class="row">
                            <div class="col-md-8">
                                <h6>${product.name}</h6>
                                <p class="mb-1">ID: ${product.id} | 价格: ¥${product.price} | 销量: ${product.sales || 0}</p>
                                <p class="mb-0">排序权重: ${product.sortOrder || 0} | 库存: ${product.stock || 0}</p>
                            </div>
                            <div class="col-md-4 text-end">
                                <span class="badge bg-primary">第 ${index + 1} 个</span>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }

        function validateResults(products, expectedLimit) {
            const container = document.getElementById('validationResults');
            
            const actualCount = products.length;
            const isLimitCorrect = expectedLimit === 0 || actualCount <= expectedLimit;
            
            let html = `
                <div class="alert ${isLimitCorrect ? 'alert-success' : 'alert-danger'}">
                    <h6>${isLimitCorrect ? '✅ 限制验证通过' : '❌ 限制验证失败'}</h6>
                    <p><strong>期望限制:</strong> ${expectedLimit === 0 ? '无限制' : expectedLimit + ' 个'}</p>
                    <p><strong>实际数量:</strong> ${actualCount} 个</p>
                    <p><strong>验证结果:</strong> ${isLimitCorrect ? '符合预期' : '超出限制'}</p>
                </div>
            `;
            
            // 检查排序
            if (products.length > 1) {
                const sortType = document.getElementById('sortInput').value;
                let isSorted = true;
                let sortField = '';
                
                switch (sortType) {
                    case 'sortOrder':
                        sortField = 'sortOrder';
                        for (let i = 1; i < products.length; i++) {
                            if ((products[i-1].sortOrder || 0) < (products[i].sortOrder || 0)) {
                                isSorted = false;
                                break;
                            }
                        }
                        break;
                    case 'sales':
                        sortField = 'sales';
                        for (let i = 1; i < products.length; i++) {
                            if ((products[i-1].sales || 0) < (products[i].sales || 0)) {
                                isSorted = false;
                                break;
                            }
                        }
                        break;
                    case 'price':
                        sortField = 'price';
                        for (let i = 1; i < products.length; i++) {
                            if (parseFloat(products[i-1].price || 0) < parseFloat(products[i].price || 0)) {
                                isSorted = false;
                                break;
                            }
                        }
                        break;
                }
                
                html += `
                    <div class="alert ${isSorted ? 'alert-success' : 'alert-warning'}">
                        <h6>${isSorted ? '✅ 排序验证通过' : '⚠️ 排序验证失败'}</h6>
                        <p><strong>排序字段:</strong> ${sortField}</p>
                        <p><strong>排序方式:</strong> 降序 (DESC)</p>
                    </div>
                `;
            }
            
            container.innerHTML = html;
        }

        // 页面加载时自动测试
        document.addEventListener('DOMContentLoaded', function() {
            testAPI();
        });
    </script>
</body>
</html>
