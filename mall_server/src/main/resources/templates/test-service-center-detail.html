<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>服务网点详情测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
        }
        
        /* 申请材料图片样式 */
        .business-license-container {
            display: flex;
            justify-content: flex-start;
            margin-bottom: 20px;
        }

        .business-license-image {
            max-width: 300px;
            max-height: 200px;
            border-radius: 8px;
            border: 2px solid #e0e0e0;
            object-fit: contain;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .business-license-image:hover {
            border-color: #1e88e5;
            transform: scale(1.02);
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
        }

        .certificates-gallery,
        .station-images-gallery {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .certificate-image,
        .station-image {
            width: 120px;
            height: 120px;
            border-radius: 8px;
            border: 2px solid #e0e0e0;
            object-fit: cover;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .certificate-image:hover,
        .station-image:hover {
            border-color: #1e88e5;
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        /* 图片预览模态框 */
        .image-preview-modal {
            display: none;
            position: fixed;
            z-index: 9999;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.8);
            cursor: pointer;
        }

        .image-preview-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            max-width: 90%;
            max-height: 90%;
        }

        .image-preview-img {
            width: 100%;
            height: auto;
            border-radius: 8px;
        }

        .image-preview-close {
            position: absolute;
            top: 15px;
            right: 25px;
            color: white;
            font-size: 35px;
            font-weight: bold;
            cursor: pointer;
        }

        .image-preview-close:hover {
            opacity: 0.7;
        }

        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }

        .test-button {
            background: #1e88e5;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }

        .test-button:hover {
            background: #1565c0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 服务网点详情测试页面</h1>
        
        <div class="test-section">
            <h2>📋 测试服务网点详情显示</h2>
            <button class="test-button" onclick="loadTestServiceCenter()">加载测试数据</button>
            <button class="test-button" onclick="loadRealServiceCenter()">加载真实数据</button>
            <button class="test-button" onclick="clearContent()">清空内容</button>
            
            <div id="serviceCenterDetailContent" class="mt-4">
                <!-- 服务网点详情内容将在这里显示 -->
            </div>
        </div>
    </div>

    <!-- 图片预览模态框 -->
    <div id="imagePreviewModal" class="image-preview-modal" onclick="closeImagePreview()">
        <span class="image-preview-close" onclick="closeImagePreview()">&times;</span>
        <div class="image-preview-content">
            <img id="previewImage" class="image-preview-img" src="" alt="预览图片">
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 显示图片预览
        function showImagePreview(imageSrc) {
            const modal = document.getElementById('imagePreviewModal');
            const previewImg = document.getElementById('previewImage');
            
            previewImg.src = imageSrc;
            modal.style.display = 'block';
            
            // 阻止事件冒泡
            if (event) {
                event.stopPropagation();
            }
        }

        // 关闭图片预览
        function closeImagePreview() {
            const modal = document.getElementById('imagePreviewModal');
            modal.style.display = 'none';
        }

        // 处理图片URL，避免重复路径
        function processImageUrl(url) {
            if (url.startsWith('http')) {
                return url;
            } else if (url.startsWith('/uploads/')) {
                return url;
            } else {
                return '/uploads/' + url;
            }
        }

        // 渲染资质证书图片
        function renderCertificateImages(gallery, certificates) {
            gallery.innerHTML = '';
            
            certificates.forEach((url, index) => {
                const img = document.createElement('img');
                img.src = processImageUrl(url);
                img.className = 'certificate-image';
                img.alt = `资质证书 ${index + 1}`;
                img.onclick = () => showImagePreview(img.src);
                
                img.onload = () => console.log(`✅ 资质证书${index}加载成功:`, img.src);
                img.onerror = () => {
                    console.error(`❌ 资质证书${index}加载失败:`, img.src);
                    img.style.display = 'none';
                };
                
                gallery.appendChild(img);
            });
        }

        // 渲染网点照片
        function renderStationImages(gallery, images) {
            gallery.innerHTML = '';
            
            images.forEach((url, index) => {
                const img = document.createElement('img');
                img.src = processImageUrl(url);
                img.className = 'station-image';
                img.alt = `网点照片 ${index + 1}`;
                img.onclick = () => showImagePreview(img.src);
                
                img.onload = () => console.log(`✅ 网点照片${index}加载成功:`, img.src);
                img.onerror = () => {
                    console.error(`❌ 网点照片${index}加载失败:`, img.src);
                    img.style.display = 'none';
                };
                
                gallery.appendChild(img);
            });
        }

        // 初始化图片显示
        function initServiceCenterImages() {
            // 初始化资质证书图片
            const certificatesGalleries = document.querySelectorAll('.certificates-gallery');
            certificatesGalleries.forEach(gallery => {
                const certificatesData = gallery.getAttribute('data-certificates');
                if (certificatesData) {
                    try {
                        // 解码HTML实体
                        const decodedData = certificatesData.replace(/&quot;/g, '"');
                        const certificates = JSON.parse(decodedData);
                        renderCertificateImages(gallery, certificates);
                    } catch (e) {
                        console.error('解析资质证书数据失败:', e, certificatesData);
                    }
                }
            });

            // 初始化网点照片
            const stationImagesGalleries = document.querySelectorAll('.station-images-gallery');
            stationImagesGalleries.forEach(gallery => {
                const imagesData = gallery.getAttribute('data-images');
                if (imagesData) {
                    try {
                        // 解码HTML实体
                        const decodedData = imagesData.replace(/&quot;/g, '"');
                        const images = JSON.parse(decodedData);
                        renderStationImages(gallery, images);
                    } catch (e) {
                        console.error('解析网点照片数据失败:', e, imagesData);
                    }
                }
            });
        }

        // 加载测试服务网点数据
        function loadTestServiceCenter() {
            const mockServiceCenter = {
                name: "测试充电站",
                phone: "***********",
                contactPerson: "张三",
                email: "<EMAIL>",
                address: "北京市朝阳区测试路123号",
                businessHours: "08:00-22:00",
                serviceDescription: "专业的充电桩维修和安装服务",
                businessLicense: "/uploads/20250610_143914_aea5df47.png",
                qualificationCertificates: '["/uploads/20250610_143914_aea5df47.png", "/uploads/20250610_143914_4a433ed2.png"]',
                images: '["/uploads/20250610_143914_aea5df47.png", "/uploads/20250610_143914_4a433ed2.png"]'
            };

            renderServiceCenterDetail(mockServiceCenter);
        }

        // 加载真实服务网点数据
        async function loadRealServiceCenter() {
            // 模拟真实数据（包含转义的JSON）
            const mockRealServiceCenter = {
                name: "真实充电站数据",
                phone: "***********",
                contactPerson: "李四",
                email: "<EMAIL>",
                address: "上海市浦东新区真实路456号",
                businessHours: "24小时营业",
                serviceDescription: "提供专业的新能源汽车充电服务，包括快充、慢充等多种充电方式",
                businessLicense: "/uploads/20250610_143914_aea5df47.png",
                qualificationCertificates: '["\/uploads\/20250610_143914_aea5df47.png", "\/uploads\/20250610_143914_4a433ed2.png"]',
                images: '["\/uploads\/20250610_143914_aea5df47.png", "\/uploads\/20250610_143914_4a433ed2.png", "\/uploads\/20250610_143914_aea5df47.png"]'
            };

            renderServiceCenterDetail(mockRealServiceCenter);
        }

        // 渲染服务网点详情
        function renderServiceCenterDetail(serviceCenter) {
            const content = document.getElementById('serviceCenterDetailContent');

            content.innerHTML = `
                <div class="row">
                    <!-- 基本信息 -->
                    <div class="col-md-6 mb-4">
                        <h6 class="border-bottom pb-2 mb-3">
                            <i class="bi bi-building me-2"></i>基本信息
                        </h6>
                        <table class="table table-borderless">
                            <tr><td class="fw-bold">网点名称:</td><td>${serviceCenter.name || '-'}</td></tr>
                            <tr><td class="fw-bold">联系电话:</td><td>${serviceCenter.phone || '-'}</td></tr>
                            <tr><td class="fw-bold">联系人:</td><td>${serviceCenter.contactPerson || '-'}</td></tr>
                            <tr><td class="fw-bold">邮箱:</td><td>${serviceCenter.email || '-'}</td></tr>
                            <tr><td class="fw-bold">营业时间:</td><td>${serviceCenter.businessHours || '-'}</td></tr>
                        </table>
                    </div>
                </div>

                <!-- 申请材料 -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h6 class="border-bottom pb-2 mb-3">
                            <i class="bi bi-file-earmark-image me-2"></i>申请材料
                        </h6>
                        
                        <!-- 营业执照 -->
                        ${serviceCenter.businessLicense ? `
                            <div class="mb-4">
                                <h6 class="text-primary mb-2">
                                    <i class="bi bi-file-earmark-text me-1"></i>营业执照
                                </h6>
                                <div class="business-license-container">
                                    <img src="${serviceCenter.businessLicense}" 
                                         class="business-license-image" 
                                         alt="营业执照"
                                         onclick="showImagePreview('${serviceCenter.businessLicense}')"
                                         onerror="this.style.display='none'; this.nextSibling.style.display='block';">
                                    <div style="display: none; color: red;">❌ 营业执照加载失败</div>
                                </div>
                            </div>
                        ` : ''}

                        <!-- 资质证书 -->
                        ${serviceCenter.qualificationCertificates ? `
                            <div class="mb-4">
                                <h6 class="text-success mb-2">
                                    <i class="bi bi-award me-1"></i>资质证书
                                </h6>
                                <div class="certificates-gallery" data-certificates="${serviceCenter.qualificationCertificates.replace(/"/g, '&quot;')}">
                                    <!-- 资质证书将通过JavaScript动态加载 -->
                                </div>
                            </div>
                        ` : ''}

                        <!-- 网点照片 -->
                        ${serviceCenter.images ? `
                            <div class="mb-4">
                                <h6 class="text-info mb-2">
                                    <i class="bi bi-camera me-1"></i>网点照片
                                </h6>
                                <div class="station-images-gallery" data-images="${serviceCenter.images.replace(/"/g, '&quot;')}">
                                    <!-- 网点照片将通过JavaScript动态加载 -->
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;

            // 初始化图片显示
            setTimeout(() => {
                initServiceCenterImages();
            }, 100);
        }

        // 清空内容
        function clearContent() {
            document.getElementById('serviceCenterDetailContent').innerHTML = '';
        }
    </script>
</body>
</html>
