<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工程师服务次数增加测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }

        .test-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .data-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }

        .count-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: #007bff;
        }

        .log-area {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1rem;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-white text-center mb-4">🧪 工程师服务次数增加测试</h1>
        
        <!-- 测试控制面板 -->
        <div class="test-card">
            <h5 class="mb-3">测试控制面板</h5>
            <div class="row">
                <div class="col-md-4">
                    <label for="engineerId" class="form-label">工程师ID</label>
                    <input type="number" class="form-control" id="engineerId" value="1" min="1">
                </div>
                <div class="col-md-8 d-flex align-items-end">
                    <button class="btn btn-primary me-2" onclick="loadEngineerData()">
                        加载工程师数据
                    </button>
                    <button class="btn btn-success me-2" onclick="testIncrementService()">
                        测试增加服务次数
                    </button>
                    <button class="btn btn-outline-secondary" onclick="clearLogs()">
                        清空日志
                    </button>
                </div>
            </div>
        </div>

        <!-- 工程师数据展示 -->
        <div class="test-card">
            <h5 class="mb-3">工程师数据</h5>
            <div id="engineerData">
                <div class="text-muted">请先加载工程师数据...</div>
            </div>
        </div>

        <!-- 测试结果 -->
        <div class="test-card">
            <h5 class="mb-3">测试结果</h5>
            <div id="testResult">
                <div class="text-muted">等待测试...</div>
            </div>
        </div>

        <!-- 操作日志 -->
        <div class="test-card">
            <h5 class="mb-3">操作日志</h5>
            <div id="logArea" class="log-area">
                <div class="text-muted">等待操作...</div>
            </div>
        </div>

        <!-- 返回按钮 -->
        <div class="text-center">
            <a href="/admin/engineers" class="btn btn-light">
                返回工程师管理
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 加载工程师数据
        async function loadEngineerData() {
            const engineerId = document.getElementById('engineerId').value;
            const container = document.getElementById('engineerData');
            
            container.innerHTML = '<div class="text-info">正在加载工程师数据...</div>';
            addLog(`🔍 加载工程师数据，ID: ${engineerId}`);

            try {
                const response = await fetch(`/admin/api/engineers/${engineerId}`);
                const data = await response.json();

                if (data.success && data.data) {
                    displayEngineerData(data.data);
                    addLog(`✅ 工程师数据加载成功`);
                } else {
                    container.innerHTML = '<div class="alert alert-warning">工程师数据加载失败</div>';
                    addLog(`❌ 工程师数据加载失败: ${data.message}`);
                }
            } catch (error) {
                console.error('加载工程师数据失败:', error);
                container.innerHTML = '<div class="alert alert-danger">请求失败</div>';
                addLog(`❌ 请求失败: ${error.message}`);
            }
        }

        // 测试增加服务次数
        async function testIncrementService() {
            const engineerId = document.getElementById('engineerId').value;
            const container = document.getElementById('testResult');
            
            container.innerHTML = '<div class="text-info">正在测试增加服务次数...</div>';
            addLog(`🧪 开始测试增加服务次数，工程师ID: ${engineerId}`);

            try {
                const response = await fetch(`/admin/api/test/engineer/${engineerId}/increment-service`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                const data = await response.json();

                displayTestResult(data);
                
                if (data.success) {
                    addLog(`✅ 测试成功: ${data.message}`);
                    // 重新加载工程师数据
                    setTimeout(() => {
                        loadEngineerData();
                    }, 1000);
                } else {
                    addLog(`❌ 测试失败: ${data.message}`);
                }
            } catch (error) {
                console.error('测试失败:', error);
                container.innerHTML = '<div class="alert alert-danger">测试请求失败</div>';
                addLog(`❌ 测试请求失败: ${error.message}`);
            }
        }

        // 显示工程师数据
        function displayEngineerData(engineer) {
            const container = document.getElementById('engineerData');
            
            const html = `
                <div class="data-display">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>${engineer.name} (ID: ${engineer.id})</h6>
                            <p><strong>电话:</strong> ${engineer.phone}</p>
                            <p><strong>状态:</strong> 
                                <span class="badge ${engineer.status === 'approved' ? 'bg-success' : 'bg-secondary'}">
                                    ${getStatusText(engineer.status)}
                                </span>
                            </p>
                        </div>
                        <div class="col-md-6">
                            <div class="row text-center">
                                <div class="col-4">
                                    <div class="count-number">${engineer.totalOrders || 0}</div>
                                    <small class="text-muted">总接单数</small>
                                </div>
                                <div class="col-4">
                                    <div class="count-number">${engineer.completedOrders || 0}</div>
                                    <small class="text-muted">完成订单数</small>
                                </div>
                                <div class="col-4">
                                    <div class="count-number">${engineer.successRate || 0}%</div>
                                    <small class="text-muted">成功率</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            container.innerHTML = html;
        }

        // 显示测试结果
        function displayTestResult(result) {
            const container = document.getElementById('testResult');
            
            if (result.success) {
                const beforeData = result.beforeData;
                const afterData = result.afterData;
                
                const html = `
                    <div class="alert alert-success">
                        <h6>✅ 测试成功！</h6>
                        <p>${result.message}</p>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="data-display">
                                <h6>更新前数据</h6>
                                <div class="row text-center">
                                    <div class="col-4">
                                        <div class="count-number">${beforeData.totalOrders}</div>
                                        <small class="text-muted">总接单数</small>
                                    </div>
                                    <div class="col-4">
                                        <div class="count-number">${beforeData.completedOrders}</div>
                                        <small class="text-muted">完成订单数</small>
                                    </div>
                                    <div class="col-4">
                                        <div class="count-number">${beforeData.successRate}%</div>
                                        <small class="text-muted">成功率</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="data-display">
                                <h6>更新后数据</h6>
                                <div class="row text-center">
                                    <div class="col-4">
                                        <div class="count-number text-primary">${afterData.totalOrders}</div>
                                        <small class="text-muted">总接单数</small>
                                    </div>
                                    <div class="col-4">
                                        <div class="count-number text-success">${afterData.completedOrders}</div>
                                        <small class="text-muted">完成订单数</small>
                                    </div>
                                    <div class="col-4">
                                        <div class="count-number text-info">${afterData.successRate}%</div>
                                        <small class="text-muted">成功率</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="alert alert-info mt-3">
                        <strong>变化:</strong>
                        完成订单数从 ${beforeData.completedOrders} 增加到 ${afterData.completedOrders} 
                        ${afterData.completedOrders > beforeData.completedOrders ? '(+' + (afterData.completedOrders - beforeData.completedOrders) + ')' : ''}
                    </div>
                `;
                
                container.innerHTML = html;
            } else {
                container.innerHTML = `
                    <div class="alert alert-danger">
                        <h6>❌ 测试失败</h6>
                        <p>${result.message}</p>
                    </div>
                `;
            }
        }

        // 添加日志
        function addLog(message) {
            const logArea = document.getElementById('logArea');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            
            if (logArea.textContent.includes('等待操作...')) {
                logArea.innerHTML = '';
            }
            
            logArea.innerHTML += logEntry + '\n';
            logArea.scrollTop = logArea.scrollHeight;
        }

        // 清空日志
        function clearLogs() {
            document.getElementById('logArea').innerHTML = '<div class="text-muted">等待操作...</div>';
        }

        // 获取状态文本
        function getStatusText(status) {
            const statusMap = {
                'pending': '待审核',
                'approved': '已通过',
                'rejected': '已拒绝',
                'suspended': '已暂停',
                'active': '活跃中'
            };
            return statusMap[status] || status;
        }

        // 页面加载时自动加载数据
        document.addEventListener('DOMContentLoaded', function() {
            loadEngineerData();
        });
    </script>
</body>
</html>
