<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小程序API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-button {
            background: #1e88e5;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #1565c0;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .form-group {
            margin: 15px 0;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input, .form-group textarea, .form-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .form-group textarea {
            height: 100px;
            resize: vertical;
        }
    </style>
</head>
<body>
    <h1>🧪 小程序API测试页面</h1>
    
    <!-- 测试1: 服务网点申请API -->
    <div class="test-section">
        <h2>📋 测试1: 服务网点申请API</h2>
        
        <div class="form-group">
            <label>网点名称:</label>
            <input type="text" id="stationName" value="测试充电站" placeholder="请输入网点名称">
        </div>
        
        <div class="form-group">
            <label>联系人:</label>
            <input type="text" id="contactPerson" value="张三" placeholder="请输入联系人">
        </div>
        
        <div class="form-group">
            <label>联系电话:</label>
            <input type="text" id="phone" value="***********" placeholder="请输入联系电话">
        </div>
        
        <div class="form-group">
            <label>邮箱:</label>
            <input type="email" id="email" value="<EMAIL>" placeholder="请输入邮箱">
        </div>
        
        <div class="form-group">
            <label>详细地址:</label>
            <input type="text" id="address" value="北京市朝阳区测试路123号" placeholder="请输入详细地址">
        </div>
        
        <div class="form-group">
            <label>营业时间:</label>
            <input type="text" id="businessHours" value="08:00-22:00" placeholder="请输入营业时间">
        </div>
        
        <div class="form-group">
            <label>服务描述:</label>
            <textarea id="serviceDescription" placeholder="请输入服务描述">专业的充电桩维修和安装服务，提供7x24小时技术支持</textarea>
        </div>
        
        <button class="test-button" onclick="testStationApplication()">提交申请</button>
        <button class="test-button" onclick="fillTestData()">填充测试数据</button>
        <button class="test-button" onclick="clearForm()">清空表单</button>
        
        <div id="stationApplicationResult" class="result" style="display: none;"></div>
    </div>

    <!-- 测试2: 图片上传API -->
    <div class="test-section">
        <h2>📋 测试2: 图片上传API</h2>
        
        <div class="form-group">
            <label>选择图片:</label>
            <input type="file" id="imageFile" accept="image/*" multiple>
        </div>
        
        <button class="test-button" onclick="testImageUpload()">上传图片</button>
        <button class="test-button" onclick="testBatchImageUpload()">批量上传</button>
        
        <div id="imageUploadResult" class="result" style="display: none;"></div>
        <div id="uploadedImages" style="margin-top: 20px;"></div>
    </div>

    <!-- 测试3: 查询API -->
    <div class="test-section">
        <h2>📋 测试3: 查询API</h2>
        
        <button class="test-button" onclick="testGetServiceCenters()">获取服务网点列表</button>
        <button class="test-button" onclick="testGetServiceCenterStats()">获取统计信息</button>
        
        <div id="queryResult" class="result" style="display: none;"></div>
    </div>

    <script>
        // 填充测试数据
        function fillTestData() {
            document.getElementById('stationName').value = '测试充电站 ' + new Date().getTime();
            document.getElementById('contactPerson').value = '张三';
            document.getElementById('phone').value = '***********';
            document.getElementById('email').value = '<EMAIL>';
            document.getElementById('address').value = '北京市朝阳区测试路123号';
            document.getElementById('businessHours').value = '08:00-22:00';
            document.getElementById('serviceDescription').value = '专业的充电桩维修和安装服务，提供7x24小时技术支持';
        }

        // 清空表单
        function clearForm() {
            document.getElementById('stationName').value = '';
            document.getElementById('contactPerson').value = '';
            document.getElementById('phone').value = '';
            document.getElementById('email').value = '';
            document.getElementById('address').value = '';
            document.getElementById('businessHours').value = '';
            document.getElementById('serviceDescription').value = '';
        }

        // 测试服务网点申请
        async function testStationApplication() {
            const resultDiv = document.getElementById('stationApplicationResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.textContent = '提交中...';

            const data = {
                name: document.getElementById('stationName').value,
                contactPerson: document.getElementById('contactPerson').value,
                phone: document.getElementById('phone').value,
                email: document.getElementById('email').value,
                address: document.getElementById('address').value,
                province: '北京市',
                city: '北京市',
                district: '朝阳区',
                latitude: '39.9042',
                longitude: '116.4074',
                businessHours: document.getElementById('businessHours').value,
                serviceTypes: '["充电桩维修", "充电桩安装"]',
                equipmentTypes: '["直流充电桩", "交流充电桩"]',
                serviceDescription: document.getElementById('serviceDescription').value,
                facilities: '["停车场", "休息区"]',
                serviceFee: '100.00',
                parkingInfo: '免费停车2小时',
                businessLicense: '/uploads/20250610_143914_aea5df47.png',
                qualificationCertificates: '["/uploads/20250610_143914_aea5df47.png"]',
                images: '["/uploads/20250610_143914_aea5df47.png"]'
            };

            try {
                const response = await fetch('/api/applications/station', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();
                
                if (result.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 申请提交成功！\n申请ID: ${result.data.applicationId}\n消息: ${result.message}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 申请提交失败！\n错误: ${result.message}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 网络错误！\n错误: ${error.message}`;
            }
        }

        // 测试图片上传
        async function testImageUpload() {
            const fileInput = document.getElementById('imageFile');
            const resultDiv = document.getElementById('imageUploadResult');
            
            if (!fileInput.files.length) {
                alert('请先选择图片文件');
                return;
            }

            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.textContent = '上传中...';

            const formData = new FormData();
            formData.append('file', fileInput.files[0]);

            try {
                const response = await fetch('/api/upload/image', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();
                
                if (result.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 图片上传成功！\n文件名: ${result.data.fileName}\n访问URL: ${result.data.url}`;
                    
                    // 显示上传的图片
                    const imagesDiv = document.getElementById('uploadedImages');
                    imagesDiv.innerHTML += `
                        <div style="margin: 10px 0;">
                            <img src="${result.data.url}" style="max-width: 200px; border: 1px solid #ddd; border-radius: 4px;">
                            <p>文件: ${result.data.originalName} (${(result.data.size / 1024).toFixed(2)} KB)</p>
                        </div>
                    `;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 图片上传失败！\n错误: ${result.message}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 网络错误！\n错误: ${error.message}`;
            }
        }

        // 测试批量图片上传
        async function testBatchImageUpload() {
            const fileInput = document.getElementById('imageFile');
            const resultDiv = document.getElementById('imageUploadResult');
            
            if (!fileInput.files.length) {
                alert('请先选择图片文件');
                return;
            }

            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.textContent = '批量上传中...';

            const files = Array.from(fileInput.files);
            const results = [];

            for (let i = 0; i < files.length; i++) {
                const formData = new FormData();
                formData.append('file', files[i]);

                try {
                    const response = await fetch('/api/upload/image', {
                        method: 'POST',
                        body: formData
                    });

                    const result = await response.json();
                    results.push({
                        file: files[i].name,
                        success: result.success,
                        data: result.data,
                        message: result.message
                    });
                } catch (error) {
                    results.push({
                        file: files[i].name,
                        success: false,
                        message: error.message
                    });
                }
            }

            const successCount = results.filter(r => r.success).length;
            const failCount = results.length - successCount;

            resultDiv.className = failCount > 0 ? 'result error' : 'result success';
            resultDiv.textContent = `批量上传完成！\n成功: ${successCount} 个\n失败: ${failCount} 个\n\n详细结果:\n${results.map(r => `${r.file}: ${r.success ? '✅' : '❌'} ${r.message || ''}`).join('\n')}`;
        }

        // 测试查询服务网点
        async function testGetServiceCenters() {
            const resultDiv = document.getElementById('queryResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.textContent = '查询中...';

            try {
                const response = await fetch('/api/service-centers/approved');
                const result = await response.json();
                
                if (result.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 查询成功！\n服务网点数量: ${result.data.length}\n\n前3个网点:\n${result.data.slice(0, 3).map(sc => `- ${sc.name} (${sc.city})`).join('\n')}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 查询失败！\n错误: ${result.message}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 网络错误！\n错误: ${error.message}`;
            }
        }

        // 测试统计信息
        async function testGetServiceCenterStats() {
            const resultDiv = document.getElementById('queryResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.textContent = '查询统计信息中...';

            try {
                const response = await fetch('/api/service-centers/stats');
                const result = await response.json();
                
                if (result.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 统计信息查询成功！\n${JSON.stringify(result.data, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 统计信息查询失败！\n错误: ${result.message}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 网络错误！\n错误: ${error.message}`;
            }
        }

        // 页面加载时填充测试数据
        window.onload = function() {
            fillTestData();
        };
    </script>
</body>
</html>
