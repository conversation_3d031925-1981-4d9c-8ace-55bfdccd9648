<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品详情页优化测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }

        .test-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .product-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            background: #f8f9fa;
        }

        .json-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1rem;
            font-family: monospace;
            font-size: 0.9rem;
            max-height: 300px;
            overflow-y: auto;
        }

        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
        }

        .status-success {
            background-color: #d4edda;
            color: #155724;
        }

        .status-error {
            background-color: #f8d7da;
            color: #721c24;
        }

        .status-warning {
            background-color: #fff3cd;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-white text-center mb-4">🔧 商品详情页优化测试</h1>
        
        <!-- 测试控制面板 -->
        <div class="test-card">
            <h5 class="mb-3">
                <i class="bi bi-gear me-2"></i>测试控制面板
            </h5>
            <div class="row">
                <div class="col-md-6">
                    <label for="productId" class="form-label">商品ID</label>
                    <input type="number" class="form-control" id="productId" value="1" min="1">
                </div>
                <div class="col-md-6 d-flex align-items-end">
                    <button class="btn btn-primary me-2" onclick="testProductDetail()">
                        <i class="bi bi-play me-1"></i>测试商品详情
                    </button>
                    <button class="btn btn-outline-secondary" onclick="clearResults()">
                        <i class="bi bi-trash me-1"></i>清空结果
                    </button>
                </div>
            </div>
        </div>

        <!-- API响应测试 -->
        <div class="test-card">
            <h5 class="mb-3">
                <i class="bi bi-cloud-download me-2"></i>API响应测试
            </h5>
            <div id="apiResults">
                <div class="text-muted">等待测试...</div>
            </div>
        </div>

        <!-- 数据处理测试 -->
        <div class="test-card">
            <h5 class="mb-3">
                <i class="bi bi-cpu me-2"></i>数据处理测试
            </h5>
            <div id="dataProcessingResults">
                <div class="text-muted">等待测试...</div>
            </div>
        </div>

        <!-- 字段完整性检查 -->
        <div class="test-card">
            <h5 class="mb-3">
                <i class="bi bi-check-square me-2"></i>字段完整性检查
            </h5>
            <div id="fieldCheckResults">
                <div class="text-muted">等待测试...</div>
            </div>
        </div>

        <!-- 返回按钮 -->
        <div class="text-center">
            <a href="/admin/products" class="btn btn-light">
                <i class="bi bi-arrow-left me-1"></i>返回产品管理
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 测试商品详情API
        async function testProductDetail() {
            const productId = document.getElementById('productId').value;
            
            if (!productId) {
                alert('请输入商品ID');
                return;
            }

            // 清空之前的结果
            document.getElementById('apiResults').innerHTML = '<div class="text-info">正在测试...</div>';
            document.getElementById('dataProcessingResults').innerHTML = '<div class="text-info">正在处理...</div>';
            document.getElementById('fieldCheckResults').innerHTML = '<div class="text-info">正在检查...</div>';

            try {
                // 测试API响应
                const response = await fetch(`/api/products/detail/${productId}`);
                const data = await response.json();

                displayApiResults(data);
                
                if (data.success) {
                    testDataProcessing(data.data);
                    checkFieldCompleteness(data.data.product);
                } else {
                    document.getElementById('dataProcessingResults').innerHTML = 
                        '<div class="alert alert-warning">API返回失败，无法进行数据处理测试</div>';
                    document.getElementById('fieldCheckResults').innerHTML = 
                        '<div class="alert alert-warning">API返回失败，无法进行字段检查</div>';
                }
            } catch (error) {
                console.error('测试失败:', error);
                document.getElementById('apiResults').innerHTML = 
                    `<div class="alert alert-danger">测试失败: ${error.message}</div>`;
            }
        }

        // 显示API结果
        function displayApiResults(data) {
            const container = document.getElementById('apiResults');
            
            let html = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>API状态</h6>
                        <span class="status-badge ${data.success ? 'status-success' : 'status-error'}">
                            ${data.success ? '✅ 成功' : '❌ 失败'}
                        </span>
                        <p class="mt-2"><strong>消息:</strong> ${data.message || '无'}</p>
                    </div>
                    <div class="col-md-6">
                        <h6>响应数据</h6>
                        <div class="json-display">
                            ${JSON.stringify(data, null, 2)}
                        </div>
                    </div>
                </div>
            `;
            
            container.innerHTML = html;
        }

        // 测试数据处理
        function testDataProcessing(productData) {
            const container = document.getElementById('dataProcessingResults');
            const product = productData.product;
            
            let results = [];
            
            // 测试JSON字段解析
            const jsonFields = ['features', 'specifications', 'specs', 'services', 'images'];
            jsonFields.forEach(field => {
                if (product[field]) {
                    try {
                        let parsed;
                        if (typeof product[field] === 'string') {
                            if (field === 'images' && !product[field].startsWith('[')) {
                                // 处理逗号分隔的图片
                                parsed = product[field].split(',').map(url => url.trim());
                            } else {
                                parsed = JSON.parse(product[field]);
                            }
                        } else {
                            parsed = product[field];
                        }
                        
                        results.push({
                            field: field,
                            status: 'success',
                            message: `解析成功，包含 ${Array.isArray(parsed) ? parsed.length : Object.keys(parsed).length} 项`,
                            data: parsed
                        });
                    } catch (e) {
                        results.push({
                            field: field,
                            status: 'error',
                            message: `解析失败: ${e.message}`,
                            data: product[field]
                        });
                    }
                } else {
                    results.push({
                        field: field,
                        status: 'warning',
                        message: '字段为空或不存在',
                        data: null
                    });
                }
            });

            // 显示结果
            let html = '<div class="row">';
            results.forEach((result, index) => {
                if (index % 2 === 0 && index > 0) {
                    html += '</div><div class="row">';
                }
                
                html += `
                    <div class="col-md-6 mb-3">
                        <div class="product-card">
                            <h6>${result.field}</h6>
                            <span class="status-badge status-${result.status}">
                                ${result.status === 'success' ? '✅' : result.status === 'error' ? '❌' : '⚠️'} 
                                ${result.message}
                            </span>
                            ${result.data ? `<div class="json-display mt-2">${JSON.stringify(result.data, null, 2)}</div>` : ''}
                        </div>
                    </div>
                `;
            });
            html += '</div>';
            
            container.innerHTML = html;
        }

        // 检查字段完整性
        function checkFieldCompleteness(product) {
            const container = document.getElementById('fieldCheckResults');
            
            const requiredFields = [
                { name: 'name', label: '商品名称', required: true },
                { name: 'price', label: '价格', required: true },
                { name: 'stock', label: '库存', required: true },
                { name: 'mainImage', label: '主图', required: true },
                { name: 'description', label: '描述', required: false },
                { name: 'brand', label: '品牌', required: false },
                { name: 'model', label: '型号', required: false },
                { name: 'features', label: '特点', required: false },
                { name: 'specifications', label: '规格参数', required: false },
                { name: 'services', label: '服务', required: false },
                { name: 'images', label: '轮播图', required: false },
                { name: 'detailImage', label: '详情图', required: false }
            ];

            let html = '<div class="row">';
            requiredFields.forEach((field, index) => {
                if (index % 3 === 0 && index > 0) {
                    html += '</div><div class="row">';
                }
                
                const value = product[field.name];
                const hasValue = value !== null && value !== undefined && value !== '';
                const status = field.required ? (hasValue ? 'success' : 'error') : (hasValue ? 'success' : 'warning');
                
                html += `
                    <div class="col-md-4 mb-2">
                        <div class="d-flex align-items-center">
                            <span class="status-badge status-${status} me-2">
                                ${hasValue ? '✅' : (field.required ? '❌' : '⚠️')}
                            </span>
                            <small>${field.label}</small>
                        </div>
                    </div>
                `;
            });
            html += '</div>';
            
            container.innerHTML = html;
        }

        // 清空结果
        function clearResults() {
            document.getElementById('apiResults').innerHTML = '<div class="text-muted">等待测试...</div>';
            document.getElementById('dataProcessingResults').innerHTML = '<div class="text-muted">等待测试...</div>';
            document.getElementById('fieldCheckResults').innerHTML = '<div class="text-muted">等待测试...</div>';
        }

        // 页面加载时自动测试
        document.addEventListener('DOMContentLoaded', function() {
            // 可以在这里添加自动测试逻辑
        });
    </script>
</body>
</html>
