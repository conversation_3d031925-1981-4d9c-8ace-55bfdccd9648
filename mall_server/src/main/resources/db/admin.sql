-- 管理员表
CREATE TABLE IF NOT EXISTS `admin_users` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '管理员ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码（加密存储）',
  `real_name` varchar(50) DEFAULT NULL COMMENT '真实姓名',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像URL',
  
  -- 权限相关
  `role` varchar(20) DEFAULT 'admin' COMMENT '角色：super_admin-超级管理员，admin-管理员，operator-操作员',
  `permissions` text COMMENT '权限列表（JSON数组）',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态 0-禁用 1-启用',
  
  -- 登录相关
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(50) DEFAULT NULL COMMENT '最后登录IP',
  `login_count` int(11) DEFAULT '0' COMMENT '登录次数',
  `password_updated_at` datetime DEFAULT NULL COMMENT '密码更新时间',
  
  -- 系统字段
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `created_by` bigint(20) DEFAULT NULL COMMENT '创建人ID',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  KEY `idx_email` (`email`),
  KEY `idx_status` (`status`),
  KEY `idx_role` (`role`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员用户表';

-- 管理员操作日志表
CREATE TABLE IF NOT EXISTS `admin_operation_logs` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `admin_id` bigint(20) NOT NULL COMMENT '管理员ID',
  `admin_username` varchar(50) NOT NULL COMMENT '管理员用户名',
  `operation_type` varchar(50) NOT NULL COMMENT '操作类型',
  `operation_desc` varchar(255) NOT NULL COMMENT '操作描述',
  `target_type` varchar(50) DEFAULT NULL COMMENT '操作对象类型',
  `target_id` bigint(20) DEFAULT NULL COMMENT '操作对象ID',
  `request_method` varchar(10) DEFAULT NULL COMMENT '请求方法',
  `request_url` varchar(255) DEFAULT NULL COMMENT '请求URL',
  `request_params` text COMMENT '请求参数',
  `response_result` text COMMENT '响应结果',
  `ip_address` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `execution_time` int(11) DEFAULT NULL COMMENT '执行时间（毫秒）',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态 0-失败 1-成功',
  `error_message` text COMMENT '错误信息',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  
  PRIMARY KEY (`id`),
  KEY `idx_admin_id` (`admin_id`),
  KEY `idx_operation_type` (`operation_type`),
  KEY `idx_target` (`target_type`, `target_id`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_admin_operation_logs_admin_id` FOREIGN KEY (`admin_id`) REFERENCES `admin_users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员操作日志表';

-- 插入默认超级管理员账号
-- 密码：admin123 （使用BCrypt加密）
INSERT INTO `admin_users` (`id`, `username`, `password`, `real_name`, `email`, `phone`, `role`, `permissions`, `status`, `login_count`, `created_at`, `updated_at`, `password_updated_at`) VALUES
(1, 'admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXIGfkPNtmismLQdQIG.ZjUON4C', '超级管理员', '<EMAIL>', '13800000000', 'super_admin', 
'["user_management", "engineer_management", "service_center_management", "order_management", "review_management", "system_management"]', 
1, 0, NOW(), NOW(), NOW()),

(2, 'operator', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXIGfkPNtmismLQdQIG.ZjUON4C', '运营管理员', '<EMAIL>', '13800000001', 'admin', 
'["engineer_management", "service_center_management", "order_management", "review_management"]', 
1, 0, NOW(), NOW(), NOW());

-- 插入一些示例操作日志
INSERT INTO `admin_operation_logs` (`id`, `admin_id`, `admin_username`, `operation_type`, `operation_desc`, `target_type`, `target_id`, `request_method`, `request_url`, `ip_address`, `status`, `created_at`) VALUES
(1, 1, 'admin', 'LOGIN', '管理员登录', 'admin', 1, 'POST', '/admin/login', '127.0.0.1', 1, NOW()),
(2, 1, 'admin', 'ENGINEER_APPROVE', '审核通过工程师申请', 'engineer', 1, 'PUT', '/admin/engineers/1/approve', '127.0.0.1', 1, NOW()),
(3, 1, 'admin', 'SERVICE_CENTER_APPROVE', '审核通过服务网点申请', 'service_center', 1, 'PUT', '/admin/service-centers/1/approve', '127.0.0.1', 1, NOW());
