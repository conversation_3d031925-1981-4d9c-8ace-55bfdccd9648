-- 商城订单表
CREATE TABLE IF NOT EXISTS `mall_orders` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_no` varchar(50) NOT NULL COMMENT '订单编号',
  `open_id` varchar(100) NOT NULL COMMENT '用户openId',
  `user_nickname` varchar(50) DEFAULT NULL COMMENT '用户昵称（冗余字段）',
  `order_type` varchar(20) DEFAULT 'normal' COMMENT '订单类型（normal-普通订单，presale-预售订单）',
  `order_status` varchar(20) NOT NULL DEFAULT 'pending_payment' COMMENT '订单状态（pending_payment-待付款，paid-已付款，shipped-已发货，delivered-已送达，completed-已完成，cancelled-已取消，refunding-退款中，refunded-已退款）',
  `payment_status` varchar(20) DEFAULT 'unpaid' COMMENT '支付状态（unpaid-未支付，paid-已支付，refunded-已退款）',
  `shipping_status` varchar(20) DEFAULT 'unshipped' COMMENT '物流状态（unshipped-未发货，shipped-已发货，delivered-已送达）',
  
  -- 收货信息
  `receiver_name` varchar(50) NOT NULL COMMENT '收货人姓名',
  `receiver_phone` varchar(20) NOT NULL COMMENT '收货人手机号',
  `receiver_province` varchar(50) NOT NULL COMMENT '收货省份',
  `receiver_city` varchar(50) NOT NULL COMMENT '收货城市',
  `receiver_district` varchar(50) NOT NULL COMMENT '收货区县',
  `receiver_address` varchar(255) NOT NULL COMMENT '收货详细地址',
  `receiver_full_address` varchar(500) NOT NULL COMMENT '完整收货地址',
  `address_id` bigint(20) DEFAULT NULL COMMENT '地址ID（关联用户地址表）',
  
  -- 金额信息
  `total_amount` decimal(10,2) NOT NULL COMMENT '商品总金额',
  `shipping_fee` decimal(10,2) DEFAULT '0.00' COMMENT '运费',
  `discount_amount` decimal(10,2) DEFAULT '0.00' COMMENT '优惠金额',
  `coupon_amount` decimal(10,2) DEFAULT '0.00' COMMENT '优惠券金额',
  `actual_amount` decimal(10,2) NOT NULL COMMENT '实际支付金额',
  
  -- 支付信息
  `payment_method` varchar(20) DEFAULT NULL COMMENT '支付方式',
  `payment_time` datetime DEFAULT NULL COMMENT '支付时间',
  `payment_transaction_id` varchar(100) DEFAULT NULL COMMENT '支付交易号',
  
  -- 物流信息
  `shipping_company` varchar(50) DEFAULT NULL COMMENT '物流公司',
  `shipping_no` varchar(100) DEFAULT NULL COMMENT '物流单号',
  `shipped_time` datetime DEFAULT NULL COMMENT '发货时间',
  `delivered_time` datetime DEFAULT NULL COMMENT '送达时间',
  
  -- 其他信息
  `remark` text COMMENT '订单备注',
  `cancel_reason` varchar(255) DEFAULT NULL COMMENT '取消原因',
  `refund_reason` varchar(255) DEFAULT NULL COMMENT '退款原因',
  `refund_amount` decimal(10,2) DEFAULT '0.00' COMMENT '退款金额',
  `invoice_type` varchar(20) DEFAULT 'none' COMMENT '发票类型（none-不开票，personal-个人，company-企业）',
  `invoice_title` varchar(100) DEFAULT NULL COMMENT '发票抬头',
  `invoice_tax_no` varchar(50) DEFAULT NULL COMMENT '纳税人识别号',
  
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `completed_at` datetime DEFAULT NULL COMMENT '完成时间',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `idx_open_id` (`open_id`),
  KEY `idx_order_status` (`order_status`),
  KEY `idx_payment_status` (`payment_status`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_address_id` (`address_id`),
  CONSTRAINT `fk_mall_orders_address_id` FOREIGN KEY (`address_id`) REFERENCES `user_addresses` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商城订单表';
