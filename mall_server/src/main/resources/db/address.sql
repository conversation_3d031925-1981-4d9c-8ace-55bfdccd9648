-- 地址表
CREATE TABLE IF NOT EXISTS `user_addresses` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '地址ID',
  `open_id` varchar(100) NOT NULL COMMENT '用户openId',
  `name` varchar(50) NOT NULL COMMENT '收货人姓名',
  `phone` varchar(20) NOT NULL COMMENT '收货人手机号',
  `province` varchar(50) NOT NULL COMMENT '省份',
  `city` varchar(50) NOT NULL COMMENT '城市',
  `district` varchar(50) NOT NULL COMMENT '区/县',
  `address` varchar(255) NOT NULL COMMENT '详细地址',
  `tag` varchar(20) DEFAULT NULL COMMENT '地址标签（家、公司、学校等）',
  `is_default` tinyint(1) DEFAULT '0' COMMENT '是否默认地址 0-否 1-是',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_open_id` (`open_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户地址表';
