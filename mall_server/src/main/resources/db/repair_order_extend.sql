-- 扩展repair_orders表，添加维修详情和耗材信息字段

-- 添加维修详情相关字段
ALTER TABLE `repair_orders` 
ADD COLUMN `repair_result` varchar(20) DEFAULT NULL COMMENT '维修结果（success-维修成功，partial-部分修复，failed-维修失败，replacement-需要更换设备）' AFTER `remark`,
ADD COLUMN `repair_time` decimal(4,1) DEFAULT NULL COMMENT '维修耗时（小时）' AFTER `repair_result`,
ADD COLUMN `repair_description` text DEFAULT NULL COMMENT '维修详情描述' AFTER `repair_time`,
ADD COLUMN `repair_images` text DEFAULT NULL COMMENT '维修过程照片（JSON数组）' AFTER `repair_description`;

-- 添加费用明细字段
ALTER TABLE `repair_orders`
ADD COLUMN `labor_fee` decimal(10,2) DEFAULT '0.00' COMMENT '人工费' AFTER `repair_images`,
ADD COLUMN `service_fee` decimal(10,2) DEFAULT '0.00' COMMENT '服务费' AFTER `labor_fee`,
ADD COLUMN `materials_fee` decimal(10,2) DEFAULT '0.00' COMMENT '耗材费用' AFTER `service_fee`;

-- 添加耗材明细字段
ALTER TABLE `repair_orders`
ADD COLUMN `materials_detail` text DEFAULT NULL COMMENT '耗材明细（JSON数组）' AFTER `materials_fee`;

-- 添加完成时间字段
ALTER TABLE `repair_orders`
ADD COLUMN `completed_at` datetime DEFAULT NULL COMMENT '完成时间' AFTER `materials_detail`;

-- 添加评价相关字段
ALTER TABLE `repair_orders`
ADD COLUMN `rating` decimal(2,1) DEFAULT NULL COMMENT '用户评分（1-5分）' AFTER `completed_at`,
ADD COLUMN `review_content` text DEFAULT NULL COMMENT '用户评价内容' AFTER `rating`,
ADD COLUMN `review_images` text DEFAULT NULL COMMENT '评价照片（JSON数组）' AFTER `review_content`,
ADD COLUMN `review_time` datetime DEFAULT NULL COMMENT '评价时间' AFTER `review_images`;

-- 添加账单相关字段
ALTER TABLE `repair_orders`
ADD COLUMN `bill_sent` tinyint(1) DEFAULT '0' COMMENT '是否已发送账单（0-未发送，1-已发送）' AFTER `review_time`,
ADD COLUMN `bill_sent_time` datetime DEFAULT NULL COMMENT '账单发送时间' AFTER `bill_sent`;

-- 更新现有字段注释，使其更清晰
ALTER TABLE `repair_orders` 
MODIFY COLUMN `repair_fee` decimal(10,2) DEFAULT '0.00' COMMENT '维修费用（已废弃，使用labor_fee）',
MODIFY COLUMN `parts_fee` decimal(10,2) DEFAULT '0.00' COMMENT '配件费用（已废弃，使用materials_fee）';

-- 创建索引以提高查询性能
CREATE INDEX `idx_repair_result` ON `repair_orders` (`repair_result`);
CREATE INDEX `idx_completed_at` ON `repair_orders` (`completed_at`);
CREATE INDEX `idx_rating` ON `repair_orders` (`rating`);

-- 显示表结构
DESCRIBE `repair_orders`;
