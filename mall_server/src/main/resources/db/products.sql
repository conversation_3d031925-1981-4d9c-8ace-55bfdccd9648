-- 商品表
CREATE TABLE IF NOT EXISTS `products` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '商品ID',
  `name` varchar(255) NOT NULL COMMENT '商品名称',
  `short_name` varchar(100) DEFAULT NULL COMMENT '商品简称（用于列表显示）',
  `price` decimal(10,2) NOT NULL COMMENT '商品价格',
  `original_price` decimal(10,2) DEFAULT NULL COMMENT '原价（用于显示折扣）',
  `sales` int(11) DEFAULT '0' COMMENT '销量',
  `stock` int(11) DEFAULT '0' COMMENT '库存',
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `brand` varchar(100) DEFAULT NULL COMMENT '品牌',
  `model` varchar(100) DEFAULT NULL COMMENT '型号',
  `main_image` varchar(500) NOT NULL COMMENT '主图URL',
  `images` text COMMENT '商品图片组（JSON数组）',
  `detail_image` varchar(500) DEFAULT NULL COMMENT '详情图URL',
  `features` text COMMENT '产品特点（JSON数组）',
  `compatible_cars` text COMMENT '适用车型',
  `description` text COMMENT '商品描述',
  `specifications` text COMMENT '规格参数（JSON数组）',
  `specs` text COMMENT '规格选项（JSON数组）',
  `services` text COMMENT '服务说明（JSON数组）',
  `weight` decimal(8,2) DEFAULT NULL COMMENT '重量（kg）',
  `dimensions` varchar(100) DEFAULT NULL COMMENT '尺寸',
  `warranty_period` varchar(50) DEFAULT NULL COMMENT '保修期',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态 0-下架 1-上架',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序权重',
  `seo_title` varchar(255) DEFAULT NULL COMMENT 'SEO标题',
  `seo_keywords` varchar(500) DEFAULT NULL COMMENT 'SEO关键词',
  `seo_description` text COMMENT 'SEO描述',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_status` (`status`),
  KEY `idx_sales` (`sales`),
  KEY `idx_price` (`price`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品表';

-- 商品分类表
CREATE TABLE IF NOT EXISTS `product_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `name` varchar(50) NOT NULL COMMENT '分类名称',
  `icon` varchar(255) DEFAULT NULL COMMENT '分类图标',
  `selected_icon` varchar(255) DEFAULT NULL COMMENT '选中状态图标',
  `parent_id` int(11) DEFAULT '0' COMMENT '父分类ID，0表示顶级分类',
  `level` tinyint(4) DEFAULT '1' COMMENT '分类层级',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序权重',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态 0-禁用 1-启用',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_status` (`status`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品分类表';

-- 插入默认分类数据
INSERT INTO `product_categories` (`id`, `name`, `icon`, `selected_icon`, `parent_id`, `level`, `sort_order`, `status`, `created_at`, `updated_at`) VALUES
(1, '充电枪', '/images/icons/充电枪.png', '/images/icons/充电枪_selected.png', 0, 1, 1, 1, NOW(), NOW()),
(2, '线缆', '/images/icons/线缆.png', '/images/icons/线缆_selected.png', 0, 1, 2, 1, NOW(), NOW()),
(3, '模块', '/images/icons/模块.png', '/images/icons/模块_selected.png', 0, 1, 3, 1, NOW(), NOW()),
(4, '保护器', '/images/icons/保护器.png', '/images/icons/保护器_selected.png', 0, 1, 4, 1, NOW(), NOW()),
(5, '配件', '/images/icons/配件.png', '/images/icons/配件_selected.png', 0, 1, 5, 1, NOW(), NOW()),
(6, '工具', '/images/icons/工具.png', '/images/icons/工具_selected.png', 0, 1, 6, 1, NOW(), NOW());
