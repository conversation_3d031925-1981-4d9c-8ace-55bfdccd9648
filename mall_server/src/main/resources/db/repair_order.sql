-- 维修订单表
CREATE TABLE IF NOT EXISTS `repair_orders` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '维修订单ID',
  `order_no` varchar(50) NOT NULL COMMENT '订单编号',
  `open_id` varchar(100) NOT NULL COMMENT '用户openId',
  `fault_type` varchar(50) NOT NULL COMMENT '故障类型',
  `model` varchar(100) NOT NULL COMMENT '充电桩型号',
  `description` text NOT NULL COMMENT '故障描述',
  `name` varchar(50) NOT NULL COMMENT '联系人姓名',
  `phone` varchar(20) NOT NULL COMMENT '联系电话',
  `address_id` bigint(20) DEFAULT NULL COMMENT '地址ID',
  `full_address` varchar(255) NOT NULL COMMENT '完整地址',
  `service_type` varchar(20) NOT NULL COMMENT '服务方式（home-上门维修，remote-远程指导）',
  `images` text COMMENT '故障照片（JSON数组）',
  `appointment_date` date NOT NULL COMMENT '预约日期',
  `appointment_time` varchar(20) NOT NULL COMMENT '预约时间段',
  `status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '订单状态（pending-待接单，accepted-待上门，processing-维修中，completed-已完成）',
  `engineer_id` bigint(20) DEFAULT NULL COMMENT '工程师ID',
  `engineer_name` varchar(50) DEFAULT NULL COMMENT '工程师姓名',
  `engineer_phone` varchar(20) DEFAULT NULL COMMENT '工程师电话',
  `repair_fee` decimal(10,2) DEFAULT '0.00' COMMENT '维修费用',
  `parts_fee` decimal(10,2) DEFAULT '0.00' COMMENT '配件费用',
  `total_fee` decimal(10,2) DEFAULT '0.00' COMMENT '总费用',
  `remark` text COMMENT '备注',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `idx_open_id` (`open_id`),
  KEY `idx_status` (`status`),
  KEY `idx_appointment_date` (`appointment_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='维修订单表';
