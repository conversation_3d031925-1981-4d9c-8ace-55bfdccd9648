-- 客服系统相关表

-- 1. 客服会话表
CREATE TABLE IF NOT EXISTS `customer_service_sessions` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '会话ID',
  `session_id` varchar(100) NOT NULL COMMENT '会话唯一标识',
  `open_id` varchar(100) NOT NULL COMMENT '用户openId',
  `user_nickname` varchar(50) DEFAULT NULL COMMENT '用户昵称',
  `user_avatar` varchar(255) DEFAULT NULL COMMENT '用户头像',
  `admin_id` bigint(20) DEFAULT NULL COMMENT '分配的客服管理员ID',
  `admin_name` varchar(50) DEFAULT NULL COMMENT '客服管理员姓名',
  `status` varchar(20) DEFAULT 'waiting' COMMENT '会话状态：waiting-等待接入，active-进行中，closed-已关闭',
  `last_message_time` datetime DEFAULT NULL COMMENT '最后消息时间',
  `last_message_content` text COMMENT '最后一条消息内容',
  `unread_count_user` int(11) DEFAULT '0' COMMENT '用户未读消息数',
  `unread_count_admin` int(11) DEFAULT '0' COMMENT '客服未读消息数',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `closed_at` datetime DEFAULT NULL COMMENT '关闭时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_session_id` (`session_id`),
  KEY `idx_open_id` (`open_id`),
  KEY `idx_admin_id` (`admin_id`),
  KEY `idx_status` (`status`),
  KEY `idx_last_message_time` (`last_message_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客服会话表';

-- 2. 客服消息表
CREATE TABLE IF NOT EXISTS `customer_service_messages` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '消息ID',
  `session_id` varchar(100) NOT NULL COMMENT '会话ID',
  `sender_type` varchar(20) NOT NULL COMMENT '发送者类型：user-用户，admin-客服',
  `sender_id` varchar(100) NOT NULL COMMENT '发送者ID（用户为openId，客服为admin_id）',
  `sender_name` varchar(50) DEFAULT NULL COMMENT '发送者姓名',
  `sender_avatar` varchar(255) DEFAULT NULL COMMENT '发送者头像',
  `message_type` varchar(20) DEFAULT 'text' COMMENT '消息类型：text-文本，image-图片，file-文件',
  `content` text NOT NULL COMMENT '消息内容',
  `extra_data` text COMMENT '额外数据（JSON格式，如图片URL、文件信息等）',
  `is_read` tinyint(1) DEFAULT '0' COMMENT '是否已读：0-未读，1-已读',
  `read_at` datetime DEFAULT NULL COMMENT '阅读时间',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_session_id` (`session_id`),
  KEY `idx_sender_type` (`sender_type`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_is_read` (`is_read`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客服消息表';

-- 3. 客服工作状态表
CREATE TABLE IF NOT EXISTS `customer_service_status` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `admin_id` bigint(20) NOT NULL COMMENT '管理员ID',
  `admin_name` varchar(50) NOT NULL COMMENT '管理员姓名',
  `status` varchar(20) DEFAULT 'offline' COMMENT '状态：online-在线，busy-忙碌，offline-离线',
  `max_sessions` int(11) DEFAULT '5' COMMENT '最大同时接待会话数',
  `current_sessions` int(11) DEFAULT '0' COMMENT '当前接待会话数',
  `last_active_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '最后活跃时间',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_admin_id` (`admin_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客服工作状态表';

-- 4. 客服快捷回复表
CREATE TABLE IF NOT EXISTS `customer_service_quick_replies` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `admin_id` bigint(20) DEFAULT NULL COMMENT '管理员ID（NULL表示公共快捷回复）',
  `category` varchar(50) DEFAULT NULL COMMENT '分类',
  `title` varchar(100) NOT NULL COMMENT '快捷回复标题',
  `content` text NOT NULL COMMENT '快捷回复内容',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否启用',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_admin_id` (`admin_id`),
  KEY `idx_category` (`category`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客服快捷回复表';

-- 插入一些默认的快捷回复
INSERT INTO `customer_service_quick_replies` (`category`, `title`, `content`, `sort_order`) VALUES
('greeting', '欢迎语', '您好！欢迎使用成都桩郎中新能源技术有限公司客服服务，我是您的专属客服，有什么可以帮助您的吗？', 1),
('greeting', '稍等回复', '好的，请稍等，我马上为您查询处理。', 2),
('service', '维修服务', '我们提供专业的充电桩维修服务，包括上门维修和远程指导。您可以通过小程序提交维修申请，我们会安排专业工程师为您服务。', 3),
('service', '服务范围', '我们的服务覆盖全国主要城市，支持各种品牌的充电桩维修。具体服务范围请告诉我您的所在城市，我来为您查询。', 4),
('order', '订单查询', '您可以在小程序的"我的订单"中查看订单状态，或者告诉我您的订单号，我来为您查询。', 5),
('order', '取消订单', '如需取消订单，请提供您的订单号，我来为您处理。请注意，已分配工程师的订单可能会产生取消费用。', 6),
('common', '联系方式', '如需紧急联系，您可以拨打我们的客服热线：400-123-4567（工作时间：9:00-18:00）', 7),
('common', '感谢语', '感谢您的咨询，如果还有其他问题，随时联系我们。祝您生活愉快！', 8);

-- 创建索引以提高查询性能
CREATE INDEX `idx_session_open_id_status` ON `customer_service_sessions` (`open_id`, `status`);
CREATE INDEX `idx_message_session_time` ON `customer_service_messages` (`session_id`, `created_at`);
