-- 插入测试工程师数据
INSERT INTO `engineers` (
    `name`, `phone`, `email`, `gender`, `age`, `id_card`,
    `specialties`, `experience_years`, `education`, `certifications`, `skills`,
    `work_areas`, `work_time`, `hourly_rate`, `service_fee`,
    `bio`, `introduction`, `status`, `is_online`, `is_available`,
    `rating`, `total_orders`, `completed_orders`, `success_rate`,
    `created_at`, `updated_at`
) VALUES 
(
    '张工程师', '13900139001', '<EMAIL>', '男', 32, '110101199001011234',
    '["充电故障", "通信故障", "硬件故障"]', 8, '本科', '["电工证", "充电桩维修证"]', '["电路维修", "通信调试", "硬件更换"]',
    '["北京市", "天津市"]', '周一至周日 8:00-18:00', 150.00, 50.00,
    '专业充电桩维修工程师，8年维修经验', '擅长各类充电桩故障诊断和维修，服务态度好，技术过硬',
    'approved', true, true, 4.8, 156, 148, 94.87,
    NOW(), NOW()
),
(
    '李工程师', '13900139002', '<EMAIL>', '男', 28, '110101199201011234',
    '["软件故障", "网络故障", "系统升级"]', 5, '本科', '["软件工程师证", "网络工程师证"]', '["软件调试", "网络配置", "系统维护"]',
    '["上海市", "江苏省"]', '周一至周六 9:00-17:00', 120.00, 40.00,
    '软件和网络专家，专注充电桩智能化维修', '精通充电桩软件系统和网络通信，解决问题快速高效',
    'approved', true, true, 4.9, 89, 86, 96.63,
    NOW(), NOW()
),
(
    '王工程师', '13900139003', '<EMAIL>', '女', 35, '110101198801011234',
    '["电源故障", "安全故障", "显示故障"]', 10, '硕士', '["高级电工证", "安全工程师证"]', '["电源维修", "安全检测", "显示屏维修"]',
    '["广州市", "深圳市"]', '周一至周日 7:00-19:00', 180.00, 60.00,
    '资深电气工程师，安全维修专家', '10年电气维修经验，特别擅长电源和安全系统故障处理',
    'approved', false, true, 4.95, 203, 198, 97.54,
    NOW(), NOW()
),
(
    '赵工程师', '13900139004', '<EMAIL>', '男', 26, '110101199401011234',
    '["通用维修", "预防性维护"]', 3, '专科', '["电工证"]', '["基础维修", "设备保养"]',
    '["杭州市", "宁波市"]', '周一至周五 8:30-17:30', 100.00, 30.00,
    '年轻有为的维修工程师', '虽然经验不多，但学习能力强，服务认真负责',
    'approved', true, true, 4.6, 45, 42, 93.33,
    NOW(), NOW()
),
(
    '陈工程师', '13900139005', '<EMAIL>', '男', 40, '110101198301011234',
    '["硬件故障", "机械故障", "结构维修"]', 15, '本科', '["机械工程师证", "高级维修工证"]', '["机械维修", "结构调整", "硬件更换"]',
    '["成都市", "重庆市"]', '周一至周日 6:00-20:00', 200.00, 80.00,
    '机械维修专家，经验丰富', '15年机械和硬件维修经验，能处理各种复杂故障',
    'approved', true, true, 4.85, 312, 298, 95.51,
    NOW(), NOW()
);

-- 更新一些工程师的在线状态和可用性
UPDATE engineers SET is_online = false WHERE name = '王工程师';
UPDATE engineers SET is_available = false WHERE name IN ('陈工程师');

-- 为工程师添加一些工作照片（示例）
UPDATE engineers SET work_photos = '["photo1.jpg", "photo2.jpg", "photo3.jpg"]' WHERE name = '张工程师';
UPDATE engineers SET work_photos = '["photo4.jpg", "photo5.jpg"]' WHERE name = '李工程师';
