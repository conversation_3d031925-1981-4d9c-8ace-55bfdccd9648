-- 插入测试维修订单数据
INSERT INTO `repair_orders` (
    `order_no`, `open_id`, `fault_type`, `model`, `description`, 
    `name`, `phone`, `full_address`, `service_type`, 
    `appointment_date`, `appointment_time`, `status`, 
    `created_at`, `updated_at`
) VALUES 
(
    'RO202312010001', 'test_openid_001', '充电故障', 'DC-60KW-001', 
    '充电桩无法正常启动，显示屏黑屏，指示灯不亮', 
    '张先生', '13800138001', '北京市朝阳区建国门外大街1号', 'home',
    '2023-12-02', '09:00-12:00', 'pending',
    NOW(), NOW()
),
(
    'RO202312010002', 'test_openid_002', '通信故障', 'AC-7KW-002', 
    '充电桩与后台通信异常，无法远程监控', 
    '李女士', '13800138002', '上海市浦东新区陆家嘴环路1000号', 'home',
    '2023-12-02', '14:00-17:00', 'accepted',
    NOW(), NOW()
),
(
    'RO202312010003', 'test_openid_003', '硬件故障', 'DC-120KW-003', 
    '充电枪接触不良，充电过程中经常断开', 
    '王先生', '13800138003', '广州市天河区珠江新城花城大道85号', 'home',
    '2023-12-01', '10:00-12:00', 'processing',
    NOW(), NOW()
),
(
    'RO202312010004', 'test_openid_004', '软件故障', 'AC-22KW-004', 
    '充电桩系统升级后无法正常工作', 
    '赵女士', '13800138004', '深圳市南山区科技园南区深南大道9988号', 'remote',
    '2023-11-30', '15:00-18:00', 'completed',
    NOW(), NOW()
),
(
    'RO202312010005', 'test_openid_005', '电源故障', 'DC-180KW-005', 
    '充电桩电源模块故障，无法提供正常功率', 
    '陈先生', '13800138005', '杭州市西湖区文三路259号', 'home',
    '2023-12-03', '08:00-11:00', 'pending',
    NOW(), NOW()
),
(
    'RO202312010006', 'test_openid_006', '显示故障', 'AC-11KW-006', 
    '充电桩显示屏花屏，无法正常显示信息', 
    '刘女士', '13800138006', '成都市锦江区红星路三段1号', 'home',
    '2023-12-03', '13:00-16:00', 'pending',
    NOW(), NOW()
),
(
    'RO202312010007', 'test_openid_007', '网络故障', 'DC-60KW-007', 
    '充电桩网络连接异常，支付功能无法使用', 
    '周先生', '13800138007', '武汉市武昌区中南路99号', 'home',
    '2023-12-04', '09:30-12:30', 'accepted',
    NOW(), NOW()
),
(
    'RO202312010008', 'test_openid_008', '安全故障', 'AC-7KW-008', 
    '充电桩漏电保护器频繁跳闸', 
    '吴女士', '13800138008', '西安市雁塔区高新四路15号', 'home',
    '2023-12-04', '14:30-17:30', 'processing',
    NOW(), NOW()
);

-- 更新部分订单的工程师信息（假设已有工程师数据）
UPDATE repair_orders SET 
    engineer_id = 1, 
    engineer_name = '张工程师', 
    engineer_phone = '13900139001'
WHERE order_no IN ('RO202312010002', 'RO202312010003', 'RO202312010007', 'RO202312010008');

-- 更新已完成订单的费用信息
UPDATE repair_orders SET 
    repair_fee = 200.00,
    parts_fee = 150.00,
    total_fee = 350.00,
    remark = '更换充电模块，系统重新配置完成'
WHERE order_no = 'RO202312010004';
