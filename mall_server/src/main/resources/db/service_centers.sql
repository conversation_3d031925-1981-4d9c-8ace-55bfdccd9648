-- 服务网点表
CREATE TABLE IF NOT EXISTS `service_centers` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '服务网点ID',
  `name` varchar(100) NOT NULL COMMENT '网点名称',
  `address` varchar(255) NOT NULL COMMENT '详细地址',
  `province` varchar(50) NOT NULL COMMENT '省份',
  `city` varchar(50) NOT NULL COMMENT '城市',
  `district` varchar(50) DEFAULT NULL COMMENT '区县',
  `phone` varchar(20) NOT NULL COMMENT '联系电话',
  `contact_person` varchar(50) DEFAULT NULL COMMENT '联系人',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱地址',

  -- 位置信息
  `latitude` decimal(10,7) NOT NULL COMMENT '纬度',
  `longitude` decimal(10,7) NOT NULL COMMENT '经度',
  `location_accuracy` varchar(50) DEFAULT NULL COMMENT '位置精度描述',

  -- 营业信息
  `business_hours` varchar(100) DEFAULT NULL COMMENT '营业时间',
  `business_days` varchar(50) DEFAULT '周一至周日' COMMENT '营业日期',
  `is_24_hours` tinyint(1) DEFAULT '0' COMMENT '是否24小时营业',

  -- 服务信息
  `service_types` text COMMENT '服务类型（JSON数组）',
  `equipment_types` text COMMENT '支持设备类型（JSON数组）',
  `service_description` text COMMENT '服务描述',
  `facilities` text COMMENT '设施设备（JSON数组）',

  -- 评价信息
  `rating` decimal(3,2) DEFAULT '5.00' COMMENT '平均评分',
  `review_count` int(11) DEFAULT '0' COMMENT '评价数量',
  `total_services` int(11) DEFAULT '0' COMMENT '总服务次数',

  -- 费用信息
  `service_fee` decimal(8,2) DEFAULT '0.00' COMMENT '基础服务费',
  `inspection_fee` decimal(8,2) DEFAULT '0.00' COMMENT '检测费用',
  `parking_info` varchar(255) DEFAULT NULL COMMENT '停车信息',

  -- 状态管理
  `status` varchar(20) DEFAULT 'pending' COMMENT '状态：pending-待审核，approved-已通过，rejected-已拒绝，suspended-已暂停，closed-已关闭',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否启用 0-禁用 1-启用',
  `is_featured` tinyint(1) DEFAULT '0' COMMENT '是否推荐 0-否 1-是',

  -- 审核信息
  `application_time` datetime DEFAULT NULL COMMENT '申请时间',
  `review_time` datetime DEFAULT NULL COMMENT '审核时间',
  `reviewer_id` bigint(20) DEFAULT NULL COMMENT '审核人ID',
  `review_notes` text COMMENT '审核备注',
  `rejection_reason` text COMMENT '拒绝原因',

  -- 资质信息
  `business_license` varchar(255) DEFAULT NULL COMMENT '营业执照',
  `qualification_certificates` text COMMENT '资质证书（JSON数组）',
  `images` text COMMENT '网点图片（JSON数组）',

  -- 系统字段
  `sort_order` int(11) DEFAULT '0' COMMENT '排序权重',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `last_active_at` datetime DEFAULT NULL COMMENT '最后活跃时间',

  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_phone` (`phone`),
  KEY `idx_status` (`status`),
  KEY `idx_location` (`latitude`, `longitude`),
  KEY `idx_city` (`province`, `city`, `district`),
  KEY `idx_rating` (`rating`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_is_featured` (`is_featured`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='服务网点表';

-- 服务网点服务类型表（用于规范化管理）
CREATE TABLE IF NOT EXISTS `service_center_types` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '服务类型ID',
  `name` varchar(50) NOT NULL COMMENT '服务类型名称',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `icon` varchar(255) DEFAULT NULL COMMENT '图标URL',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序权重',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态 0-禁用 1-启用',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_name` (`name`),
  KEY `idx_status` (`status`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='服务网点服务类型表';

-- 服务网点评价表
CREATE TABLE IF NOT EXISTS `service_center_reviews` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '评价ID',
  `service_center_id` bigint(20) NOT NULL COMMENT '服务网点ID',
  `order_id` bigint(20) DEFAULT NULL COMMENT '订单ID',
  `open_id` varchar(100) NOT NULL COMMENT '用户openId',
  `user_nickname` varchar(50) DEFAULT NULL COMMENT '用户昵称',
  `user_avatar` varchar(255) DEFAULT NULL COMMENT '用户头像',
  `rating` tinyint(4) NOT NULL COMMENT '评分 1-5星',
  `content` text NOT NULL COMMENT '评价内容',
  `images` text COMMENT '评价图片（JSON数组）',
  `service_type` varchar(100) DEFAULT NULL COMMENT '服务类型',
  `is_anonymous` tinyint(1) DEFAULT '0' COMMENT '是否匿名评价',
  `reply_content` text COMMENT '网点回复内容',
  `reply_time` datetime DEFAULT NULL COMMENT '回复时间',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态 0-隐藏 1-显示',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_service_center_id` (`service_center_id`),
  KEY `idx_open_id` (`open_id`),
  KEY `idx_rating` (`rating`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_service_center_reviews_center_id` FOREIGN KEY (`service_center_id`) REFERENCES `service_centers` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='服务网点评价表';

-- 插入服务类型数据
INSERT INTO `service_center_types` (`id`, `name`, `description`, `icon`, `sort_order`, `status`, `created_at`, `updated_at`) VALUES
(1, '充电桩维修', '各类充电桩故障维修服务', '/images/icons/repair.png', 1, 1, NOW(), NOW()),
(2, '充电桩安装', '新充电桩安装调试服务', '/images/icons/install.png', 2, 1, NOW(), NOW()),
(3, '设备检测', '充电设备安全检测服务', '/images/icons/check.png', 3, 1, NOW(), NOW()),
(4, '系统升级', '充电桩软硬件升级服务', '/images/icons/upgrade.png', 4, 1, NOW(), NOW()),
(5, '预防性维护', '定期保养维护服务', '/images/icons/maintain.png', 5, 1, NOW(), NOW()),
(6, '应急抢修', '24小时应急抢修服务', '/images/icons/emergency.png', 6, 1, NOW(), NOW());

-- 插入测试服务网点数据
INSERT INTO `service_centers` (`id`, `name`, `address`, `province`, `city`, `district`, `phone`, `contact_person`, `email`, `latitude`, `longitude`, `location_accuracy`, `business_hours`, `business_days`, `is_24_hours`, `service_types`, `equipment_types`, `service_description`, `facilities`, `rating`, `review_count`, `total_services`, `service_fee`, `inspection_fee`, `parking_info`, `status`, `is_active`, `is_featured`, `application_time`, `review_time`, `reviewer_id`, `review_notes`, `business_license`, `qualification_certificates`, `images`, `sort_order`, `created_at`, `updated_at`, `last_active_at`) VALUES

(1, '成都中心维修站', '中国四川省成都市龙泉驿区翠柏路288号', '四川省', '成都市', '龙泉驿区', '***********', '张经理', '<EMAIL>', 30.502928, 104.231101, '精确定位', '09:00-18:00', '周一至周日', 0,
'["充电桩维修", "充电桩安装", "设备检测", "系统升级"]',
'["交流充电桩", "直流充电桩", "便携式充电器", "充电线缆"]',
'成都地区最大的充电桩维修服务中心，拥有专业的技术团队和完善的设备，提供全方位的充电桩服务。',
'["专业维修车间", "设备检测室", "配件仓库", "客户休息区", "免费停车场"]',
4.9, 126, 856, 50.00, 30.00, '免费停车，可容纳20辆车', 'approved', 1, 1, '2023-01-15 10:00:00', '2023-01-16 14:30:00', 1, '资质齐全，设备完善，通过审核', 'SC20230115001',
'["电气安装资质证", "充电桩维修许可证", "ISO9001质量认证"]',
'["/images/centers/chengdu_center_1.jpg", "/images/centers/chengdu_center_2.jpg", "/images/centers/chengdu_center_3.jpg"]',
100, NOW(), NOW(), NOW()),

(2, '北京朝阳服务站', '北京市朝阳区建国路88号SOHO现代城', '北京市', '北京市', '朝阳区', '010-85647382', '李主管', '<EMAIL>', 39.915119, 116.463983, '精确定位', '08:00-20:00', '周一至周六', 0,
'["充电桩维修", "设备检测", "应急抢修"]',
'["交流充电桩", "直流充电桩", "超级充电桩"]',
'北京朝阳区专业充电桩服务站，快速响应，专业维修，为您的充电设备保驾护航。',
'["标准维修间", "应急服务车", "配件库房", "客户接待区"]',
4.7, 89, 523, 60.00, 40.00, '地下停车场，收费标准5元/小时', 'approved', 1, 0, '2023-02-20 09:00:00', '2023-02-21 16:00:00', 1, '服务质量良好，通过审核', 'BJ20230220001',
'["电气工程资质", "特种设备维修许可"]',
'["/images/centers/beijing_chaoyang_1.jpg", "/images/centers/beijing_chaoyang_2.jpg"]',
95, NOW(), NOW(), NOW()),

(3, '上海浦东技术中心', '上海市浦东新区张江高科技园区科苑路399号', '上海市', '上海市', '浦东新区', '021-58473629', '王工程师', '<EMAIL>', 31.209316, 121.595503, '精确定位', '09:00-17:30', '周一至周五', 0,
'["充电桩维修", "充电桩安装", "系统升级", "预防性维护"]',
'["智能充电桩", "快充设备", "慢充设备", "充电管理系统"]',
'上海浦东专业充电桩技术服务中心，专注于高端充电设备的维修和技术升级服务。',
'["高端检测设备", "技术研发室", "培训中心", "展示厅"]',
4.8, 156, 734, 80.00, 50.00, '园区内免费停车', 'approved', 1, 1, '2023-03-10 11:00:00', '2023-03-11 10:30:00', 1, '技术实力强，设备先进，通过审核', 'SH20230310001',
'["高新技术企业证书", "充电设备检测资质", "软件著作权证书"]',
'["/images/centers/shanghai_pudong_1.jpg", "/images/centers/shanghai_pudong_2.jpg", "/images/centers/shanghai_pudong_3.jpg"]',
98, NOW(), NOW(), NOW()),

(4, '广州天河快修点', '广州市天河区珠江新城花城大道85号', '广东省', '广州市', '天河区', '020-38756429', '陈师傅', '<EMAIL>', 23.120049, 113.331617, '精确定位', '10:00-22:00', '周一至周日', 0,
'["充电桩维修", "应急抢修"]',
'["家用充电桩", "商用充电桩", "公共充电桩"]',
'广州天河区快速维修服务点，专业快速，当天修复，为您节省宝贵时间。',
'["快修工具", "常用配件", "移动服务车"]',
4.6, 67, 298, 45.00, 25.00, '路边停车，注意限时', 'approved', 1, 0, '2023-04-05 14:00:00', '2023-04-06 09:15:00', 1, '服务及时，通过审核', 'GZ20230405001',
'["维修服务许可证", "电工操作证"]',
'["/images/centers/guangzhou_tianhe_1.jpg"]',
85, NOW(), NOW(), NOW()),

(5, '深圳南山创新站', '深圳市南山区科技园南区深南大道9988号', '广东省', '深圳市', '南山区', '0755-26847392', '刘总监', '<EMAIL>', 22.537845, 113.934734, '精确定位', '09:00-18:00', '周一至周六', 0,
'["充电桩维修", "充电桩安装", "设备检测", "系统升级", "预防性维护"]',
'["新能源充电桩", "智能充电系统", "无线充电设备"]',
'深圳南山高新技术服务站，引领充电桩维修技术创新，提供最前沿的技术服务。',
'["创新实验室", "智能检测设备", "技术培训室", "客户体验中心", "VIP服务区"]',
4.9, 203, 1156, 100.00, 60.00, '智能停车系统，前2小时免费', 'approved', 1, 1, '2023-05-12 08:30:00', '2023-05-13 15:45:00', 1, '创新能力强，服务优质，通过审核', 'SZ20230512001',
'["高新技术企业证书", "创新技术认证", "质量管理体系认证", "环境管理体系认证"]',
'["/images/centers/shenzhen_nanshan_1.jpg", "/images/centers/shenzhen_nanshan_2.jpg", "/images/centers/shenzhen_nanshan_3.jpg", "/images/centers/shenzhen_nanshan_4.jpg"]',
99, NOW(), NOW(), NOW());

-- 插入测试评价数据
INSERT INTO `service_center_reviews` (`id`, `service_center_id`, `order_id`, `open_id`, `user_nickname`, `user_avatar`, `rating`, `content`, `images`, `service_type`, `is_anonymous`, `reply_content`, `reply_time`, `status`, `created_at`, `updated_at`) VALUES
(1, 1, NULL, 'test_user_1', '张先生', 'https://randomuser.me/api/portraits/men/32.jpg', 5, '服务非常专业，技师很有经验，充电桩修好了，价格也合理。', '[]', '充电桩维修', 0, '感谢您的信任，我们会继续提供优质服务！', NOW(), 1, '2023-06-01 14:30:00', '2023-06-01 14:30:00'),
(2, 1, NULL, 'test_user_2', '李女士', 'https://randomuser.me/api/portraits/women/44.jpg', 5, '成都中心维修站真的很棒，设备齐全，服务态度好，强烈推荐！', '[]', '设备检测', 0, NULL, NULL, 1, '2023-06-05 10:15:00', '2023-06-05 10:15:00'),
(3, 2, NULL, 'test_user_3', '王先生', 'https://randomuser.me/api/portraits/men/22.jpg', 4, '北京朝阳服务站位置方便，技术过硬，就是停车费有点贵。', '[]', '充电桩维修', 0, '我们正在协调停车费优惠政策，感谢建议！', NOW(), 1, '2023-06-08 16:20:00', '2023-06-08 16:20:00'),
(4, 3, NULL, 'test_user_4', '陈工程师', 'https://randomuser.me/api/portraits/men/35.jpg', 5, '上海浦东技术中心技术实力确实强，解决了我们的技术难题。', '[]', '系统升级', 0, '技术创新是我们的使命，很高兴能帮到您！', NOW(), 1, '2023-06-12 09:45:00', '2023-06-12 09:45:00'),
(5, 5, NULL, 'test_user_5', '刘总', 'https://randomuser.me/api/portraits/men/45.jpg', 5, '深圳南山创新站服务一流，设施现代化，体验很好。', '[]', '预防性维护', 0, '感谢认可，我们致力于提供最好的服务体验！', NOW(), 1, '2023-06-15 11:30:00', '2023-06-15 11:30:00');
