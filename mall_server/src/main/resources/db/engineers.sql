-- 工程师表
CREATE TABLE IF NOT EXISTS `engineers` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '工程师ID',
  `open_id` varchar(100) DEFAULT NULL COMMENT '微信openId（如果是微信用户）',
  `name` varchar(50) NOT NULL COMMENT '工程师姓名',
  `phone` varchar(20) NOT NULL COMMENT '联系电话',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱地址',
  `avatar` varchar(500) DEFAULT NULL COMMENT '头像URL',
  `gender` varchar(10) DEFAULT NULL COMMENT '性别',
  `age` int(11) DEFAULT NULL COMMENT '年龄',
  `id_card` varchar(18) DEFAULT NULL COMMENT '身份证号（加密存储）',

  -- 专业信息
  `specialties` text COMMENT '专业领域（JSON数组）',
  `experience_years` int(11) DEFAULT '0' COMMENT '工作经验年数',
  `education` varchar(50) DEFAULT NULL COMMENT '学历',
  `certifications` text COMMENT '资质证书（JSON数组）',
  `skills` text COMMENT '技能标签（JSON数组）',

  -- 工作信息
  `work_areas` text COMMENT '服务区域（JSON数组）',
  `work_time` varchar(100) DEFAULT NULL COMMENT '工作时间',
  `hourly_rate` decimal(8,2) DEFAULT NULL COMMENT '时薪',
  `service_fee` decimal(8,2) DEFAULT NULL COMMENT '上门服务费',

  -- 评价信息
  `rating` decimal(3,2) DEFAULT '5.00' COMMENT '平均评分',
  `total_orders` int(11) DEFAULT '0' COMMENT '总接单数',
  `completed_orders` int(11) DEFAULT '0' COMMENT '完成订单数',
  `success_rate` decimal(5,2) DEFAULT '100.00' COMMENT '成功率',

  -- 状态信息
  `status` varchar(20) DEFAULT 'pending' COMMENT '状态：pending-待审核，approved-已通过，rejected-已拒绝，suspended-已暂停，active-活跃中',
  `is_online` tinyint(1) DEFAULT '0' COMMENT '是否在线 0-离线 1-在线',
  `is_available` tinyint(1) DEFAULT '1' COMMENT '是否可接单 0-不可接单 1-可接单',

  -- 审核信息
  `application_time` datetime DEFAULT NULL COMMENT '申请时间',
  `review_time` datetime DEFAULT NULL COMMENT '审核时间',
  `reviewer_id` bigint(20) DEFAULT NULL COMMENT '审核人ID',
  `review_notes` text COMMENT '审核备注',

  -- 个人介绍
  `bio` text COMMENT '个人简介',
  `introduction` text COMMENT '详细介绍',
  `work_photos` text COMMENT '工作照片（JSON数组）',

  -- 系统字段
  `sort_order` int(11) DEFAULT '0' COMMENT '排序权重',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `last_active_at` datetime DEFAULT NULL COMMENT '最后活跃时间',

  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_phone` (`phone`),
  KEY `idx_open_id` (`open_id`),
  KEY `idx_status` (`status`),
  KEY `idx_rating` (`rating`),
  KEY `idx_work_areas` (`work_areas`(255)),
  KEY `idx_specialties` (`specialties`(255)),
  KEY `idx_is_online` (`is_online`),
  KEY `idx_is_available` (`is_available`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工程师表';

-- 工程师专业领域表（用于规范化管理）
CREATE TABLE IF NOT EXISTS `engineer_specialties` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '专业领域ID',
  `name` varchar(50) NOT NULL COMMENT '专业领域名称',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `icon` varchar(255) DEFAULT NULL COMMENT '图标URL',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序权重',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态 0-禁用 1-启用',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_name` (`name`),
  KEY `idx_status` (`status`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工程师专业领域表';

-- 工程师服务区域表
CREATE TABLE IF NOT EXISTS `engineer_service_areas` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `engineer_id` bigint(20) NOT NULL COMMENT '工程师ID',
  `province` varchar(50) NOT NULL COMMENT '省份',
  `city` varchar(50) NOT NULL COMMENT '城市',
  `district` varchar(50) DEFAULT NULL COMMENT '区县',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_engineer_id` (`engineer_id`),
  KEY `idx_area` (`province`, `city`, `district`),
  CONSTRAINT `fk_engineer_service_areas_engineer_id` FOREIGN KEY (`engineer_id`) REFERENCES `engineers` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工程师服务区域表';

-- 插入专业领域数据
INSERT INTO `engineer_specialties` (`id`, `name`, `description`, `icon`, `sort_order`, `status`, `created_at`, `updated_at`) VALUES
(1, '充电桩安装', '专业充电桩安装服务', '/images/icons/install.png', 1, 1, NOW(), NOW()),
(2, '充电桩维修', '充电桩故障诊断与维修', '/images/icons/repair.png', 2, 1, NOW(), NOW()),
(3, '电路检测', '电路系统检测与优化', '/images/icons/circuit.png', 3, 1, NOW(), NOW()),
(4, '设备调试', '充电设备调试与配置', '/images/icons/debug.png', 4, 1, NOW(), NOW()),
(5, '系统升级', '充电系统软硬件升级', '/images/icons/upgrade.png', 5, 1, NOW(), NOW()),
(6, '故障排除', '各类故障快速排除', '/images/icons/troubleshoot.png', 6, 1, NOW(), NOW());

-- 插入测试工程师数据
INSERT INTO `engineers` (`id`, `name`, `phone`, `email`, `avatar`, `gender`, `age`, `specialties`, `experience_years`, `education`, `certifications`, `skills`, `work_areas`, `work_time`, `hourly_rate`, `service_fee`, `rating`, `total_orders`, `completed_orders`, `success_rate`, `status`, `is_online`, `is_available`, `bio`, `introduction`, `work_photos`, `sort_order`, `created_at`, `updated_at`, `last_active_at`) VALUES

(1, '张工程师', '13800138001', '<EMAIL>', 'https://randomuser.me/api/portraits/men/32.jpg', '男', 35,
'["充电桩安装", "充电桩维修", "电路检测"]', 8, '本科',
'["电工证", "充电桩安装资质证", "高级电气工程师"]',
'["充电桩安装", "电路维修", "故障诊断", "设备调试"]',
'["北京市", "天津市"]', '周一至周日 8:00-20:00', 150.00, 50.00, 4.8, 156, 152, 97.44, 'approved', 1, 1,
'8年充电桩行业经验，专业可靠', '张工程师拥有8年充电桩行业经验，持有电工证和充电桩安装资质证书。擅长各类充电桩的安装、维修和故障排除，服务过上百个充电站项目。',
'["/images/engineers/zhang_work1.jpg", "/images/engineers/zhang_work2.jpg"]', 100, NOW(), NOW(), NOW()),

(2, '李技师', '13800138002', '<EMAIL>', 'https://randomuser.me/api/portraits/men/45.jpg', '男', 42,
'["充电桩维修", "故障排除", "系统升级"]', 12, '大专',
'["高级电工证", "充电设备维修证", "安全作业证"]',
'["故障诊断", "系统维护", "设备升级", "安全检测"]',
'["上海市", "江苏省"]', '周一至周六 9:00-18:00', 180.00, 80.00, 4.9, 203, 198, 97.54, 'approved', 1, 1,
'12年电气维修经验，故障排除专家', '李技师在电气设备维修领域有12年丰富经验，特别擅长充电桩故障诊断和系统升级。曾参与多个大型充电站的维护项目。',
'["/images/engineers/li_work1.jpg", "/images/engineers/li_work2.jpg", "/images/engineers/li_work3.jpg"]', 95, NOW(), NOW(), NOW()),

(3, '王师傅', '13800138003', '<EMAIL>', 'https://randomuser.me/api/portraits/men/38.jpg', '男', 38,
'["充电桩安装", "电路检测", "设备调试"]', 10, '中专',
'["电工证", "充电桩安装证", "特种作业证"]',
'["充电桩安装", "线路布设", "设备调试", "现场施工"]',
'["广东省", "深圳市"]', '周一至周日 7:00-19:00', 120.00, 60.00, 4.7, 89, 86, 96.63, 'approved', 0, 1,
'10年安装经验，施工质量可靠', '王师傅专注充电桩安装10年，施工经验丰富，质量可靠。熟悉各种充电桩型号的安装要求，能够快速完成现场施工。',
'["/images/engineers/wang_work1.jpg"]', 90, NOW(), NOW(), DATE_SUB(NOW(), INTERVAL 2 HOUR)),

(4, '陈工', '13800138004', '<EMAIL>', 'https://randomuser.me/api/portraits/men/29.jpg', '男', 29,
'["设备调试", "系统升级", "电路检测"]', 5, '本科',
'["电气工程师", "充电设备调试证", "软件工程师"]',
'["设备调试", "软件升级", "系统集成", "技术支持"]',
'["浙江省", "杭州市"]', '周一至周五 9:00-17:00', 200.00, 100.00, 4.6, 45, 43, 95.56, 'approved', 1, 1,
'年轻有为的技术专家', '陈工是充电桩行业的新生代技术专家，精通设备调试和系统升级。虽然年轻，但技术能力强，善于解决复杂的技术问题。',
'["/images/engineers/chen_work1.jpg", "/images/engineers/chen_work2.jpg"]', 85, NOW(), NOW(), NOW()),

(5, '刘师傅', '13800138005', '<EMAIL>', 'https://randomuser.me/api/portraits/men/50.jpg', '男', 50,
'["充电桩维修", "故障排除", "电路检测"]', 15, '高中',
'["高级电工证", "维修电工证", "安全员证"]',
'["故障维修", "应急抢修", "预防性维护", "安全检查"]',
'["四川省", "成都市"]', '24小时待命', 160.00, 120.00, 4.9, 312, 305, 97.76, 'approved', 1, 1,
'15年维修经验，24小时应急服务', '刘师傅是行业资深维修专家，15年维修经验，提供24小时应急服务。无论多复杂的故障都能快速定位并解决。',
'["/images/engineers/liu_work1.jpg", "/images/engineers/liu_work2.jpg", "/images/engineers/liu_work3.jpg"]', 98, NOW(), NOW(), NOW());
