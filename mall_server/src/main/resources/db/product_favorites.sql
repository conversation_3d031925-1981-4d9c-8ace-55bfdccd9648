-- 商品收藏表
CREATE TABLE IF NOT EXISTS `product_favorites` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '收藏ID',
  `open_id` varchar(100) NOT NULL COMMENT '用户openId',
  `product_id` bigint(20) NOT NULL COMMENT '商品ID',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_product` (`open_id`, `product_id`),
  KEY `idx_open_id` (`open_id`),
  KEY `idx_product_id` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品收藏表';

-- 插入测试数据（可选）
-- INSERT INTO `product_favorites` (`open_id`, `product_id`, `created_at`) VALUES
-- ('test_open_id_1', 1, NOW()),
-- ('test_open_id_1', 2, NOW()),
-- ('test_open_id_2', 1, NOW());
