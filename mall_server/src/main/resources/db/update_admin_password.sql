-- 更新管理员密码
-- 使用正确的BCrypt加密方式

-- 删除现有的管理员数据（如果存在）
DELETE FROM admin_users WHERE username IN ('admin', 'operator');

-- 重新插入管理员数据，使用新的密码哈希
-- 密码：admin123
-- 这个哈希是通过 BCryptPasswordEncoder 生成的
INSERT INTO `admin_users` (`id`, `username`, `password`, `real_name`, `email`, `phone`, `role`, `permissions`, `status`, `login_count`, `created_at`, `updated_at`, `password_updated_at`) VALUES
(1, 'admin', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '超级管理员', '<EMAIL>', '13800000000', 'super_admin', 
'["user_management", "engineer_management", "service_center_management", "order_management", "review_management", "system_management"]', 
1, 0, NOW(), NOW(), NOW()),

(2, 'operator', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '运营管理员', '<EMAIL>', '13800000001', 'admin', 
'["engineer_management", "service_center_management", "order_management", "review_management"]', 
1, 0, NOW(), NOW(), NOW());

-- 或者直接更新现有记录的密码
-- UPDATE admin_users SET password = '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi' WHERE username = 'admin';
-- UPDATE admin_users SET password = '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi' WHERE username = 'operator';
