-- 商品评价表
CREATE TABLE IF NOT EXISTS `product_reviews` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '评价ID',
  `product_id` bigint(20) NOT NULL COMMENT '商品ID',
  `order_id` bigint(20) DEFAULT NULL COMMENT '订单ID（关联购买订单）',
  `order_item_id` bigint(20) DEFAULT NULL COMMENT '订单商品项ID',
  `open_id` varchar(100) NOT NULL COMMENT '用户openId',
  `user_nickname` varchar(50) DEFAULT NULL COMMENT '用户昵称（冗余字段，避免关联查询）',
  `user_avatar` varchar(255) DEFAULT NULL COMMENT '用户头像（冗余字段）',
  `rating` tinyint(4) NOT NULL COMMENT '评分 1-5星',
  `content` text NOT NULL COMMENT '评价内容',
  `images` text COMMENT '评价图片（JSON数组）',
  `spec_info` varchar(255) DEFAULT NULL COMMENT '购买规格信息',
  `is_anonymous` tinyint(1) DEFAULT '0' COMMENT '是否匿名评价 0-否 1-是',
  `reply_content` text COMMENT '商家回复内容',
  `reply_time` datetime DEFAULT NULL COMMENT '商家回复时间',
  `is_top` tinyint(1) DEFAULT '0' COMMENT '是否置顶 0-否 1-是',
  `like_count` int(11) DEFAULT '0' COMMENT '点赞数',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态 0-隐藏 1-显示 2-待审核',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_open_id` (`open_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_rating` (`rating`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_product_reviews_product_id` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品评价表';

-- 评价点赞表
CREATE TABLE IF NOT EXISTS `review_likes` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '点赞ID',
  `review_id` bigint(20) NOT NULL COMMENT '评价ID',
  `open_id` varchar(100) NOT NULL COMMENT '用户openId',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_review_user` (`review_id`, `open_id`),
  KEY `idx_review_id` (`review_id`),
  KEY `idx_open_id` (`open_id`),
  CONSTRAINT `fk_review_likes_review_id` FOREIGN KEY (`review_id`) REFERENCES `product_reviews` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评价点赞表';

-- 评价统计表（用于快速查询商品评价统计信息）
CREATE TABLE IF NOT EXISTS `product_review_stats` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '统计ID',
  `product_id` bigint(20) NOT NULL COMMENT '商品ID',
  `total_reviews` int(11) DEFAULT '0' COMMENT '总评价数',
  `average_rating` decimal(3,2) DEFAULT '0.00' COMMENT '平均评分',
  `rating_1_count` int(11) DEFAULT '0' COMMENT '1星评价数',
  `rating_2_count` int(11) DEFAULT '0' COMMENT '2星评价数',
  `rating_3_count` int(11) DEFAULT '0' COMMENT '3星评价数',
  `rating_4_count` int(11) DEFAULT '0' COMMENT '4星评价数',
  `rating_5_count` int(11) DEFAULT '0' COMMENT '5星评价数',
  `with_images_count` int(11) DEFAULT '0' COMMENT '有图评价数',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_product_id` (`product_id`),
  CONSTRAINT `fk_product_review_stats_product_id` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品评价统计表';
