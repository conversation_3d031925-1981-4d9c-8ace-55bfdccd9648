-- 订单商品表
CREATE TABLE IF NOT EXISTS `mall_order_items` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '订单商品ID',
  `order_id` bigint(20) NOT NULL COMMENT '订单ID',
  `product_id` bigint(20) NOT NULL COMMENT '商品ID',
  `product_name` varchar(255) NOT NULL COMMENT '商品名称（冗余字段，防止商品信息变更）',
  `product_image` varchar(500) NOT NULL COMMENT '商品图片（冗余字段）',
  `product_spec` varchar(255) DEFAULT NULL COMMENT '商品规格',
  `product_price` decimal(10,2) NOT NULL COMMENT '商品单价（下单时价格）',
  `quantity` int(11) NOT NULL COMMENT '购买数量',
  `total_price` decimal(10,2) NOT NULL COMMENT '商品总价（单价 * 数量）',
  `discount_amount` decimal(10,2) DEFAULT '0.00' COMMENT '商品优惠金额',
  `actual_price` decimal(10,2) NOT NULL COMMENT '实际支付金额',
  `is_reviewed` tinyint(1) DEFAULT '0' COMMENT '是否已评价 0-未评价 1-已评价',
  `review_id` bigint(20) DEFAULT NULL COMMENT '评价ID',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_review_id` (`review_id`),
  CONSTRAINT `fk_mall_order_items_order_id` FOREIGN KEY (`order_id`) REFERENCES `mall_orders` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_mall_order_items_product_id` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE RESTRICT,
  CONSTRAINT `fk_mall_order_items_review_id` FOREIGN KEY (`review_id`) REFERENCES `product_reviews` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单商品表';

-- 购物车表
CREATE TABLE IF NOT EXISTS `shopping_cart` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '购物车ID',
  `open_id` varchar(100) NOT NULL COMMENT '用户openId',
  `product_id` bigint(20) NOT NULL COMMENT '商品ID',
  `product_spec` varchar(255) DEFAULT NULL COMMENT '商品规格',
  `quantity` int(11) NOT NULL COMMENT '数量',
  `is_selected` tinyint(1) DEFAULT '1' COMMENT '是否选中 0-未选中 1-选中',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_product_spec` (`open_id`, `product_id`, `product_spec`),
  KEY `idx_open_id` (`open_id`),
  KEY `idx_product_id` (`product_id`),
  CONSTRAINT `fk_shopping_cart_product_id` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='购物车表';

-- 商品收藏表
CREATE TABLE IF NOT EXISTS `product_favorites` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '收藏ID',
  `open_id` varchar(100) NOT NULL COMMENT '用户openId',
  `product_id` bigint(20) NOT NULL COMMENT '商品ID',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_product` (`open_id`, `product_id`),
  KEY `idx_open_id` (`open_id`),
  KEY `idx_product_id` (`product_id`),
  CONSTRAINT `fk_product_favorites_product_id` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品收藏表';
