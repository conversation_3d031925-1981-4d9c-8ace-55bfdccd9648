package com.zlz.mall_server;

import com.zlz.mall_server.mapper.ProductMapper;
import com.zlz.mall_server.model.Products;
import jakarta.annotation.Resource;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;


@SpringBootTest
@RunWith(SpringRunner.class)
public class MallTest {
    @Resource
    private ProductMapper productMapper;

    @Test
    public void testSelect() {
        System.out.println("----select----");
        List<Products> productList = productMapper.selectList(null);
        Assert.assertEquals(12, productList.size());
        productList.forEach(System.out::println);
    }
//    @Autowired
//    private DataSource dataSource;
//
//    @Test
//    public void testConnection() throws Exception {
//        try (Connection conn = dataSource.getConnection()) {
//            System.out.println("✅ 数据库连接成功：" + conn);
//        } catch (Exception e) {
//            System.err.println("❌ 数据库连接失败！");
//        }
//    }

}
