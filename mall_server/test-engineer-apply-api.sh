#!/bin/bash

echo "🧪 测试工程师申请API修复"
echo "========================"

# 服务器地址
SERVER_URL="https://localhost:8443"

echo ""
echo "📋 测试API接口可用性"
echo "===================="

echo "1. 测试工程师列表API:"
echo "GET $SERVER_URL/api/engineers/approved"
ENGINEERS_RESPONSE=$(curl -k -s "$SERVER_URL/api/engineers/approved")
echo "响应: $(echo "$ENGINEERS_RESPONSE" | head -c 100)..."

echo ""
echo "2. 测试工程师申请API:"
echo "POST $SERVER_URL/api/engineers/apply"

# 构造测试数据
TEST_DATA='{
  "name": "测试工程师",
  "phone": "13800138000",
  "email": "<EMAIL>",
  "gender": "男",
  "age": "30",
  "idCard": "123456789012345678",
  "specialties": "[\"充电桩安装\", \"充电桩维修\"]",
  "experienceYears": "5",
  "education": "大专",
  "certifications": "[\"电工证\", \"技能证书\"]",
  "skills": "[\"电路维修\", \"设备安装\"]",
  "workAreas": "[\"成都市\", \"绵阳市\"]",
  "workTime": "周一至周五 9:00-18:00",
  "hourlyRate": "100",
  "serviceFee": "50",
  "bio": "专业充电桩维修工程师",
  "introduction": "拥有5年充电桩维修经验",
  "workPhotos": "[\"https://example.com/photo1.jpg\", \"https://example.com/photo2.jpg\"]"
}'

APPLY_RESPONSE=$(curl -k -s -X POST \
  -H "Content-Type: application/json" \
  -d "$TEST_DATA" \
  "$SERVER_URL/api/engineers/apply")

echo "申请响应:"
echo "$APPLY_RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$APPLY_RESPONSE"

echo ""
echo "📊 API状态检查"
echo "=============="

# 检查申请响应
if echo "$APPLY_RESPONSE" | grep -q '"success":true'; then
    echo "✅ 工程师申请API正常工作"
    
    # 提取申请ID
    APPLICATION_ID=$(echo "$APPLY_RESPONSE" | grep -o '"applicationId":[0-9]*' | grep -o '[0-9]*')
    if [ -n "$APPLICATION_ID" ]; then
        echo "📝 申请ID: $APPLICATION_ID"
        
        echo ""
        echo "3. 测试工程师详情API:"
        echo "GET $SERVER_URL/api/engineers/detail/$APPLICATION_ID"
        DETAIL_RESPONSE=$(curl -k -s "$SERVER_URL/api/engineers/detail/$APPLICATION_ID")
        echo "详情响应: $(echo "$DETAIL_RESPONSE" | head -c 100)..."
        
        if echo "$DETAIL_RESPONSE" | grep -q '"success":true'; then
            echo "✅ 工程师详情API正常工作"
        else
            echo "❌ 工程师详情API有问题"
        fi
    fi
    
elif echo "$APPLY_RESPONSE" | grep -q '"success":false'; then
    echo "⚠️  工程师申请API返回失败，但接口可访问"
    echo "错误信息: $(echo "$APPLY_RESPONSE" | grep -o '"message":"[^"]*"' | cut -d'"' -f4)"
elif echo "$APPLY_RESPONSE" | grep -q '404'; then
    echo "❌ 工程师申请API返回404，接口路径错误"
else
    echo "❌ 工程师申请API无响应或格式错误"
fi

echo ""
echo "📋 API路径对比"
echo "=============="
echo "小程序调用路径: /api/engineers/apply"
echo "后端接口路径1: /api/engineers/apply (EngineerController - 新增)"
echo "后端接口路径2: /api/applications/engineer (ApplicationController - 原有)"

echo ""
echo "🔧 修复说明"
echo "============"
echo "问题: 小程序调用 /api/engineers/apply，但后端只有 /api/applications/engineer"
echo "解决方案: 在 EngineerController 中添加了 /api/engineers/apply 接口"
echo "优势: 保持API路径的一致性和直观性"

echo ""
echo "📱 小程序端测试建议"
echo "=================="
echo "1. 重新启动小程序开发工具"
echo "2. 清除小程序缓存"
echo "3. 在工程师申请页面填写表单并提交"
echo "4. 检查控制台是否还有404错误"
echo "5. 验证申请是否成功提交"

echo ""
echo "🖥️ 管理端验证"
echo "=============="
echo "1. 登录管理后台: $SERVER_URL/admin/engineers"
echo "2. 查看是否有新的工程师申请记录"
echo "3. 测试审核功能是否正常"
echo "4. 验证图片显示是否正常"

echo ""
echo "✅ API修复完成"
echo "=============="
echo "现在工程师申请功能应该可以正常工作了！"
