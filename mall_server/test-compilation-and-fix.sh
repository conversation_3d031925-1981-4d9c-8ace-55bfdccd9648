#!/bin/bash

echo "🔧 测试编译和修复验证"
echo "===================="

echo ""
echo "📋 步骤1: 检查Java编译"
echo "======================"

echo "编译项目..."
mvn compile -q

if [ $? -eq 0 ]; then
    echo "✅ Java编译成功！"
else
    echo "❌ Java编译失败，请检查代码"
    exit 1
fi

echo ""
echo "📋 步骤2: 检查修复前状态"
echo "========================"

# 服务器地址
SERVER_URL="https://localhost:8443"

echo "API: $SERVER_URL/api/service-centers/approved"
BEFORE_RESPONSE=$(curl -k -s "$SERVER_URL/api/service-centers/approved")
BEFORE_COUNT=$(echo "$BEFORE_RESPONSE" | grep -o '"id":[0-9]*' | wc -l | tr -d ' ')
echo "当前API返回数量: $BEFORE_COUNT"

echo ""
echo "API: $SERVER_URL/api/service-centers/stats"
STATS_RESPONSE=$(curl -k -s "$SERVER_URL/api/service-centers/stats")
echo "统计信息:"
echo "$STATS_RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$STATS_RESPONSE"

echo ""
echo "📋 步骤3: 分析问题"
echo "=================="

# 从统计信息中提取数据
TOTAL=$(echo "$STATS_RESPONSE" | grep -o '"total":[0-9]*' | grep -o '[0-9]*')
APPROVED=$(echo "$STATS_RESPONSE" | grep -o '"approved":[0-9]*' | grep -o '[0-9]*')

echo "数据库中总服务网点数: $TOTAL"
echo "数据库中已审核通过数: $APPROVED"
echo "API返回的可显示数: $BEFORE_COUNT"

if [ "$APPROVED" -gt "$BEFORE_COUNT" ]; then
    INACTIVE_COUNT=$((APPROVED - BEFORE_COUNT))
    echo ""
    echo "⚠️  发现问题: 有 $INACTIVE_COUNT 个已审核通过但未激活的服务网点"
    echo ""
    echo "🔧 解决方案:"
    echo "1. 通过管理后台重新审核这些服务网点"
    echo "2. 现在的审核逻辑会自动设置 isActive = true"
    echo "3. 或者直接修改数据库:"
    echo "   UPDATE service_centers SET is_active = 1 WHERE status = 'approved';"
else
    echo ""
    echo "✅ 数据一致，没有发现问题"
fi

echo ""
echo "📋 步骤4: 修复说明"
echo "=================="

echo "✅ 已修复的代码问题:"
echo "1. AdminApiController.java 编译错误已修复"
echo "2. 服务网点审核逻辑已修复 - 审核通过时设置 isActive = true"
echo "3. 工程师审核逻辑已修复 - 移除了不存在的 setIsActive 调用"

echo ""
echo "✅ 修复效果:"
echo "- 代码可以正常编译"
echo "- 新的服务网点审核操作会正确设置 isActive 字段"
echo "- 审核通过的服务网点会在小程序端显示"

echo ""
echo "🚀 下一步操作:"
echo "=============="
echo "1. 重启服务器以应用代码修复:"
echo "   mvn spring-boot:run"
echo ""
echo "2. 登录管理后台处理历史数据:"
echo "   访问: $SERVER_URL/admin/service-centers"
echo "   重新审核已审核通过但未激活的服务网点"
echo ""
echo "3. 验证小程序端显示:"
echo "   重新打开小程序服务网点页面"
echo "   下拉刷新数据"
echo "   检查是否显示了所有新增的维修中心"

echo ""
echo "🎯 问题解决状态:"
echo "================"
echo "✅ 后端编译错误 - 已修复"
echo "✅ 审核逻辑错误 - 已修复"
echo "⏳ 历史数据修复 - 需要手动处理"
echo "⏳ 小程序端验证 - 等待历史数据修复完成"

echo ""
echo "💡 重要提醒:"
echo "============"
echo "现在审核服务网点时会自动激活，但需要处理历史数据中"
echo "那些已审核通过但未激活的服务网点。"
